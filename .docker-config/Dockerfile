# Use the official PHP 8.2 FPM image as the base image
FROM php:8.2-fpm-bookworm

WORKDIR /var/www/html

ENV COMPOSER_ALLOW_SUPERUSER=1

# Install system dependencies and PHP extensions
RUN apt-get update && apt-get install -y --no-install-recommends \
    unzip \
    zip \
    git \
    libcurl4-openssl-dev \
    libpq-dev \
    pkg-config \
    zlib1g-dev \
    libxml2-dev \
    librabbitmq-dev \
    libzip-dev \
    libonig-dev \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    supervisor \
    nginx \
    && curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install \
    pdo_mysql \
    exif \
    curl \
    sockets \
    gd \
    zip \
    && pecl install redis \
    && pecl install amqp \
    && docker-php-ext-enable redis \
    && docker-php-ext-enable amqp \
    && docker-php-ext-enable opcache \
    && docker-php-ext-enable gd

RUN rm -rf /var/www/html/*

COPY --chown=www-data:www-data . .

RUN composer update

RUN composer dump-autoload

#RUN php artisan migrate

RUN mkdir -p /var/www/html/storage/logs/ \
    && touch /var/www/html/storage/logs/laravel.log \
    && chown www-data:www-data /var/www/html/storage/logs/laravel.log \
    && chown -R www-data:www-data /var/www/html/storage/

COPY .docker-config/supervisord.conf /etc/supervisord.conf
COPY .docker-config/nginx.conf /etc/nginx/sites-available/default
COPY .docker-config/php.ini /usr/local/etc/php/conf.d/custom-php.ini

EXPOSE 9000

EXPOSE 80

ENTRYPOINT ["/usr/bin/supervisord", "-n", "-c", "/etc/supervisord.conf"]
