server {
    listen 80;
    server_name _;
    root /var/www/html/public;

    index index.php index.html index.htm;

    access_log /dev/stdout;
    error_log  /dev/stderr;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }

    # Optional: Limit the size of uploaded files
    client_max_body_size 100M;  # Adjust as per your requirement
}
