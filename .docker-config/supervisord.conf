[supervisord]
nodaemon=true

[program:php-fpm]
command=php-fpm -F
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=true
startretries=0

[program:nginx]
command=/usr/sbin/nginx -g 'daemon off;'
stderr_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stdout_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=true
startretries=0

[program:laravel-log-tail]
command=tail -f /var/www/html/storage/logs/laravel.log
stderr_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stdout_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=true
startretries=0

[program:wholesales-back-end-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:listen redis --queue=rqueue,productqueue,eventqueue,createcwqueue
autostart=true
autorestart=true
user=root
numprocs=4
redirect_stderr=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:laravel-scheduler]
process_name=%(program_name)s
command=php /var/www/html/artisan schedule:work
autostart=true
autorestart=true
user=root
redirect_stderr=true
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0



