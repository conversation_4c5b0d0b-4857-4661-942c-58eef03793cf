# Application Name, Must be same with Integration 
# and Notification Services
APP_NAME=

#value : local, production
APP_ENV=

#value : from php artisan generate:key / as is
APP_KEY=

#value : false, true
APP_DEBUG=

#value : discord webhook url, empty
#ketika value diisi & app debug true maka setiap response
#exception akan dikirim ke channel discord
DEBUG_WEBHOOK=
  
#url backend 
APP_URL=
#url wholesales FE
WHOLESALES_APP_URL=
#url b2b FE
B2B_APP_URL=
#URL careom internal FE
INTERNAL_APP_URL=
#URL Integration Backend
S3_STREAM_URL=
#directory bucket gambar article
S3_PRODUCT_FOLDER=
#directory bucket gambar banner promo
S3_BANNER_FOLDER=
#directory bucket file stock utk diupload
S3_STOCK_FOLDER=
#directory bucket utk upload faktur pajak
S3_FAKTUR_FOLDER=
#directory bucket utk upload gambar ktp
S3_KTP_FOLDER=
#directory bucket utk upload gambar npwp
S3_NPWP_FOLDER=

#as is
LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

#db pdo yg dipakai, cth: mysql, pgsql
DB_CONNECTION=

# database credentials
DB_HOST=
DB_PORT=
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=

#value as is  
BROADCAST_DRIVER=pusher
CACHE_DRIVER=redis
FILESYSTEM_DRIVER=local
SESSION_DRIVER=file
SESSION_LIFETIME=120
MEMCACHED_HOST=127.0.0.1

#redis credential
REDIS_HOST=
REDIS_PASSWORD=
REDIS_PORT=

#mailer credential
MAIL_MAILER=
MAIL_HOST=
MAIL_PORT=
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=
MAIL_FROM_ADDRESS=
MAIL_FROM_NAME=

#aws bucket credentials
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_BUCKET=
AWS_USE_PATH_STYPE_ENDPOINT=

#websocket credentials
PUSHER_APP_KEY=
PUSHER_APP_ID=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=
PUSHER_SCHEME=

#as is
MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

#pathfinder sap credentials
API_USERNAME=
API_EIGER_URL=
API_PASSWORD=

#pathfinder sap stock credentials
ARTICLE_STOCK_USERNAME=
ARTICLE_STOCK_PASSWORD=
ARTICLE_STOCK_SAPUSER=
ARTICLE_STOCK_API_URL=

#jne api credentials
JNE_USERNAME=
JNE_API_KEY=
SC_URL=
SC_API_KEY=
SHIPMENTS_JNE_CHECKPRICE =

#rabbitmq credentials
QUEUE_CONNECTION=rabbitmq
RABBITMQ_HOST=
RABBITMQ_PORT=
RABBITMQ_USER=
RABBITMQ_PASSWORD=
RABBITMQ_VHOST=

#Danamon Credential
DANAMON_URL=
DANAMON_BDI_KEY=
DANAMON_SECRET_KEY=
DANAMON_CLIENT_ID=
DANAMON_SECRET_CLIENT_ID=
DANAMON_ADDITIONAL_KEY=
DANAMON_PARTNER_ID=
DANAMON_BIN_NO=
#true = integrasi danamon akan di mock
DANAMON_MOCK=

#redis queue name
REDIS_QUEUE=

#api versioning
API_VERSION=v1
#value: development, staging
API_DEPLOYMENT=development

#botika api token
TOKEN_OTP=
