stages:
  - build
  - deploy

.prepare-deploy-dev:
  image: ubuntu:22.04
  before_script:
    - apt-get update && apt-get install -y ssh
    - touch ${SSH_FILE_DEV} && cat /dev/null | tee ${SSH_FILE_DEV} && echo ${SSH_PEM_KEY_DEV} | base64 -d > ${SSH_FILE_DEV} && chmod 600 ${SSH_FILE_DEV}

.prepare-deploy-staging:
  image: ubuntu:22.04
  before_script:
    - apt-get update && apt-get install -y ssh
    - touch ${SSH_CONFIG_FILENAME_STAGING} && cat /dev/null | tee ${SSH_CONFIG_FILENAME_STAGING} && echo ${SSH_CONFIG_STAGING} | base64 -d > ${SSH_CONFIG_FILENAME_STAGING} && chmod 600 ${SSH_CONFIG_FILENAME_STAGING}
    - touch ${SSH_FILE_STAGING} && cat /dev/null | tee ${SSH_FILE_STAGING} && echo ${SSH_PEM_KEY_STAGING} | base64 -d > ${SSH_FILE_STAGING} && chmod 600 ${SSH_FILE_STAGING}

deploy-dev:
  stage: deploy
  extends: .prepare-deploy-dev
  script:
    - ssh -o StrictHostKeyChecking=no -i ${SSH_FILE_DEV} ${SSH_USER_DEV}@${SSH_HOST_DEV} -p ${SSH_PORT_DEV} "git -C ${SSH_PROJECT_DIR_DEV} pull origin ${CI_COMMIT_BRANCH}"
    - ssh -o StrictHostKeyChecking=no -i ${SSH_FILE_DEV} ${SSH_USER_DEV}@${SSH_HOST_DEV} -p ${SSH_PORT_DEV} "docker compose -f ${SSH_DOCKER_COMPOSE_DEV} up --build --force-recreate -d && docker builder prune -f"
  tags:
    - careom
  only:
    - development

deploy-wholesales-backend-staging:
  stage: deploy
  extends: .prepare-deploy-staging
  retry: 2
  script:
    - |
      ssh -F ${SSH_CONFIG_FILENAME_STAGING} backend-wholesales << ENDSSH
        git -C ${SSH_PROJECT_DIR_STAGING} pull origin ${CI_COMMIT_BRANCH}
        docker compose -f ${SSH_DOCKER_COMPOSE_STAGING} up --build --force-recreate -d wholesales-backend
      ENDSSH
  tags:
    - careom
  only:
    - staging

deploy-b2b-backend-staging:
  stage: deploy
  extends: .prepare-deploy-staging
  retry: 2
  script:
    - |
      ssh -F ${SSH_CONFIG_FILENAME_STAGING} backend-b2b << ENDSSH
        git -C ${SSH_PROJECT_DIR_STAGING} pull origin ${CI_COMMIT_BRANCH}
        docker compose -f ${SSH_DOCKER_COMPOSE_STAGING} up --build --force-recreate -d wholesales-backend
      ENDSSH
  tags:
    - careom
  only:
    - staging
