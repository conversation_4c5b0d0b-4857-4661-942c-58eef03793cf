<?php

namespace App\Console\Commands;

use App\Helpers\RestHelper;
use App\Jobs\MailSender;
use App\Models\BankAccount;
use App\Models\Invoice;
use App\Models\Order;
use App\Models\Product;
use App\Models\ProductSku;
use App\Models\User;
use App\Models\VirtualAccount;
use App\Repositories\GetSocialsRepo;
use App\Repositories\GetStokRepo;
use Faker\Core\Uuid;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToArray;

class ArticleStockScheduler extends Command
{
    // use RestHelper;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:articleStock';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scheduled Article Stock';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Product::chunk(200, function ($products) {
            // $sapArticles = $this->getArticleData($products->pluck('article')->toArray());

            // $existingSkus = ProductSku::whereIn('sku_id', array_column($sapArticles, 'article'))
            //     ->get(['sku_id', 'stock', 'mock'])
            //     ->keyBy('sku_id');

            RestHelper::syncStock($products->pluck('article')->toArray(), null, null);
            // $dataToInsert = [];
            // foreach ($sapArticles as $sapArticle) {
            //     $skuId = $sapArticle['article'];
            //     $stock = $sapArticle['qty'];
            //     $mock = $sapArticle['moq'] ?? 0;

            //     // if (isset($existingSkus[$skuId]) && $existingSkus[$skuId]->stock == $stock && $existingSkus[$skuId]->mock == $mock) {
            //     //     continue;
            //     // }

            //     $dataToInsert[] = [
            //         'id' => Str::uuid(),
            //         'sku_id' => $skuId,
            //         'sku_code_c' => substr($sapArticle['article'], 0, -3),
            //         'stock' => $stock,
            //         'mock' => $mock ?? 0,
            //         'created_at' => now(),
            //         'updated_at' => now(),
            //     ];
            // }

            // if (!empty($dataToInsert)) {
            //     ProductSku::upsert($dataToInsert, ['sku_id'], ['stock', 'mock', 'updated_at']);
            // }
        });
    }

    public function getArticleData($article)
    {
        $article_map = array_map(function ($key) use ($article) {
            return [
                'source' => 'CAREOM',
                'destination' => 'STK',
                'article' => (string) $key,
            ];
        }, $article);

        $article_chunks = array_chunk($article, 80, true);

        foreach ($article_chunks as $key => $article_chunk) {

            $data = [
                "source" => "CAREOM",
                "destination" => "STK",
                "detail" => $article_map
            ];

            $getStokRepo = new GetStokRepo();
            $resp = $getStokRepo->getStock($data);
            $new_resp = $getStokRepo->getData($resp);
        }
        return isset($new_resp['data']) ? $new_resp['data'] : [];
    }
}