<?php

namespace App\Console\Commands;

use App\Helpers\RestHelper;
use App\Models\OrderReseller;
use App\Models\ResellerArticleStock;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Helpers\OrderResellerHelper;
use App\Helpers\Shipments\ShipmentHelper;
use App\Models\ResellerOrderShipment;

class CheckResellerStock extends Command
{
    use RestHelper,OrderResellerHelper,ShipmentHelper;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reseller:checkstock';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'send notif regarding stock';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $resellerStocks = ResellerArticleStock::get();
        foreach($resellerStocks as $stock){
            if($stock->available_stock == 0){
                $msg = 'Produk dengan SKU '.$stock->article.' telah habis. Klik pesan ini untuk melihat detail.';
                $this->notifStore('95d18c54-0036-11ee-bf25-06c39b9216e2', 'Produk Habis', 'customer-internal',$msg);
            }
            if($stock->available_stock <= 3){
                $msg = 'Produk dengan SKU '.$stock->article.' akan segera habis. Klik pesan ini untuk melihat detail.';
                $this->notifStore('95d18c54-0036-11ee-bf25-06c39b9216e2', 'Produk Akan Habis', 'customer-internal',$msg);
            }
        }

        Log::info('orders update to done success');
    }
}
