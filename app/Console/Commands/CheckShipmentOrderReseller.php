<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Jobs\MailSender;
use App\Models\Reseller;
use App\Helpers\RestHelper;
use App\Models\OrderReseller;
use App\Models\MasterParameter;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

use Illuminate\Support\Facades\Log;
use App\Helpers\OrderResellerHelper;
use App\Repositories\GetSocialsRepo;
use App\Models\ResellerOrderShipment;
use App\Helpers\Shipments\ShipmentHelper;
use App\Models\ResellerOrderDetails as od;
use App\Models\ResellerOrderHeaders as oh;
use App\Models\ResellerOrderPromotion as op;

class CheckShipmentOrderReseller extends Command
{
    use RestHelper,OrderResellerHelper,ShipmentHelper;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check-shipment:order-reseller';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $ohs = OrderReseller::where('order_status','Dikirim')->get();
            if($ohs->isEmpty()){
                Log::info('no orders ');
                throw new \Exception('No orders found');
            }

            foreach($ohs as $oh){
                if (str_contains(strtolower($oh->shipment_method),'jne')) {
                    $ros = ResellerOrderShipment::where('order_header_id',$oh->id)->where('order_status','Dikirim')->first();
                    if ($ros != null) {
                        $checkStatus = $this->checkStatusShipmentJNE($ros);
                    
                        if (!isset($checkStatus['error']) && strtolower($checkStatus['cnote']['pod_status']) == 'delivered' && (str_contains(strtolower(end($checkStatus['history'])['desc']),'received') || str_contains(strtolower(end($checkStatus['history'])['desc']),'delivered'))) {
                            $lastStatus = end($checkStatus['history']);
                            $c_ros = ResellerOrderShipment::where('order_header_id',$oh->id)->where('order_status',OrderReseller::ORDER_RESELLER_DITERIMA)->first();
                            if ($c_ros == null) {
                                ResellerOrderShipment::create([
                                    'order_header_id' => $oh->id,
                                    'location_code' => $ros->location_code,
                                    'transporter_id' => $ros->transporter_id,
                                    'awb_no' => $ros->awb_no,
                                    'delivery_number' => $ros->delivery_number,
                                    'shipment_date' => Carbon::parse($lastStatus['date'])->format('Y-m-d H:i:s'),
                                    'shipment_status' => 'Arrived',
                                    'order_status' => 'Diterima'
                                ]);

                                $oh->order_status = 'Diterima';
                                $oh->save();
    
                                $msg_internal = 'Pesanan dengan nomor '.$oh->order_no.' dari reseller '.$oh->reseller->reseller_id.' telah diterima oleh pembeli. Klik pesan ini untuk melihat detail.';
                                $msg_reseller = 'Pesanan dengan nomor '.$oh->order_no.' telah sampai di pembeli. Klik di sini untuk melihat lebih detail!';
                                $sales_all = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'RESELLER')->first();
                                $this->notifStore($sales_all->value,'Pesanan Diterima Oleh Pembeli','order-rsl-internal',$msg_internal,$oh->order_no,'transaction');
                                $this->notifStore($oh->reseller->reseller_id,'Pesanan Diterima Oleh Pembeli','order-rsl-reseller',$msg_reseller,$oh->order_no,'transaction');
                            }
                        }
                    }
                }

                if (str_contains(strtolower($oh->shipment_method),'sicepat')) {
                    $ros = ResellerOrderShipment::where('order_header_id',$oh->id)->where('order_status','Dikirim')->first();
                    if ($ros != null) {
                        $checkStatus = $this->checkStatusShipmentSC($ros);
                    
                        if (!isset($checkStatus['error']) && strtolower($checkStatus['result']['last_status']['status']) == 'delivered' && str_contains(strtolower($checkStatus['result']['last_status']['receiver_name']),'diterima')) {
                            $lastStatus = $checkStatus['result']['last_status'];
                            $c_ros = ResellerOrderShipment::where('order_header_id',$oh->id)->where('order_status',OrderReseller::ORDER_RESELLER_DITERIMA)->first();
                            if ($c_ros == null) {
                                ResellerOrderShipment::create([
                                    'order_header_id' => $oh->id,
                                    'location_code' => $ros->location_code,
                                    'transporter_id' => $ros->transporter_id,
                                    'awb_no' => $ros->awb_no,
                                    'delivery_number' => $ros->delivery_number,
                                    'shipment_date' => Carbon::parse($lastStatus['date_time'])->format('Y-m-d H:i:s'),
                                    'shipment_status' => 'Arrived',
                                    'order_status' => 'Diterima'
                                ]);
                                
                                $oh->order_status = 'Diterima';
                                $oh->save();

                                $msg_internal = 'Pesanan dengan nomor '.$oh->order_no.' dari reseller '.$oh->reseller->reseller_id.' telah diterima oleh pembeli. Klik pesan ini untuk melihat detail.';
                                $msg_reseller = 'Pesanan dengan nomor '.$oh->order_no.' telah sampai di pembeli. Klik di sini untuk melihat lebih detail!';
                                $sales_all = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'RESELLER')->first();
                                $this->notifStore($sales_all->value,'Pesanan Diterima Oleh Pembeli','order-rsl-internal',$msg_internal,$oh->order_no,'transaction');
                                $this->notifStore($oh->reseller->reseller_id,'Pesanan Diterima Oleh Pembeli','order-rsl-reseller',$msg_reseller,$oh->order_no,'transaction');
                            }
                        }
                    }
                }
            }

            Log::info('check shipment order success');    
        } catch (\Exception $e) {
            Log::info('check shipment order failed',[
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ]); 
        }    
    }
}
