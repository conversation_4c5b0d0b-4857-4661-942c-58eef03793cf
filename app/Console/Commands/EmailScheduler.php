<?php

namespace App\Console\Commands;

use App\Jobs\MailSender;
use App\Models\BankAccount;
use App\Models\Invoice;
use App\Models\Order;
use App\Models\User;
use App\Models\VirtualAccount;
use App\Repositories\GetSocialsRepo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EmailScheduler extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:email';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scheduled email sender';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $queue_list = [];

        $users = Invoice::select('u.email', 'invoice.down_payment', 'invoice.due_payment', 'invoice.gross_price', 'invoice.order_no', 'u.reference_id', 'invoice.due_date', 'invoice.invoice_no', 'invoice.invoice_type')
        ->leftJoin('user as u', 'u.reference_id', '=', 'invoice.customer_id')
        ->where(function ($query) {
            $query->whereRaw('DATEDIFF(CURRENT_DATE, invoice.due_date) = -3')
                ->orWhereRaw('DATEDIFF(CURRENT_DATE, invoice.due_date) = -2')
                ->orWhereRaw('DATEDIFF(CURRENT_DATE, invoice.due_date) = -1');
        })
        ->whereIn('invoice.status', ['BELUM LUNAS', 'BELUM DIBAYAR', 'UNPAID'])
        ->whereHas('order', function ($subquery) {
            $subquery->select('order_no')
                ->from('order_header')
                ->whereRaw('invoice.order_no = order_header.order_no');
        })
        ->distinct()
        ->get();

        if ($users->isEmpty()) {
            Log::info('Users not found');
            throw new \Exception('No users found.');
        }

        foreach($users as $user){
            $date_order = Order::where('order_no', $user->order_no)->first()->created_date ?? '-';
            $virtual_account = VirtualAccount::where('customer_id',$user->reference_id)->get()->toArray();
            $bank_account = BankAccount::active()->get()->toArray();

            if($user->invoice_type == 'DOWN PAYMENT'){
                $duetime = \Carbon\Carbon::parse($user->due_date)->format('Y-m-d');
                $duedate = \Carbon\Carbon::parse($user->due_date)->format('H:i:s');
    
                //GET SOCIAL MEDIA ACCOUNTS
                $socmed = new GetSocialsRepo();
                $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
                $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
                $twitter = $socmed->getSocialMediaParameters('TWITTER');
                $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
                $youtube = $socmed->getSocialMediaParameters('YOUTUBE');
                $support = $socmed->getSocialMediaParameters('SUPPORT');
    
    
                $param['orders'] = [
                    'po_no' => $user->order_no,
                    'billingdocument' => $user->invoice_no,
                    'duetime' => $duetime,
                    'duedate' => $duedate,
                    'billingdate' => \Carbon\Carbon::parse($date_order)->format('Y-m-d'),
                    'billingtime' => \Carbon\Carbon::parse($date_order)->format('H:i:s'),
                    'duepayment' => $user->due_payment,
                    'bank_account' => $bank_account,
    
                    //GET SOCIAL MEDIA ACCOUNTS
                    'facebook' => $facebook,
                    'youtube' => $youtube,
                    'twitter' => $twitter,
                    'instagram' => $instagram,
                    'tiktok' => $tiktok,
                    'support' => $support
                ];
    
                array_push($queue_list, new MailSender($user->email, json_encode($param), 'mail_invoice_reminder_dp'));
                Log::info('Sending reminder to '.$user->email);

            }else{
                $resis = DB::table('delivery_number')
                ->leftJoin('invoice', 'invoice.invoice_no' ,'=','delivery_number.invoice_no')
                ->where('invoice.invoice_no', $user->invoice_no)
                ->select('delivery_number.delivery_no')
                ->get()
                ->toArray();
    
                $deliveryNos = array_map(function ($resi) {
                    return $resi->delivery_no;
                }, $resis);
                
                $resiComma = implode(', ', $deliveryNos);
    
                if($user->down_payment != null){
                    $duepayment = $user->gross_price - $user->due_payment;
                } else {
                    $duepayment = $user->due_payment;
                }
    
                $duetime = \Carbon\Carbon::parse($user->due_date)->format('Y-m-d');
                $duedate = \Carbon\Carbon::parse($user->due_date)->format('H:i:s');
    
                //GET SOCIAL MEDIA ACCOUNTS
                $socmed = new GetSocialsRepo();
                $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
                $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
                $twitter = $socmed->getSocialMediaParameters('TWITTER');
                $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
                $youtube = $socmed->getSocialMediaParameters('YOUTUBE');
                $support = $socmed->getSocialMediaParameters('SUPPORT');
    
    
                $param['orders'] = [
                    'po_no' => $user->order_no,
                    'billingdocument' => $user->invoice_no,
                    'duetime' => $duetime,
                    'duedate' => $duedate,
                    'delivery' => $resiComma,
                    'billingdate' => \Carbon\Carbon::parse($date_order)->format('Y-m-d'),
                    'billingtime' => \Carbon\Carbon::parse($date_order)->format('H:i:s'),
                    'duepayment' => $duepayment,
                    'virtual_account' => $virtual_account,
                    'bank_account' => $bank_account,
    
                    //GET SOCIAL MEDIA ACCOUNTS
                    'facebook' => $facebook,
                    'twitter' => $twitter,
                    'instagram' => $instagram,
                    'support' => $support
                ];
    
                array_push($queue_list, new MailSender($user->email, json_encode($param), 'mail_invoice_reminder'));
                Log::info('Sending reminder to '.$user->email);
            }

        }

        if(count($queue_list) > 0){
            Bus::batch($queue_list)->dispatch();
        }

        Log::info('Sent reminder to '.count($queue_list).' emails');
    }
}

