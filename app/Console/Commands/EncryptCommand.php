<?php

namespace App\Console\Commands;

use App\Models\Customer;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Crypt;

class EncryptCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'customer:encrypt-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'encrypting data .....';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $customers = Customer::all();
        foreach ($customers as $customer) {
            $fields = ['email', 'phone_number', 'national_id', 'npwp'];
            $dirty = false;

            foreach ($fields as $field) {
                $value = $customer->$field;

                try {
                    $decrypted = Crypt::decryptString($value);
                    $newEncrypted = Crypt::encryptString($decrypted);
                } catch (\Exception $e) {
                    $newEncrypted = Crypt::encryptString($value);
                }

                if ($customer->$field !== $newEncrypted) {
                    $customer->$field = $newEncrypted;
                    $dirty = true;
                }
            }

            if ($dirty) {
                $customer->save();
                $this->info("Updated customer ID {$customer->id}");
            }
        }

        $this->info("Encryption check & update completed.");
        return 0;
    }
}