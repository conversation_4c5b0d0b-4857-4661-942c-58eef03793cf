<?php

namespace App\Console\Commands;

use App\Jobs\MailSender;
use App\Models\Reseller;
use App\Helpers\RestHelper;
use App\Models\OrderReseller;
use App\Models\MasterParameter;
use App\Models\ResellerTransaction;
use Illuminate\Console\Command;
use App\Models\UserNotification;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Helpers\OrderResellerHelper;
use App\Repositories\GetSocialsRepo;
use App\Models\ResellerOrderShipment;
use App\Helpers\Shipments\ShipmentHelper;
use App\Models\ResellerOrderDetails as od;
use App\Models\ResellerOrderHeaders as oh;
use App\Models\ResellerOrderPromotion as op;

class OrderResellerDone extends Command
{
    use RestHelper,OrderResellerHelper,ShipmentHelper;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order-reseller:done';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $ids = ResellerOrderShipment::where('order_status','Diterima')
        ->whereRaw('DATE_ADD(created_date, INTERVAL 8 DAY) <= CURRENT_TIMESTAMP')
        ->pluck('order_header_id')->toArray();

        $ohs = OrderReseller::whereIn('id',$ids)->where('order_status', 'Diterima')->where('remarks', null)->get();

        if($ohs->isEmpty()){
            Log::info('no orders ');
            throw new \Exception('No orders found');
        }

        try {
            foreach($ohs as $order){
                $order->order_status = 'Selesai';
                $order->remarks = 'commission_released';
                $order->modified_date = now();
                $order->completed_date = now();
                $order->save();
                $log_tr = $this->logTransactions($order->order_no,['ip','d','sc','c'],'Completed');
    
                $msg_internal = 'Pesanan dengan nomor order '.$order->order_no.' dari reseller '.$order->reseller->reseller_id.' telah selesai. Klik disini untuk melihat detail.';
                $msg_reseller = 'Pesanan dengan nomor '.$order->order_no.' telah selesai dan saldo komisi Anda bertambah. Klik di sini untuk melihat lebih detail!';

                $sales_all = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'RESELLER')->first();
                $this->notifStore($sales_all->value,'Pesanan Telah Selesai','order-rsl-internal',$msg_internal,$order->order_no, UserNotification::RESELLER_TRANSACTION);
                $this->notifStore($order->reseller->reseller_id,'Pesanan Selesai dan Saldo Komisi Diterima','order-rsl-reseller',$msg_reseller,$order->order_no, UserNotification::RESELLER_TRANSACTION);
            }
    
            Log::info('orders update to done success');
        } catch (\Exception $e) {
            $error =  [
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ];
            Log::info("Scheduler order selesai : ",$error);
        }
    }
}
