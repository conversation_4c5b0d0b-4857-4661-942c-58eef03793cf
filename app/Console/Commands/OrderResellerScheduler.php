<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Jobs\MailSender;
use App\Models\Customer;
use App\Models\OrderItem;
use App\Helpers\RestHelper;
use App\Models\OrderReseller;
use App\Models\MasterParameter;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Helpers\OrderResellerHelper;
use App\Repositories\GetSocialsRepo;
use App\Models\ResellerOrderPromotion;

class OrderResellerScheduler extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    use RestHelper,OrderResellerHelper;
    protected $signature = 'order-reseller:cancel';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel Order Reseller Scheduler';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $mp = MasterParameter::where('group_key','RESELLER_TIMER')->where('key','MINUTES_PENDING')->first();
        $oh = OrderReseller::where('order_status','Pending')
        ->whereRaw('DATE_ADD(created_date, INTERVAL '.$mp->value.' MINUTE) <= CURRENT_TIMESTAMP')->with('items')->get();
        if($oh->isEmpty()){
            Log::info('no orders to cancel');
            throw new \Exception('No orders found to cancel');
        }

        try {
            foreach($oh as $order){
            
                $order->order_status = 'Batal';
                $order->save();
                $this->releaseStock($order->all_items->toArray(),$order->location_code);
    
                $ids = $order->items->pluck('id')->toArray();
                $ids[] = $order->id;
                $data = ResellerOrderPromotion::whereIn('reference_id',$ids)->whereIn('discount_type',['coupon','voucher'])->get();
                foreach($data as $d){
                    if (strtolower($d->discount_type) == 'voucher') {
                        $voucher = $d->voucher;
            
                        if (strtolower($voucher->discount_type) == 'percentage') {
                            $voucher->remaining_amount = $voucher->amount;
                            $voucher->used_amount = 0;
                            $voucher->save();
                        }
            
                        if (strtolower($voucher->discount_type) == 'absolute') {
                            $voucher->remaining_amount = $voucher->remaining_amount + $d->amount;
                            $voucher->used_amount = $voucher->used_amount - $d->amount;
                            $voucher->save();
                        }
                    }
                }
    
                $this->orderToCart($order->toArray());
            }
    
            Log::info('orders cancelled successfully');
        } catch (\Exception $e) {
            $error =  [
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ];
            Log::info("Cancel order error : ",$error);
        }

    }
}

