<?php

namespace App\Console\Commands;

use App\Helpers\RestHelper;
use App\Jobs\MailSender;
use App\Models\Customer;
use App\Models\Order;
use App\Models\OrderItem;
use App\Repositories\GetSocialsRepo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class OrderScheduler extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    use RestHelper;
    protected $signature = 'order:cancel';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cancel Order Scheduler';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $oh = Order::where('order_status','Menunggu Konfirmasi')->where('distribution_channel','WHOLESALES')
        ->whereRaw('DATE_ADD(created_date, INTERVAL 1 HOUR) <= CURRENT_TIMESTAMP')->get();
        if($oh->isEmpty()){
            Log::info('no orders ');
            throw new \Exception('No orders found');
        }

        foreach($oh as $order){
            
            $userid = $order->customer_id;
            $order->order_status = 'Batal';
            $order->save();

            if($order->items != null && count($order->items) > 0){
                foreach($order->items as $i){
                    $rq = new \stdClass;
                    $rq->article = $i->article_id;
                    $rq->qty = $i->qty;
                    $rq->is_custom = 0;
                    $this->cartStore($userid, $rq);
                }
            }
            
            $msg = 'Pesanan anda dengan nomor pesanan '.$order->order_no.' Dibatalkan. Segera periksa kelengkapan dan lakukan order ulang.';
            $this->notifStore($userid, 'Pesanan Dibatalkan', 'order', $msg, $order->order_no);

            //data email batalkan pesanan
            $param = [];
            $ord = Order::where('order_no', $order->order_no)->first()->toArray();
            $ordet = OrderItem::with('product.mainImageVariant')->where('order_no', $order->order_no)->get();
            foreach($ordet as $od){
                $od['product']['flag'] = $od->product->flag();
            }
            $ord['order_detail'] = $ordet->toArray();
            $Customer = Customer::where('customer_id', $userid)->first();    
            
            // email data
            $param['customer'] = $Customer->toArray();
            $param['order'] = $ord;
            $socmed = new GetSocialsRepo();
            $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
            $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
            $twitter = $socmed->getSocialMediaParameters('TWITTER');
            $support = $socmed->getSocialMediaParameters('SUPPORT');
            $linkedin = $socmed->getSocialMediaParameters('LINKEDIN');
            $param['social_media'] = [
                'facebook' => $facebook,
                'twitter' => $twitter,
                'instagram' => $instagram,
                'support' => $support,
                'linkedin'=> $linkedin
            ];


            MailSender::dispatch($Customer->email, json_encode($param), 'mail_order_reject');

        }

        Log::info('orders cancelled successfully');

    }
}

