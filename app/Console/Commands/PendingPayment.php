<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\Invoice;
use App\Helpers\RestHelper;
use Illuminate\Console\Command;
use App\Models\ConfigNotification;

class PendingPayment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:paymentNotif';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $notifConfig = ConfigNotification::first();
        $before = $notifConfig->frekuensi_before_day_zero;
        $after = $notifConfig->frekuensi_after_day_zero;
        $periode = $notifConfig->periode;
        $todayBefore = [];
        $todayAfter = [];

        $duePayment = [];

        for ($b = 0; $b < $before; $b++) {
            $todayBefore[Carbon::now()->addDays($periode)->toDateString()] = $periode;
            $periode += $periode;
        }

        $periode = $notifConfig->periode;

        for ($a = 0; $a < $after; $a++) {
            $todayAfter[Carbon::now()->subDays($periode)->toDateString()] = $periode;
            $periode += $periode;
        }

        $invoiceBase = Invoice::where('status', 'BELUM DIBAYAR')
            ->whereHas('customer', function ($q) {
                $q->where('top', '!=', 'T001');
            })
            ->leftJoin('customer_sales', 'invoice.customer_id', '=', 'customer_sales.customer_id');

        $paymentBefore = [];

        (clone $invoiceBase)
            ->whereIn(\DB::raw('DATE(due_date)'), array_keys($todayBefore))
            ->select(['invoice_no', 'order_no', 'due_date', 'invoice.customer_id'])
            ->chunk(100, function ($invoices) use (&$paymentBefore, $todayBefore) {
                foreach ($invoices as $item) {
                    $date = date('Y-m-d', strtotime($item->due_date));
                    $key = $todayBefore[$date] ?? 'unknown';
                    $paymentBefore[$key][] = $item;
                }
            });

        RestHelper::notifPayment($paymentBefore, 'before');
        // TODAY

        $paymentToday = [];

        (clone $invoiceBase)
            ->whereDate('due_date', now()->toDateString())
            ->select(['invoice_no', 'order_no', 'due_date', 'invoice.customer_id', 'customer_sales.sales_id'])
            ->chunk(100, function ($invoices) use (&$paymentToday) {
                foreach ($invoices as $item) {
                    $date = date('Y-m-d', strtotime($item->due_date));
                    $paymentToday[$date][] = $item;
                }
            });

        RestHelper::notifPayment($paymentToday, 'today');


        $paymentAfter = [];

        (clone $invoiceBase)
            ->whereIn(\DB::raw('DATE(due_date)'), array_keys($todayAfter))
            ->select(['invoice_no', 'order_no', 'due_date', 'invoice.customer_id', 'customer_sales.sales_id'])
            ->chunk(100, function ($invoices) use (&$paymentAfter, $todayAfter) {
                foreach ($invoices as $item) {
                    $date = date('Y-m-d', strtotime($item->due_date));
                    $key = $todayAfter[$date] ?? 'unknown';
                    $paymentAfter[$key][] = $item;
                }
            });

        RestHelper::notifPayment($paymentAfter, 'after');

        return 0;
    }
}