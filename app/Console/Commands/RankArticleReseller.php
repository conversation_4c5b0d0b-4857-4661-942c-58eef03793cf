<?php

namespace App\Console\Commands;

use Throwable;
use Illuminate\Bus\Batch;
use App\Models\PublicProduct;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Jobs\RankDiscountArticleReseller;

class RankArticleReseller extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reseller:rank-article';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Currently to rank discount of each article for reseller';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $batches = [];
            PublicProduct::where('is_reseller',1)
                        ->chunk(100,function($d)use(&$batches){
                            RankDiscountArticleReseller::dispatch($d->toArray());
                        });

            // Bus::batch($batches)->then(function (Batch $batch) {
            //     Log::info(json_encode($batch));
            // })->catch(function (Batch $batch, Throwable $e) {
            //     Log::error('ERROR BATCH');
            //     Log::info(json_encode($batch));
            // })->finally(function (Batch $batch) {
            //     Log::info('BATCH DONE');
            //     Log::info(json_encode($batch));
            // })->onConnection('redis')->onQueue('rqueue')->dispatch();
            
        } catch(\Exception $e){
            $error =  [
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ];
            Log::channel('stderr')->info("Rank discount article reseller error : ",$error);
        }
    }
}
