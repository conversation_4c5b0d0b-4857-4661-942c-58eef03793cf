<?php

namespace App\Console\Commands;

use App\Jobs\StockUpdateBatch;
use App\Models\Product;
use App\Models\VirtualAccount;
use App\Repositories\GetSocialsRepo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StockCacher extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'article:cache';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Stock Cacher';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

            Product::IsWholesales()->select('article')->chunk(10, function($i) {  
                $article_map = array_map(function ($b) {
                    return [
                     'source' => 'CAREOM',
                     'destination' => 'STK',
                     'article' => $b,
                    //  'site' => '1200'
                 ];
                 }, $i->pluck('article')->toArray()); 
                 StockUpdateBatch::dispatch($article_map, env('ARTICLE_STOCK_SAPUSER'));

                });
                Log::info('Article Cache Batching on progress');
                return 1;


    }
}

