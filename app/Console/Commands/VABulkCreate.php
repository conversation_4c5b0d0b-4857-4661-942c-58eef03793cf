<?php

namespace App\Console\Commands;

use App\Jobs\StockUpdateBatch;
use App\Models\Reseller;
use App\Models\VirtualAccount;
use App\Repositories\GetSocialsRepo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Helpers\Danamon\DanamonTrait;
use App\Helpers\DanamonHelper;
class VABulkCreate extends Command
{
    use DanamonTrait;
    private $dn_helper;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'danamon:va';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Danamon VA Bulk Creator';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->dn_helper = new DanamonHelper;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

            $dat = Reseller::whereDoesntHave('va')->get();
             foreach($dat as $d){  
                $rq = $this->MappingCreateVA($d);
                $this->dn_helper->queueDispatch($rq,'create_va_debit');
                
                };
                return 1;


    }
}

