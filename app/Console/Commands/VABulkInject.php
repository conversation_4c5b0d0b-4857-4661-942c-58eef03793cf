<?php

namespace App\Console\Commands;

use App\Jobs\StockUpdateBatch;
use App\Models\ResellerCommission;
use App\Models\VirtualAccount;
use App\Repositories\GetSocialsRepo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Helpers\Danamon\DanamonTrait;
use App\Helpers\DanamonHelper;
class VABulkInject extends Command
{
    use DanamonTrait;
    private $dn_helper;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'danamon:inject';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Danamon VA Inject Komisi';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->dn_helper = new DanamonHelper;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

            $dat = ResellerCommission::where('commission_amount','>', 0)->whereNotIn('reseller_id', ['38872b66-94b2-11ee-8524-06c39b9216e2','423c782d-9409-11ee-8524-06c39b9216e2'])->get();
             foreach($dat as $d){  
                $rq = $this->InjectVA($d);
                $this->dn_helper->queueDispatch($rq,'topup_transfer');
                
                };
                return 1;


    }
}

