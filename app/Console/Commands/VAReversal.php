<?php

namespace App\Console\Commands;

use App\Jobs\StockUpdateBatch;
use App\Models\LogDanamon;
use App\Models\ResellerCommission;
use App\Models\VirtualAccount;
use App\Repositories\GetSocialsRepo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Helpers\Danamon\DanamonTrait;
use App\Helpers\DanamonHelper;
class VAReversal extends Command
{
    use DanamonTrait;
    private $dn_helper;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'danamon:reversal';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Danamon VA Reversal Komisi';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->dn_helper = new DanamonHelper;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

            $dat = LogDanamon::where('created_date',">=","2023-12-11 11:24:11")->get();
             foreach($dat as $d){  
                $rq = $this->ReversalVA($d);
                $this->dn_helper->queueDispatch($rq,'transfer_overbooking');
                
                };
                return 1;


    }
}

