<?php
namespace App\Events;
 
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
 
class CreditLimitSynced implements ShouldBroadcast
{
    use SerializesModels;
    public $connection = 'redis';
    public $queue = 'rqueue';
    public $user;
    public $credit_limit;

 
    public function __construct(string $user, $credit_limit)
    {
        $this->user = $user;
        $this->credit_limit = $credit_limit;
    }

    public function broadcastOn()
    {
        return new Channel('user.'.$this->user);
    }

    public function broadcastAs()

    {

        return 'credit.limit';

    }


    public function broadcastWith()

    {

        return $this->credit_limit->resolve();
        // return [
        //     'credit_limit' => $this->cust,
        //     'credit_limit_used' => $this->cust,
        //     'credit_limit_used_percentage' => 100 ,
        //     'credit_limit_remaining' => 0,
        //     'currency' => 0,
        //     'avg_transaction' => 123123,
        //     'total_transaction' => 69,
        //     'last_transaction' => "30 May 1999",
        //     'last_update'=>"1999-05-22T08:57:06.000000Z"
        // ];

    }
}
