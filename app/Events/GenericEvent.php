<?php
namespace App\Events;
 
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

class GenericEvent implements ShouldBroadcast
{
    use SerializesModels;
    public $connection = 'redis';
    public $queue = 'eventqueue';
    public $user;
    public $data;
    public $eventName;

 
    public function __construct(string $user, $data, string $eventName)
    {
        $this->user = $user;
        $this->data = $data;
        $this->eventName = $eventName;
    }

    public function broadcastOn()
    {
        return new Channel('user.'.$this->user);
    }

    public function broadcastAs()

    {

        return $this->eventName;

    }


    public function broadcastWith()

    {
        Log::info($this->user);
        // pakek resolve kalau passing jsonresource
        // return $this->credit_limit->resolve();

        if($this->data instanceof JsonResource){
            return $this->data->resolve();
        }
        return $this->data;

    }
}
