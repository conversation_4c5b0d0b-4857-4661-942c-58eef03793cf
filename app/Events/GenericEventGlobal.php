<?php
namespace App\Events;
 
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Queue\SerializesModels;
use Illuminate\Http\Resources\Json\JsonResource;

class GenericEventGlobal implements ShouldBroadcast
{
    use SerializesModels;
    public $connection = 'redis';
    public $queue = 'eventqueue';
    public $data;
    public $eventName;

 
    public function __construct($data, string $eventName)
    {
        $this->data = $data;
        $this->eventName = $eventName;
    }

    public function broadcastOn()
    {
        return [
            new PresenceChannel('stock.event'),
        ];
    }

    public function broadcastAs()

    {

        return $this->eventName;

    }


    public function broadcastWith()

    {
        // pakek resolve kalau passing jsonresource
        // return $this->credit_limit->resolve();

        if($this->data instanceof JsonResource){
            return $this->data->resolve();
        }
        return $this->data;

    }
}
