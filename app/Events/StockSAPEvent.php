<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class StockSAPEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $connection = 'redis';
    public $queue = 'eventqueue';
    public $data;
    public $status;
    public $sku;
    public function __construct($sku, $status, $data)
    {
        $this->data = $data;
        $this->status = $status;
        $this->sku = $sku;
    }

    public function broadcastOn()
    {
        return new Channel('article.' . $this->sku);
    }

    public function broadcastAs()
    {
        return 'stock.update';
    }

    public function broadcastWith()
    {

        \Log::info('success');
        return [
            'status' => $this->status,
            'data' => $this->data
        ];
    }
}