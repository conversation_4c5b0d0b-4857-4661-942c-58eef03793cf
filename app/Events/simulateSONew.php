<?php

namespace App;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class simulateSONew implements ShouldBroadcast
{
    use SerializesModels;
    public $connection = 'redis';
    public $queue = 'rqueue';
    public $user;
    public $simulate_so;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(string $user, $simulate_so)
    {
        $this->user = $user;
        $this->simulate_so = $simulate_so;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new Channel('user.'.$this->user);
    }

    public function broadcastAS()
    {
        return 'simulateSO.new';
    }
}
