<?php

namespace App\Exceptions;

use Throwable;
use App\Traits\ResponseAPI;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Validation\ValidationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;

class Handler extends ExceptionHandler
{
    use ResponseAPI;
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->renderable(function (NotFoundHttpException $e, $request) {
            if ($request->is('api/*')) {
                return response()->json(
                    [
                        'error' => true,
                        'status' => '404 Error!',
                        'message' => 'Record not found.',
                        'data' => []
                    ],
                    404,
                    [],
                    JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT
                );
            }
            if ($request->is('/')) {
                return response()->json(
                    [
                        'error' => false,
                        'status' => 'success',
                        'message' => 'hi.',
                        'data' => []
                    ],
                    200,
                    [],
                    JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT
                );
            }
        });

        $this->renderable(function (Throwable $e, $request) {
            if (getenv('APP_DEBUG') == 'true' && $e->getMessage() != '') {
                // $this->webhookException($e, $request);
            }

            if ($e instanceof AuthenticationException) {
                return response()->json(
                    [
                        'error' => true,
                        'status' => '401 Bad Request.',
                        'message' => 'Unauthenticated',
                        'data' => []
                    ],
                    401,
                    [],
                    JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT
                );
            }

            if (!$e instanceof AuthenticationException && !$e instanceof ValidationException) {
                return $this->sendError('Terjadi Kesalahan!', 500, '', [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
            };

        });

    }

    // protected function getStatusCode(Throwable $e)
    // {
    //     if ($e instanceof \Symfony\Component\HttpKernel\Exception\HttpExceptionInterface) {
    //         return $e->getStatusCode();
    //     }

    //     if ($e instanceof \Illuminate\Auth\AuthenticationException) {
    //         return 401;
    //     }

    //     return 500;
    // }
}