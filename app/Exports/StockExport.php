<?php

namespace App\Exports;

use App\Models\Article;
use App\Models\Product;
use App\Models\ProductSku;
use App\Models\CustomerStock;
use App\Models\CustomerShipment;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;

class StockExport implements FromView, WithColumnFormatting
{
    /**
     * @return \Illuminate\Support\Collection
     */

    private $isBulk;

    public function __construct($isBulk = false)
    {
        $this->isBulk = $isBulk;
    }
    public function view(): View
    {


        if ($this->isBulk == true) {
            $tokoId = CustomerShipment::where('customer_id', auth()->user()->customer->customer_id)->first();
            if ($tokoId) {
                $product = CustomerStock::where('customer_shipment_id', $tokoId->customer_shipment_id)->pluck('article_id')->toArray();
                $articles = Article::whereIn('article', $product)
                    ->join('master_color', 'article.product_variant_c', '=', 'master_color.key')
                    ->select('article.*', 'master_color.value as color_value')
                    ->get();
            }
        } else {
            $customer_type = request()->user()->customer->customer_type;
            $stock = ProductSku::where('stock', '>', 0)->pluck('sku_id')->toArray();
            $articles = Article::whereIn('article', $stock)
                ->join('master_color', 'article.product_variant_c', '=', 'master_color.key')
                ->select('article.*', 'master_color.value as color_value')
                ->where('is_wholesales', 1)
                ->get();
        }

        return view('exports.stock', [
            'articles' => $articles,
            'isBulk' => $this->isBulk
        ]);
    }

    public function columnFormats(): array
    {
        return [
            'A' => NumberFormat::FORMAT_TEXT,
        ];
    }
}