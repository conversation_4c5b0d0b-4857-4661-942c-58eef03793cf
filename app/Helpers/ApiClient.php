<?php

namespace App\Helpers;

use App\Services\LoggerIntegration;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;


class ApiClient
{

    public static function _post($url, $payload = [], $headers = ['Content-Type' => 'application/json'])
    {
        try {
            $response = Http::asForm()
                ->withHeaders($headers)->post($url, $payload);
            return json_decode($response, true);
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    public static function _get($url, $payload = [], $headers = ['Content-Type' => 'application/json'])
    {
        try {
            $response = Http::withHeaders($headers)->get($url, $payload);
            return json_decode($response, true);
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }



    public static function request($method, $url, $options = [], $username, $password, $headers = [])
    {
        $client = new Client([
            'base_uri' => $url,
            'headers' => $headers,
            'auth' => [
                $username,
                $password
            ]
        ]);


        try {
            //  Log::channel('stderr')->info('[RESPONSE CALL SAP]');
            $response = $client->request($method, '', $options);

            // return response()->json([
            //     "success" => true,
            //     "message" => "Stock loaded successfully!",
            //     "data" => [
            //         [
            //             "article" => 910000811005,
            //             "site" => "1100",
            //             "qty" => "79",
            //             "uom" => "PC",
            //             "moq" => "1"
            //         ],
            //         [
            //             "article" => 910000811005,
            //             "site" => "1100",
            //             "qty" => "80",
            //             "uom" => "PC",
            //             "moq" => "1"
            //         ],
            //         [
            //             "article" => 910009028001,
            //             "site" => "1100",
            //             "qty" => "90",
            //             "uom" => "PC",
            //             "moq" => "1"
            //         ],
            //         [
            //             "article" => 910001241003,
            //             "site" => "1100",
            //             "qty" => "190",
            //             "uom" => "PC",
            //             "moq" => "1"
            //         ],
            //         [
            //             "article" => 910000005003,
            //             "site" => "1100",
            //             "qty" => "190",
            //             "uom" => "PC",
            //             "moq" => "1"
            //         ],
            //         [
            //             "article" => 910000849002,
            //             "site" => "1100",
            //             "qty" => "190",
            //             "uom" => "PC",
            //             "moq" => "1"
            //         ],
            //         [
            //             "article" => 910000854002,
            //             "site" => "1100",
            //             "qty" => "190",
            //             "uom" => "PC",
            //             "moq" => "1"
            //         ],
            //     ]
            // ]);
            return $response;
            //return new \GuzzleHttp\Psr7\Response($response);


            //return json_decode($response->getBody(), true);
        } catch (\Exception $e) {
            Log::channel('stderr')->info('[ERR CA`LL SAP]');
            Log::channel('stderr')->info($e->getMessage());
            Log::channel('stderr')->info('payload', [
                'base_uri' => $url,
                'headers' => $headers,
                'auth' => [
                    $username,
                    $password
                ]
            ]);

            //     response()->json([
            //     'name' => 'John Doe',
            //     'email' => '<EMAIL>',
            //     'phone' => '555-1234'
            // ]);

            return response()->json([
                "success" => false,
                "message" => $e->getMessage(),
                "data" => [],
                "code" => "500"
            ]);
            // return $response;
        }

    }
}