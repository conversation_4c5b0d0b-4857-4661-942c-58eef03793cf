<?php

namespace App\Helpers\Coupon;

use App\Helpers\OrderResellerHelper;
use App\Helpers\Promotion\PromotionHelper;
use Carbon\Carbon;
use App\Models\Coupon;
use App\Models\OrderReseller;
use App\Models\ResellerToken;
use App\Models\OrderItemReseller;
use App\Models\ResellerOrderHeaders;
use Illuminate\Support\Facades\Auth;
use App\Models\ResellerOrderPromotion;

trait CouponHelper 
{   
    use PromotionHelper,OrderResellerHelper;
    public $ada = 0;

    public function addCoupon(OrderReseller $order, Coupon $coupon)
    {
        try {
            $this->applyBestPromotion(ResellerOrderHeaders::where('id',$order->id)->first());
            $validation = $this->couponValidation($order,$coupon);
            if ($validation['valid'] == false) {
                return $validation;
            }
            
            if (strtolower($coupon->condition_type) == 'promotion') {
                $ids = $order->items()->pluck('id')->toArray();
                $ids[] = $order->id;
                $op = ResellerOrderPromotion::whereIn('reference_id',$ids)->where('reference_name','details')->where('discount_type','promotions')->get();

                foreach ($op as $d) {
                    if ($d->detail != null) {
                        $item = $d->detail;

                        $item->line_amount = $item->unit_price * $item->qty;
                        $item->discount_amount = 0;
                        $item->total_amount = $item->total_amount + $d->amount;
                        $item->save();
                    }
    
                    if (strtolower($d->discount_type) == 'voucher') {
                        $voucher = $d->voucher;
            
                        if (strtolower($voucher->discount_type) == 'percentage') {
                            $voucher->remaining_amount = $voucher->amount;
                            $voucher->used_amount = 0;
                            $voucher->save();
                        }
            
                        if (strtolower($voucher->discount_type) == 'absolute') {
                            $voucher->remaining_amount = $voucher->remaining_amount + $d->amount;
                            $voucher->used_amount = $voucher->used_amount - $d->amount;
                            $voucher->save();
                        }
                    }
                    $d->delete();
                }
            }

            if (strtolower($coupon->type) == 'bill') {
                $apply = $this->applyCouponBill($order,$coupon);
            }

            if (strtolower($coupon->type) == 'line') {

                if (strtolower($coupon->condition_type) == 'all') {
                    $items = $order->items()->get();

                    foreach($items as $i => $item){
                        if ($i == count($items)) {
                            $apply = $this->applyCoupon($item,$coupon,$order->totalLineAmount($order),true);
                        } else {
                            $apply = $this->applyCoupon($item,$coupon,$order->totalLineAmount($order));
                        }
                        
                    }
                } else {
                    $c_articles = $coupon->articles()->pluck('article_id')->toArray();
                    $items = $order->items()->whereIn('article_id',$c_articles)->get();
                    foreach($items as $i => $item){
                        if ($i == count($items)) {
                            $apply = $this->applyCoupon($item,$coupon,$order->totalLineAmountIn($order, $c_articles),true);
                        } else {
                            $apply = $this->applyCoupon($item,$coupon,$order->totalLineAmountIn($order, $c_articles));
                        }
                    }
                }
                
            }

            return 'pass';
        } catch (\Exception $e) {            
            return $e;
        }

        
    }

    public function couponValidation(OrderReseller $order, Coupon $coupon)
    {
        try {
            $c_articles = $coupon->articles->toArray();
            $de = strtolower(date('l')).'_enabled';
            $dsa = strtolower(date('l')).'_start_at';
            $dea = strtolower(date('l')).'_end_at';
            
            $valMsg = [
                'valid' => true,
                'validation' => []
            ];

            if (( strtolower($coupon->category) == 'coupon' || strtolower($coupon->category) == 'coupon site') && $coupon->mix_promotion == 0) {
                $ids = $order->items()->pluck('id')->toArray();
                $ids[] = $order->id;
                $count_op = ResellerOrderPromotion::whereIn('reference_id',$ids)->whereIn('discount_type',['coupon','voucher','promotions'])->count();
                if ($count_op > 0) {
                    $valMsg['valid'] = false;
                    $valMsg['validation'][] = [
                        'message' => 'Coupon can not mix with other promotions!'
                    ];
                }
            }
            
            if ($coupon->status != 'active') {
                $valMsg['valid'] = false;
                $valMsg['validation'][] = [
                    'message' => 'Coupon not active!'
                ];
            }
            
            // dd($coupon->$de == 1,Carbon::parse($coupon->$dsa)->lte(now()),Carbon::parse($coupon->$dea)->gte(now()));
            if ($coupon->$de != 1 && !Carbon::parse($coupon->$dsa)->lte(now()) && !Carbon::parse($coupon->$dea)->gte(now()) ) {
                $valMsg['valid'] = false;
                $valMsg['validation'][] = [
                    'message' => 'Time is not applicable!'
                ];
            }

            $all_order = OrderReseller::whereNotIn('order_status',[OrderReseller::ORDER_RESELLER_BATAL,OrderReseller::ORDER_RESELLER_MENUNGGU_PEMBAYARAN,OrderReseller::ORDER_RESELLER_PENDING,OrderReseller::ORDER_RESELLER_PENGEMBALIAN])->get();
            $applied_orders_count = 0;
            foreach ($all_order as $d) {
                $rop = ResellerOrderPromotion::whereIn('reference_id',$d->items()->pluck('id')->toArray())
                                            ->where('discount_id',$coupon->id)
                                            ->where('discount_type','coupon')->exists();
                if ($rop == true) {
                    $applied_orders_count += 1;
                }

            }
            
            if ($coupon->max_use_count > 0 && $applied_orders_count >= $coupon->max_use_count){
                $valMsg['valid'] = false;
                $valMsg['validation'][] = [
                    'message' => 'Coupon has reached maximum use!'
                ];
            }
            
            $user = Auth::guard('sanctum')->user();
            $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;
            $user_coupon_used = 0;
            $user_order = OrderReseller::where('customer_id',$customer_id)->whereNotIn('order_status',[OrderReseller::ORDER_RESELLER_BATAL,OrderReseller::ORDER_RESELLER_MENUNGGU_PEMBAYARAN,OrderReseller::ORDER_RESELLER_PENDING,OrderReseller::ORDER_RESELLER_PENGEMBALIAN])->get();
            foreach ($user_order as $d) {
                $rop = ResellerOrderPromotion::whereIn('reference_id',$d->items()->pluck('id')->toArray())
                                            ->where('discount_id',$coupon->id)
                                            ->where('discount_type','coupon')->exists();
                if ($rop == true) {
                    $user_coupon_used += 1;
                }

            }

            if ($coupon->max_per_user > 0 && $user_coupon_used >= $coupon->max_per_user) {
                $valMsg['valid'] = false;
                $valMsg['validation'][] = [
                    'message' => 'Coupon has reached maximum use per user!'
                ];
            }
            
            if (strtolower($coupon->condition_type) == 'article') {
                if (empty($c_articles) || empty($order->items()->get()->toArray())) {
                    $matched = [];
                } else {
                    $matched = array_filter($order->items()->get()->toArray(),function ($data) use ($c_articles){
                        return in_array($data['article_id'], array_column($c_articles,'article_id'));
                    });
                }
                $valMsg['matched'] = array_column($matched,'article_id');
        
                if (empty($matched)) {
                    $valMsg['valid'] = false;
                    $valMsg['validation'][] = [
                        'message' => 'No item in order can use this coupon'
                    ];
                }
            }
            // location masih belum ada pencarahan
            // dd($valMsg);
            
            return $valMsg;
        } catch (\Exception $e) {
            return [
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ];
            //throw $th;
        }
    }
    
    public function getCurrentAvailableCoupons()
    {
        $cd = strtolower(date('l'));
        return Coupon::active()->activeDate()->activeDay($cd."_enabled",$cd."_start_at", $cd."_end_at");
    }

    public function applyCoupon(OrderItemReseller $item, Coupon $coupon, $totalAmount = 0, $last = false)
    {
        
        if (strtolower($coupon->discount_type) == 'percentage') {
            $discount_amount = $this->getProrateDiscount($totalAmount,$item->line_amount, $totalAmount*($coupon->amount/100));
            if ($last == true) {
                if (($discount_amount + $this->ada)/$totalAmount*100 != $coupon->amount) {
                    $miss_dp = $coupon->amount - (($discount_amount + $this->ada)/$totalAmount*100);
                    $adj_discount_amount = $totalAmount*$miss_dp;
                    $discount_amount = $discount_amount + $adj_discount_amount;
                }
            }
            if ($discount_amount > $item->total_amount) {
                $discount_amount = $item->total_amount;
                $total_amount = 0;
            } else {
                $total_amount = $item->total_amount - $discount_amount;
            }
            ResellerOrderPromotion::insert([
                'reference_name' => 'details',
                'reference_id' => $item->id,
                'discount_type' => 'coupon',
                'discount_id' => $coupon->id,
                'amount' => $discount_amount,
            ]);
            $item->discount_amount = $item->discount_amount + $discount_amount;
            $item->total_amount = $total_amount;
            $item->save();
        }

        if (strtolower($coupon->discount_type) == 'absolute') {
            $discount_amount = $this->getProrateDiscount($totalAmount,$item->line_amount,$coupon->amount);
            if ($last == true) {
                if ($coupon->amount - ($this->ada + $discount_amount) != 0) {
                    $discount_amount = $discount_amount + ($coupon->amount - ($this->ada + $discount_amount));
                }
            }
            if ($discount_amount > $item->total_amount) {
                $discount_amount = $item->total_amount;
                $total_amount = 0;
            } else {
                $total_amount = $item->total_amount - $discount_amount; 
            }
            ResellerOrderPromotion::insert([
                'reference_name' => 'details',
                'reference_id' => $item->id,
                'discount_type' => 'coupon',
                'discount_id' => $coupon->id,
                'amount' => $discount_amount,
            ]);
            $item->discount_amount = $item->discount_amount + $discount_amount;
            $item->total_amount = $total_amount;
            $item->save();
        }

        $this->ada += $discount_amount;

        return 'pass';
    }

    public function applyCouponBill(OrderReseller $order, Coupon $coupon)
    {  
        if (strtolower($coupon->condition_type) == 'all') {
            $ga = $order->items()->sum('line_amount');
            
            foreach($order->items as $i => $item)
            {
                if (strtolower($coupon->discount_type) == 'percentage') {
                    $discount_amount = $this->getProrateDiscount($ga,$item->line_amount,$ga*($coupon->amount/100));
                    if ($i == $order->items()->count()) {
                        if (($discount_amount + $this->ada)/$ga*100 != $coupon->amount) {
                            $miss_dp = $coupon->amount - (($discount_amount + $this->ada)/$ga*100);
                            $adj_discount_amount = $ga*$miss_dp;
                            $discount_amount = $discount_amount + $adj_discount_amount;
                        }
                    }
                }
        
                if (strtolower($coupon->discount_type) == 'absolute') {
                    $discount_amount = $this->getProrateDiscount($ga,$item->line_amount,$coupon->amount);
                    if ($i == $order->items()->count()) {
                        if ($coupon->amount - ($this->ada + $discount_amount) != 0) {
                            $discount_amount = $discount_amount + ($coupon->amount - ($this->ada + $discount_amount));
                        }
                    }
                }

                if ($discount_amount > $item->total_amount) {
                    $discount_amount = $item->total_amount;
                    $total_amount = 0;
                } else {
                    $total_amount = $item->total_amount - $discount_amount; 
                }
                ResellerOrderPromotion::insert([
                    'reference_name' => 'details',
                    'reference_id' => $item->id,
                    'discount_type' => 'coupon',
                    'discount_id' => $coupon->id,
                    'amount' => $discount_amount,
                ]);
                
                $item->discount_amount = $item->discount_amount + $discount_amount;
                $item->total_amount = $total_amount;
                $item->save();
                $this->ada += $discount_amount;
            }
        } else {
            $c_articles = $coupon->articles->toArray();
            $items = $order->items->toArray();
            $matched = array_filter($items,function ($data) use ($c_articles){
                return in_array($data['article_id'], array_column($c_articles,'article_id'));
            });
            
            $articles = array_column($matched,'article_id');

            $ga = $order->items()->whereIn('article_id',$articles)->sum('line_amount');

            $items = $order->items()->whereIn('article_id',$articles)->get();

            foreach ($items as $i => $item) {
                if (strtolower($coupon->discount_type) == 'percentage') {
                    $discount_amount = $this->getProrateDiscount($ga,$item->line_amount,$ga*($coupon->amount/100));
                    if ($i == $order->items()->count()) {
                        if (($discount_amount + $this->ada)/$ga*100 != $coupon->amount) {
                            $miss_dp = $coupon->amount - (($discount_amount + $this->ada)/$ga*100);
                            $adj_discount_amount = $ga*$miss_dp;
                            $discount_amount = $discount_amount + $adj_discount_amount;
                        }
                    }
                }
        
                if (strtolower($coupon->discount_type) == 'absolute') {
                    $discount_amount = $this->getProrateDiscount($ga,$item->line_amount,$coupon->amount);
                    if ($i == $order->items()->count()) {
                        if ($coupon->amount - ($this->ada + $discount_amount) != 0) {
                            $discount_amount = $discount_amount + ($coupon->amount - ($this->ada + $discount_amount));
                        }
                    }
                }

                if ($discount_amount > $item->total_amount) {
                    $discount_amount = $item->total_amount;
                    $total_amount = 0;
                } else {
                    $total_amount = $item->total_amount - $discount_amount; 
                }
                ResellerOrderPromotion::insert([
                    'reference_name' => 'details',
                    'reference_id' => $item->id,
                    'discount_type' => 'coupon',
                    'discount_id' => $coupon->id,
                    'amount' => $discount_amount,
                ]);
                
                $item->discount_amount = $item->discount_amount + $discount_amount;
                $item->total_amount = $total_amount;
                $item->save();
                $this->ada += $discount_amount;
            }
        }

        return 'pass';
    }
}