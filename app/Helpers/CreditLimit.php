<?php

namespace App\Helpers;

use DB;
use App\Models\{Customer, Sales};
use App\Models\CreditLimit as CreditLimitDB;
use App\Helpers\ApiClient;
use App\Services\LoggerIntegration;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CreditLimit
{
    private $detailPlafond = [];
    private $url, $username, $password = '';
    private $headers = [];
    private $success, $failed = 0;
    public function __construct($customers = [])
    {
        $this->url = env('API_EIGER_URL') . '/api/care-om/plafond';
        $this->detailPlafond = $customers;
        $this->username = env('API_USERNAME');
        $this->password = env('API_PASSWORD');
        $this->headers = ['sapuser' => env('ARTICLE_SAPUSER', 'eg_taryono')];
        $this->success = 0;
        $this->failed = 0;
    }

    public function response()
    {
        Log::channel('stderr')->info('[REQUEST SAPUSER PLAFOND]', [$this->detailPlafond[0]['customer']]);
        $this->headers['sapuser'] = Customer::where('customer_id', $this->detailPlafond[0]['customer'] ?? '0')->first()->customer_sales[0]->sap_username ?? env('ARTICLE_STOCK_SAPUSER', 'eg_taryono');
        Log::channel('stderr')->info('[REQUEST SAPUSER LIMIT]', $this->headers);

        return ApiClient::request(
            'POST',
            $this->url,
            [
                'json' => [
                    'source' => 'CAREOM',
                    'destination' => 'PLF',
                    'detailPlafond' => $this->detailPlafond
                ]
            ],
            $this->username,
            $this->password,
            $this->headers
        );
    }

    public function syncQueue()
    {
        try {
            if (Cache::has('SAP-Access-Cache') && Cache::get('SAP-Access-Cache') === "not-available") {
                return Cache::get('SAP-Access-Cache');
            } else {
                $t_response = $this->response();
                if (is_array($t_response)) {
                    $response = $t_response;
                } elseif ($t_response->getStatusCode() == 200) {
                    $response = json_decode($t_response->content(), true);
                } elseif ($t_response->getStatusCode() == 201) {
                    $response = json_decode($t_response->getBody(), true);
                }
                Log::channel('stderr')->info($response);
                if (!isset($response['success']) || $response['success'] != true) {
                    Cache::set('SAP-Access-Cache', "not-available", now()->addMinutes(10));
                    Log::channel('stderr')->info("NA");
                    return "not-available";
                } else {
                    $data = $response['data'];
                    Log::info($data);
                    DB::transaction(function () use ($data) {
                        $customerId = data_get($this->detailPlafond, '0.customer');
                        foreach ($data as $d) {
                            try {
                                CreditLimitDB::where('customer_external_id', $customerId)->updateOrcreate(
                                    ['customer_external_id' => $customerId],
                                    [
                                        'credit_limit' => $d['plafond'],
                                        'credit_limit_used' => $d['terpakai'],
                                        'credit_limit_remaining' => $d['sisa'],
                                        'credit_limit_used_percentage' => $d['percentage'],
                                        'currency' => $d['currency'],
                                        'created_by' => $customerId,
                                        'modified_by' => $customerId
                                    ]
                                );

                                $this->success++;
                                $status = 'success';
                                $body = $d;
                            } catch (\Exception $e) {
                                //
                            }
                            (new LoggerIntegration)->InsertLogger([
                                'reference_no' => $customerId,
                                'module' => 'SAP',
                                'name' => 'Logging Integration Check Plafond',
                                'type' => 'Inbound',
                                'status' => $status,
                                'description' => $body
                            ]);
                        }
                    });

                }

                // Cache::set('SAP-Access-Cache', "available", now()->addMinutes(10));
                Log::channel('stderr')->info("av");
                return "available";
            }

        } catch (\Exception $e) {
            Cache::set('SAP-Access-Cache', "not-available", now()->addMinutes(10));
            Log::channel('stderr')->info($e->getMessage());
            return "available";
        }
    }

    public function sync()
    {
        if (is_array($this->response())) {
            $response = $this->response();
        } elseif ($this->response()->getStatusCode() == 200) {
            $response = json_decode($this->response()->content(), true);
        } elseif ($this->response()->getStatusCode() == 201) {
            $response = json_decode($this->response()->getBody(), true);
        }


        // dd($response);
        if (!isset($response['success']) || $response['success'] != true) {
            Log::channel('stderr')->info('SAP Unreachable');
            return false;
        } else {
            $data = $response['data'];
            DB::transaction(function () use ($data) {
                $customerId = data_get($this->detailPlafond, '0.customer');
                foreach ($data as $d) {
                    try {
                        CreditLimitDB::where('customer_external_id', $customerId)->updateOrcreate(
                            ['customer_external_id' => $customerId],
                            [
                                'credit_limit' => $d['plafond'],
                                'credit_limit_used' => $d['terpakai'],
                                'credit_limit_remaining' => $d['sisa'],
                                'credit_limit_used_percentage' => $d['percentage'],
                                'currency' => $d['currency'],
                                'created_by' => $customerId,
                                'modified_by' => $customerId
                            ]
                        );

                        $this->success++;
                        $status = 'success';
                        $body = $d;
                    } catch (\Exception $e) {
                        $this->failed++;
                        $status = 'failed';
                        $body = $e->getMessage();
                    }
                    (new LoggerIntegration)->InsertLogger([
                        'reference_no' => $customerId,
                        'module' => 'SAP',
                        'name' => 'Logging Integration Check Plafond',
                        'type' => 'Inbound',
                        'status' => $status,
                        'description' => $body
                    ]);
                }
            });

            return [
                'success' => $this->success,
                'failed' => $this->failed
            ];
        }
    }
}