<?php
namespace App\Helpers\Danamon;

use App\Helpers\Danamon\Specification\{
    CreateVADebit, 
    DeleteVADebit, 
    UpdateVADebit, 
    BalanceInquiry, 
    MutationInquiry, 
    VAInquiry, 
    OauthToken,
    BankInquiry,
    TransferOnline,
    TransferOverbooking,
    TransferRTGS,
    TransferSKN,
    BankDanamonInquiry,
    TopupInquiry,
    TopupTransfer,
    CasaInquiry
};

Class DanamonConfig
{
    public static function resources(){
        return [
       'create_va_debit' => [
            'class'         => CreateVADebit::class,
            'url'           => getenv('DANAMON_URL').CreateVADebit::url(),
            'relativeurl'   => CreateVADebit::url(),
            'header'        => CreateVADebit::header(),
            'body'          => CreateVADebit::rq(),
            'response'      => CreateVADebit::rs(),
            'mock'          => CreateVADebit::mock(),
            'is_transaction' => false
        ],
       
       'delete_va_debit' => [
            'class'         => DeleteVADebit::class,
            'url'           => getenv('DANAMON_URL').DeleteVADebit::url(),
            'relativeurl'   => DeleteVADebit::url(),
            'header'        => DeleteVADebit::header(),
            'body'          => DeleteVADebit::rq(),
            'response'      => DeleteVADebit::rs(),
            'mock'          => CreateVADebit::mock(),
            'is_transaction' => false
        ],

       'update_va_debit' => [
            'class'         => UpdateVADebit::class,
            'url'           => getenv('DANAMON_URL').UpdateVADebit::url(),
            'relativeurl'   => UpdateVADebit::url(),
            'header'        => UpdateVADebit::header(),
            'body'          => UpdateVADebit::rq(),
            'response'      => UpdateVADebit::rs(),
            'mock'          => CreateVADebit::mock(),
            'is_transaction' => false
        ],

       'balance_inquiry' => [
            'class'         => BalanceInquiry::class,
            'url'           => getenv('DANAMON_URL').BalanceInquiry::url(),
            'relativeurl'   => BalanceInquiry::url(),
            'header'        => BalanceInquiry::header(),
            'body'          => BalanceInquiry::rq(),
            'response'      => BalanceInquiry::rs(),
            'mock'          => BalanceInquiry::mock(),
            'is_transaction' => false
        ],


       'mutation_inquiry' => [
            'class'         => MutationInquiry::class,
            'url'           => getenv('DANAMON_URL').MutationInquiry::url(),
            'relativeurl'   => MutationInquiry::url(),
            'header'        => MutationInquiry::header(),
            'body'          => MutationInquiry::rq(),
            'response'      => MutationInquiry::rs(),
            'mock'          => CreateVADebit::mock(),
            'is_transaction' => false
        ],

       'va_inquiry' => [
            'class'         => VAInquiry::class,
            'url'           => getenv('DANAMON_URL').VAInquiry::url(),
            'relativeurl'   => VAInquiry::url(),
            'header'        => VAInquiry::header(),
            'body'          => VAInquiry::rq(),
            'response'      => VAInquiry::rs(),
            'mock'          => VAInquiry::mock(),
            'is_transaction' => false
        ],

       'get_auth_token' => [
            'class'         => OauthToken::class,
            'url'           => getenv('DANAMON_URL').OauthToken::url(),
            'relativeurl'   => OauthToken::url(),
            'header'        => OauthToken::header(),
            'body'          => OauthToken::rq(),
            'response'      => OauthToken::rs(),
            'mock'          => BankInquiry::mock(),
            'is_transaction' => false
        ],

        'bank_inquiry' => [
            'class'         => BankInquiry::class,
            'url'           => getenv('DANAMON_URL').BankInquiry::url(),
            'relativeurl'   => BankInquiry::url(),
            'header'        => BankInquiry::header(),
            'body'          => BankInquiry::rq(),
            'response'      => BankInquiry::rs(),
            'mock'          => BankInquiry::mock(),
            'is_transaction' => false
        ],
        'bank_danamon_inquiry' => [
            'class'         => BankDanamonInquiry::class,
            'url'           => getenv('DANAMON_URL').BankDanamonInquiry::url(),
            'relativeurl'   => BankDanamonInquiry::url(),
            'header'        => BankDanamonInquiry::header(),
            'body'          => BankDanamonInquiry::rq(),
            'response'      => BankDanamonInquiry::rs(),
            'mock'          => BankDanamonInquiry::mock(),
            'is_transaction' => false
        ],
        
        'casa_balance_inquiry' => [
            'class'         => CasaInquiry::class,
            'url'           => getenv('DANAMON_URL').CasaInquiry::url(),
            'relativeurl'   => CasaInquiry::url(),
            'header'        => CasaInquiry::header(),
            'body'          => CasaInquiry::rq(),
            'response'      => CasaInquiry::rs(),
            'mock'          => CasaInquiry::mock(),
            'is_transaction' => false
        ],

        'transfer_online' => [
            'class'         => TransferOnline::class,
            'url'           => getenv('DANAMON_URL').TransferOnline::url(),
            'relativeurl'   => TransferOnline::url(),
            'header'        => TransferOnline::header(),
            'body'          => TransferOnline::rq(),
            'response'      => TransferOnline::rs(),
            'mock'          => TransferOverbooking::mock(),
            'is_transaction' => true
        ],

        'transfer_overbooking' => [
            'class'         => TransferOverbooking::class,
            'url'           => getenv('DANAMON_URL').TransferOverbooking::url(),
            'relativeurl'   => TransferOverbooking::url(),
            'header'        => TransferOverbooking::header(),
            'body'          => TransferOverbooking::rq(),
            'response'      => TransferOverbooking::rs(),
            'mock'          => TransferOverbooking::mock(),
            'is_transaction' => true

        ],

        'transfer_rtgs' => [
            'class'         => TransferRTGS::class,
            'url'           => getenv('DANAMON_URL').TransferRTGS::url(),
            'relativeurl'   => TransferRTGS::url(),
            'header'        => TransferRTGS::header(),
            'body'          => TransferRTGS::rq(),
            'response'      => TransferRTGS::rs(),
            'mock'          => TransferOverbooking::mock(),
            'is_transaction' => true
        ],

        'transfer_skn' => [
            'class'         => TransferSKN::class,
            'url'           => getenv('DANAMON_URL').TransferSKN::url(),
            'relativeurl'   => TransferSKN::url(),
            'header'        => TransferSKN::header(),
            'body'          => TransferSKN::rq(),
            'response'      => TransferSKN::rs(),
            'mock'          => TransferOverbooking::mock(),
            'is_transaction' => true
        ],

        'topup_inquiry' => [
            'class'         => TopupInquiry::class,
            'url'           => getenv('DANAMON_URL').TopupInquiry::url(),
            'relativeurl'   => TopupInquiry::url(),
            'header'        => TopupInquiry::header(),
            'body'          => TopupInquiry::rq(),
            'response'      => TopupInquiry::rs(),
            'mock'          => TopupInquiry::mock(),
            'is_transaction' => false
        ],
        
        'topup_transfer' => [
            'class'         => TopupTransfer::class,
            'url'           => getenv('DANAMON_URL').TopupTransfer::url(),
            'relativeurl'   => TopupTransfer::url(),
            'header'        => TopupTransfer::header(),
            'body'          => TopupTransfer::rq(),
            'response'      => TopupTransfer::rs(),
            'mock'          => TopupTransfer::mock(),
            'is_transaction' => true,
        ],
    ];
}
}