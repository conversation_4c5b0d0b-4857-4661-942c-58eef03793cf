<?php

namespace App\Helpers\Danamon;
use App\Models\{Reseller, ResellerVA, MasterParameter};
use App\Helpers\Danamon\Specification\{TopupTransfer,TransferOverbooking};
use App\Models\LogDanamon;
use App\Models\ResellerCommission;
trait DanamonTrait
{
 
  public function MappingCreateVA(Reseller $param){
    $ph = strlen($param->phone_number) > 12 ? substr($param->phone_number, 0, 12) : str_pad($param->phone_number, 12, '0', STR_PAD_LEFT);
    return [
      "UserReferenceNumber" =>  getenv('DANAMON_PARTNER_ID').$param->reseller_id,
      "RequestTime" => date("YmdHis"),
      "VirtualAccountNumber"=> getenv('DANAMON_BIN_NO').$ph,
      "VirtualAccountName"=> preg_replace("/[^a-zA-Z]/", "", $param->name),
      "Currency"=> "IDR",
      "AuthorizedName"=>  preg_replace("/[^a-zA-Z]/", "", $param->name),
      "Email" => null,
      "Mobile" => null,
      "NIK"=> $param->national_id,
      "BinNumber"=> getenv('DANAMON_BIN_NO'),
      "ExpiredDate"=> date("c", strtotime("9999-12-31 23:59:59"))
    ];
  }

  public function InjectVA(ResellerCommission $param){
    $nominal = $param->commission_amount??0;
    $resellerBank = ResellerVA::where('reseller_id', $param->reseller_id)->first();
    $operational = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','OPERATIONAL')->first()->value??'********';
    $rq = TopupTransfer::rq();
    $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').uniqid('EXTCR').'OV';
    $rq["RequestTime"] = date("YmdHis");
    $rq["VirtualAccountNumber"] = $resellerBank->virtual_account_no;
    $rq["DebitedAccountNumber"] = $operational;
    $rq["Amount"] = strval($nominal);
    // $rq["Description"] = 'Transfer Migrasi TADA RSID ' . $param->reseller_id??'-';
    $rq["PaymentType"] = "O";
    return $rq;
  }

  public function ReversalVA(LogDanamon $param){
    // $nominal = ResellerCommission::where('reseller_id', $param->id)->first()->commission_amount??0;
    $operational = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','OPERATIONAL')->first()->value??'********';
    $operational_name = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','OPERATIONAL_NAME')->first()->value??'********';
    $rq = TransferOverbooking::rq();
    $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').uniqid('EXTCR').'OV';
    $rq["RequestTime"] = date("YmdHis");
    $rq["SourceAccountNumber"] = $param->to;
    $rq["SourceCardNumber"] = '';
    $rq["BeneficiaryAccountNumber"] = $operational;
    $rq["BeneficiaryName"] = $operational_name;
    $rq["Amount"] = str_replace(".","",$param->amount);
    $rq["Description"] = 'Reversal ' . $param->danamon_reference??'-';
    $rq["TransactionDate"] =  date("Ymd");
    return $rq;
  }

  public function MappingDeleteVA(Reseller $param){
    if($param->va != null){
      return [
        "UserReferenceNumber" => $param->va->reference_no,
        "RequestTime" => date("YmdHis"),
        "BinNumber"=> $param->va->bin_no,
        "VirtualAccountNumber"=> $param->va->virtual_account_no
      ];
    }
    return [];

  }


}
