<?php
namespace App\Helpers\Danamon\Specification;

Class BalanceInquiry
{
    public static function url() {
        return "/v1/api/financialinfo/va/binBalance";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
            "UserReferenceNumber" => "APIINV202127091700000",
            "RequestTime" => "**************",
            "BinNumber" => "7887"
        ];
    } 

    public static function rs() {
       return [
        "BinName",
        "AccountNumber",
        "AccountName",
        "Balance",
        "Currency",
        "ResponseTime",
        "UserReferenceNumber",
        "CodeStatus",
        "DescriptionStatus"
        ];
    }

    public static function mock() {
        return [
            "BinName" => "PT EIGERINDO MULTI INDUSTRI",
            "AccountNumber" => "************",
            "AccountName"=> "MEDIO MAYO",
            "Balance"=> "**********.00",
            "Currency"=> "360",
            "ResponseTime"=> "**************",
            "UserReferenceNumber"=> "APIINV202127091700000",
            "CodeStatus"=> "API000",
            "DescriptionStatus"=> "Success"
        ];
     }

}