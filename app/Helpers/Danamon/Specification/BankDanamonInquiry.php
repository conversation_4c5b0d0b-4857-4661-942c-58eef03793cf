<?php
namespace App\Helpers\Danamon\Specification;

Class BankDanamonInquiry
{
    public static function url() {
        return "/v1/api/financialinfo/casa/accountinquiryname";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
            "UserReferenceNumber"=>"901BDI2018270917000020",
            "RequestTime"=>"**************",
            "AccountNumber"=>"************"
        ];
    } 

    public static function rs() {
       return [
        "UserReferenceNumber",
        "ResponseTime",
        "AccountNumber",
        "AccountName",
        "CodeStatus",
        "DescriptionStatus"
        ];
    }

    public static function mock(){
        return [
            "UserReferenceNumber"=>"901BDI201827091700002",
            "ResponseTime"=>"**************",
            "AccountNumber"=>"************",
            "AccountName"=>"MEDIO MAYO",
            "CodeStatus"=>"API000",
            "DescriptionStatus"=>"Success"
        ];
    }
 
}