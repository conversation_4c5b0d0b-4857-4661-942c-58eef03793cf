<?php
namespace App\Helpers\Danamon\Specification;

Class BankInquiry
{
    public static function url() {
        return "/v1/api/financialinfo/casa/otherbankinquiry";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
            "UserReferenceNumber" => "APIINV202127091700000",
            "RequestTime" => "**************",
            "AccountNumber" => "************",
            "BankCode" => "014",
        ];
    } 

    public static function rs() {
       return [
        "UserReferenceNumber",
        "ResponseTime",
        "AccountNumber",
        "AccountName",
        "CodeStatus",
        "DescriptionStatus"
        ];
    }

    public static function mock(){
        return [
            "UserReferenceNumber"=>"901BDI201827091700002",
            "ResponseTime"=>"**************",
            "AccountNumber"=>"************",
            "AccountName"=>"MEDIO MAYO",
            "CodeStatus"=>"API000",
            "DescriptionStatus"=>"Success"
        ];
    }
 
}