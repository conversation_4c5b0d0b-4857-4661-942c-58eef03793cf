<?php
namespace App\Helpers\Danamon\Specification;

Class CasaInquiry
{
    public static function url() {
        return "/v1/api/financialinfo/casa/accountinquirybalance";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
            "UserReferenceNumber"=>"901BDI2018270917000020",
            "RequestTime"=>"**************",
            "AccountNumber"=>"************"
        ];
    } 

    public static function rs() {
        return [
            "UserReferenceNumber",
            "ResponseTime",
            "AccountCurrency",
            "CurrentBalance",
            "AvailableBalance",
            "OverDraftLimit",
            "HoldAmount",
            "MinimumBalance",
            "EndingBalance",
            "BeginningBalance",
            "CodeStatus",
            "DescriptionStatus"
        ];
    }

    public static function mock(){
        return [
            "UserReferenceNumber"=>"901BDI2018270917000020",
            "ResponseTime"=>"**************",
            "AccountCurrency"=>"360",
            "CurrentBalance"=>"*********",
            "AvailableBalance"=>"*********",
            "OverDraftLimit"=>"********",
            "HoldAmount"=>"********",
            "MinimumBalance"=>"5000000",
            "EndingBalance"=>"*********",
            "BeginningBalance"=>"*********",
            "CodeStatus"=>"API000",
            "DescriptionStatus"=>"Success"
        ];
    }
 
}