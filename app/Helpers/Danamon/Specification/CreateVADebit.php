<?php
namespace App\Helpers\Danamon\Specification;

Class CreateVADebit
{
    public static function url() {
        return "/v1/api/productcreation/va/createvadebit";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
            "UserReferenceNumber" => "APIINV202127091700000",
            "RequestTime" => "**************",
            "VirtualAccountNumber" => "707920000153010488",
            "VirtualAccountName" => "Medio Mayo",
            "Currency" => "IDR",
            "AuthorizedName" => "Medio Mayo",
            "Email" => "<EMAIL>",
            "Mobile" => "***********",
            "NIK" => "****************",
            "BinNumber" => "7079",
            "ExpiredDate" => "2022-03-02T11:17:10.383Z"
        ];
    } 

    public static function rs() {
       return [
        "UserReferenceNumber",
        "ResponseTime",
        "BinName",
        "VirtualAccountNumber",
        "VirtualAccountName",
        "Currency",
        "AuthorizedName",
        "Email",
        "Mobile",
        "BinNumber",
        "NIK",
        "ExpiredDate",
        "CodeStatus",
        "DescriptionStatus"
        ];
    }

    public static function mock() {
        return [
            "UserReferenceNumber" => "APIINV202127091700000",
            "ResponseTime" => "**************",
            "BinName" => "PT VALUESTREAM INTERNATIONAL",
            "VirtualAccountNumber" => "707920000153010488",
            "VirtualAccountName" => "MEDIO MAYO",
            "Currency" => "IDR",
            "AuthorizedName"  => "MEDIO MAYO",
            "Email" => "<EMAIL>",
            "Mobile" => "***********",
            "BinNumber" => "7079",
            "NIK" => "****************",
            "ExpiredDate" => "2022-03-02T11:17:10.383Z",
            "CodeStatus" => "API000",
            "DescriptionStatus" => "Success"
        ];
    }

    public static function mockSuccess() {
        return json_encode(array_flip(self::rs()), JSON_FORCE_OBJECT);
    }
 
}