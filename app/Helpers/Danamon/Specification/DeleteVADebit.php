<?php
namespace App\Helpers\Danamon\Specification;

Class DeleteVADebit
{
    public static function url() {
        return "/v1/api/productcreation/va/deletevadebit";
    } 
    public static function header(){
        return [
            'Authorization' => '',
            'Content-Type'  => 'application/json',
            'BDI-Key'       => '',
            'BDI-Timestamp' => '',
            'BDI-Signature' => '',
        ];
    }
  
    public static function rq() {
       return [
            "UserReferenceNumber" => "APIINV202127091700000",
            "RequestTime" => "**************",
            "BinNumber" => "7079",
            "VirtualAccountNumber" => "707920000153010488"
        ];
    } 
 
    public static function rs(){
       return [
            "UserReferenceNumber",
            "ResponseTime",
            "BinNumber",
            "VirtualAccountNumber",
            "CodeStatus",
            "DescriptionStatus"
            ];
    }

    public static function mock() {
        return [
            "UserReferenceNumber" => "APIINV202127091700000",
            "ResponseTime" => "**************",
            "BinNumber" => "7079",
            "VirtualAccountNumber" => '***************',
            "CodeStatus" => "API000",
            "DescriptionStatus" => "Success"
        ];
    }



}