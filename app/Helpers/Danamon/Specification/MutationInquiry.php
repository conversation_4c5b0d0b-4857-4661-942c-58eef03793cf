<?php
namespace App\Helpers\Danamon\Specification;

Class MutationInquiry
{
    public static function url(){
        return  "/v1/api/financialinfo/va/statement";
    }
    public static function header(){
        return [
            'Authorization' => '',
            'Content-Type'  => 'application/json',
            'BDI-Key'       => '',
            'BDI-Timestamp' => '',
            'BDI-Signature' => '',
        ];
    } 
    public static function rq(){
        return [
            "UserReferenceNumber" => "APIINV202127091700000",
            "RequestTime" => "**************",
            "StatementDate" =>"12-09-2021",
            "VirtualAccountNumber" =>"707920000153010488"
        ];
    } 
    public static function rs(){
        return [
            "UserReferenceNumber",
            "ResponseTime",
            "VirtualAccountNumber",
            "VirtualAccountName",
            "BeginingBalance",
            "EndingBalance",
            "CreditTotal",
            "DebitTotal",
            "Statements",
            "CodeStatus",
            "DescriptionStatus",
            ];
    } 



}