<?php
namespace App\Helpers\Danamon\Specification;

Class OauthToken
{
    public static function url() {
        return '/api/oauth/token';
    } 

    public static function header() {
    return [

            'Authorization' => 'Basic ' . base64_encode(getenv('DANAMON_CLIENT_ID') . ":" . getenv('DANAMON_SECRET_CLIENT_ID')),
            'Content-Type'  => 'application/x-www-form-urlencoded'
        ];
    }
    public static function rq(){
        return  [
            'grant_type' => 'client_credentials'
        ];
    } 

    public static function rs() {
       return [
            "access_token",
            "token_type",
            "expires_in",
            "scope"
        ];
    }

}