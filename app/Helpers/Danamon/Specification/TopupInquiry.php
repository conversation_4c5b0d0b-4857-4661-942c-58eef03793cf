<?php
namespace App\Helpers\Danamon\Specification;

Class TopupInquiry
{
    public static function url() {
        return "/v1/api/financialinfo/casa/virtualaccountinquiry";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
            "UserReferenceNumber"=> "APIINV202027091700000",
            "RequestTime"=> "**************",
            "VirtualAccountNumber"=> "****************",
            "Amount"=>"100000.00",
            "DebitedAccountNumber"=> "************",
            "PaymentType"=>"O"
        ];
    } 

    public static function mock() {
        return [
            "UserReferenceNumber"=> "901BDI2020270917000020",
            "ResponseTime"=> "**************",
            "VirtualAccountNumber"=> "****************",
            "VirtualAccountName"=> "MEDIO MAYO",
            "Amount"=> "100000.00",
            "BillerName"=> "PT VALUESTREAM INTERNATIONAL",
            "PaymentType"=> "O",
            "CodeStatus"=> "API000",
            "DescriptionStatus"=> "Success",
            "DebitedAccountNumber"=> "************"
        ];
    }

    public static function rs() {
       return [
        "UserReferenceNumber",
        "ResponseTime",
        "VirtualAccountNumber",
        "VirtualAccountName",
        "Amount",
        "BillerName",
        "PaymentType",
        "CodeStatus",
        "DescriptionStatus",
        "DebitedAccountNumber"
        ];
    }
 
}
