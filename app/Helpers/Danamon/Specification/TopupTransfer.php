<?php
namespace App\Helpers\Danamon\Specification;
use App\Services\LoggerDanamon;

Class TopupTransfer {
    public static function url() {
        return "/v1/api/financialtransaction/casa/virtualaccountpayment";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
                "UserReferenceNumber"=> "APIINV202027091700000",
                "RequestTime"=>"**************",
                "VirtualAccountNumber"=> "****************",
                "Amount" => "100000.00",
                "DebitedAccountNumber"=> "************",
                "PaymentType"=>"O"
        ];
    } 

    public static function mock() {
        return [
            "UserReferenceNumber"=> "901BDI2020270917000020",
            "ResponseTime"=> "**************",
            "VirtualAccountNumber"=> "****************",
            "Amount"=> "100000.00",
            "PaymentType"=> "O",
            "CodeStatus"=> "API000",
            "DescriptionStatus"=> "Success",
            "DebitedAccountNumber"=> "************"
        ];
    }

    public static function rs() {
       return [
        "UserReferenceNumber",
        "ResponseTime",
        "VirtualAccountNumber",
        "Amount",
        "PaymentType",
        "CodeStatus",
        "DescriptionStatus",
        "DebitedAccountNumber"
        ];
    }

    public function log($rq, $rs=0, $care='', $module =''){
        $log = [
            'danamon_reference' =>$rq['UserReferenceNumber'],
            'careom_reference' =>$care,
            'from' => $rq['DebitedAccountNumber'],
            'to' => $rq['VirtualAccountNumber'],
            'amount' => $rq['Amount'],
            'remarks' => $module,
            'success' => $rs,
            'created_by' => 'DANAMON_SERVICE',
            'modified_by' => 'DANAMON_SERVICE'
        ];
        LoggerDanamon::log($log);
    }
 
}
