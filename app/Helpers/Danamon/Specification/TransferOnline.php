<?php
namespace App\Helpers\Danamon\Specification;
use App\Services\LoggerDanamon;

Class TransferOnline
{
    public static function url() {
        return "/v1/api/financialtransaction/transfer/cashoutonline";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
            "UserReferenceNumber" => "APIINV202127091700000",
            "RequestTime" => "**************",
            "SourceAccountNumber" => "************",
            "BeneficiaryAccountNumber" => "**********",
            "BeneficiaryBankCode" => "013",
            "Amount" => "1500050",
            "Description" => "Transfer IBFT"
        ];
    } 

    public static function rs() {
       return [
        "UserReferenceNumber",
        "ResponseTime",
        "CodeStatus",
        "DescriptionStatus"
        ];
    }

    public function log($rq, $rs=0, $care='', $module =''){
        $log = [
            'danamon_reference' =>$rq['UserReferenceNumber'],
            'careom_reference' =>$care,
            'from' => $rq['SourceAccountNumber'],
            'to' => $rq['BeneficiaryAccountNumber'],
            'amount' => $rq['Amount']/100,
            'remarks' => $module,
            'success' => $rs,
            'created_by' => 'DANAMON_SERVICE',
            'modified_by' => 'DANAMON_SERVICE'
        ];
        LoggerDanamon::log($log);
    }
 
}
