<?php
namespace App\Helpers\Danamon\Specification;
use App\Services\LoggerDanamon;

Class TransferOverbooking
{
    public static function url() {
        return "/v1/api/financialtransaction/transfer/cashoutoverbooking";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
            "UserReferenceNumber" => "APIINV202127091700000",
            "RequestTime" => "**************",
            "SourceAccountNumber" => "************",
            "SourceCardNumber" => "",
            "BeneficiaryAccountNumber" => "845612",
            "BeneficiaryName" => "MEDIO MAYO",
            "Amount" => "150000",
            "Description" => "Transfer",
            "TransactionDate" => "********"
        ];
    } 

    public static function mock() {
        return [
            "UserReferenceNumber" => "MOCKEIGER".date("YmdHis"),
            "ResponseTime" => date("YmdHis"),
            "CodeStatus" => "API000",
            "DescriptionStatus" => "Success",
        ];
    }

    public static function rs() {
       return [
        "UserReferenceNumber",
        "ResponseTime",
        "CodeStatus",
        "DescriptionStatus"
        ];
    }

    public function log($rq, $rs=0, $care='', $module =''){
        $log = [
            'danamon_reference' =>$rq['UserReferenceNumber'],
            'careom_reference' =>$care,
            'from' => $rq['SourceAccountNumber'],
            'to' => $rq['BeneficiaryAccountNumber'],
            'amount' => $rq['Amount']/100,
            'remarks' => $module,
            'success' => $rs,
            'created_by' => 'DANAMON_SERVICE',
            'modified_by' => 'DANAMON_SERVICE'
        ];
        LoggerDanamon::log($log);
    }
 
}
