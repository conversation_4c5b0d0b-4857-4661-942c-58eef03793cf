<?php
namespace App\Helpers\Danamon\Specification;
use App\Services\LoggerDanamon;

Class TransferRTGS
{
    public static function url() {
        return "/v1/api/financialtransaction/transfer/cashoutrtgs";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
            "UserReferenceNumber" => "APIINV202127091700000",
            "RequestTime" => "**************",
            "SourceAccountNumber" => "************",
            "SourceCardNumber" => "**********",
            "BeneficiaryAccountNumber" => "845612",
            "BeneficiaryName" => "MEDIO MAYO",
            "BeneficiaryAddress" => "Jakarta",
            "BeneficiaryType" => "1",
            "BeneficiaryStatus" => "1",
            "BeneficiaryBICode" => "0090010",
            "BeneficiaryBranchCode" => "901",
            "BeneficiaryBankName" => "BNI",
            "Amount" => "150000",
            "Description" => "Transfer",
            "TransactionDate" => "********"
        ];
    } 

    public static function rs() {
       return [
        "UserReferenceNumber",
        "ResponseTime",
        "CodeStatus",
        "DescriptionStatus"
        ];
    }

    public function log($rq, $rs=0, $care='', $module =''){
        $log = [
            'danamon_reference' =>$rq['UserReferenceNumber'],
            'careom_reference' =>$care,
            'from' => $rq['SourceAccountNumber'],
            'to' => $rq['BeneficiaryAccountNumber'],
            'amount' => $rq['Amount']/100,
            'remarks' => $module,
            'success' => $rs,
            'created_by' => 'DANAMON_SERVICE',
            'modified_by' => 'DANAMON_SERVICE'
        ];
        LoggerDanamon::log($log);
    }
 
}
