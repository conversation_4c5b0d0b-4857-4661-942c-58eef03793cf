<?php
namespace App\Helpers\Danamon\Specification;

Class UpdateVADebit
{
    public static function url() {
        return "/v1/api/productcreation/va/updatevadebit";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
            "UserReferenceNumber" => "APIINV202127091700000",
            "RequestTime" => "**************",
            "VirtualAccountNumber" => "707920000153010488",
            "VirtualAccountName" => "Medio Mayo",
            "AuthorizedName" => "Medio Mayo",
            "Email" => "<EMAIL>",
            "Mobile" => "***********",
            "NIK" => "****************",
            "BinNumber" => "7079",
            "ExpiredDate" => "2022-03-02T11:17:10.383Z",
            "VirtualAccountStatus" => "I"
        ];
    } 

    public static function rs() {
       return [
        "UserReferenceNumber",
        "ResponseTime",
        "VirtualAccountNumber",
        "VirtualAccountName",
        "Currency",
        "AuthorizedName",
        "Email",
        "Mobile",
        "BinNumber",
        "NIK",
        "ExpiryDate",
        "CodeStatus",
        "DescriptionStatus"
        ];
    }
 
}
