<?php
namespace App\Helpers\Danamon\Specification;

Class VAInquiry
{
    public static function url() {
        return "/v1/api/financialinfo/va/virtualaccountdebitinquiry";
    } 

    public static function header() {
    return [
        'Authorization' => '',
        'Content-Type'  => 'application/json',
        'BDI-Key'       => '',
        'BDI-Timestamp' => '',
        'BDI-Signature' => '',
        ];
    }
    public static function rq(){
        return  [
            "UserReferenceNumber" => "APIINV202127091700000",
            "RequestTime" => "**************",
            "VirtualAccountNumber" => "707920000153010488"
        ];
    } 

    public static function rs() {
       return [
        "UserReferenceNumber",
        "ResponseTime",
        "VirtualAccountNumber",
        "VirtualAccountName",
        "BinName",
        "Currency",
        "AvailableBalance",
        "MobileNumber",
        "EmailAddress",
        "AuthorizedName",
        "NIK",
        "ExpiryDate",
        "Status",
        "CodeStatus",
        "DescriptionStatus"
        ];
    }

    public static function mock() {
        return [
         "UserReferenceNumber" => 'APIINV202127091700000',
         "ResponseTime"=> '**************',
         "VirtualAccountNumber"=> '707920000153010488',
         "VirtualAccountName"=> 'MEDIO MAYO',
         "BinName"=> 'EIGER',
         "Currency"=> '360',
         "AvailableBalance"=> '*************.00',
         "MobileNumber"=> '***********',
         "EmailAddress"=> '<EMAIL>',
         "AuthorizedName"=> 'MEDIO MAYO',
         "NIK"=> '****************',
         "ExpiryDate"=> '********',
         "Status"=> 'Active',
         "CodeStatus"=> 'API000',
         "DescriptionStatus"=> 'Success'
         ];
     }
 
}
