<?php

namespace App\Helpers;
use App\Helpers\Danamon\Specification\BalanceInquiry;
use App\Helpers\Danamon\Specification\CreateVADebit;
use App\Models\MasterParameter;
use App\Models\ResellerVA;
use App\Models\Reseller;
use App\Traits\ResponseAPI;
use App\Helpers\RestHelper;
use App\Models\ResellerDanamonToken;
use App\Services\LoggerIntegration;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use App\Helpers\Danamon\DanamonConfig;
use App\Jobs\BankQueue;
use GuzzleHttp\Client;

class DanamonHelper
{
  use ResponseAPI;
  private $config;
  private $token = [];
  public function __construct()
  {
    $this->config = DanamonConfig::resources();
    
  }

  public function cacheToken()
  {
    if(Cache::has('DN_TOKEN')){
          return Cache::get('DN_TOKEN');
      }
      return $this->generateToken();
  }

  public function getOperationalAccount(){
    return MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','OPERATIONAL')->first()->value??'*********';
  }

  public function getTaxAccount(){
    return MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','TAX')->first()->value??'*********';
  }

  public function generateToken()
  {
    return $this->doAction('get_auth_token');
  }

  public function queueDispatch($rq = null, $action = ''){
    BankQueue::dispatch($rq, $action);
    return true;
  }

  public function createVADebit($rq = null){
    return $this->doAction('create_va_debit', $rq);
  }

  public function deleteVADebit($rq = null){
    return $this->doAction('delete_va_debit', $rq);
  }

  public function updateVADebit($rq = null){
    return $this->doAction('update_va_debit', $rq);
  }

  public function balanceInquiry($rq = null){
    return $this->doAction('balance_inquiry', $rq);
  }
  public function mutationInquiry($rq = null){
    return $this->doAction('mutation_inquiry', $rq);
  }
  public function vaInquiry($rq = null){
    return $this->doAction('va_inquiry', $rq);
  }

  public function bankInquiry($rq = null){
    return $this->doAction('bank_inquiry', $rq);
  }

  public function bankDanamonInquiry($rq = null){
    return $this->doAction('bank_danamon_inquiry', $rq);
  }
  
  
  public function transferOnline($rq = null){
      return $this->doAction('transfer_online', $rq);
  }

  public function transferRTGS($rq = null){
    return $this->doAction('transfer_rtgs', $rq);
  }

  public function transferSKN($rq = null){
    return $this->doAction('transfer_skn', $rq);
  }

  public function transferOverbooking($rq = null){
    return $this->doAction('transfer_overbooking', $rq);
  }

  public function topupInquiry($rq = null){
    return $this->doAction('topup_inquiry', $rq);
  }

  public function casaInquiry($rq = null){
    return $this->doAction('casa_balance_inquiry', $rq);
  }

  public function topupTransfer($rq = null){
    return $this->doAction('topup_transfer', $rq);
  }
  

  public function generateBDISignature($relativeURL = '', $request = null, $method = 'POST')
  {
    $timestampBDI = Carbon::now('UTC')->format('Y-m-d\TH:i:s.v\Z');
    $secretKey = getenv('DANAMON_SECRET_KEY');
    $additionalKey =  getenv('DANAMON_ADDITIONAL_KEY');
    $value = $relativeURL . $timestampBDI . $secretKey . $additionalKey . ($method == 'POST' || $method == 'PUT' ? str_replace(" ","",json_encode($request))  : '');

    if ($method == "POST" || $method == "PUT") {
      // $generateSignature = hash('sha256', $value);
      $generateSignature = hash('sha256', $value);
    } else if ($method == "GET" || $method == "DELETE") {
      $generateSignature = hash('sha256', $value);
    }
    $prepareLogIntegration = [
      'reference_no' => null,
      'module' => 'Danamon',
      'name' => 'BDI Signature Log',
      'type' => 'Outbound',
      'status' =>'success',
      'description' => [
        'payload' => $value,
        'hash' => $generateSignature,
        'request' => json_encode($request, JSON_UNESCAPED_SLASHES),
        ]
    ];
    (new LoggerIntegration())->InsertLogger($prepareLogIntegration);

    return [
      "BDI-Key" => getenv("DANAMON_BDI_KEY"),
      "BDI-Timestamp" => $timestampBDI,
      "BDI-Signature" => $generateSignature,
      "Content-Type" => 'application/json',
      "User-Agent"   => 'Careom-Production/1.0',
      "Accept"      => '*/*',
      "Cache-Control" => "no-cache",
      "Host" => str_replace("https://","",getenv("DANAMON_URL")),
      "Accept-Enconding" => "gzip, deflate, br",
      "Connection"      => "keep-alive"
    ];
  }


  public function doAction(String $param, Array $computed = null){
    $this->token = $param == 'get_auth_token' ? [] : ['Authorization' => "Bearer " . $this->cacheToken()];
    $p = $this->config[$param]??[];
    $payload = $computed != null 
    ? array_replace($p['body'], $computed)
    : $p['body'];
    $header = $param == 'get_auth_token' ? $p['header'] :array_merge($this->token, $this->generateBDISignature($p['relativeurl'], $payload));
    try{
      // dd($header, $payload);
      $request = $param == 'get_auth_token' ? Http::asForm()->withHeaders($header)->post(
        $p['url'],
        $payload
      ) : (env('DANAMON_MOCK', true) == false ?  Http::withHeaders($header)->post(
        $p['url'],
        $payload) : $p['mock']
      );
     
      // $rssss = $request->body();

      $rs = empty($p)? $p : (env('DANAMON_MOCK', true) == false ? json_decode($request->body(), true) : $request);
      $err  = uniqid("DNLOG");
      if(env('DANAMON_MOCK', true) == false && $param != 'get_auth_token'  && $p['is_transaction'] == true){
        if($p['is_transaction'] == true) Cache::forget('transaction-lock');
        (new $p['class'])->log($payload, Arr::has($rs, $p['response']) ? 1 : 0, $err, $param);
      }
      $prepareLogIntegration = [
        'reference_no' => $err,
        'module' => 'Danamon',
        'name' => $param,
        'type' => 'Outbound',
        'status' => Arr::has($rs, $p['response']) ? 'success' : 'failed',
        'description' => [
          'payload' => $payload,
          'response' => $rs,
          'header' => $header,
          'url' => $p['url']
          ]
      ];
      (new LoggerIntegration())->InsertLogger($prepareLogIntegration);

      if($param == 'get_auth_token') {
        Cache::set('DN_TOKEN', $rs['access_token']??'', $rs['expires_in']-5??1); 
        return $rs['access_token'];
      }
      if($param == 'delete_va_debit') {
        if(Arr::has($rs, $p['response'])){
          ResellerVA::where('virtual_account_no', $rs['VirtualAccountNumber'])->delete();
        }
      }
      if($param == 'create_va_debit'){
        if(Arr::has($rs, $p['response'])){

          $reseller_id = Reseller::where('reseller_id',str_replace(env('DANAMON_PARTNER_ID', 'BDIEGR'),'',$rs["UserReferenceNumber"]))->first();
          if($reseller_id){
            ResellerVA::updateOrCreate(
              [
                "reseller_id" => $reseller_id->id,
              ],
              [
                "reseller_id" => $reseller_id->id,
                "reference_no" => $rs["UserReferenceNumber"],
                "created_date" => $rs["ResponseTime"],
                "bin_name" => $rs["BinName"],
                "virtual_account_no" => $rs["VirtualAccountNumber"],
                "virtual_account_name" => $rs["VirtualAccountName"],
                "name" => $rs["AuthorizedName"],
                "email" => "-",
                "phone_number" => "-",
                "bin_no" => $rs["BinNumber"],
                "national_id" => $rs["NIK"],
                "expired_date" => $rs["ExpiredDate"],
                "status_code" => $rs["CodeStatus"],
                "status" => $rs["DescriptionStatus"],
              ]);
          }
        
        }
      }
      return Arr::has($rs, $p['response']) ? $this->sendSuccess('success', $rs) : $this->sendSuccess('failed', ['log_id' => $err, 'response' => $rs]);
        }
    catch(\Exception $e){
      $err  = uniqid("DNLOG");
      $prepareLogIntegration = [
        'reference_no' => $err,
        'module' => 'Danamon',
        'name' => $param,
        'type' => 'Outbound',
        'status' => 'exception',
        'description' => [
          'payload' => $payload,
          'response' => [
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'message' => $e->getMessage(),
        ],
        ]
      ];
      (new LoggerIntegration())->InsertLogger($prepareLogIntegration);
      return $this->sendSuccess('failed', ['log_id' => $err]);
      // return $e->getMessage();
    } 
  }
}
