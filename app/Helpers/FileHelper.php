<?php

namespace App\Helpers;

use Aws\S3\S3Client;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

trait FileHelper
{

    public function fileTransfer($filepath = null, $type = 'dump', $is_remove = false)
    {
        try {
            // validasi cek semua file yg match dengan filename tapi ext beda
            // $files = Storage::disk('s3-public')->files(pathinfo($filepath)['dirname']);
            // foreach ($files as $file) {
            //     if (pathinfo($filepath)['filename'] == pathinfo($file)['filename'] && pathinfo($filepath)['extension'] != pathinfo($file)['extension']) {
            //         Storage::disk('s3-public')->delete($filepath);
            //         return [
            //             'error' => true,
            //             'message' => 'Extension does not match'
            //         ];
            //     }
            // }

            if ($filepath == null) {
                return [
                    'error' => true,
                    'message' => 'Filepath parameter required!'
                ];
            }

            // validasi file exists
            $exists = Storage::disk('s3-public')->exists($filepath);
            if ($exists == false) {
                return [
                    'error' => true,
                    'message' => 'File not found!'
                ];
            }

            $destFilepath = $type . '/' . Str::uuid() . '.' . pathinfo($filepath)['extension'];
            $sourceClient = Storage::disk('s3-public')->getDriver()->getAdapter()->getClient();

            $sourceClient->copyObject([
                'Bucket' => env('AWS_BUCKET', 'bucket-careorder'),
                'Key' => $destFilepath,
                'CopySource' => env('AWS_BUCKET_STAGING', 'bucket-public-careorder') . '/' . $filepath
            ]);

            if ($is_remove == true)
                Storage::disk('s3-public')->delete($filepath);

            return [
                'error' => false,
                'filepath' => $destFilepath
            ];
        } catch (\Exception $e) {
            $error = [
                'error' => true,
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ];
            Log::info('error transfer file', $error);
            return $error;
        }
    }

    public function deletePublicFile($files = [])
    {
        try {
            // isi array filepath
            $deleted = [];
            foreach ($files as $file) {
                if (Storage::disk('s3-public')->exists($file)) {
                    Storage::disk('s3-public')->delete($file);
                    $deleted[] = $file;
                }
            }
            return [
                'error' => false,
                'data' => $deleted
            ];
        } catch (\Exception $e) {
            $error = [
                'error' => true,
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ];
            Log::info('error delete public file', $error);
            return $error;
        }
    }
}