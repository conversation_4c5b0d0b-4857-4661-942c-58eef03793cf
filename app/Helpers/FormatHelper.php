<?php

namespace App\Helpers;
trait FormatHelper {
    
    //format string inputs to NPWP format
    public static function formatNpwp($param){
        $formattedNumber = substr($param, 0, 2) . '.' . 
        substr($param, 2, 3) . '.' . 
        substr($param, 5, 3) . '.' . 
        substr($param, 8, 1) . '-' . 
        substr($param, 9, 3) . '.' . 
        substr($param, 12, 3);

        return $formattedNumber;    
    }

    //format first 2 digits (e.g. '62') to '0'
    public static function formatPhoneNo($param){
        $formattedPhoneNo = '0' . substr($param, 2);

        return $formattedPhoneNo;    
    }

    public static function formatPeriodQuery($start, $to)
    {
        if ($start && $to) {
            $start;
            $to;
        } elseif ($start && !$to) {
            $start;
            $to = now()->format('Y-m-d');
        } elseif (!$start && $to) {
            $start = now()->format('Y-m-d');
            $to;
        } else {
            $start = now()->subDays(30)->format('Y-m-d');
            $to = now()->format('Y-m-d');
        }
        $dates = [$start,$to];
        return $dates;
    }

    public static function getMonthfromNumber($number){

        $monthNames = [
            1 => 'Januari',
            2 => 'Februari',
            3 => 'Maret',
            4 => 'April',
            5 => 'Mei',
            6 => 'Juni',
            7 => 'Juli',
            8 => 'Agustus',
            9 => 'September',
            10 => 'Oktober',
            11 => 'November',
            12 => 'Desember',
        ];

        if (isset($monthNames[$number])) {
            $monthName = $monthNames[$number];
        } else {
            $monthName = $monthNames[1];
        }

        return $monthName;
    }

    public static function mapNumbersToMonth3LF($months){

        $monthNames = [
            'JAN', 'FEB', 'MAR', 'APR', 'MEI', 'JUN',
            'JUL', 'AGU', 'SEP', 'OKT', 'NOV', 'DES'
        ];

        $monthResultsMapped = array_map(function ($monthNumber) use ($monthNames) {
            return $monthNames[$monthNumber - 1];
        }, $months);

        return $monthResultsMapped;
    }
}
