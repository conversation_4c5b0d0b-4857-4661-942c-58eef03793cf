<?php

namespace App\Helpers;



trait LinkHelper
{
  public function countingHistoryLink($dataBfr = [], $dataNow = [])
  {
    $processBfr = array_reduce($dataBfr,function($carry, $item) {
      $type = $item["type"];
      $count = $item["history_count"];
      
      $carry['kumulatif'] += $count;
      if (strtolower($type) == 'store' || strtolower($type) == 'list') {
        $carry['katalog'] += $count;
      }
      if (strtolower($type) == 'detail') {
        $carry['produk'] += $count;
      }
      
      return $carry;
    }, [
      'kumulatif' => 0,
      'katalog' => 0,
      'produk' => 0
    ]);

    $processNow = array_reduce($dataNow,function($carry, $item) {
      $type = $item["type"];
      $count = $item["history_count"];
      
      $carry['kumulatif'] += $count;
      if (strtolower($type) == 'store' || strtolower($type) == 'list') {
        $carry['katalog'] += $count;
      }
      if (strtolower($type) == 'detail') {
        $carry['produk'] += $count;
      }
      
      return $carry;
    }, [
      'kumulatif' => 0,
      'katalog' => 0,
      'produk' => 0
    ]);

    $processed = array_map(function($key,$bfr,$now){
      return [
        'type' => $key,
        'count' => $now,
        'diffPercent' => $now == 0 || $bfr == 0 ? 0 : (($now-$bfr)/$bfr)*100
      ];
    },array_keys($processNow),$processBfr,$processNow);

    return $processed;
  }
}
