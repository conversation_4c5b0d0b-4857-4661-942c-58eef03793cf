<?php

namespace App\Helpers;

use Carbon\Carbon;
use App\Models\Coupon;
use App\Models\Voucher;
use App\Models\OrderItem;
use App\Models\ResellerCart;
use App\Models\OrderReseller;
use App\Models\ResellerToken;
use App\Helpers\DanamonHelper;
use App\Jobs\CommissionRelease;
use App\Models\MasterParameter;
use App\Models\OrderItemReseller;
use App\Models\ResellerPromotion;
use App\Models\ResellerCartDetail;
use App\Models\ResellerCommission;
use Illuminate\Support\Facades\DB;
use App\Models\ResellerTransaction;
use App\Models\ResellerArticleStock;
use App\Models\ResellerOrderHeaders;
use App\Models\ResellerOrderPromotion;
use App\Models\ResellerCommissionLedger;
use App\Helpers\Promotion\PromotionHelper;

trait OrderResellerHelper {

    use PromotionHelper;    
    public function validateStock($article,$qty)
    {
        $rsl_stock = ResellerArticleStock::where('article',$article)->first();
        
        if ($rsl_stock->available_stock < $qty) {
            return false;
        }

        return true;
    }

    // harus ada field article_id dan qty (nama field harus sama)
    public function bookStock($datas = [],$site)
    {
        foreach ($datas as $data) {
            $stock = ResellerArticleStock::where('article',$data['article_id'])->where('location_code',$site)->first();
            $stock->booked_stock = $stock->booked_stock + $data['qty'];
            if (empty($stock->available_stock)) {
                $stock->available_stock = $stock->stock - ($stock->booked_stock);
            } else {
                $stock->available_stock = $stock->available_stock - $data['qty'];
            }
            $stock->save();
        }

        return 'success';
    }

    // harus ada field article_id dan qty (nama field harus sama)
    public function releaseStock($datas = [],$site)
    {
        foreach ($datas as $data) {
            $stock = ResellerArticleStock::where('article',$data['article_id'])->where('location_code',$site)->first();
            $stock->booked_stock = $stock->booked_stock - $data['qty'];
            $stock->available_stock = $stock->available_stock + $data['qty'];
            $stock->save();
        }

        return 'success';
    }

    public function buyStock($datas = [],$site)
    {
        foreach ($datas as $data) {
            $stock = ResellerArticleStock::where('article',$data['article_id'])->where('location_code',$site)->first();
            $stock->booked_stock = $stock->booked_stock - $data['qty'];
            $stock->available_stock = $stock->available_stock - $data['qty'];
            $stock->save();
        }

        return 'success';
    }

    public function orderAuthorization($order_no)
    {
        $order = OrderReseller::where('order_no', $order_no)->first();
        $customer_id = ResellerToken::customerID(request()->header('X-AUTH-DEVICE'));

        if (!$order) {
            $error = true;
            $message = 'Order is not exist.';
        } elseif ($order->customer_id != $customer_id) {
            $error = true;
            $message = 'You dont have access for this order';
        }

        return [
            'error' => $error??false,
            'message' => $message??null
        ];
    }

    public function orderToCart($order = [])
    {
        DB::beginTransaction();
        try {
            $customer_id = $order['customer_id'];

            ResellerCart::firstOrCreate([
                'link_id' => $order['link_id'],
                'customer_id' => $customer_id
            ]);

            if (!empty($order['items'])) {
                $q_cart = ResellerCart::where('link_id',$order['link_id'])->where('customer_id',$customer_id)->first();
                
                foreach($order['items'] as $data){
                    $cart_detail = ResellerCartDetail::where('cart_id',$q_cart->id)->where('article_id',$data['article_id'])->first();
                    if ($cart_detail != null) {
                        $cart_detail->qty = $cart_detail->qty + $data['qty'];
                        $cart_detail->save();
                    } else {
                        ResellerCartDetail::create([
                            'cart_id' => $q_cart->id,
                            'article_id' => $data['article_id'],
                            'qty' => $data['qty']
                        ]);
                    }
                }
            }

            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ];
        }
        
    }

    public function payloadCreateOrderCareOmni($order_no)
    {
        $order = OrderReseller::where('order_no',$order_no)->with(['items','customer_shipment'])->first();
        $mp = MasterParameter::where('group_key','CHANNEL_CODE')->where('value','RESELLER')->first();

        foreach($order->items as $d)
        {
            $items[] = [
                'remarks' => '-',
                'skucode' => $d->article_id,
                'order_qty' => $d->qty,
                'unit_price' => $d->unit_price,
                'line_amount' => $d->line_amount - $d->discount_amount,
                'discount_code' => null, // menunggu pencerahan
                'discount_type' => null,
                'discount_name' => null,
                'discount_amount' => $d->discount_amount,
                'fulfillments' => [[
                    'loccode' => $order->location_code,
                    'qty' => $d->qty
                ]]
            ];
        }
        foreach($order->item_bundlings as $d)
        {
            $items[] = [
                'remarks' => $d->remarks??'-',
                'skucode' => $d->article_id,
                'order_qty' => $d->qty,
                'unit_price' => $d->unit_price,
                'line_amount' => $d->line_amount - $d->discount_amount,
                'discount_code' => null, // menunggu pencerahan
                'discount_type' => null,
                'discount_name' => null,
                'discount_amount' => $d->discount_amount,
                'fulfillments' => [[
                    'loccode' => $order->location_code,
                    'qty' => $d->qty
                ]]
            ];
        }

        return [
            'ext_order_id' => $order_no,
            'channel_code' => $mp->key,
            'loccode' => $order->location_code,
            'order_date' => $order->created_date,
            'ext_invoice_no' => $order->invoice_no,
            'remarks' => null,
            'currency' => $order->currency??'IDR',
            'sla_date' => $order->sla_date,
            'ext_status' => $order->order_status,
            'pay_amount' => $order->pay_amount,
            'order_items' => $items,
            'shipping_charges' => $order->shipment_charges,
            'total_amount' => $order->total_amount,
            'customer_note' => null,
            'customer_type' => 'customer',
            'delivery_date' => null,
            'transporter_id' => $order->transporter_id,
            'bill_expired_at' => Carbon::now()->addSeconds(310)->format('Y-m-d H:i:s'),
            'payment_details' => null,
            'customer_details' => [
                'name' => $order->customer_name,
                'type' => 'customer',
                'email' => $order->customer_email,
                'phone' => $order->customer_phone_number,
                'recipient_name' => $order->customer_shipment_name,
                'recipient_phone' => $order->customer_shipment_phone_number,
                'shipping_latitude' => null,
                'shipping_city_code' => $order->customer_shipment->city_code,
                'shipping_longitude' => null,
                'shipping_postal_code' => $order->customer_shipment->zip_code,
                'shipping_address_name' => $order->customer_shipment->address,
                'shipping_country_code' => 'ID',
                'shipping_full_address' => $order->customer_shipment->address,
                'shipping_district_code' => $order->customer_shipment->district_code,
                'shipping_province_code' => $order->customer_shipment->region_code,
            ],
            'handling_charges' => $order->handling_charges
        ];
    }

    public function payloadXenditInvoices($order_no)
    {
        $order = OrderReseller::where('order_no',$order_no)->with('items')->first();

        foreach ($order->items as $d) {
            $items[] = [
                'name' => $d->product_name,
                'quantity' => $d->qty,
                'price' => $d->unit_price // belom diskon
            ];
        }
        foreach ($order->item_bundlings as $d) {
            $items[] = [
                'name' => "[Bundling $d->remarks] $d->product_name",
                'quantity' => $d->qty,
                'price' => 0 // belom diskon
            ];
        }

        $fees = [
            [
                'type' => 'Biaya Pengiriman',
                'value' => $order->shipment_charges
            ]
        ];

        if (!empty($order->discount_amount) && $order->discount_amount > 0) {
            $fees[] = [
                'type' => 'Discount',
                'value' => -($order->discount_amount)
            ];
        }

        return [
            'external_id' => $order->order_no,
            'amount' => $order->pay_amount,
            'payer_email' => $order->customer_email,
            'description' => 'Invoice '.$order->order_no,
            'invoice_duration' => 310,
            'currency' => $order->currency,
            'locale' => 'id',
            'items' => $items,
            'fees' => $fees
        ];
        
    }

    public function calculateWeight($items = []){
        if (!empty($items)) {
            $weight = array_reduce($items,function($w,$item){

                if ($item['product']['uom'] == 'G') {
                    $w += $item['qty']*($item['product']['weight']/1000);
                }

                if ($item['product']['uom'] == 'KG') {
                    $w += $item['qty']*$item['product']['weight'];
                }

                return $w;
            });
            return $weight;
        } else {
            return 0;
        }
    }

    // type case sensitive
    public function logTransactions($order_no = '',$type = [],$status = '',$notes = '-')
    {
        DB::beginTransaction();
        try {
            $order = OrderReseller::where('order_no',$order_no)->first();
            $mp = MasterParameter::where('group_key','RESELLER_COMMISSION')->where('key','COMMISSION_PERCENTAGE')->first();

            if (in_array('ip',$type)) {
                DB::table('rsl_transactions')->insert([
                    'reference_name' => 'order',
                    'reference_id' => $order->order_no,
                    'amount' => $order->sub_total_amount,
                    'order_status' => $order->order_status,
                    'type' => ResellerTransaction::TX_TYPE_INVOICE_PAYMENT,
                    'status' => $status,
                    'remarks' => $notes
                ]);
            }

            if (in_array('d',$type)) {
                $items = $order->items()->pluck('id')->toArray();
                $items[] = $order->id;
                $ops = ResellerOrderPromotion::whereIn('reference_id',$items)->get();

                if ($ops->isEmpty() == false) {
                    $d_d = [];
                    foreach ($ops as $op) {
                        $d_d[] = [
                            'reference_name' => 'order',
                            'reference_id' => $order->order_no,
                            'discount_type' => $op->discount_type,
                            'discount_id' => $op->discount_id,
                            'amount' => $op->amount,
                            'order_status' => $order->order_status,
                            'type' => ResellerTransaction::TX_TYPE_DISCOUNT,
                            'status' => $status,
                            'remarks' => $notes
                        ];
                        // ResellerTransaction::insert([
                        //     'reference_name' => 'order',
                        //     'reference_id' => $order->order_no,
                        //     'discount_type' => $op->discount_type,
                        //     'discount_id' => $op->discount_id,
                        //     'amount' => $op->amount,
                        //     'order_status' => $order->order_status,
                        //     'type' => ResellerTransaction::TX_TYPE_DISCOUNT,
                        //     'status' => $status,
                        //     'remarks' => $notes
                        // ]);
                    }
                    DB::table('rsl_transactions')->insert($d_d);
                }
            }

            if (in_array('sc',$type)) {
                DB::table('rsl_transactions')->insert([
                    'reference_name' => 'order',
                    'reference_id' => $order->order_no,
                    'amount' => $order->shipment_charges,
                    'order_status' => $order->order_status,
                    'type' => ResellerTransaction::TX_TYPE_SHIPMENT_CHARGE,
                    'status' => $status,
                    'remarks' => $notes
                ]);
            }

            if (in_array('pc',$type)) {
                $rt = DB::table('rsl_transactions')->insert([
                    'reference_name' => 'order',
                    'reference_id' => $order->order_no,
                    'amount' => round($order->total_amount*($mp->value/100)),
                    'order_status' => $order->order_status,
                    'type' => ResellerTransaction::TX_TYPE_POTENTIAL_COMMISSION,
                    'status' => $status,
                    'remarks' => $notes
                ]);
                $rt = ResellerTransaction::where([
                    ['reference_name', '=', 'order'],
                    ['reference_id' , '=', $order->order_no],
                    ['amount' , '=', round($order->total_amount*($mp->value/100))],
                    ['order_status' ,'=', $order->order_status],
                    ['type' ,'=', ResellerTransaction::TX_TYPE_POTENTIAL_COMMISSION],
                    ['status' ,'=', $status],
                    ['remarks' ,'=', $notes]
                ])->first();
                if ($status == 'Completed') {
                    $rc = ResellerCommission::where('reseller_id',$order->reseller_id)->first();
                    if ($rc != null) {
                        $rc->potential_amount = round($rc->potential_amount+($order->total_amount*($mp->value/100)));
                        $rc->save();
                    } else {
                        $rc = ResellerCommission::create([
                            'reseller_id' => $order->reseller_id,
                            'potential_amount' => round($order->total_amount*($mp->value/100)),
                        ]);
                    }
                    $rc = ResellerCommission::where('reseller_id',$order->reseller_id)->first();
                    DB::table('rsl_commission_ledgers')->insert([
                        'commission_id' => $rc->id,
                        'transaction_id' => $rt->id,
                        'reseller_id' => $order->reseller->reseller_id,
                        'type' => 'Credit',
                        'commission_type' => 'Potential',
                        'amount' => round($order->total_amount*($mp->value/100)),
                        'remarks' => $notes
                    ]);
                }
            }

            if (in_array('c',$type)) {
                $rt1 = DB::table('rsl_transactions')->insert([
                    'reference_name' => 'order',
                    'reference_id' => $order->order_no,
                    'amount' => round($order->total_amount*($mp->value/100)),
                    'order_status' => $order->order_status,
                    'type' => ResellerTransaction::TX_TYPE_COMMISSION,
                    'status' => $status,
                    'remarks' => $notes
                ]);
                $rt1 = ResellerTransaction::where([
                    ['reference_name', '=', 'order'],
                    ['reference_id' , '=', $order->order_no],
                    ['amount' , '=', round($order->total_amount*($mp->value/100))],
                    ['order_status' ,'=', $order->order_status],
                    ['type' ,'=', ResellerTransaction::TX_TYPE_COMMISSION],
                    ['status' ,'=', $status],
                    ['remarks' ,'=', $notes]
                ])->first();
                if ($status == 'Completed') {
                    $rc = ResellerCommission::where('reseller_id',$order->reseller_id)->first();
                    if ($rc != null) {
                        $rc->potential_amount = round($rc->potential_amount-($order->total_amount*($mp->value/100)));
                        $rc->commission_amount = round($rc->commission_amount+($order->total_amount*($mp->value/100)));
                        $rc->save();
                    } else {
                        $rc = ResellerCommission::create([
                            'reseller_id' => $order->reseller_id,
                            'potential_amount' => 0,
                            'commission_amount' => round($order->total_amount*($mp->value/100))
                        ]);
                    }
                    $rc = ResellerCommission::where('reseller_id',$order->reseller_id)->first();

                    CommissionRelease::dispatch($order->reseller_id, round($order->total_amount*($mp->value/100)));

                    $d_l = [
                        [
                            'commission_id' => $rc->id,
                            'transaction_id' => $rt1->id,
                            'reseller_id' => $order->reseller->reseller_id,
                            'type' => 'Debit',
                            'commission_type' => 'Potential',
                            'amount' => round($order->total_amount*($mp->value/100)),
                            'remarks' => $notes
                        ],
                        [
                            'commission_id' => $rc->id,
                            'transaction_id' => $rt1->id,
                            'reseller_id' => $order->reseller->reseller_id,
                            'type' => 'Credit',
                            'commission_type' => 'Commission',
                            'amount' => round($order->total_amount*($mp->value/100)),
                            'remarks' => $notes
                        ]
                    ];
                    DB::table('rsl_commission_ledgers')->insert($d_l);
                    // ResellerCommissionLedger::insert([
                    //     'commission_id' => $rc->id,
                    //     'transaction_id' => $rt1->id,
                    //     'reseller_id' => $order->reseller->reseller_id,
                    //     'type' => 'Debit',
                    //     'commission_type' => 'Potential',
                    //     'amount' => round($order->total_amount*($mp->value/100)),
                    //     'remarks' => $notes
                    // ]);
                    // ResellerCommissionLedger::insert([
                    //     'commission_id' => $rc->id,
                    //     'transaction_id' => $rt1->id,
                    //     'reseller_id' => $order->reseller->reseller_id,
                    //     'type' => 'Credit',
                    //     'commission_type' => 'Commission',
                    //     'amount' => round($order->total_amount*($mp->value/100)),
                    //     'remarks' => $notes
                    // ]);
                }
            }

            DB::commit();
            return 'pass';
        } catch (\Exception $e) {
            DB::rollBack();

            return [
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ];
        }
    }

    public function adjustCommissionAmount(OrderReseller $oh)
    {
        $mp = MasterParameter::where('group_key','RESELLER_COMMISSION')->where('key','COMMISSION_PERCENTAGE')->first();
        $oh->commission_amount = $oh->total_amount == 0 ? 0 : round($oh->total_amount*($mp->value/100));
        $oh->save();
    }

    public function transformProrateCV(OrderReseller $oh)
    {
        $all_discount_amount = ResellerOrderPromotion::where('reference_id',$oh->id)->whereIn('discount_type',['voucher','coupon'])->sum('amount');
        foreach ($oh->items as $item) {
            $discount_amount = $this->getProrateDiscount($oh->items()->sum('line_amount'),$item->line_amount,$all_discount_amount);
            $item->discount_amount = $item->discount_amount + $discount_amount;
            $item->total_amount = $item->total_amount - $discount_amount;
            $item->save();
        }
    }

    public function adjustCouponVoucher(OrderReseller $oh)
    {
        $ids = $oh->items()->pluck('id')->toArray();
        $ids[] = $oh->id;
        
        $ops = ResellerOrderPromotion::whereIn('reference_id',$ids)->whereIn('discount_type',['voucher','coupon'])->get()->unique('discount_id');
        foreach ($ops as $op) {
            if (strtolower($op->discount_type) == 'voucher') {
                $voucher = Voucher::where('id',$op->discount_id)->first();
                if (strtolower($voucher->type) == 'percentage') {
                    $voucher->used_amount = $voucher->amount;
                    $voucher->remaining_amount = 0;
                    $voucher->is_used = 1;
                    $voucher->used_at = now();
                    $voucher->save();
                } else {
                    $voucher->remaining_amount = $voucher->remaining_amount - $op->amount;
                    $voucher->used_amount = $op->amount;
                    $voucher->is_used = 1;
                    $voucher->used_at = now();
                    $voucher->save();
                }
            }
            if (strtolower($op->discount_type) == 'coupon') {
                $coupon = Coupon::where('id',$op->discount_id)->first();
                $coupon->used_count += 1;
                $coupon->save();
            }
        }
    }
}