<?php

namespace App\Helpers\Promotion;

use App\Models\ResellerOrderHeaders;
use Illuminate\Queue\SerializesModels;
use Throwable;
use VXM\Async\Invocation;
use App\Helpers\Promotion\PromotionHelper;
class PromotionEngine
{
    use PromotionHelper;
    use Invocation;
    use SerializesModels;
    protected $order;

    public function __construct(ResellerOrderHeaders $order)
    {
        $this->order         = $order;

    }

    public function handle()
    {
        try{
            // return $this->order;
            return $this->applyBestPromotion($this->order);
        }
        catch(\Exception $e){
            return ['error' => $e];
        }
    }

    
}
