<?php
namespace App\Helpers\Promotion;
use App\Traits\ResponseAPI;
use App\Helpers\RedisBuilder;
use App\Models\PublicProduct;
use App\Rules\DiscountAmount;
use Illuminate\Support\Facades\DB;
use App\Services\LoggerIntegration;
use App\Models\ResellerOrderDetails;
use App\Models\ResellerOrderHeaders;
use Illuminate\Support\Facades\Http;
use App\Models\Promotions\Promotions;
use Illuminate\Support\Facades\Cache;
use App\Models\ResellerOrderPromotion;
use App\Models\ResellerShipments as Sh;
use App\Models\Promotions\PromotionItems;
use App\Models\Promotions\PromotionTiers;
use App\Http\Resources\ListBundlingResource;
use Illuminate\Database\Eloquent\Collection;
use App\Models\Promotions\PromotionExceptionItems;

trait PromotionHelper
{

    public function applyBundling(ResellerOrderHeaders $order){
        try{
            $e = self::getEligibleBundling($order);
            $b = [];
            if(count($e) > 0){
                foreach($e as $p){
                    $l = self::applyBundlingPromotion($order->fresh(), $p);
                    if(!empty($l)){
                        array_push($b, ...$l);
                    }

                }
                return $b;
            }
        }
        catch(\Exception $e){
            return [];
        }
    }

    public function applyBestPromotion(ResellerOrderHeaders $order){
        try{
            if ($order->order_status == ResellerOrderHeaders::ORDER_RESELLER_PENDING) {
                self::clearPromotion($order);
                $e = self::getEligiblePromotionForOrder($order);
                // dd($e);
                if(count($e) > 0){
                    foreach($e as $p){
                        if(strtolower($p->action) == 'single discount'){
                            self::ApplySingleDiscount($order->fresh(), $p);
                        }
                        if(strtolower($p->action) == 'tier discount'){
                            self::ApplyTierDiscount($order->fresh(), $p);
                        }
                        if(strtolower($p->action) == 'bogo'){
                            self::ApplyBogoDiscount($order->fresh(), $p);
                        }
                    }
                    // ResellerOrderDetails::update($detail);
                    ResellerOrderHeaders::adjust($order);
                    
                    // kalo mau liat hasil apply promo langsung
                    return $order->fresh(['detail','detail.promotion']);
                    
                }
                ResellerOrderHeaders::adjust($order);
                return $order->fresh(['detail','detail.promotion']);
            }

        }
        catch(\Exception $e){
            return $e;
        }
      
    }

    public function validateQueue(Promotions $promo, $order_no){
        $usage = $promo->usage->pluck('items.order')->unique('order_no')->count()??0;
        $is_order = $promo->usage->pluck('items.order')->unique('order_no')->where("order_no",$order_no)->first();
        if($is_order) return true;
        if($promo->first_n_customers == null) return true;
        if($promo->first_n_customers == 0) return false;
        if($promo->first_n_customers < $usage) return false;
        if($promo->first_n_customers > $usage) return true;
        return false;
    }

    public static function validateQueueSt(Promotions $promo, $order_no){
        $usage = $promo->usage->pluck('items.order')->unique('order_no')->count()??0;
        $is_order = $promo->usage->pluck('items.order')->unique('order_no')->where("order_no",$order_no)->first();
        if($is_order) return true;
        if($promo->first_n_customers == null) return true;
        if($promo->first_n_customers == 0) return false;
        if($promo->first_n_customers < $usage) return false;
        if($promo->first_n_customers > $usage) return true;
        return false;
    }

    public function validateSamePromo(Promotions $promo, ResellerOrderHeaders $order){
        $ids = $order->detail()->pluck('id')->toArray();
        $ids[] = $order->id;
        $ops = ResellerOrderPromotion::whereIn('reference_id',$ids)->where('discount_id',$promo->id)->exists();
        return $ops;
    }



    public function ApplySingleDiscount($order, $promo){
        if(!empty($order->detail??[])){
            if(self::validateQueue($promo, $order->order_no??'') == true){
                foreach($order->detail as $item){
                    if($item->qty > 0){
                        if (($promo->items()->item($item->article_id??'')->exists() && !$promo->exception_items()->item($item->article_id??'')->exists()) || $promo->item_discount == null){
                            // $price = self::calculateSinglePromo($item->line_amount??0, $promo, $item->article_id??'-');
                            // echo($item); die();
                            // $curr_price = $item->promotion != null ? self::calculateSinglePromo($item->line_amount??0, $item->promotion->promo, $item->article_id) : 0;
                            // $discount_amount = $price > $curr_price ? $price : $curr_price;
                            $discount_amount = $item->qty * self::applyArticlePromotion($item->article_id??0, $item->unit_price??0)['disc']??0;
                            if(($item->promotion == null && $item->discount_amount < $discount_amount) || ($item->promotion->promo->action??'' !== 'Bogo' && $item->discount_amount??0 < $discount_amount)){
                                $item->discount_amount = $discount_amount;
                                $item->line_amount = $item->unit_price * $item->qty;
                                $item->total_amount = $item->unit_price * $item->qty - $discount_amount;
                                $item->additional_qty = null;
                                $item->save();
    
                                if($item->promotion == null){
                                    $item->promotion()->create([
                                        'reference_name' => 'details',
                                        'reference_id'   => $item->id,
                                        'discount_type'  => 'promotions',
                                        'discount_id'    => self::applyArticlePromotion($item->article_id??0, $item->unit_price??0)['promotion']['id']??$promo->id,
                                        'amount'         => $discount_amount,
                                        'created_by'     => 'PROMOTION_ENGINE_SINGLE'.($promo->first_n_customers != null?'_QUEUE':''),
                                        'modified_by'    => 'PROMOTION_ENGINE_SINGLE'.($promo->first_n_customers != null?'_QUEUE':''),
                                    ]);
                                }
                                else{
                                    $item->promotion()->update([
                                        'reference_name' => 'details',
                                        'reference_id'   => $item->id,
                                        'discount_type'  => 'promotions',
                                        'discount_id'    => self::applyArticlePromotion($item->article_id??0, $item->unit_price??0)['promotion']['id']??$promo->id,
                                        'amount'         => $discount_amount,
                                        'created_by'     => 'PROMOTION_ENGINE_SINGLE'.($promo->first_n_customers != null?'_QUEUE':''),
                                        'modified_by'    => 'PROMOTION_ENGINE_SINGLE'.($promo->first_n_customers != null?'_QUEUE':''),
                                    ]);
                                }
                        }
                            // dd($discount_amount, $item->line_amount);
                            // if($discount_amount <= $item->line_amount??0){
                           
                               
                            // }
                        }
                    }
            }
            }
          
    }
}

//     function ApplySingleDiscount(order: Order, promo: Promotion)

// FOREACH order_items THEN
//       IF order_item.line_amount > 0 AND
//          order_item.order_qty > 0 AND
//          order_item.promotion_id IS NOT 0
      		
//            Check order_item is in promotion_items
//            Check order_item is not in promotion_exception_items

//            Get discount_amount from CalculateApplyPromotionToOrderItem
//            Compare discount_amount is larger than current single discount

//            IF discount_amount <= order_item.line_amount AND
//             LoyaltyIsNotApplied AND
//             order_item.promotion_id IS null OR can replace existing discount

//                  Add Single Discount to order_item
//            ENDIF

//       	ENDIF
// END FOREACH 

public function calculatePromotionTierDiscount(ResellerOrderHeaders $order, Promotions $promo){
    $promotionDiscount = 0;
    $promotionTier = $promo->tiers();
    if(!$promotionTier) return 0;

    if(strtolower($promo->based_on??'') == 'gross'){
        $promotionTier = $promotionTier->orderBy('minimum_qty', 'DESC');
    }
    $promotionTier = $promotionTier->get();

    if ($promo->item_discount == null) {
        $articles = $order->detail()->pluck('article_id')->toArray();
        $exc = $promo->exception_items()->whereIn('item_id', $articles)->pluck('item_id')->toArray();
        $eligible = array_merge(array_diff($articles, $exc), array_diff($exc, $articles));
    } else {
        $eligible = $promo->items()->pluck('item_id')->toArray();
    }

    $totalOrderItemQty = empty($eligible) ? $order->totalOrderItemQty($order) : $order->totalOrderItemQtyIn($order,$eligible);
    // dd($promotionTier);
    $totalLineAmount = empty($eligible) ? $order->totalLineAmount($order) : $order->totalLineAmountIn($order, $eligible);
    // dd($totalLineAmount);
    $totalGrossLineAmount = $order->totalGrossLineAmount($order);
    foreach($promotionTier as $p){
        if($totalOrderItemQty >= $p->minimum_qty){
            if($promo->based_on == "Net"){
                $discount = self::getPromotionDiscountTier($promo, $p, $totalLineAmount - $promotionDiscount);
                if($promotionDiscount + $discount >= $totalLineAmount) break;
                $promotionDiscount = $discount; 
            }
            else if($promo->based_on == "Gross"){
                $discount = self::getPromotionDiscountTier($promo, $p, $totalLineAmount);
                $promotionDiscount = $discount;
                break;
                }
            else{
                $discount = self::getPromotionDiscountTier($promo, $p, $totalLineAmount);
                $promotionDiscount = $discount;
                break;
            }
            }
        }
    // dd($promotionDiscount);
    return $promotionDiscount;
    }


public function getPromotionDiscountTier(Promotions $promo, PromotionTiers $tier, $beforeDiscount, $multiplier = 1){
    if($promo->discount_type == "Value"){
        return $tier->discount_amount??0 * $multiplier;
    }
    if($promo->discount_type == "Percentage"){
        $dc = $tier->discount_amount??0;
        $mult = $dc == 0 ? 0 : $dc / 100;
        // dd($beforeDiscount, $dc, $mult);
        return min(($beforeDiscount * $mult) ?? 0, $tier->maximum_value??(($beforeDiscount * $mult) ?? 0)) * $multiplier;
    }
    return 0;
}

public static function getProrateDiscount($order_total_price = 0, $item_total_price = 0, $discount = 0){
    if($discount == 0 || $order_total_price == 0 || $item_total_price == 0) return 0;
    $r = $discount / $order_total_price; //rate discount
    // dd($r);
    return round($item_total_price * $r,1,PHP_ROUND_HALF_DOWN);
}
public function ApplyTierDiscount($order, $promo){
    if(!empty($order->detail??[])){
        if(self::validateQueue($promo, $order->order_no) == true){
        $d = [];
        foreach($order->detail as $item){
             if ($promo->item_discount != null && $promo->items()->item($item->article_id??'')->exists() && !$promo->exception_items()->item($item->article_id??'')->exists() || $promo->item_discount == null){
                if($item->line_amount > 0 && $item->qty > 0){
                    $tierDiscount = self::calculatePromotionTierDiscount($order, $promo);
                    // dd($tierDiscount);
                    if ($promo->item_discount == null) {
                        // $articles = DB::table('view_reseller_article_detail')->pluck('article')->toArray();
                        $articles = $order->detail()->pluck('article_id')->toArray();
                        $exc = $promo->exception_items()->whereIn('item_id', $articles)->pluck('item_id')->toArray();
                        $eligible = array_merge(array_diff($articles, $exc), array_diff($exc, $articles));
                        // $eligible = array_values(array_filter($articles,function ($articles) use ($exc){return !in_array($articles,$exc);}));
                    } else {
                        $eligible = $promo->items()->pluck('item_id')->toArray();
                    }
                    $totalLineAmount = empty($eligible) ? $order->totalLineAmount($order) : $order->totalLineAmountIn($order, $eligible);
                    $discount = in_array($item->article_id??'', $eligible) ? self::getProrateDiscount($totalLineAmount, $item->line_amount??0, $tierDiscount) : 0;
                    // return ['item_id' => [$item->article_id, "$item->product_name, $item->product_variant, $item->product_size"],'total_sum_line_amount' => $totalLineAmount, 'item_line_amount' => $item->line_amount, 'discount' => $discount,'promotion' => $promo->withoutRelations()->toArray(), 'ruleset' => $promo->tiers->toArray()];
                    // $d[] = $discount;
                    if($item->promotion == null && $item->discount_amount??0 < $discount || $item->promotion->promo->action??'' !== 'Bogo' && $item->discount_amount??0 < $discount){
                            $item->discount_amount = $discount;
                            $item->line_amount = $item->unit_price * $item->qty;
                            $item->total_amount = $item->unit_price * $item->qty - $discount;
                            $item->additional_qty = null;
                            $item->save();
    
                        
                            if($item->promotion == null) {
                                $item->promotion()->create([
                                    'reference_name' => 'details',
                                    'reference_id'   => $item->id,
                                    'discount_type'  => 'promotions',
                                    'discount_id'    => $promo->id,
                                    'amount'         => $discount,
                                    'created_by'     => 'PROMOTION_ENGINE_TIER'.($promo->first_n_customers != null?'_QUEUE':''),
                                    'modified_by'    => 'PROMOTION_ENGINE_TIER'.($promo->first_n_customers != null?'_QUEUE':''),
                                ]);
                            }
                         
                            else {
                                $item->promotion()->update([
                                    'reference_name' => 'details',
                                    'reference_id'   => $item->id,
                                    'discount_type'  => 'promotions',
                                    'discount_id'    => $promo->id,
                                    'amount'         => $discount,
                                    'created_by'     => 'PROMOTION_ENGINE_TIER'.($promo->first_n_customers != null?'_QUEUE':''),
                                    'modified_by'    => 'PROMOTION_ENGINE_TIER'.($promo->first_n_customers != null?'_QUEUE':''),
                                ]);
                            }
                    }
                    // if ($promo->items()->item($item->article_id??'')->exists() && !$promo->exception_items()->item($item->article_id??'')->exists()){
                    //     $price = self::calculateSinglePromo($item->unit_price??0, $promo);
                    //     $curr_price = $item->promotion != null ? self::calculateSinglePromo($item->unit_price??0, $item->promotion) : 0;
                    //     $discount_amount = $price > $curr_price ? $price : $curr_price;
                    //     if($discount_amount <= $item->line_amount??0 && strtolower($promo->action) != 'single discount'){
                    //         $item->promotion = $promo->id;
                    //     }   
                    // }
                }
            }
        
    }
    // dd($d,$tierDiscount);
}
}
}


// function ApplyTierDiscount(order: Order, promo: Promotion)

// Get totalDiscountTier from CalculatePromotionTierDiscount
// Get order_items from OrderItemsCanBeAppliedPromotion
// Get loadshareEiger
// Get loadsharePartner
// Get totalGrossLineAmount

// Validate if discount > order_items.line_amount

// FOREACH order_items THEN
//       IF order_item.line_amount > 0 AND
//          order_item.order_qty > 0 AND
//          order_item.promotion_id IS NULL AND LoyaltyIsNotApplied 
      		
//            Calculate prorate discount
//            Add Tier Discount to order_item
//       	ENDIF
// END FOREACH 

// Fix Prorate Calculation

    public function formatBundlingCode($order_no='', $item=['sequence' => 0, 'article'=>'-', 'promotion_id' => 0]){
        return "$order_no/".$item['sequence']??''.$item['promotion_id']??'0';
    }

    public function applyBogoDiscount($order, $promo){
        $itemsToDiscount = [];
        if(!empty($order->detail??[])){
            if(self::validateQueue($promo, $order->order_no) == true && self::validateSamePromo($promo,$order) == false){
                //filter article yg masuk gobo
                $a = $promo->items()->pluck('item_id')->toArray();
                $exc_a = $promo->exception_items()->pluck('item_id')->toArray();

                $i = array_filter($order->detail->toArray(), fn($i) => in_array($i['article_id'], $a) && !in_array($i['article_id'], $exc_a));

                //sort article gobo termahal
                $bogo = collect($i)->sortBy('unit_price')->values();

                //get gobo eligible qty
                $bogo_qty = $order->detail()->whereIn('article_id', $a)->whereNotIn('article_id',$exc_a)->sum('qty') > 0 ? floor($order->detail()->whereIn('article_id', $a)->whereNotIn('article_id',$exc_a)->sum('qty')/2) : 0;
                //intinya bogo
                // dd($bogo);
                foreach($bogo as $item){
                    if ((count($bogo) == 1 && $item['qty'] > 1) || (count($bogo) > 1)) {
                        $i = $order->detail()->where('article_id', $item['article_id'])->first();

                        if ($i->promotion != null && !str_contains($i->promotion->created_by,'PROMOTION_ENGINE_BOGO')) {
                            $item['discount_amount'] = 0;
                            $item['total_amount'] = $item['line_amount'];
                            $i->update($item);
                        }

                        $test1[] = $bogo_qty;
                        if($bogo_qty > 0){
                            if($item['line_amount'] > 0 && $item['qty'] > 0){
                                //2 >= 4 ?
                                $qty_disc = $bogo_qty >= $item['qty'] ? $item['qty'] : $bogo_qty ;
                                
                                $bogo_qty = $bogo_qty - $qty_disc;
                                $disc =  $item['unit_price'] * $qty_disc;
                                $item['discount_amount'] = $disc;
                                $item['total_amount'] = $item['line_amount']-$disc;
                                // dd($qty_disc, $item, $bogo_qty, $order->detail()->where('article_id', $item['article_id'])->first());
                                
                                unset($item['promotion']);
                                $i->update($item);
                                $test[] = $item;
                            }
                        }
                        
                        if($i->promotion == null) {
                            $i->promotion()->create([
                                'reference_name' => 'details',
                                'reference_id'   => $item['id'],
                                'discount_type'  => 'promotions',
                                'discount_id'    => $promo->id,
                                'amount'         => $disc??0,
                                'created_by'     => 'PROMOTION_ENGINE_BOGO'.($promo->first_n_customers != null?'_QUEUE':''),
                                'modified_by'    => 'PROMOTION_ENGINE_BOGO'.($promo->first_n_customers != null?'_QUEUE':''),
                            ]);
                        } else {
                            $i->promotion()->update([
                                'reference_name' => 'details',
                                'reference_id'   => $item['id'],
                                'discount_type'  => 'promotions',
                                'discount_id'    => $promo->id,
                                'amount'         => $disc??0,
                                'created_by'     => 'PROMOTION_ENGINE_BOGO'.($promo->first_n_customers != null?'_QUEUE':''),
                                'modified_by'    => 'PROMOTION_ENGINE_BOGO'.($promo->first_n_customers != null?'_QUEUE':''),
                            ]);
                        }
                        $disc = 0;
                    }       
                }
                // dd($test);
                // dd($test,$test1,$order->detail()->get()->toArray());
            }
        }
    }

    public function deleteBundling(ResellerOrderHeaders $order){

        $order->bundlings()->delete();
        return $order->detail_bundling()->delete();
    }

    public function applyBundlingPromotion($order, $promo){
        if(!empty($order->detail??[])){     
                $list_bundling = [];
                $a = $promo->items()->pluck('item_id')->toArray();
                $exc_a = $promo->exception_items()->pluck('item_id')->toArray();
                $i = !$promo->item_discount ? array_filter($order->detail->toArray(), fn($i) => !in_array($i['article_id'], $exc_a)) :array_filter($order->detail->toArray(), fn($i) => in_array($i['article_id'], $a) && !in_array($i['article_id'], $exc_a));
                if(empty($i)) return $i;
                    $total_bundlings = collect($i)->sum('line_amount');
                    $bundlings = $promo->bundlings()->where('minimum_value', '<=', $total_bundlings)->first();
                if($bundlings == null) return [];
                if(empty($bundlings->items)) return [];
                    $eligible_bundling = $promo->is_multiple_qty == 1 ? floor($total_bundlings/$bundlings->minimum_value) : 1;
                    for($i = 0; $i < $eligible_bundling; $i++){
                        $choose = $bundlings->items->map(function($item) {
                            $item['article']['image_url'] =  $item['article']['mainImageVariant']  == null ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL').$item['article']['mainImageVariant']['file_path'];
                            return $item;
                        })->filter(function ($value) {
                            return $value->stock && $value->stock->available_stock > 0;
                        })->toArray();
                        $arr = ['promotion_name' => $promo->name, 'promotion_id'=>$promo->id, 'sequence' => $i, 'choose' => $choose
                    ];
                        if(!empty($choose)) array_push($list_bundling, $arr);
                    } 
                
                    return $list_bundling;
                
            
        }
    }



// function ApplyBogoDiscount(order: Order, promo: Promotion)

// Set order_items_index With empty list
// FOREACH order_items THEN
//       Check order_item is in promotion_items
//       Check order_item is not in promotion_exception_items

//       IF order_item.line_amount > 0 AND
//          order_item.order_qty > 0 AND
//          LoyaltyIsNotApplied 
      		
//            Add order_item to order_items_index 

//       	ENDIF
// END FOREACH 

// Calculate numberBogoPairs
// FOREACH order_items_index in numberBogoPairs THEN
//       Remove Others Discount from order_items_index 
//       Remove Others Discount from order_items_index_pair 

//       Add Bogo Discount to order_items_index 
//       Add Bogo Discount with amount 0 to order_items_index_pair 
// END FOREACH 


    public static function calculateSinglePromo(String $numberPrice, Promotions $e, String $article){

        try{
            $items = $e->items()->item($article??'')->first();
            $total = fn($price, $promotions) =>
            strtolower($promotions->discount_type??'-') == 'value' ? (double)$items->discount_amount??0 : ($items->discount_amount > 0 ? $price * ($items->discount_amount / 100) : 0);
            
            // ini return discount price mas
            // $res = $total($numberPrice, $e);
            // return empty($res) ? 0 : ($numberPrice < $res ? $res : $numberPrice - $res); (ada casenya kah discount amount lebih gede dari unit price? jadinya gk diskon dong?)

            // ini return discount amount
            $res = $total($numberPrice, $e);
            return $res;

        }
        catch(\Exception $e){
            return 0;
        }
    
    }

    public static function applyArticlePromotion(String $article, String $numberPrice = '0'){

        try{
            // $article = substr($article, 0, 9);
            // if(Cache::has('cache_promotion')){
            //     $e = Cache::get('cache_promotion');
            // }
            // else{
                // $e = self::filterPromotion(
                //     Promotions::
                //     with('items','exception_items')->active()->activeDate(), ['day', 'date'])->where('action', 'Single Discount')->get();
                // Cache::set('cache_promotion', $e, 3600);
            // }
          
            // $total = fn($price, $promotions) =>
            // strtolower($promotions['discount_type']) == 'value' ?  $promotions['items'][0]['discount_amount']??$promotions['discount_amount']??0 : $price * $promotions['items'][0]['discount_amount']??$promotions['discount_amount']??0 / 100;

            // $res = array_map(fn($x) => self::getArticlePromotion($numberPrice, $x, $article) , $e->all());
            // return empty($res) ? 0 : ($numberPrice < max(array_column($res, 'disc')) ? max(array_column($res, 'disc')) : $numberPrice - max(array_column($res, 'disc')));
            // return empty($res) ? ['disc' => 0, 'promotion' => null] : collect($res)->sortByDesc('disc')->first();
            $query = (array) \DB::select('call get_article_max_single_promotions(?)',array($article??''));
            if(array_key_exists(0, $query)){
                $d = (array) $query[0];
            }
            else{
                $d = null;
            }
                if($d == null){
                    return ['disc' => 0, 'promotion' => null];
                }
                return ['disc' => $d['discount_amount'], 'promotion' => Promotions::where('id', $d['id'])->first()];
        }
        catch(\Exception $e){
        
            return ['disc' => 0, 'promotion' => null];
        }
      
    }

    public static function getArticlePromotion($price, $promotions, $article){
        $include = collect($promotions['items']);
        $exclude = collect($promotions['exception_items']);
        // dd($c)
        $calculate = fn($price, $disc, $type) => $type == "Value" ? $disc : $price * $disc / 100;
        // if(self::validateQueueSt($promotions) != true){
        //     return ['disc' => 0, 'promotion' => null];
        // };
        if($promotions['item_discount'] == null && $promotions['item_exception'] == null){
            return ['disc' => $calculate($price,$promotions['discount_amount'],$promotions['discount_type']), 'promotion' => $promotions];
            
        }
        if($promotions['item_discount'] == null && $promotions['item_exception'] != null){
            if(!$exclude->firstWhere('item_id', $article)){
                return ['disc' => $calculate($price,$promotions['discount_amount'],$promotions['discount_type']), 'promotion' => $promotions];
            }
            else{
                return ['disc' => 0, 'promotion' => null];
            }
        }
        if($promotions['item_discount'] != null && $promotions['item_exception'] == null){
            $pr = $include->firstWhere('item_id', $article);
            if($pr){
                return ['disc' => $calculate($price,$pr['discount_amount'],$promotions['discount_type']), 'promotion' => $promotions];
            }
            else{
                return ['disc' => 0, 'promotion' => null];
            }
            
        }
        if($promotions['item_discount'] != null && $promotions['item_exception'] != null){
            $pr = $include->firstWhere('item_id', $article);
            if($pr && !$exclude->firstWhere('item_id', $article)){
                return ['disc' => $calculate($price,$pr['discount_amount'],$promotions['discount_type']), 'promotion' => $promotions];
            }
            else{
                return ['disc' => 0, 'promotion' => null];
            }
        }
        return ['disc' => 0, 'promotion' => null];
     
        // return empty($res) ? 0 : ($numberPrice < max(array_column($res, 'disc')) ? max(array_column($res, 'disc')) : $numberPrice - max(array_column($res, 'disc')));
        // return empty($res) ? ['disc' => 0, 'promotion' => null] : collect($res)->sortByDesc('disc')->first();
    }

    public function clearPromotion(ResellerOrderHeaders $order){
        foreach($order->detail as $item){
            $item->promotion()->delete();
            $discount_amount = ResellerOrderPromotion::where('id',$item->id)->whereIn('discount_type',['voucher','coupon'])->where('reference_name','details')->sum('amount');

            if($item->line_amount > 0 && $item->qty > 0){
                $item->line_amount = $item->unit_price * $item->qty;
                $item->discount_amount = 0+$discount_amount;
                $item->total_amount = $item->line_amount - $item->discount_amount;
                $item->additional_qty = 0;
                $item->save();
            }
            // if (!empty($i->promotion)) {
            //     $promotion = $i->promotion;
            
            //     $promo = Promotions::where('id',$promotion->discount_id)->first();
                // minta tolong buatin tiap jenis promotion ya mas eheheh
                // if (strtolower($promo->action) == 'bogo') {
                    // foreach ($order->detail as $item) {
                    //     if ($promo->items()->item($item->article_id??'')->exists() && !$promo->exception_items()->item($item->article_id??'')->exists()){
                    //         $discount_amount = ResellerOrderPromotion::where('id',$item->id)->whereIn('discount_type',['voucher','coupon'])->where('reference_name','details')->sum('amount');
                    //         if($item->line_amount > 0 && $item->qty > 0){
                    //             $item->line_amount = $item->unit_price * $item->qty;
                    //             $item->discount_amount = $discount_amount??0;
                    //             $item->total_amount = $item->line_amount - $item->discount_amount;
                    //             $item->additional_qty = 0;
                    //             $item->save();
                    //         }
                    //     }
                    // }
                // }
            // }
            // else{

            // }
        }
    }

    public function getEligiblePromotionForOrder(ResellerOrderHeaders $order){
        $pr_list = [];
        try{
            foreach(self::getEligiblePromotions() as $p){
                // canCustomerApplied gaperlu di reseller
                // if(self::canCustomerApplied($p, $order) && self::canBeApplied($p, $order)){
                if(self::canBeApplied($p, $order) === true){
                    array_push($pr_list, $p);
                }
            }
    
            return $pr_list;        
        }
        catch(\Exception $e){
            return $e;
        }

    
    }

    public function getEligibleBundling(ResellerOrderHeaders $order){
        $pr_list = [];
        try{
            foreach(self::getBundling() as $p){
                // canCustomerApplied gaperlu di reseller
                // if(self::canCustomerApplied($p, $order) && self::canBeApplied($p, $order)){
                if(self::canBeApplied($p, $order) === true){
                    array_push($pr_list, $p);
                }
            }
    
            return $pr_list;        
        }
        catch(\Exception $e){
            return $e;
        }

    
    }

    public static function statementMapper($str, $arg1, $arg2){
        switch(strtolower($str)){
            case 'equal or greater than':
                return $arg1 >= $arg2;
            case 'equal or less than':
                return $arg1 <= $arg2;
            case 'greater than':
                return $arg1 > $arg2;
            case 'less than':
                return $arg1 < $arg2;
            default:
                return true;
        }
    }

    public static function canBeApplied(Promotions $p ,ResellerOrderHeaders $order){
        if(empty($p->parameters)) return true;
        if ($p->item_discount == null) {
            $articles = $order->detail()->pluck('article_id')->toArray();
            $exc = $p->exception_items()->whereIn('item_id', $articles)->pluck('item_id')->toArray();
            $eligible = array_merge(array_diff($articles, $exc), array_diff($exc, $articles));
        } else {
            $eligible = $p->items()->pluck('item_id')->toArray();
        }
        $totalOrderItemQty = empty($eligible) ? $order->totalOrderItemQty($order) : $order->totalOrderItemQtyIn($order,$eligible);
        $totalLineAmount = empty($eligible) ? $order->totalLineAmount($order) : $order->totalLineAmountIn($order, $eligible);
        
        if(empty($eligible)) return false;

        foreach($p->parameters as $param){
            if($param->type == 'Bill-Qty' && self::statementMapper($param->statement??'',$totalOrderItemQty, $param->value??0) == false) return false;
            if($param->type == 'Bill-Value' && self::statementMapper($param->statement??'',$totalLineAmount, $param->value??0) == false) return false;
        }
        return true;
    }

    public function getBundling() : Collection {
        return self::filterPromotion(self::getCurrentAvailablePromotion(), ['day', 'time'])->where('action', 'Bundling')->get();
    }

    public function getEligiblePromotions() : Collection {
        return self::filterPromotion(self::getCurrentAvailablePromotion(), ['day', 'time'])->get();
    }

    public static function filterPromotion(RedisBuilder $promo, $args = []){
        $cd = strtolower(date('l'));
        foreach($args as $f){
            switch($f){
                case 'day':
                    $promo->activeDay($cd."_enabled");
                case 'time':
                    $promo->activeDayDate($cd."_start_at", $cd."_end_at");
            }
        }
        return $promo;
    }

    public function getCurrentAvailablePromotion(){
        return Promotions::active()->activeDate()->orderByRaw("FIELD(action,'Single Discount','Tier Discount','Bogo')");
    }

}