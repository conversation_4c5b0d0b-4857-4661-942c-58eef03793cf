<?php
namespace App\Helpers;


/*Parameter Query
        Struktur:
         [
            'name' => 'identifier',
            'parameter' => [
                'request_parameter' => 'table_parameter'
            ]
        ]

*/ 
Class QueryMapping
{
    public static $resources =
    [
        [
            'name' => 'transaction_internal',
            'parameter' => [
                'order_no' => [
                    'field_name' => 'order_no',
                    'relation' => '',
                    'type' => 'text',
                    'query' => 'where',
                    'operation' => 'LIKE',
                ],
                'ref_order' => [
                    'field_name' => 'external_order_no',
                    'relation' => '',
                    'type' => 'text',
                    'query' => 'where',
                    'operation' => 'LIKE',
                ],
                'nama_akun' => [
                    'field_name' => 'owner_name',
                    'relation' => 'customer',
                    'type' => 'text',
                    'query' => 'where',
                    'operation' => 'LIKE',
                ],
                'location_code' => [
                    'field_name' => 'location_code',
                    'relation' => '',
                    'type' => 'text',
                    'query' => 'where',
                    'operation' => 'LIKE',
                ],
                'status_pesanan' => [
                    'field_name' => 'order_status',
                    'relation' => '',
                    'type' => 'delimeter',
                    'query' => 'whereIn',
                    'operation' => '=',
                ],
                'date_to' => [
                    'field_name' => 'created_date',
                    'relation' => '',
                    'type' => 'date',
                    'query' => 'whereDate',
                    'operation' => '<=',
                ],
                'date_from' => [
                    'field_name' => 'created_date',
                    'relation' => '',
                    'type' => 'date',
                    'query' => 'whereDate',
                    'operation' => '>=',
                ],
            ]
            ],
            [
                'name' => 'transaction_internal_ws',
                'parameter' => [
                    'order_no' => [
                        'field_name' => 'order_no',
                        'relation' => '',
                        'type' => 'text',
                        'query' => 'where',
                        'operation' => 'LIKE',
                    ],
                    'ref_order' => [
                        'field_name' => 'sales_order_no',
                        'relation' => '',
                        'type' => 'text',
                        'query' => 'where',
                        'operation' => 'LIKE',
                    ],
                    'owner_name' => [
                        'field_name' => 'owner_name',
                        'relation' => 'customer',
                        'type' => 'text',
                        'query' => 'where',
                        'operation' => 'LIKE',
                    ],
                    'store_name' => [
                        'field_name' => 'name',
                        'relation' => 'customer_shipment',
                        'type' => 'text',
                        'query' => 'where',
                        'operation' => 'LIKE',
                    ],
                    'sales_name' => [
                        'field_name' => 'sales_name',
                        'relation' => 'customer_sales.sales',
                        'type' => 'text',
                        'query' => 'where',
                        'operation' => 'LIKE',
                    ],
                    'status' => [
                        'field_name' => 'order_status',
                        'relation' => '',
                        'type' => 'delimeter',
                        'query' => 'whereIn',
                        'operation' => '=',
                    ],
                    'date_to' => [
                        'field_name' => 'created_date',
                        'relation' => '',
                        'type' => 'date',
                        'query' => 'whereDate',
                        'operation' => '<=',
                    ],
                    'date_from' => [
                        'field_name' => 'created_date',
                        'relation' => '',
                        'type' => 'date',
                        'query' => 'whereDate',
                        'operation' => '>=',
                    ],
                ]
            ]
        ];
}