<?php
namespace App\Helpers;

use App\Models\FilterableModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
class RedisBuilder extends Builder
{
 
    public function redisPaginate(int|null $perPage = null, array $columns, string $pageName = "page", int|null $page = null)
    {
        if (Cache::has(FilterableModel::getQueryWithBindings($this).$perPage.$page)) {
            return Cache::get(FilterableModel::getQueryWithBindings($this).$perPage.$page);
        }

        Cache::put(FilterableModel::getQueryWithBindings($this).$perPage.$page, parent::paginate($perPage, $columns, $pageName, $page), 60);
        return parent::paginate($perPage, $columns, $pageName, $page);
    }


    //todo handling function get, dkk


}
