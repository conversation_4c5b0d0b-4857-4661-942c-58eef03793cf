<?php

namespace App\Helpers;

use DB;
use Validator;
use Carbon\Carbon;
use App\Models\Cart;
use App\Models\Sales;
use App\Models\Product;
use Webpatser\Uuid\Uuid;
use App\Models\CartDetail;
use App\Models\ProductSku;
use App\Models\UserMatrix;
use App\Traits\ResponseAPI;
use Illuminate\Support\Str;
use App\Events\GenericEvent;
use App\Models\BusinessUnit;
use Illuminate\Http\Request;
use App\Events\StockSAPEvent;
use App\Models\CustomerStock;
use App\Models\OrderReseller;
use App\Jobs\StockUpdateQueue;
use App\Models\MasterParameter;
use App\Models\UserNotification;
use App\Repositories\GetStokRepo;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

trait RestHelper
{
  use ResponseAPI;

  public static function hashURL(Request $request)
  {
    return hash('sha256', $request->fullUrl() . $request->bearerToken()??'');
  }

  
  public static function getCustomerStock($customer_id, $article_id){
    $list = CustomerStock::with('customer_shipment')->where(['customer_id' => $customer_id, 'article_id' => $article_id])->get()->toArray();
    return [
      'total_sum' => array_reduce($list, function($total, $items)
      {
          return $total += $items['qty'] ?? 0;
      }, 0),
      'store_list' => $list
    ];
  }

  public static function searchStock($response_article = [], $article = null){
    // return array_search($article, array_column($response_article,'article'));
    return array_values(array_filter($response_article,function ($item) use ($article){
      return $item['article'] === $article;
    }))[0]??['article' => $article, 'qty' => '0', 'moq' => '0'];
  }

  public static function addFieldStock($stock_cache = [] , $data = []){
    return array_map(function ($item) use ($stock_cache){
      $item['stock'] = self::searchStock($stock_cache,$item['article'])['qty'];
      return $item;
    },$data);
  }

  public static function addFieldMoq($stock_cache = [] , $data = []){
    return array_map(function ($item) use ($stock_cache){
      $item['moq'] = self::searchStock($stock_cache,$item['article'])['moq'];
      return $item;
    },$data);
  }

  public static function addFieldStockMoq($stock_cache = [] , $data = []){
    return array_map(function ($item) use ($stock_cache){
      $item['stock'] = self::searchStock($stock_cache,$item['article'])['qty'];
      $item['moq'] = self::searchStock($stock_cache,$item['article'])['moq'];
      return $item;
    },$data);
  }

  public static function vArrayToObject($data = []){
    return array_map(function ($item){
      return (object)$item;
    },$data);
  }

  public static function getDefaultResponse($data = []){
    $cache = Cache::tags('article_stock');
        $rs = [];
        foreach ($data as $dat){
            $rs[] = $cache->has($dat) ? $cache->get($dat) :  [
                "article"=> $dat,
                "qty"=> "0",
                "moq" => "1"
            ];
        }
        return $rs;
  }


  public static function stockCache($article = [], $customer_id = null)
  {
    $customer_id = $customer_id == null ? env('ARTICLE_STOCK_SAPUSER') : $customer_id;
    $cache = Cache::tags('article_stock');
    $max_article = env('MAX_ARTICLE_PER_REQUEST', 80);
    $chunks = array_chunk($article, $max_article);
    $return_data = [];
    foreach ($chunks as $chunk) {
      if (env('STOCK_CACHE', false) == true) {
        foreach($chunk as $i){
          $return_data[] = $cache->has($i) ? $cache->get($i) : ["article"=>$i,"qty"=>"0","moq"=>"0"];
        }
        StockUpdateQueue::dispatch($chunk, $customer_id);
      } else {
        StockUpdateQueue::dispatch($chunk, $customer_id);
        // Log::info("FETCHING STOCK NON-QUEUE ARTICLE : {$article} !");
         $article_map = array_map(function ($b) {
              return [
                'source' => 'CAREOM',
                'destination' => 'STK',
                'article' => $b
              ];
          }, $chunk);
  
  
        $data = [
          "source" => "CAREOM",
          "destination" => "STK",
          "detail" => $article_map
        ];
  
        $getStokRepo = new GetStokRepo();
        $response = $getStokRepo->getStock($data, auth()->user()->customer->customer_id ?? null);
        $new_resp = $getStokRepo->getData($response);
  
  
        try {
          foreach($new_resp['data'] as $it){
            Cache::tags('article_stock')->put($it['article'],   ['article'=> $it['article'], 'qty' => (int)$it['qty']??0, 'moq' => $it['moq']??'1']);
        }
          $result = count($new_resp['data']) > 0 ?  
          array_map(fn($i)=>[
            "article" => $i['article'],
            "qty" => (int)$i['qty']??0,
            "moq" => $i['moq']??'1'
        ],$new_resp['data'])
          : self::getDefaultResponse($article);
          $return_data = array_merge($return_data,$result);
        } catch (\Exception $e) {
          $result = self::getDefaultResponse($article);
          $return_data = array_merge($return_data,$result);

        }
      }
    }

    return $return_data;
    
  }

  public static function syncStock($article = [], $customer_id = null, $sku = null){

    $max_article = env('MAX_ARTICLE_PER_REQUEST', 80);
    $chunks = array_chunk($article, $max_article);

    
    $getStokRepo = new GetStokRepo();
    $new_resp = '';

    try{
      foreach ($chunks as $chunk){
      
        $article_map = array_map(function ($b) {
          return [
            'source' => 'CAREOM',
            'destination' => 'STK',
            'article' => (string)$b
            ];
        }, $chunk);
    
        $data = [
          "source" => "CAREOM",
          "destination" => "STK",
          "detail" => $article_map
        ];
        
        \Log::info($data);
        $response = $getStokRepo->getStock($data, auth()->user()->customer->customer_id ?? null);
        $new_resp = $getStokRepo->getData($response);  
  
        $result = array_map(function ($item) {
          return [
              'article' => $item['article'],
              'qty' => (int) $item['qty'],
          ];
          }, $new_resp['data']);
          \Log::info($sku);
          event(new StockSAPEvent($sku, true,$result));
        }
  
        if (is_string($new_resp)) {
          $new_resp = json_decode($new_resp, true);
      }
      
      if (is_string($new_resp)) {
        $new_resp = json_decode($new_resp, true);
    }
    
    $data = is_array($new_resp['data'] ?? null) ? $new_resp['data'] : [];

        $mappedData = array_map(function($sapArticle) {
          $skuId = $sapArticle['article'];
          return [
              'id' => (string) Str::uuid(),
              'sku_id' => $skuId,
              'sku_code_c' => substr($skuId, 0, -3),
              'stock' => (int)$sapArticle['qty'],
              'mock' => (int)isset($sapArticle['moq']) ? (int) $sapArticle['moq'] : 0,
              'created_at' => Carbon::now(),
              'updated_at' => Carbon::now(),
          ];
      }, $data);
      
      ProductSku::upsert($mappedData, ['sku_id'], ['stock', 'mock', 'updated_at']);
        \Log::info($new_resp['data']);
        
      
      
      return  $mappedData;
    }catch (\Exception $e) {
      $errors = $e->getMessage();
      event(new StockSAPEvent($sku, false,[]));
      return $errors;
  }
    

  
  }

  

  public static function fetchCheck($cart, $article)
  {
    $cache = Cache::tags($cart);
    if ($cache->has($article)) {
      return $cache->get($article);
    } else {
      $cache->put($article, true);
      return true;
    }
  }

  public static function deleteAllCheck($cart)
  {
    Cache::tags($cart)->deleteAll();
  }

  public static function deleteCheck($cart, $article)
  {
    Cache::tags($cart)->delete($article);
  }

  public static function storeCheck($cart, $article, $value = true)
  {
    Cache::tags($cart)->put($article, $value);
  }


  function customerId_recursive($model)
  {
    $result = [];
    foreach ($model->child as $child) {
      $result[] = $child;
      if ($child->child) {
        $result = array_merge($result, $this->customerId_recursive($child));
      }
    }
    return $result;
  }

  function array_map_recursive($callback, $array)
  {
    if (is_array($callback)) {
      foreach ($callback as $function) {
        $array = $this->array_map_recursive($function, $array);
      }
      return $array;
    }

    $func = function ($item) use (&$func, &$callback) {
      return is_array($item) ? array_map($func, $item) : call_user_func($callback, $item);
    };

    return array_map($func, $array);
  }

  public function cartStore($custId, $request)
  {

    DB::beginTransaction();
    try {
      $cart = Cart::firstOrCreate(
        ['customer_id' => $custId]
      );

      $cart_header_id = $cart::where('customer_id', $custId)->first()->id;
      $cartDetail = CartDetail::updateOrCreate(
        [
          'cart_id' => $cart_header_id,
          'article' => $request->article,
          'is_custom' => $request->is_custom == true ? 1 : 0
        ],
        [
          'qty' => $request->qty,
        ]
      );

      DB::commit();
      return $this->sendSuccessCreated("Cart created/updated successfully.", $cartDetail);
    } catch (\Exception $e) {

      DB::rollback();
      Log::info($e->getMessage());
      return $this->sendError("Sorry system can't create/update checkout,  ", 500);
    }
  }

  public static function notifPayment($datas = [], $type = null){
    $dueMessage = [];
    foreach($datas as $key => $data){
      foreach($data as $d){
        $invoice_no = $d->order_no;
        if($type == 'before'){
          $message = "Terdapat tagihan yang akan jatuh tempo " . $key . " hari lagi, dengan nomor order " . "$invoice_no";
          $level = 'reminder';
        }
        
        if($type == 'today'){
          $message = "Tagihan dengan nomor order " . $invoice_no . " sudah jatuh tempo pada hari ini";
          $level = 'reminder';

          $salesMessage = "Order #$invoice_no akan jatuh tempo hari ini. Pastikan proses pembayaran atau pengiriman selesai tepat waktu.";
        }
        
        if($type == 'after'){
          $message = "Terdapat tagihan yang sudah jatuh tempo " . $key . " hari yang lalu, dengan nomor order " . "$invoice_no";
          $level = 'overdue';

          $salesMessage = "Order #$invoice_no telah melewati batas waktu. Mohon segera tindak lanjuti untuk menghindari keterlambatan lanjutan.";
        }
        
        $dueMessage[] = [
          "user_id" => $d->customer_id,
          "name" => 'Pending invoice reminder',
          "module" => null,
          "category" => 'Transaksi',
          "message" => $message,
          "channel" => 'WHOLESALES',
          "level" => $level
        ];

        if (isset($salesMessage)) {
          $dueMessage[] = [
            "user_id" => $d->sales_id,
            "name" => 'Pengingat: Jatuh tempo hari ini',
            "module" => null,
            "category" => 'Transaksi',
            "message" => $salesMessage,
            "channel" => 'WHOLESALES',
            "level" => $level
          ];
        }
      }
     
    }

    UserNotification::insert($dueMessage);
    return true;
  }

  public static function notifStoreQueue($user_id = null, $title = null, $type = null, $content = null, $key = null, $category = null, $channel = null, $level = null)
  {
    try {
      $insert = [
        'user_id' => $user_id,
        'name' => $title,
        'module' => '',
        'message' => $content,
        'is_read' => 0,
        'created_by' => 'SYSTEM',
        'modified_by' => 'SYSTEM',
        'category' => $category,
        'channel' => $channel,
        'level' => $level
      ];

      switch ($type) {
        case "customer-internal":
          $insert['module'] = "/customer/b2b";
          break;
        case "order":
          $insert['module'] = "/order/detail/$key";
          break;
        case "invoice":
          $insert['module'] = "/transactions/bill/detail/$key";
          break;
        case "order-internal":
          $insert['module'] = "/order/Wholesales/detail/$key";
          break;
        case "order-b2b":
          $insert['module'] = "/order/b2b/detail/$key";
          break;
        case "invoice-internal":
          $insert['module'] = "/bill/Wholesales/detail/$key";
          break;
        case "invoice-b2b-internal":
          $insert['module'] = "/bill/b2b/detail/$key";
          break;
        case "order-rsl-reseller":
          $insert['module'] = "/transaction/order/detail/$key";
          break;
        case "order-rsl-internal":
          $insert['module'] = "/order/reseller/detail/$key";

          $bu = BusinessUnit::where('name','Reseller')->first();
          $um = UserMatrix::where('business_unit_id',$bu->id)->get();
          foreach($um as $d){
            if ($d->user != null) {
              event(new GenericEvent($d->user->reference_id, [
                'date' => Carbon::parse()->format('Y-m-d H:i:s'),
                'title' => $title,
                'message' => $content,
                'url' => "/order/reseller/detail/$key",
                'is_read' => 0
                ], 'notification.new'));
            }
          }
          break;
        default:
          $insert['module'] = $key;
          break;
      }

      UserNotification::create($insert);
      return true;
    } catch (\Exception $e) {
      return false;
    }
  }

  //store to Notification table then fire notification event
  public function notifStore($user_id = null, $title = null, $type = null, $content = null, $key = null, $category = null, $channel = null, $level = null)
  {
    try {
      $sales_all = MasterParameter::where('group_key', 'B2B_NOTIF')->where('key', 'ALL_SALES_NOTIF')->first();
      if ($user_id == $sales_all->value) {
        $datas = [];

        //$saleses = Sales::get();
        //foreach ($saleses as $sales) {
        //$channel = $sales->user?->business_units()->pluck('name')->toArray() ?: [];
        //if (in_array('B2B',$channel)) {
        $insert = [
          'user_id' => $sales_all->value,
          'name' => $title,
          'module' => '',
          'message' => $content,
          'is_read' => 0,
          'created_by' => 'SYSTEM',
          'modified_by' => 'SYSTEM',
          'category' => $category,
          'channel' => $channel,
          'level' => $level
        ];
        
        switch ($type) {
          case "customer-internal":
            $insert['module'] = "/customer/b2b";
            break;
          case "order":
            $insert['module'] = "/order/detail/$key";
            break;
          case "order-b2b-internal":
            $insert['module'] = "/order/b2b/detail/$key";
            break;
          case "order-rsl-reseller":
            $insert['module'] = "/transaction/order/detail/$key";
            break;
          case "order-rsl-internal":
            $insert['module'] = "/order/reseller/detail/$key";
            $bu = BusinessUnit::where('name','Reseller')->first();
            $um = UserMatrix::where('business_unit_id',$bu->id)->get();
            foreach($um as $d){
              if ($d->user != null) {
                event(new GenericEvent($d->user->reference_id, [
                  'date' => Carbon::parse()->format('Y-m-d H:i:s'),
                  'title' => $title,
                  'message' => $content,
                  'url' => "/order/reseller/detail/$key",
                  'is_read' => 0
                  ], 'notification.new'));
              }
            }
            break;
          case "invoice":
            $insert['module'] = "/transactions/bill/detail/$key";
            break;
          case "order-internal":
            $insert['module'] = "/order/wholesales/detail/$key";
            break;
          case "invoice-internal":
            $insert['module'] = "/bill/wholesales/detail/$key";
            break;
          
          default:
            $insert['module'] = $key;
            break;
        }
        $datas[] = $insert;
        //}
        //}
        UserNotification::insert($datas);
        return $this->sendSuccess('Penyimpanan data berhasil!');
      } else {
        $insert = [
          'user_id' => $user_id,
          'name' => $title,
          'module' => '',
          'message' => $content,
          'is_read' => 0,
          'created_by' => 'SYSTEM',
          'modified_by' => 'SYSTEM',
          'category' => $category,
          'channel' => $channel,
          'level' => $level
        ];

        switch ($type) {
          case "order":
            $insert['module'] = "/order/detail/$key";
            break;
          case "order-b2b-internal":
            $insert['module'] = "/order/b2b/detail/$key";
            break;
          case "invoice":
            $insert['module'] = "/transactions/bill/detail/$key";
            break;
          case "order-internal":
            $insert['module'] = "/order/wholesales/detail/$key";
            break;
          case "invoice-internal":
            $insert['module'] = "/bill/wholesales/detail/$key";
            break;
          case "reseller-commission-withdrawal":
            $insert['module'] = "/transaction/commission/detail/$key";
            break;
          case "reseller-registration":
            $insert['module'] = "/reseller/list-applicants/detail/$key";
            break;
          case "order-rsl-reseller":
            $insert['module'] = "/transaction/order/detail/$key";
            break;
          case "order-rsl-internal":
            $insert['module'] = "/order/reseller/detail/$key";
            $bu = BusinessUnit::where('name','Reseller')->first();
            $um = UserMatrix::where('business_unit_id',$bu->id)->get();
            foreach($um as $d){
              if ($d->user != null) {
                event(new GenericEvent($d->user->reference_id, [
                  'date' => Carbon::parse()->format('Y-m-d H:i:s'),
                  'title' => $title,
                  'message' => $content,
                  'url' => "/order/reseller/detail/$key",
                  'is_read' => 0
                  ], 'notification.new'));
              }
            }
            break;
          case "reseller-commission-withdrawal-internal":
            $insert['module'] = "/comissions/withdraw/detail/$key";
            break;
          default:
            $insert['module'] = $key;
            break;
        }

        UserNotification::create($insert);
        return $this->sendSuccess('Penyimpanan data berhasil!');
      }
    } catch (\Exception $e) {
      return $this->sendException('Penyimpanan data gagal!', 422, '', $e);
    }
  }


  public function validateCartCategory($items)
  {
    $error = [
      'error' => true,
      'message' => MasterParameter::CrossError()
    ];

    $list_category = CartDetail::whereIn('cart_detail_id',collect($items)->pluck('cart_detail_id')->toArray())->pluck('article')->toArray();
    $query_category = Product::whereIn('article', $list_category)->pluck('lvl3_description')->all();
    $lowered_category = array_map('strtolower', $query_category);

    //counting cart items
    // $items_count = count($lowered_category);
    // $bags_count = count(array_filter($lowered_category, function ($e) {
    //   return $e == 'bags';
    // }));
    // $footwear_count = count(array_filter($lowered_category, function ($e) {
    //   return $e == 'footwear';
    // }));
    // $other_count = count(array_filter($lowered_category, function ($e) {
    //   return $e != 'footwear' && $e != 'bags';
    // }));

    //logic validation
    // if ($bags_count > 0 &&  $items_count !== $bags_count) {
    //   return $error;
    // }
    // if ($footwear_count > 0 && $items_count !== $footwear_count) {
    //   return $error;
    // }
    // if ($other_count > 0 && $items_count !== $other_count) {
    //   return $error;
    // } else {
      return [
        'error' => false,
        'message' => 'success'
      ];
    // }
  }

  public function detectDelimiter($csvFile)
  {
    $delimiters = [";" => 0, "," => 0, "\t" => 0, "|" => 0];

    $handle = fopen($csvFile, "r");
    $firstLine = fgets($handle);
    fclose($handle);
    foreach ($delimiters as $delimiter => &$count) {
      $count = count(str_getcsv($firstLine, $delimiter));
    }
    return array_search(max($delimiters), $delimiters);
  }

  public function countCsv($filename)
  {
    ini_set('auto_detect_line_endings', TRUE);
    $row_count = 0;
    if (($handle = fopen($filename, "r")) !== FALSE) {
      while (($row_data = fgetcsv($handle, 2000, $this->detectDelimiter($filename))) !== FALSE) {
        $row_count++;
      }
      fclose($handle);
      $row_count--;
      return $row_count;
    }
  }

  public function validateCsv($filename)
  {
    $requiredHeaders = array('Sku', 'Stok');

    $f = fopen($filename, 'r');
    $firstLine = fgets($f);
    fclose($f);

    $foundHeaders = str_getcsv(trim($firstLine), ',', '"');

    if ($foundHeaders !== $requiredHeaders) {
      return false;
    }
    return true;
  }


  public function sliceCsv($filename, $start, $desired_count)
  {
    $row = 0;
    $count = 0;
    $rows = array();
    if (($handle = fopen($filename, "r")) === FALSE) {
      return FALSE;
    }
    while (($row_data = fgetcsv($handle, 2000, $this->detectDelimiter($filename))) !== FALSE) {
      if ($row == 0) {
        $headings = array_map('strtolower', $row_data);
        $row++;
        continue;
      }

      if ($row++ < $start) {
        continue;
      }

      $rows[] = $row_data;
      $count++;
      if ($count == $desired_count) {
        return $rows;
      }
    }
    return $rows;
  }

  // store item
  public function doStore($model, $request, $override = [], $uuid = false, $validation = true)
  {
    try {
      $fields = $model::$validator;
      $rqBody = $request->all();
      if ($uuid) {
        $rqBody['uuid'] = (string) Uuid::generate(4);
      }

      if ($validation) {
        $validator = Validator::make($rqBody, $fields);
        if ($validator->fails()) {
          return $this->sendError('Penyimpanan data gagal!', 422, '', $validator->errors()->toArray());
        }
      }

      $insert = [];
      foreach ($fields as $key => $value) {
        if (isset($rqBody[$key])) {
          $insert[$key] = $rqBody[$key];
        }
      }
      foreach ($override as $key => $value) {
        $insert[$key] = $value;
      }

      $model::create($insert);
      return $this->sendSuccess('Penyimpanan data berhasil!');
    } catch (\Exception $e) {
      return $this->sendException('Penyimpanan data gagal!', 422, '', $e);
    }
  }

  public function doLogging($auth, $model, $operation, $tab, $content)
  {
    //handling ke log integration
  }

  // update item
  public function doUpdate($model, $obj, $newValues, $primaryKey, $computed = [])
  {
    $fields = Validation::update($model::$validator, $obj[$primaryKey]);
    $validator = Validator::make($newValues, $fields);
    if ($validator->fails()) {
      return ['status' => -2, 'msg' => $validator->errors()];
    }
    $update = $newValues;
    foreach ($computed as $key => $value) {
      $update[$key] = $value;
    }

    $obj->update($update);
    return $this->sendSuccess('update data berhasil!', $obj);
  }

  // destroy item
  public function doDestroy($obj)
  {
    $obj->delete();
    return $this->sendSuccess('hapus data berhasil!', $obj);  
  }
  // update multiple items
  public function doMultipleUpdate($model, $request, $primaryKey, $customFillable = false, $fillable = [])
  {
    $ids = $request->get('ids');

    $fields = Validation::noRequire($model::$validator);
    $validator = Validator::make($request->all(), $fields);
    if ($validator->fails()) {
      return $this->sendError('Penyimpanan data gagal!', 422, '', $validator->errors()->toArray());
    }

    $newValues = $request->get('request');
    if ($customFillable) {
      $update = [];
      foreach ($newValues as $key => $value) {
        if (in_array($key, $fillable)) {
          $update[$key] = $value;
        }
      }
    } else {
      $update = $newValues;
    }

    $model
      ::whereIn($primaryKey, $ids)
      ->update($update);

    return ['status' => 0, 'msg' => 'Data berhasil dihapus!'];
  }
  // delete multiple items
  public function doMultipleDelete($model, $request, $primaryKey)
  {
    $ids = $request->get('ids');

    $model::whereIn($primaryKey, $ids)->delete();

    return ['status' => 0, 'msg' => 'Data berhasil dihapus!'];
  }
  // add multiple items
  public function doMultipleAdd($model, $request)
  {
    $items = $request->get('items');
    $model::insert($items);

    return ['status' => 0, 'msg' => 'Data berhasil disimpan!'];
  }

  public static function checkStockBulk($article, $isNotBulk = false)
  {

      $article_map = array_map(function ($key) use ($article) {
          return [
              'source' => 'CAREOM',
              'destination' => 'STK',
              'article' => (string) $key,
          ];
      }, array_keys($article));

      $article_chunks = array_chunk($article, 80, true);

      foreach ($article_chunks as $key => $article_chunk) {

          $data = [
              "source" => "CAREOM",
              "destination" => "STK",
              "detail" => $article_map
          ];

          $getStokRepo = new GetStokRepo();
          $resp = $getStokRepo->getStock($data);
          $new_resp = $getStokRepo->getData($resp);
      }


      return isset($new_resp['data']) ? $new_resp['data'] : [];
  }
}