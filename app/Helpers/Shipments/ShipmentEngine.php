<?php

namespace App\Helpers\Shipments;

use Illuminate\Queue\SerializesModels;
use Throwable;
use VXM\Async\Invocation;
use App\Helpers\Shipments\ShipmentHelper;
class ShipmentEngine
{
    use ShipmentHelper;
    use Invocation;
    use SerializesModels;
    protected $from, $to, $w, $provider;

    public function __construct(Array $from, Array $to, String $w, String $provider)
    {
        $this->from         = $from;
        $this->to           = $to;
        $this->w            = $w;
        $this->provider     = $provider;
    }

    public function shipment_handler($type){
        switch (strtolower($type)){
            case "jne":
                return $this->checkJNE($this->from, $this->to, $this->w);
            case "sicepat":
                return $this->checkSC($this->from, $this->to, $this->w);
            default:
                return null;
        }
    }

    public function handle()
    {
        return $this->shipment_handler($this->provider);
    }

    
}
