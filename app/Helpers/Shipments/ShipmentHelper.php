<?php
namespace App\Helpers\Shipments ;
use App\Models\ResellerJNELocation;
use App\Models\ResellerShipments as Sh;
use App\Services\LoggerIntegration;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use App\Helpers\ApiClient;
use App\Models\ResellerOrderShipment;
use App\Models\ResellerTransporter;

trait ShipmentHelper
{

    
    public static function checkSC(Array $from, Array $to, String $w)
    {
         //$from = string city (ex: bogor)
        /*$to   = assoc array
        ex: [
            'province' => 'JaWa BaRat' (case insensitive),
            'city'     => 'Bogor',
            'subdistrict => 'Cilebut'
        ]
        $w = decimal (ex: '12.00' / '12')
        */
        try{
            // return self::originSC($from);
            $payload = [
                'origin' => self::originSC($from['city_name']??'')??self::originJNE($from['zip_code']??''),
                'destination' => self::destSC($to['province'], $to['city'], $to['subdistrict'])??self::destJNE($to['zip_code'], $to['city'], $to['subdistrict']),
                'weight' => $w,
              ];
              $rs = ApiClient::_get(getenv('SC_URL')."/customer/tariff", $payload, ['api-key' => getenv('SC_API_KEY')]);
              (new LoggerIntegration())->InsertLogger([
                'reference_no' => null,
                'module' => 'SiCepat',
                'name' => 'Get Tariff',
                'type' => 'Outbound',
                'status' => array_key_exists('error', $rs) ? 'failed' : 'succes',
                'description' => [
                    'payload' => $payload,
                    'response' => $rs
                ]
            ]);
            return array_key_exists('error', $rs) ? [] : ((array_key_exists('sicepat', $rs) ? self::SCResource($rs['sicepat']['results']??[]) : []));
        }
        catch(\Exception $e){
            return ['error' => $e->getMessage()];
        }

    }

    public static function originSC($str = ''){
        
        if(!Cache::has('sc_origin')){
            $rs = ApiClient::_get(getenv('SC_URL')."/customer/origin", [], ['api-key' => getenv('SC_API_KEY')]);
            $resp = array_key_exists('error', $rs) ? [] : ((array_key_exists('sicepat', $rs) ? $rs : []));
            Cache::add('sc_origin', $resp['sicepat']['results']??[], now()->addDays(1));
        }

        return collect(Cache::get('sc_origin'))->filter(function ($q) use ($str) {
            return str_contains(strtolower($q['origin_name']),strtolower($str));
        })->values()[0]['origin_code']??null;
    }

    public static function destSC($prov = '', $city = '', $sd = ''){
        if(!Cache::has('sc_dst')){
            $rs = ApiClient::_get(getenv('SC_URL')."/customer/destination", [], ['api-key' => getenv('SC_API_KEY')]);
            $resp = array_key_exists('error', $rs) ? [] : ((array_key_exists('sicepat', $rs) ? $rs : []));
            Cache::add('sc_dst', $resp['sicepat']['results']??[], now()->addDays(1));
        }

        return collect(Cache::get('sc_dst'))->where('province',strtoupper($prov))->where('city', ucwords($city))->firstWhere('subdistrict',ucwords($sd))['destination_code']??null;
    }

    public static function destJNE($zip = '', $city = '', $sd = ''){
        
        $m = ResellerJNELocation::where('zip_code','LIKE',"%$zip%");
        $callback = $m->first()->code??'';
        if($m->count() <= 1) return $m->first()->code??'';
        $m = $m->where('sub_district','LIKE',"%$sd%");
        return $m->first()->code??$callback;
        // $m = $m->where('city','LIKE',"%$city%");
        // if($m->count() <= 1) return $m->first()->code??'';

        // return $m->first()->code??'';
    }

    public static function originJNE($zip = ''){

        return substr(ResellerJNELocation::where('zip_code',$zip)->first()->code??'',0,3).'10000';
    }
    public static function checkJNE(Array $from, Array $to, String $w)
    {
        //$from = string (ex: CGK10000)
        /*$to   = assoc array
        ex: [
            'zip_code' => '11111',
            'city'     => 'Bogor',
            'subdistrict => 'Cilebut'
        ]
        $w = decimal (ex: '12.00' / '12')
        */
        try{
            $payload = [
                'username' => getenv('JNE_USERNAME'),
                'api_key' => getenv('JNE_API_KEY'),
                'from' => self::originJNE($from['zip_code']),
                'thru' => self::destJNE($to['zip_code'], $to['city'], $to['subdistrict']),
                'weight' => $w,
              ];
              $rs = ApiClient::_post(getenv('SHIPMENTS_JNE_CHECKPRICE'), $payload, headers:['Host' => 'apiv2.jne.co.id:10101']);

              (new LoggerIntegration())->InsertLogger([
                'reference_no' => null,
                'module' => 'JNE',
                'name' => 'Get Tariff',
                'type' => 'Outbound',
                'status' => array_key_exists('error', $rs) ? 'failed' : 'succes',
                'description' => [
                    'payload' => $payload,
                    'response' => $rs
                ]
            ]);
            return array_key_exists('error', $rs) ? [] : ((array_key_exists('price', $rs) ? self::JNEResource($rs['price']) : []));

            }
        catch(\Exception $e){
            return ['error' => $e->getMessage()];
        }

    }

    public static function shipmentsMapping($str){
        return Sh::where('service_code', $str)->first()->service_name??$str;
    }

    public static function estimationFormat($from = null, $to = null){
        $retval = "Perkiraan sampai ";
        if($from == null && $to == null){
            return $retval."Tentatif";
        }
        else{
            return $retval.$from . ' - ' . $to . ' hari';
        }
    }

    public static function JNEResource($data){
        return self::resourceSort(array_map(fn ($i) => [
        'transporter_id' => ResellerTransporter::where('name','JNE')->where('service',$i['service_display'])->first()->transporter_id??'',
        'key_shipments' => $i['service_display']??'-',
        'fullname'      => "JNE - ".self::shipmentsMapping($i['service_display'])??$i['service_display'],
        'price'         => (int)$i['price'],
        'estimation'    => self::estimationFormat($i['etd_from'], $i['etd_thru'])??""
    
    ], array_filter($data, function ($e) {
        $transporter = ResellerTransporter::where('name','JNE')->where('status','active')->pluck('service')->all();
        return in_array($e['service_display'], $transporter);
      })));


    }

    public static function SCResource($data){
        return self::resourceSort(array_map(fn ($i) => [
        'transporter_id' => ResellerTransporter::where('name','SiCepat')->where('service',$i['service'])->first()->transporter_id??'',
        'key_shipments' => $i['service'],
        'fullname'      => "SiCepat - ".self::shipmentsMapping($i['service'])??$i['service'],
        'price'         => (int)$i['tariff'],
        'estimation'    => "Perkiraan sampai ".$i['etd']??'tidak diketahui'
        // ],$data));
    ], array_filter($data, function ($e) {
        $transporter = ResellerTransporter::where('name','SiCepat')->where('status','active')->pluck('service')->all();
        return in_array($e['service'], $transporter);
      })));


    }

    public static function resourceSort($data){
        return collect(array_values($data))->sortBy('price')->values()->all();
    }

    public function checkStatusShipmentSC(ResellerOrderShipment $ros){

        try {
            $check = Http::post(env('SC_URL').'/customer/waybill-refno',[
                'api_key' => env('JNE_API_KEY'),
                'waybill' => $ros->awb_no
            ]);

            $res = $check->json();

            if ($check->failed() || (isset($res['status']['code']) && $res['status']['code'] >= 400) || (isset($res['statusCode']) && $res['statusCode'] >= 400)) {
                $status = 'failed';
            } else {
                $status = 'success';
            }

            (new LoggerIntegration())->InsertLogger([
                'reference_no' => $ros->awb_no,
                'module' => 'SiCepat',
                'name' => 'check shipment',
                'type' => 'Outbound',
                'status' => $status,
                'description' => [
                    'response' => $res
                ]
            ]);

            return $res??[];
        } catch (\Exception $e) {

            (new LoggerIntegration())->InsertLogger([
                'reference_no' => $ros->awb_no,
                'module' => 'SiCepat',
                'name' => 'check shipment',
                'type' => 'Outbound',
                'status' => 'failed',
                'description' => [
                    'error' => [
                        'code' => $e->getCode(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'message' => $e->getMessage(),
                    ]
                ]
            ]);

            return ['error' => $e->getMessage()];
        }
    }

    public function checkStatusShipmentJNE(ResellerOrderShipment $ros){

        try {
            $check = Http::asForm()->acceptJson()
                            ->post(env('SHIPMENTS_JNE_CHECKSTATUS').'/'.$ros->awb_no,[
                                'username' => env('JNE_USERNAME'),
                                'api_key' => env('JNE_API_KEY')
                            ]);

            $res = $check->json();

            if ($check->failed() || isset($res['error'])) {
                $status = 'failed';
            } else {
                $status = 'success';
            }

            (new LoggerIntegration())->InsertLogger([
                'reference_no' => $ros->awb_no,
                'module' => 'JNE',
                'name' => 'check shipment',
                'type' => 'Outbound',
                'status' => $status,
                'description' => [
                    'response' => $res
                ]
            ]);
            return $res??[];
        } catch (\Exception $e) {
            (new LoggerIntegration())->InsertLogger([
                'reference_no' => $ros->awb_no,
                'module' => 'JNE',
                'name' => 'check shipment',
                'type' => 'Outbound',
                'status' => 'failed',
                'description' => [
                    'error' => [
                        'code' => $e->getCode(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'message' => $e->getMessage(),
                    ]
                ]
            ]);

            return ['error' => $e->getMessage()];
        }
    }
}