<?php

namespace App\Helpers;


trait UserResellerHelper {
    
    public function validateLogin($user,$req)
    {
        $akses = [
            'customer' => [
                'rsl_customers'
            ],
            'reseller' => [
                'reseller'
            ]
        ];
        
        if (!in_array($user->reference_object,$akses[$req->type])) {
            return [
                'error' => true,
                'message' => 'Akun Anda tidak memiliki akses!.'
            ];
        }

        if ($user->is_active == 0) {
            return [
                'error' => true,
                'message' => 'Akun Anda belum terverifikasi. Silahkan cek email Anda untuk melakukan aktivasi akun.'
            ];
        }

        if ($user->rsl_customer->is_active == 0) {
            return [
                'error' => true,
                'message' => 'Maaf akun anda tidak bisa digunakan.'
            ];
        }

        return ['error'=>false,'message'=>null];
    }
}