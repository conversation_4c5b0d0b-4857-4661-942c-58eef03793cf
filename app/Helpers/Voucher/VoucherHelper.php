<?php

namespace App\Helpers\Voucher;

use App\Helpers\Promotion\PromotionHelper;
use App\Models\OrderReseller;
use App\Models\ResellerOrderPromotion;
use App\Models\Voucher;
use App\Services\CareOmni;

trait VoucherHelper 
{   
    use PromotionHelper;

    public $ada = 0;
    public function addPaymentVoucher(OrderReseller $order, Voucher $voucher)
    {
        $del_promo = ResellerOrderPromotion::where('reference_id',$order->id)
                        ->where('discount_id',$voucher->id)->first()?->delete();

        // order payment ?

        if ($voucher->master->is_void == 1) {
            $ods = $order->items()->pluck('id')->toArray();
            $ids = array_merge([$order->id],$ods);
            $op = ResellerOrderPromotion::whereIn('reference_id',$ids)->get();
            foreach ($op as $d) {
                if ($d->header != null) {
                    $order = $d->header;
        
                    $order->total_amount = $order->total_amount + $d->amount;
                    $order->pay_amount = $order->pay_amount + $d->amount;
                    $order->save();
                }

                if (strtolower($d->discount_type) == 'voucher') {
                    $voucher = $d->voucher;
        
                    if (strtolower($voucher->discount_type) == 'percentage') {
                        $voucher->remaining_amount = $voucher->amount;
                        $voucher->used_amount = 0;
                        $voucher->save();
                    }
        
                    if (strtolower($voucher->discount_type) == 'absolute') {
                        $voucher->remaining_amount = $voucher->remaining_amount + $d->amount;
                        $voucher->used_amount = $voucher->used_amount - $d->amount;
                        $voucher->save();
                    }
                }
                $d->delete();
            }
        }

        foreach ($order->items as $i => $item) {
            if (strtolower($voucher->type) == 'percentage') {
                $discount_amount = $this->getProrateDiscount($order->sub_total_amount,$item->line_amount,$order->pay_amount * ($voucher->amount/100));
                if ($i == $order->items()->count()) {
                    if (($discount_amount + $this->ada)/$order->sub_total_amount*100 != $voucher->amount) {
                        $miss_dp = $voucher->amount - (($discount_amount + $this->ada)/$order->sub_total_amount*100);
                        $adj_discount_amount = $order->sub_total_amount*$miss_dp;
                        $discount_amount = $discount_amount + $adj_discount_amount;
                    }
                }
            } else {
                $discount_amount = $this->getProrateDiscount($order->sub_total_amount,$item->line_amount,$voucher->amount);
                if ($i == $order->items()->count()) {
                    if ($voucher->amount - ($this->ada + $discount_amount) != 0) {
                        $discount_amount = $discount_amount + ($voucher->amount - ($this->ada + $discount_amount));
                    }
                }
            }

            if ($item->total_amount < $discount_amount) {
                $discount_amount = $item->total_amount;
                $total_amount = 0;
            } else {
                $total_amount = $item->total_amount - $discount_amount; 
            }

            ResellerOrderPromotion::updateOrCreate([
                'reference_name' => 'details',
                'reference_id' => $item->id,
                'discount_type' => 'voucher',
                'discount_id' => $voucher->id,
            ],[
                'amount' => $discount_amount,
            ]);
            
            $item->discount_amount = $item->discount_amount + $discount_amount;
            $item->total_amount = $total_amount;
            $item->save();
            $this->ada += $discount_amount;
        }

        return $order->with(['items','promotions']);
    }

    public function getEligibleVoucher($voucher_code = '')
    {
        return Voucher::active()->activeDate()->usable()->where('code',$voucher_code)->first();
    }
}