<?php

namespace App\Http\Controllers\Auth;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Auth\Events\PasswordReset;
use App\Http\Requests\ChangeForgotPasswordRequest;

class NewPasswordController extends Controller
{
    private $data;

    /**
     * Handle an incoming new password request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(ChangeForgotPasswordRequest $request): JsonResponse
    {
        $request->validated();

        // Here we will attempt to reset the user's password. If it is successful we
        // will update the password on an actual user model and persist it to the
        // database. Otherwise we will parse the error and return the response.

        try {

            $request->only('email', 'password', 'password_confirmation', 'token');
            $user = User::where('remember_token', $request->token)->first();
            
            if ($user) {
                if ($user->expired_token < now()) {
                    return $this->sendError('Your token has been expired.', 463);
                }
                $user->update([
                    'password' => Hash::make($request->password),
                    'remember_token' => null,
                    'expired_token' => null,
                    'is_change_password' => 1,
                ]);
            }

            event(new PasswordReset($user));
            return $this->sendSuccess('Your password has been change successfully.');

        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }

        // If the password was successfully reset, we will redirect the user back to
        // the application's home authenticated view. If there is an error we can
        // redirect them back to where they came from with their error message.
        // if ($status == Password::PASSWORD_RESET) {

        // } else {
        //     switch ($status) {
        //         case 'passwords.user':
        //             return $this->sendError("The user not found.");
        //             break;
        //         case 'passwords.token':
        //             return $this->sendError("Link untuk reset password sudah kadaluarsa silahkan lakukan reset password kembali");
        //             break;
        //         default:
        //             return $this->sendError("There are some issues when change new password.");
        //             break;
        //     }
        // }
    }
}