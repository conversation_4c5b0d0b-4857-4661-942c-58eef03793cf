<?php

namespace App\Http\Controllers\Auth;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Jobs\QueuedPasswordResetJob;
use Illuminate\Support\Facades\Mail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\ForgotPasswordRequest;
use App\Models\Customer;
use App\Repositories\GetSocialsRepo;

class PasswordResetLinkController extends Controller
{

    /**
     * Handle an incoming password reset link request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(ForgotPasswordRequest $request): JsonResponse
    {
        try {
            // We will send the password reset link to this user. Once we have attempted
            // to send the link, we will examine the response then see the message we
            // need to show to the user. Finally, we'll send out a proper response.
            $status = Password::sendResetLink(
                $request->only('email')
            );

            $user = User::where('email', $request->email)->first();

            if (!$user) {
                return $this->sendError('Email yang anda masukkan tidak terdaftar.', 400);
            }

            $token = Str::random(64);
            $user->remember_token = $token;
            $user->expired_token = now()->addHours(24);
            $user->save();

            $socmed     = new GetSocialsRepo();
            $instagram  = $socmed->getSocialMediaParameters('INSTAGRAM');
            $facebook   = $socmed->getSocialMediaParameters('FACEBOOK');
            $twitter    = $socmed->getSocialMediaParameters('TWITTER');
            $tiktok     = $socmed->getSocialMediaParameters('TIKTOK');
            $youtube    = $socmed->getSocialMediaParameters('YOUTUBE');
            $support    = $socmed->getSocialMediaParameters('SUPPORT');

            $param['data'] = [
                'facebook'  => $facebook,
                'twitter'   => $twitter,
                'instagram' => $instagram,
                'support'   => $support,
                'tiktok'    => $tiktok,
                'youtube'   => $youtube,
            ];

            $type = $request->type;
            if ($user->reference_object === 'customer') {
                $channel = $request->channel;
                $customer = Customer::where('customer_id', $user->reference_id)->first();
                $customer_channel =  $customer->distribution_channel;

                if ($type === 'external') {
                    if ($channel === $customer_channel) {
                        QueuedPasswordResetJob::dispatch($user, $customer_channel, $token, $param['data'])->onConnection('redis')->onQueue('rqueue');
                    }
                }
            } else {
                if ($type === 'internal') {
                    QueuedPasswordResetJob::dispatch($user, null, $token, $param['data'])->onConnection('redis')->onQueue('rqueue');
                }
            }

            Log::info('Forgot Password Log', [
                'email' => $request->input('email'),
                'status' => $status == Password::RESET_LINK_SENT ? 'success' : 'failed'
            ]);

            // return $status == Password::RESET_LINK_SENT
            //             ? $this->sendSuccess('Please check your email for the URL to reset your password.')
            //             : $this->sendError("There are some issues requesting the password reset link.");
        } catch (\Exception $e) {
            Log::info('Forgot Password Log Exception', [
                'email' => $request->input('email'),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ]);
            // return $this->sendError($e->getMessage());
        }
        return $this->sendSuccess('email reset password will be sent if your email is registered');
    }

    public function verify(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        $isToken = User::where('remember_token', $request->token)->first();

        if ($isToken) {
            if ($isToken->expired_token < now()) {
                return $this->sendError('Your token has been expired.', 463);
            }
            return $this->sendSuccess('token is available', ['email' => $isToken->email]);
        } else {
            return $this->sendError('invalid token', 404);
        }

    }
}