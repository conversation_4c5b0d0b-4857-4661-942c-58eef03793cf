<?php
namespace App\Http\Controllers;

use App\Http\Requests\CreateBannerRequest;
use App\Http\Requests\UpdateBannerRequest;
use App\Http\Resources\BannerResource;
use App\Models\BannerModel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use App\Helpers\FileHelper;

class BannerController extends Controller{
    use FileHelper;

    public function getBanner(Request $request){

        $query = BannerModel::select('id','title', 'thumbnail', 'url', 'sequence_no', 'start_period', 'end_period');
       
        $result = $this->filter($query, $request);

        return $this->sendSuccess(null, $result, null, false, BannerResource::class);

    }

    private function filter($query, $request){

        $this->periodQuery($request, $start, $to);
        $page = $request->input('page');
        $perPage = 10;

        if ($order_by = $request->query('title_sort')) {
            switch($order_by){
                case 'AZ':
                    $query->orderBy('title', 'ASC')->get();
                    break;
                case 'ZA':
                    $query->orderBy('title', 'DESC')->get();
                    break;
            }
        }

        if ($order_by = $request->query('start_period')) {
            switch($order_by){

                case 'oldest':
                    $query->orderby('start_period', 'asc')->get();
                    break;
                case 'newest':
                    $query->orderby('start_period', 'desc')->get();
                    break;
            }
        }

        if ($order_by = $request->query('end_period')) {
            switch($order_by){
                case 'oldest':
                    $query->orderby('end_period', 'asc')->get();
                    break;
                case 'newest':
                    $query->orderby('end_period', 'desc')->get();
                    break;
            }
        }
        
        if($request->has('date_from') || $request->has('date_to')){
            $query = $query->whereBetween('start_period', [$start,$to]);
        }
        $query = $query->orderBy('sequence_no')->orderBy('start_period')->paginate($perPage, ['*'], 'page', $page);;
        
        //return BannerResource::collection ($query);
        return $query;
    }

    public function periodQuery($request, &$start, &$to)
    {
        if ($request->has('date_from') && $request->has('date_to')) {
            $start = $request->query('date_from');
            $to = $request->query('date_to');
        } elseif ($request->has('date_from') && !$request->has('date_to')) {
            $start = $request->query('date_from');
            $to = now()->format('Y-m-d');
        } elseif (!$request->has('date_from') && $request->has('date_to')) {
            $start = now()->format('Y-m-d');
            $to = $request->query('date_to');
        } else {
            $start = now()->subDays(7)->format('Y-m-d');
            $to = now()->addDay()->format('Y-m-d');
        }
    }

    public function deactivateBanner(Request $request, $id){

        $query = BannerModel::where('id', $id);
        $query->update(['sequence_no' => null]);
        return response()->json([
            'error'   => false,
            'status'  => '200 OK',
            'message' => 'Deactivated Succesfully'
        ],
        200,
    );

    }

    public function postBanner(CreateBannerRequest $request){

        $request->validated();

        //check existing banner
        $existingBanner = BannerModel::where('start_period', $request->start_period)->orWhere('sequence_no',$request->sequence_no)->first();

        if ($existingBanner) {
            return response()->json([
                'error'   => true,
                'status'  => '400 Bad Request',
                'message' => ['message' => 'duplikat data, silakan periksa kembali data yang anda masukan']
            ], 400);
        }

        //upload image
        $img = $request->thumbnail;
        $image_type = ['gif','jpeg','png','jpg','swf','psd','bmp','tiff','tiff','jpc','jp2','jpx','jb2','swc','iff','wbmp','xbm','ico','webp'];
        $extension = pathinfo($img)['extension']??'';
        $cek_image = in_array($extension,$image_type);
        // $name = uniqid();
        // $sequence_no = $request->sequence_no;
        if ($cek_image == false) {
            $file_path = null;
        } else {
        // $content = file_get_contents($img);
        // $filename = $name.'-'.(string)$sequence_no.'.'.$image_type[$cek_image-1];
        $file_path = $this->fileTransfer($request->thumbnail,'banner');
        if ($file_path['error'] == true) {
            return $this->sendError($file_path['message']);
        }
        // Storage::disk('s3')->put(substr($file_path,1), $content);
        }
        $banner_check = BannerModel::where('sequence_no', $request->sequence_no)->first();
        
        $banner = $banner_check && in_array($request->sequence_no, [1,2,3,4,5]) ? $banner_check : new BannerModel;
        $banner->title = $request->title;
        $banner->thumbnail = '/'.$file_path['filepath'];
        $banner->url = $request->url;
        $banner->sequence_no = in_array($request->sequence_no, [1,2,3,4,5]) ? $request->sequence_no : null;
        $banner->start_period = \Carbon\Carbon::parse($request->start_period)->format('Y-m-d H:i:s');
        $banner->end_period =  \Carbon\Carbon::parse($request->end_period)->format('Y-m-d H:i:s');
        $banner->save();

        return response()->json([
            'error'   => false,
            'status'  => '200 OK',
            'message' => 'Banner Inserted Succesfully'
        ],
        200,
    );

    }

    public function updateBanner(UpdateBannerRequest $request, $id){

        $request->validated();

        $existingBanner = BannerModel::where('start_period', $request->start_period)->orWhere('sequence_no',$request->sequence_no)->first();

        if ($existingBanner) {
            return response()->json([
                'error'   => true,
                'status'  => '400 Bad Request',
                'message' => ['message' => 'duplikat data, silakan periksa kembali data yang anda masukan']
            ], 400);
        }

        //check existing banner
        $banner = BannerModel::where('id', $id)->first();
    
        if (!$banner) {
            return response()->json([
                'error'   => true,
                'status'  => '400 Bad Request',
                'message' => ['message' => 'banner tidak ditemukan']
            ], 400);
        }
        $banner_check = BannerModel::where('sequence_no', $request->sequence_no ?: $banner->getOriginal('sequence_no'))->first();
        $banner = $banner_check && in_array($request->sequence_no ?: $banner->getOriginal('sequence_no'), [1,2,3,4,5]) ? $banner_check : $banner;

        $file_path = null;
        //upload image
        
        if($request->has('thumbnail')){
             $img = $request->thumbnail;
        $image_type = ['gif','jpeg','png','jpg','swf','psd','bmp','tiff','tiff','jpc','jp2','jpx','jb2','swc','iff','wbmp','xbm','ico','webp'];
        $extension = pathinfo($img)['extension']??'';
        $cek_image = in_array($extension,$image_type);
        // $name = uniqid();
        // $sequence_no = $request->sequence_no;
        if ($cek_image == false) {
            $file_path = null;
        } else {
        // $content = file_get_contents($img);
        // $filename = $name.'-'.(string)$sequence_no.'.'.$image_type[$cek_image-1];
        $file_path = $this->fileTransfer($request->thumbnail,'banner');
        if ($file_path['error'] == true) {
            return $this->sendError($file_path['message']);
        }
        // Storage::disk('s3')->put(substr($file_path,1), $content);
        }
        }

        //dd($request->title);

        $banner->title = $request->title ?: $banner->getOriginal('title');
        $banner->thumbnail = '/'.$file_path['filepath'] ?: $banner->getOriginal('thumbnail');
        $banner->url = $request->url ?: $banner->getOriginal('url');
        $banner->sequence_no = in_array($request->sequence_no, [1,2,3,4,5]) ? $request->sequence_no : null;
        $banner->start_period = $request->start_period ?: $banner->getOriginal('start_period');
        $banner->end_period = $request->end_period ?: $banner->getOriginal('end_period');
        $banner->save();

        return response()->json([
            'error'   => false,
            'status'  => '200 OK',
            'message' => 'Banner Inserted Succesfully'
        ],
        200,
    );

    }


    public function deleteBannerById(Request $request){

        $id = $request->id;
        $bannerData = BannerModel::where('id', $id)->first();

        if(!$bannerData){

            return $this->sendError("Banner Not Found", 404);
        }

        $bannerData->delete();

        return response()->json([
            'error'   => false,
            'status'  => '200 OK',
            'message' => 'Deleted Succesfully'
        ],
        200,
    );
    }


}