<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\Customer;
use App\Models\BulkDraft;
use App\Models\CartDetail;
use App\Models\ProductSku;
use App\Helpers\FileHelper;
use App\Helpers\RestHelper;
use App\Imports\CartImport;
use App\Exports\StockExport;
use Illuminate\Http\Request;
use App\Models\CustomerShipment;
use App\Interfaces\UserInterface;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Requests\BulkCartRequest;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\CreditLimitResource;
use App\Http\Resources\Cart\BulkDraftResouce;
use Illuminate\Validation\ValidationException;

class BulkCartController extends Controller
{

    protected $userRepo;
    use FileHelper;
    public function __construct(UserInterface $userRepo)
    {
        $this->userRepo = $userRepo;
    }
    public function bulkUnduh()
    {
        try {
            return Excel::download(new StockExport(true), 'bulk_upload_template.xlsx', \Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            $errors = $e->getMessage();
            return $this->sendError($errors, 500);
        }
    }
    public function bulkUpload(BulkCartRequest $request)
    {
        $request->validated();
        $custId = $request->customer_id = auth()->user()->customer->customer_id;

        try {
            $s3 = \Storage::disk('s3');
            $client = $s3->getDriver()->getAdapter()->getClient();
            $expiry = "+10 minutes";

            if ($request->isChange == true) {
                $files = \Storage::disk('s3-public')->files('staging/bulk-upload/' . $custId);
                if ($files)
                    \Storage::disk('s3-public')->delete($files);
            }

            $file = $request->file;

            $cmd = $client->getCommand('PutObject', [
                'Bucket' => env('AWS_BUCKET_STAGING', 'bucket-public-careorder'),
                'Key' => 'staging/' . 'bulk-upload/' . $custId . '/' . $file,
            ]);

            $request = $client->createPresignedRequest($cmd, $expiry);
            
            $presignedUrl = (string) $request->getUri();

            return $this->sendSuccess('success', [
                "filepath" => $cmd["Key"],
                "s3_url" => $presignedUrl,
            ]);
        } catch (ValidationException $e) {
            return $this->sendError(collect($e->errors())->first(), 400);
        } catch (\Exception $e) {

            DB::rollback();
            return response()->json([
                'message' => 'Terjadi Kesalahan saat melakukan import',
            ], 500);
        }
    }

    public function processOrder(Request $request)
    {
        try {
            $custId = $request->customer_id = auth()->user()->customer->customer_id;

            $file = \Storage::disk('s3-public')->files('staging/bulk-upload/' . $custId);
            $latestFile = collect($file)->sortByDesc(function ($file) {
                return \Storage::disk('s3-public')->lastModified($file);
            })->first();

            $tf_file = $this->fileTransfer($latestFile, 'bulk-upload/' . $custId, true);
            if ($tf_file['error'] == true) {
                return $this->sendError($tf_file['message']);
            }

            $file_path = $tf_file['filepath'];

            $custId = $request->customer_id = auth()->user()->customer->customer_id;
            $cartImport = new CartImport($custId, null, true);
            Excel::import($cartImport, $file_path, 's3');
            $cartData = $cartImport->cartData;
            $cart_header_id = $cartImport->cart_header_id;

            $data = [
                'cart_id' => $cart_header_id,
                'total_data' => count($cartData)
            ];

            if ($file_path != '' && !empty($file_path)) {
                $files = \Storage::disk('s3')->files('bulk-upload/' . $custId);
                if ($files) {
                    \Storage::disk('s3')->delete($files);
                }
            }

            $bulk = BulkDraft::where('cart_id', $cart_header_id)
                ->whereColumn('qty', '>', 'stock');
            $bulk->update(['flag' => 1]);
            $overStock = $bulk->exists();

            if ($overStock) {
                $message = 'Mohon maaf, terdapat perubahan dalam pesanan yang disesuaikan dengan ketersediaan Stock.';
            } else {
                $message = count($cartData) > 0 ? 'Terdapat ' . $cartImport->totalArticle - count($cartData) . ' SKU yang tidak memiliki stock' : 'Maaf semua stock pesanan anda kosong.';
            }

            return $this->sendSuccess($message, $data);
        } catch (ValidationException $e) {
            return $this->sendError(collect($e->errors())->first()[0], 200);
        } catch (\Exception $e) {

            DB::rollback();
            return response()->json([
                'message' => 'Terjadi kesalahan saat membuat pesanan: ' . $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ], 500);
        }

    }
    public function bulkStore(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'data_update' => 'required|array',
            'data_update.*.bulk_draft_id' => 'required|uuid',
            'data_update.*.qty' => 'required|integer|min:0',
        ]);
        
        $validator->after(function ($validator) use ($request) {
            $qtyValues = collect($request->input('data_update'))->pluck('qty');
        
            if ($qtyValues->every(fn($qty) => $qty == 0)) {
                $validator->errors()->add('qty_total', 'Jumlah order tidak boleh kosong!');
            }
        });
        
        if ($validator->fails()) {
            return $this->sendError($validator->errors()->first(), 422);
        }

        try {

            $data = $request->data_update;
            $cartDetail = DB::update('
                UPDATE bulk_draft
                SET qty = CASE 
                    ' . implode(' ', array_map(function ($item) {
                return "WHEN bulk_draft_id = '{$item['bulk_draft_id']}' THEN {$item['qty']}";
            }, $data)) . '
                    END
                    WHERE bulk_draft_id IN (' . implode(',', array_map(function ($id) {
                return "'$id'";  // Wrap UUIDs in quotes
            }, array_column($data, 'bulk_draft_id'))) . ')
            ');

            $dataUpdate = [];
            if ($cartDetail) {
                $dataUpdate = BulkDraft::whereIn('bulk_draft_id', array_column($data, 'bulk_draft_id'))->article()->get();
                $dataUpdate = BulkDraftResouce::collection($dataUpdate);
            }

            $response = [
                'data_updated' => $dataUpdate,
            ];

            return $this->sendSuccess("Data Berhasil diubah", $response);
        } catch (\Exception $e) {
            $errors = $e->getMessage();
            return $this->sendError($errors, 400);
        }
    }

    public function bulkDelete(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'bulk_draft_id' => 'array',
            'deleteAll' => 'boolean',
        ]);

        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {
            $custId = $request->customer_id = auth()->user()->customer->customer_id;
            $cart = Cart::where('customer_id', $custId)->first();

            $bulkData = new BulkDraft();

            if ($cart) {
                $bulkData = $request->deleteAll ? $bulkData->where('cart_id', $cart->id) : $bulkData->whereIn('bulk_draft_id', $request->bulk_draft_id);
                $data = $bulkData->pluck('bulk_draft_id');
                $bulkData->delete();
            }

            return $this->sendSuccess("Data Berhasil Dihapus", ['total' => count($data), 'deletedData' => $data]);
        } catch (\Exception $e) {
            $errors = $e->getMessage();
            return $this->sendError($errors, 400);
        }
    }

    public function getBulkList(Request $request)
    {
        $custId = $request->customer_id = auth()->user()->customer->customer_id;
        $cart = Cart::where('customer_id', $custId)->first();
        try {

            $paginate = $request->filled('paginate') ? $request->paginate : 5;
            if ($cart) {
                $dataLimit = $this->userRepo->getCreditLimit($custId)->getData(true)['data'];

                // $bulkData = BulkDraft::where('cart_id', $cart->id)->article($request->search, $request->color, $request->size, $request->sku)->get();

                $tokoId = CustomerShipment::where('customer_id', auth()->user()->customer->customer_id)->first();
                $bulkData = BulkDraft::where('cart_id', $cart->id)
                ->leftJoin('customer_stock', function ($join) use ($custId, $tokoId) {
                        $join->on('bulk_draft.article', '=', 'customer_stock.article_id')
                        ->where('customer_stock.customer_id', $custId)
                        ->where('customer_stock.customer_shipment_id', $tokoId->customer_shipment_id);
                    })
                    ->article($request->search, $request->color, $request->size, $request->sku)
                    ->select('bulk_draft.*', 'customer_stock.qty as stock_toko', 'master_color.value')
                    ->get();


                $bulkPaginated = $bulkData->paginate($paginate);
                $bulkMapped = $bulkPaginated->through(function ($item) use ($tokoId) {
                    return new BulkDraftResouce($item, $tokoId->name);
                });

                $bulks = $bulkMapped;
                $uniqueSkuCount = $bulkData->pluck('article_detail.sku_code_c')  // Extract sku_code_c from each article_detail
                    ->unique()->count();

                $qty = $bulkData->map(function ($item) {
                    if ($item->qty > $item->stock) {
                        $item->qty = $item->stock;
                    }
                    return $item;
                });

                $totals = $qty->reduce(function ($carry, $qty) {
                    $price = $qty->article_detail->article_price->first()->amount ?? 0.00;
                    $carry['totalPrice'] += $price * $qty->qty;
                    return $carry;
                }, ['totalPrice' => 0]);
                $data = [
                    'data_limit' => [
                        'sub_total_sku' => $uniqueSkuCount,
                        'total_barang' => $qty->sum('qty'),
                        'limit_terpakai' => $dataLimit['credit_limit_used'],
                        'limit_kredit' => $dataLimit['credit_limit'],
                        'limit_tersisa' => $dataLimit['credit_limit_remaining'],
                        'percentage' => $dataLimit['credit_limit_used_percentage'],
                        'over_limit' => $totals['totalPrice'] > $dataLimit['credit_limit_remaining'] ? true : false,
                        'total_harga' => $totals['totalPrice']
                    ],
                    'data_bulk' => $bulks,
                ];

                return $this->sendSuccess("Data SKU successfully", $data);
            }
            return $this->sendSuccess("Data SKU Tidak Ditemukan", []);

        } catch (\Exception $e) {
            return $this->sendError($e->getMessage(), 400, [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);
        }
    }


    public function nextOrder(Request $request)
    {
        $custId = $request->customer_id = auth()->user()->customer->customer_id;
        $cart = Cart::where('customer_id', $custId)->first();

        try {
            if ($cart) {
                $bulks = BulkDraft::where('cart_id', $cart->id)->where('qty', '>', 0);
                if($bulks->count()<= 0){
                    return $this->sendError('Jumlah order tidak boleh kosong!', 400);
                }
                $bulkDraft = $bulks->pluck('qty', 'article')->toArray();
                RestHelper::syncStock(array_keys($bulkDraft), $custId);
                $stockData = ProductSku::whereIn('sku_id', array_keys($bulkDraft))->get(['stock', 'sku_id'])->toArray();
                $mappedData = collect($bulkDraft)->flatMap(function ($qty, $article) use ($stockData, $cart) {
                    $stockInfo = collect($stockData)->firstWhere('sku_id', $article);

                    if ($stockInfo['stock'] != 0 && $qty > $stockInfo['stock']) {
                        $entryWithStock = [
                            'cart_id' => $cart->id,
                            'article' => $article,
                            'qty' => $stockInfo['stock'],
                            'is_custom' => 0,
                            'attachment_group_id' => 0,
                            'selected' => 1,
                            'modified_date' => now()->format('Y-m-d H:i:s'),
                            'is_available' => 1
                        ];

                        return [$entryWithStock];
                    }

                    return [
                        [
                            'cart_id' => $cart->id,
                            'article' => $article,
                            'qty' => $qty,
                            'attachment_group_id' => 0,
                            'is_custom' => 0,
                            'selected' => 1,
                            'modified_date' => now()->format('Y-m-d H:i:s'),
                            'is_available' => 1
                        ]
                    ];
                })->values();


                DB::beginTransaction();

                CartDetail::upsert($mappedData->toArray(), ['cart_id', 'article', 'is_available'], ['qty', 'modified_date', 'is_available']);
                BulkDraft::where('cart_id', $cart->id)->delete();
                DB::commit();
                return $this->sendSuccessCreated("Cart created successfully.", []);

            }
        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError($e->getMessage() . ' on line ' . $e->getLine(), 400);
        }
    }

}