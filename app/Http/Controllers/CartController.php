<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\Order;
use App\Models\Remark;
use App\Models\Article;
use App\Models\Product;
use App\Models\CartItem;
use App\Models\Customer;
use App\Models\BulkDraft;
use App\Helpers\ApiClient;
use App\Jobs\SyncStockJob;
use App\Models\CartCustom;
use App\Models\CartDetail;
use App\Models\ProductSku;


use App\Helpers\FileHelper;
use App\Helpers\RestHelper;
use App\Imports\CartImport;
use App\Models\OrderCustom;
use App\Traits\ResponseAPI;
use Illuminate\Support\Str;
use App\Events\GenericEvent;
use Illuminate\Http\Request;
use App\Models\Article_price;
use App\Models\CartAttachment;
use App\Models\MatrixDiscount;
use App\Models\CartResponseList;
use App\Models\CustomerShipment;
use App\Repositories\GetStokRepo;
use App\Repositories\GetLimitRepo;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\OrderCustomAttachment;
use App\Http\Requests\BulkCartRequest;
use Illuminate\Support\Facades\Storage;
use App\Http\Resources\SkuImportResource;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\Cart\CartListResource;
use Illuminate\Validation\ValidationException;
use App\Http\Resources\CreditLimitCartResource;

class CartController extends Controller
{
    private $limit = 12;
    protected $getStockURI;
    use FileHelper;
    public function index(Request $request)
    {
        $custId = auth()->user()->customer->customer_id;
        $validated = Cart::validate($custId);
        if ($validated === true) {

            DB::table('cart_detail')
                ->join('article_skus', 'cart_detail.article', '=', 'article_skus.sku_id')
                ->update([
                    'cart_detail.is_available' => DB::raw("CASE 
                    WHEN article_skus.stock > 0 THEN 1 
                    ELSE 0 
                END")
                ]);
            // $stock = ProductSku::where('stock', 0)->pluck('sku_id')->toArray();
            // $stockAvailable = ProductSku::where('stock', '>', 0)->pluck('sku_id')->toArray();
            // CartDetail::whereIn('article', $stock)->update(["is_available" => 0]);
            // CartDetail::whereIn('article', $stockAvailable)->update(["is_available" => 1]);

            $cart = Cart::fetch($custId, $request->is_available)->first();
            $cartDetail = $cart->cart_detail;

            $dataLimit = new CreditLimitCartResource(Customer::find($custId));
            $dataLimit = $dataLimit->toArray(request());

            $isCustom = $request->filled('is_custom') ? $request->is_custom : 0;
            $totals = $cartDetail->where('is_available', 1)->where('selected', 1)->where('is_custom', $isCustom)->reduce(function ($carry, $cartDetail) {
                $price = $cartDetail->article_detail && $cartDetail->article_detail->article_price ? $cartDetail->article_detail->article_price->first()->amount : 0.00;
                $carry['totalPrice'] += $price * $cartDetail->qty;
                return $carry;
            }, ['totalPrice' => 0]);

            $uniqueSkuCount = $cartDetail->where('is_available', 1)->where('selected', 1)->where('is_custom', $isCustom)->pluck('article_detail.sku_code_c')  // Extract sku_code_c from each article_detail
                ->unique()->count();

            $cartCount = $cartDetail->where('is_available', 1)
                ->map(function ($item) {
                    return [
                        'sku_code_c' => $item->article_detail ? $item->article_detail->sku_code_c : null,
                        'is_custom' => $item->is_custom,
                        'attachment_group_id' => $item->attachment_group_id,
                    ];
                })
                ->unique(function ($item) {
                    return $item['sku_code_c'] . '|' . $item['is_custom'] . '|' . $item['attachment_group_id'];
                })
                ->values()
                ->count();

            $selectedBarang = $cartDetail->where('is_available', 1)->where('is_custom', $isCustom)->where('selected', 1)->sum('qty');

            $dataLimit = array_merge($dataLimit, [
                'total_harga' => $totals['totalPrice'],
                'cart_count' => $cartCount,
                'total_selected_barang' => $selectedBarang,
                'total_barang' => $cartDetail->where('is_available', 1)->where('is_custom', $isCustom)->sum('qty'),
                'sub_total_sku' => $uniqueSkuCount,
                'over_limit' => $totals['totalPrice'] > $dataLimit['sisa'] ? true : false,
            ]);

            if ($request->is_custom == 1) {
                $attachments = $cartDetail->where('selected', 1)->where('is_custom', 1);
                $total_custom = CartAttachment::whereIn('attachment_group_id', $attachments->pluck('attachment_group_id')->toArray());

                $groupedQty = $attachments->groupBy('attachment_group_id')->map(function ($items) {
                    return $items->sum('qty');
                });

                // Step 2: Fetch all estimate_price grouped by attachment_group_id in one query
                $estimatePrices = CartAttachment::whereIn('attachment_group_id', $groupedQty->keys())
                    ->selectRaw('attachment_group_id, SUM(estimate_price) as total_price')
                    ->groupBy('attachment_group_id')
                    ->pluck('total_price', 'attachment_group_id');

                // Step 3: Multiply qty by estimate_price per group
                $harga_custom = $groupedQty->reduce(function ($carry, $qty, $groupId) use ($estimatePrices) {
                    $price = $estimatePrices[$groupId] ?? 0;
                    return $carry + ($qty * $price);
                }, 0);


                // $harga_custom = $total_custom->sum('estimate_price') * ;
                $dataLimit = array_merge($dataLimit, [
                    'total_custom' => $total_custom->count(),
                    'sub_total' => (int) $totals['totalPrice'],
                    'harga_custom' => $harga_custom,
                    'total_harga' => (int) $totals['totalPrice'] + $harga_custom
                ]);
            }

            return $this->sendSuccess("Data Cart successfully", new CartListResource($cart, $dataLimit));
        }
    }

    public function updateQty(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'cart_detail_id' => 'required',
            'qty' => 'required'
        ]);


        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {
            $cartDetail = CartDetail::where('cart_detail_id', $request->cart_detail_id);
            if ($cartDetail->exists()) {
                $cartDetail->update(['qty' => $request->qty]);
            } else {
                return $this->sendError("Data Tidak ditemukan", 404);
            }

            return $this->sendSuccessCreated("Cart created/updated successfully.", []);

        } catch (\Exception $e) {
            DB::rollback();
            Log::info($e->getMessage());
            return $this->sendError("can't update cart, " . $e->getMessage(), 500);
        }
    }
    public function addRemark(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'attachment_group_id' => 'required',
            'remark' => 'required'
        ]);


        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {
            $remark = Remark::where('attachment_group_id', $request->attachment_group_id);
            if ($remark->exists()) {
                $remark->update(["remark" => $request->remark]);
            } else {
                Remark::create([
                    "attachment_group_id" => $request->attachment_group_id,
                    "remark" => $request->remark,
                ]);
            }

            return $this->sendSuccessCreated("Remark created/updated successfully.", []);

        } catch (\Exception $e) {
            DB::rollback();
            Log::info($e->getMessage());
            return $this->sendError("can't add attachment, " . $e->getMessage(), 500);
        }
    }
    public function checkBulkCustom(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'attachments_group_id' => 'required',
            'items' => 'required|array',
            'items.*.article' => 'required',
            'items.*.selected' => 'required|boolean',
        ]);


        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {
            $custId = auth()->user()->customer->customer_id;

            DB::beginTransaction();
            $cart = Cart::where('customer_id', $custId)->first();

            $cart_header_id = $cart->id;
            $articles = array_column($request->items, 'article');

            CartDetail::where('cart_id', $cart_header_id)->whereIn('article', $articles)
                ->where('attachment_group_id', $request->attachments_group_id)->update(['selected' => $request->items[0]['selected']]);

            DB::commit();
            return $this->sendSuccessCreated("Cart created/updated successfully.", []);

        } catch (\Exception $e) {
            DB::rollback();
            Log::info($e->getMessage());
            return $this->sendError("Sorry system can't create/update checkout, " . $e->getMessage(), 500);
        }
    }

    public function destroyBulkCustom(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'attachments_group_id' => 'required',
            'items' => 'required|array',
            'items.*.article' => 'required',
        ]);


        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {

            $custId = auth()->user()->customer->customer_id;

            $cart = Cart::where('customer_id', $custId)->first();

            $cart_header_id = $cart->id;
            $articles = array_column($request->items, 'article');

            $cartDetail = CartDetail::where('cart_id', $cart_header_id)->where('attachment_group_id', $request->attachments_group_id)->whereIn('article', $articles);
            $attachments = $cartDetail->pluck('attachment_group_id')->toArray();

            $cartAttachments = CartAttachment::whereIn('attachment_group_id', $attachments);

            if ($cartAttachments) {
                foreach ($file_paths = $cartAttachments->pluck('file_path') as $file_path) {
                    if ($file_path && $file_path != null) {
                        \Storage::disk('s3')->delete($file_path);
                    }
                }
                $cartAttachments->delete();
            }
            if ($cartDetail)
                $cartDetail->delete();

            return $this->sendSuccessCreated("Cart delete successfully.", []);
        } catch (\Exception $e) {

            Log::info($e->getMessage());
            return $this->sendError("Sorry system can't delete" . $e->getMessage(), 500);
        }
    }

    public function customUpload(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'files' => 'array',
            'sku_code_c' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {

            $custId = $request->customer_id = auth()->user()->customer->customer_id;

            $s3 = \Storage::disk('s3');
            $client = $s3->getDriver()->getAdapter()->getClient();
            $expiry = "+10 minutes";

            $files = $request->input('files');
            $sku_code_c = $request->input('sku_code_c');
            $bucket = env('AWS_BUCKET_STAGING', 'bucket-public-careorder');
            $basePath = 'staging' . env('S3_CUSTOM_LOGO_FOLDER', '/logo-custom-dev/') . $custId . '/' . $sku_code_c . '/';

            $result = [];

            if ($request->input('files')) {
                foreach ($files as $filename) {
                    $key = $basePath . $filename;

                    $cmd = $client->getCommand('PutObject', [
                        'Bucket' => $bucket,
                        'Key' => $key,
                    ]);

                    $request = $client->createPresignedRequest($cmd, $expiry);
                    $presignedUrl = (string) $request->getUri();

                    $result[] = [
                        "filepath" => $cmd["Key"],
                        "s3_url" => $presignedUrl,
                    ];
                }
            } else {
                $result[] = [
                    "filepath" => "-",
                    "s3_url" => "-",
                ];
            }

            return $this->sendSuccess('success', $result);

        } catch (\Exception $e) {

            return response()->json([
                'message' => 'Terjadi Kesalahan saat membuat pesanan ' . $e->getMessage(),
            ], 500);

        }
    }

    public function cartAttachment(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sku_code_c' => 'required',
            'text' => 'array',
            'text.*.value' => 'required|string',
            'text.*.color' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {
            $custId = auth()->user()->customer->customer_id;

            $sku_code_c = $request->input('sku_code_c');
            $text = $request->input('text');
            $basePath = "logo-custom-dev";
            $files = \Storage::disk('s3-public')->files('staging/' . $basePath . '/' . $custId . '/' . $sku_code_c);
            $mapped = [];
            $attachment = [];
            if ($files) {
                foreach ($files as $file) {
                    $tf_file = $this->fileTransfer($file, $basePath, true);
                    if ($tf_file['error'] == true) {
                        return $this->sendError($tf_file['message']);
                    }
                    $attachment[] = [
                        "filepath" => $tf_file['filepath'],
                        'text' => null,
                        'color' => null,
                    ];
                }
            }

            $mappedText = array_map(function ($item) {
                return [
                    'filepath' => null,
                    'text' => $item['value'],
                    'color' => $item['color'],
                ];
            }, $text);

            $attachment = array_merge($attachment, $mappedText);

            $attachmentGroupId = (string) Str::uuid();
            $mapped = array_map(function ($item) use ($attachmentGroupId, $sku_code_c, $custId) {
                return [
                    "id" => (string) Str::uuid(),
                    "attachment_group_id" => $attachmentGroupId,
                    "file_path" => $item['filepath'],
                    "text" => $item['text'],
                    "color" => $item['color'],
                    "sku_code_c" => $sku_code_c,
                    "customer_id" => $custId,
                    "estimate_price" => $item['filepath'] ? 30000 : 20000
                ];
            }, $attachment);

            DB::beginTransaction();

            CartAttachment::upsert(
                $mapped,
                ['id', 'attachment_group_id'],
                ['file_path', 'sku_code_c', 'customer_id']
            );

            DB::commit();

            $attachments = CartAttachment::where('attachment_group_id', $attachmentGroupId)
                ->get(['id']) // only fetch id
                ->map(function ($item) {
                    return ['attachmentId' => $item->id];
                });

            $data = [
                "attachments" => $attachments,
                "attachments_group_id" => $attachmentGroupId
            ];

            return $this->sendSuccess("Data Berhasil disimpan!", $data);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'message' => 'Terjadi Kesalahan saat membuat pesanan di baris ' . $e->getLine() . ': ' . $e->getMessage(),
            ], 500);

        }
    }


    public function customAddToCart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array',
            'attachments_group_id' => 'required',
            'items.*.article' => 'required',
            'items.*.qty' => 'required|integer',
        ]);


        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {
            DB::beginTransaction();
            $custId = auth()->user()->customer->customer_id;
            $attachmentGroupId = $request->input('attachments_group_id');
            $cart = Cart::firstOrCreate([
                'customer_id' => $custId
            ]);

            $cart_header_id = $cart->id;

            $attachments = CartAttachment::where('attachment_group_id', $attachmentGroupId)->exists();
            if (!$attachments) {
                return $this->sendError("Custom Attachment tidak ditemukan!", 500);
            }
            // RestHelper::syncStock(array_column($request->items, 'article'), $custId);
            $stockData = ProductSku::whereIn('sku_id', array_column($request->items, 'article'))->get(['stock', 'sku_id'])->toArray();

            $mappedItems = collect($request->items)->flatMap(function ($item) use ($stockData, $cart_header_id, $attachmentGroupId) {
                $article = $item['article'];
                $qty = $item['qty'];
                $stockInfo = collect($stockData)->firstWhere('sku_id', $article);

                if ($stockInfo['stock'] != 0 && $qty > $stockInfo['stock']) {
                    $entryWithStock = [
                        'cart_id' => $cart_header_id,
                        'article' => $article,
                        'qty' => $stockInfo['stock'], // Set qty to the available stock
                        'is_custom' => 1,
                        'attachment_group_id' => $attachmentGroupId,
                        'modified_date' => now()->format('Y-m-d H:i:s'), // Current timestamp
                        'is_available' => 1 // Available
                    ];

                    return [$entryWithStock];
                }

                return [
                    [
                        'cart_id' => $cart_header_id,
                        'article' => $article,
                        'qty' => $qty,
                        'is_custom' => 1,
                        'attachment_group_id' => $attachmentGroupId,
                        'modified_date' => now()->format('Y-m-d H:i:s'),
                        'is_available' => $stockInfo['stock'] > 0 ? 1 : 0
                    ]
                ];
            });

            CartDetail::upsert(
                $mappedItems->toArray(),
                ['cart_id', 'article', 'is_custom', 'is_available', 'attachment_group_id'], // Unique columns
                ['qty', 'is_available'] // Columns to update
            );

            DB::commit();

            return $this->sendSuccessCreated("Cart created/updated successfully.", $mappedItems);
        } catch (\Exception $e) {
            DB::rollback();
            Log::info($e->getMessage());
            return $this->sendError("Sorry system can't create/update checkout, " . $e->getMessage(), 500);
        }


    }

    public function indexNonCustom(Request $request)
    {
        $custId = auth()->user()->customer->customer_id;
        $validated = Cart::validate($custId);
        if ($validated == true) {
            $dataLimit = new CreditLimitCartResource(Customer::find($custId));
            return $this->sendSuccess("Data Cart successfully", new CartListResource(Cart::fetch($custId)->first(), $dataLimit));
        }

        return $this->sendSuccess($validated, []);
    }

    public function indexCustom(Request $request)
    {

    }


    public function getFlag($article)
    {
        Log::info("[GETFLAG] ArticleID " . $article);

        $flag = [];
        $date = now()->format('Y-m-d');
        $articleData = Product::Where('article', $article)->first();
        if (!empty($articleData)) {
            if ($articleData->transfer_date <= $date && $articleData->expired_date >= $date) {
                array_push($flag, 'NEW');
            }
            if ($articleData->is_custom_logo || $articleData->is_custom_size) {
                array_push($flag, 'CUSTOM');
            }
            array_push($flag, $articleData->lvl4_description);
        }
        return $flag;
    }

    //
    public function store(Request $request)
    {


        $custId = auth()->user()->customer->customer_id;
        if (auth()->user()->customer->is_active == 0 && auth()->user()->customer->distribution_channel == 'B2B') {
            return $this->sendError('Maaf tidak bisa menambahkan produk. Pelanggan belum terverifikasi', 500);
        }

        DB::beginTransaction();
        try {
            $custId = auth()->user()->customer->customer_id;
            $cart = Cart::create([
                'customer_id' => $custId,
            ]);

            $cart_header_id = $cart->id;
            $cartDetail = [
                'cart_id' => $cart_header_id,
                'article' => $request->article,
                'qty' => $request->qty,
            ];
            CartDetail::insert($cartDetail);

            DB::commit();
            return $this->sendSuccessCreated("Cart created successfully.", $cartDetail);
        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError("Sorry system can't create checkout,  ", 500);
        }
    }

    public function storeToCartDetail(Request $request)
    {
        if (auth()->user()->customer->is_active == 0 && auth()->user()->customer->distribution_channel == 'B2B') {
            return $this->sendError('Maaf tidak bisa menambahkan produk. Pelanggan belum terverifikasi', 500);
        }
        try {
            $this->validate($request, [
                'article' => 'required',
                'qty' => 'required',
            ]);
        } catch (ValidationException $e) {
            // If validation fails, an exception will be thrown
            $errors = $e->errors();
            // return response()->json([
            //     'error' => $errors,
            // ], 400);
            // Log::info($e->getMessage());
            return $this->sendError($errors, 400);
        }


        $custId = auth()->user()->customer->customer_id;

        DB::beginTransaction();
        try {
            $cart = Cart::firstOrCreate([
                'customer_id' => $custId
            ]);

            $cart_header_id = $cart::where('customer_id', $custId)->first()->id;

            // $stock = RestHelper::searchStock(RestHelper::stockCache([$request->article]), $request->article);
            // $moq = $stock['moq'] ?? 1 == 0 ? 1 : 0;
            // if ($request->qty % $moq != 0) {
            //     return $this->sendError('Jumlah quantity kurang/tidak sesuai dari moq');
            // }

            $cartDetail = CartDetail::updateOrCreate([
                'cart_id' => $cart_header_id,
                'article' => $request->article,
                'is_custom' => $request->is_custom ?? 0
            ], [
                'qty' => $request->qty,
                'modified_date' => now()->format('Y-m-d H:i:s')
            ]);
            Log::info($cartDetail);

            DB::commit();
            return $this->sendSuccessCreated("Cart created/updated successfully.", $cartDetail);
        } catch (\Exception $e) {

            DB::rollback();
            Log::info($e->getMessage());
            return $this->sendError("Sorry system can't create/update checkout, " . $e->getMessage(), 500);
        }
    }


    public function checkCart(Request $request)
    {
        try {
            $this->validate($request, [
                'article' => 'required',
                'selected' => 'required',
            ]);
            $this->storeCheck(Cart::where('customer_id', $request->user()->customer->customer_id)->first()->id, $request->article, $request->selected);
            return $this->sendSuccessCreated("Cart created/updated successfully.", []);

        } catch (\Exception $e) {
            $errors = $e->getMessage();
            return $this->sendError($errors, 400);
        }


    }
    public function uncheck(Request $request)
    {
        try {
            $this->validate($request, [
                'article' => 'required',
                'selected' => 'required',
            ]);
            $this->storeCheck(Cart::where('customer_id', $request->user()->customer->customer_id)->first()->id, $request->article, $request->selected);
            return $this->sendSuccessCreated("Cart created/updated successfully.", []);

        } catch (\Exception $e) {
            $errors = $e->getMessage();
            return $this->sendError($errors, 400);
        }
    }

    public function getCustomerShipmentList(Request $request)
    {
        $user = $request->user();
        if ($user->reference_object == 'customer') {
            $custId = auth()->user()->customer->customer_id;
        } else if ($user->reference_object == 'sales') {
            $custId = $request->input('customer_id');
        }
        ;

        $customer = Customer::where('customer_id', $custId)->first();
        $customerShipment = CustomerShipment::where('customer_id', $custId)->orderBy('is_primary', 'desc')->get();

        $data = [];
        if ($customer->distribution_channel == 'B2B' && $customer->registered_sap_at != null) {
            foreach ($customerShipment as $cs) {
                if (!Str::isUuid($cs->customer_shipment_id)) {
                    $data[] = $cs->toArray();
                }
            }
        } else {
            foreach ($customerShipment as $cs) {
                $data[] = $cs->toArray();
            }
        }

        return $this->sendSuccess("Data List Shipment", $customerShipment);
    }

    public function getLimitCustomer()
    {
        $custId = auth()->user()->customer->customer_id;
        $client = new GetLimitRepo();

        $request = [
            "source" => "CAREOM",
            "destination" => "PLF",
            "detailPlafond" => [
                [
                    "destination" => "PLF",
                    "customer" => $custId
                ]
            ]
        ];
        $dataResult = [];
        try {

            $response = $client->getLimit($request, $custId);
            $dataResult = $client->getData($response);
            // Log::channel('stderr')->info($limit);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        return $dataResult;

    }

    public function getStock($item)
    {

        $details[] = [
            'source' => 'CAREOM',
            'destination' => 'STK',
            'article' => $item->article,
            // 'site' => '1200'
        ];


        $data = [
            "source" => "CAREOM",
            "destination" => "STK",
            "detail" => $details
        ];

        $getStokRepo = new GetStokRepo();
        $response = $getStokRepo->getStock($data, auth()->user()->customer->customer_id);
        $qty = 0;
        //note sap 

        try {
            $qty = $getStokRepo->getQty($response);
        } catch (\Exception $e) {
            // \Log::error($e->getMessage());
            $qty = 0;

        }
        return $qty;

    }

    private function getMoq($data)
    {
        $details[] = [
            'source' => 'CAREOM',
            'destination' => 'STK',
            'article' => $data->article,
            // 'site' => '1200'
        ];


        $data = [
            "source" => "CAREOM",
            "destination" => "STK",
            "detail" => $details
        ];

        $getStokRepo = new GetStokRepo();
        $response = $getStokRepo->getStock($data, auth()->user()->customer->customer_id);
        $qty = 0;
        try {
            $moq = $getStokRepo->getMoq($response);
        } catch (\Exception $e) {
            Log::warning('error get stock from SAP' . $e->getMessage());
        }
        return $moq ?? 0;
    }

    public function deleteAllCart(Request $request)
    {
        $cartId = $request->input('cart_id');

        try {
            $articles = CartDetail::Where('cart_id', $cartId)->delete();
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }

        return $this->sendSuccess("Articles in cart deleted", $articles);
    }

    public function destroyBulk(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sku_code_c' => 'required',
            'items' => 'required|array',
            'items.*.article' => 'required',
        ]);


        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {

            $custId = auth()->user()->customer->customer_id;

            $cart = Cart::where('customer_id', $custId)->first();

            $cart_header_id = $cart->id;
            $articles = array_column($request->items, 'article');

            $cartDetail = CartDetail::where('cart_id', $cart_header_id)->whereIn('article', $articles)
                ->where(function ($query) {
                    $query->where('attachment_group_id', 0)
                        ->orWhereNull('attachment_group_id');
                });
            if ($cartDetail)
                $cartDetail->delete();

            return $this->sendSuccessCreated("Cart delete successfully.", []);
        } catch (\Exception $e) {

            Log::info($e->getMessage());
            return $this->sendError("Sorry system can't delete,  ", 500);
        }

    }
    public function destroy($id)
    {
        try {
            $cart = new Cart;
            $cartDetail = is_array($id) ? CartDetail::where('cart_detail_id', $id)->get() : CartDetail::where('cart_detail_id', $id)->first();

            if (!$cartDetail) {
                return $this->sendError("cart detail id not found, system can't delete,  ", 404);
            }

            if ($cartDetail->is_custom == 1) {
                $article = Product::where('article', $cartDetail->article)->first();
                $articles = Product::where('sku_code_c', $article->sku_code_c)->pluck('article')->toArray();
                $cds = CartDetail::where('cart_id', $cartDetail->cart_id)->whereIn('article', $articles)->get();
                if (count($cds) <= 1) {
                    $ocs = OrderCustom::where('reference_id', $cartDetail->cart_id)->where('sku', $article->sku_code_c)
                        ->pluck('id')->toArray();
                    OrderCustomAttachment::whereIn('order_custom_id', $ocs)->delete();
                    OrderCustom::where('reference_id', $cartDetail->cart_id)->where('sku', $article->sku_code_c)->delete();
                }
            }

            $cartDetail->delete();

            return $this->sendSuccessCreated("Cart delete successfully.", null);
        } catch (\Exception $e) {

            Log::info($e->getMessage());
            return $this->sendError("Sorry system can't delete,  ", 500);
        }
    }

    public function simulateDiscountB2B(Request $request)
    {
        try {
            if (empty($request['data'] == true)) {
                return $this->sendError('Data to simulate discount b2b is empty');
            }
            $bruto = 0;
            $qty = 0;
            foreach ($request['data'] as $data) {
                $article = Product::where('article', $data['article'])->first();
                if ($article != null) {

                    $category = $article->lvl3_description == 'BAGS' ? 'BAGS' : 'NON BAGS';
                    $bruto += ($article->price->amount + ($data['custom_price'] ?? 0)) * $data['qty'];
                    $qty += $data['qty'];

                } else {
                    $failedMessage[] = [
                        'Article' => $data['article'],
                        'Message' => 'Not Found'
                    ];
                }
            }

            $m_d = DB::table('matrix_discount')
                ->where('is_custom', $request['is_custom'] ?? 0)
                ->whereRaw('? BETWEEN min_bruto_from AND min_bruto_to', [(int) $bruto])
                ->whereRaw('? BETWEEN qty_from AND qty_to', [(int) $qty])
                ->where('category', $category)
                ->first();

            $user_id = auth()->user()->reference_object == 'customer' ? auth()->user()->customer->customer_id : auth()->user()->sales->sales_id;
            $websocket = event(new GenericEvent($user_id, [
                'total_tax' => 0,
                'total' => $bruto,
                'total_discount' => $m_d->discount * ($bruto / 100),
                'total_nett' => 0,
                'nett_before_tax' => 0
            ], 'simulateSO.new'));

            Log::info($websocket);
            return $this->sendSuccess('Simulate Discount B2B Success', [
                'total_discount' => $m_d->discount * ($bruto / 100),
                'discount_percentage' => $m_d->discount,
                'error' => @$failedMessage ?? null
            ]);

        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function addProductCustom(Request $request)
    {

        if (auth()->user()->customer->is_active == 0 && auth()->user()->customer->distribution_channel == 'B2B') {
            return $this->sendError('Maaf tidak bisa menambahkan produk. Pelanggan belum terverifikasi', 500);
        }

        DB::beginTransaction();
        try {
            $datas = $request->input();
            $j = 0;
            // if (auth()->user()->customer->getRawOriginal('status') != 'approved') {
            //     return $this->sendError('You are not allowed to make product custom');
            // }

            $cart = Cart::firstOrCreate(
                ['customer_id' => auth()->user()->reference_id]
            );

            $cart_header_id = $cart::where('customer_id', auth()->user()->reference_id)->first()->id;
            $total = 0;

            foreach ($datas['cart'] as $cart) {
                $article = Product::where('article', $cart['article'])->first();
                $cartDetail = CartDetail::updateOrCreate(
                    [
                        'cart_id' => $cart_header_id,
                        'article' => $cart['article'],
                        'is_custom' => 1,
                    ],
                    [
                        'qty' => $cart['qty'],
                        'modified_date' => now()->format('Y-m-d H:i:s')
                    ]
                );
                $total += $article->price->amount * $cart['qty'];
            }

            $ocs = OrderCustom::where('sku', $datas['sku'])
                ->where('reference_name', 'cart')
                ->where('reference_id', $cart_header_id)
                ->pluck('id')->toArray();
            OrderCustomAttachment::whereIn('order_custom_id', $ocs)->delete();
            OrderCustom::where('sku', $datas['sku'])->where('reference_name', 'cart')->where('reference_id', $cart_header_id)->delete();

            $image_type = ['jpg', 'gif', 'jpeg', 'png', 'swf', 'psd', 'bmp', 'tiff', 'tiff', 'jpc', 'jp2', 'jpx', 'jb2', 'swc', 'iff', 'wbmp', 'xbm', 'ico', 'webp'];

            foreach ($datas['customs'] as $data) {
                $insert = [];
                $attachments = $data['customLogos'];
                $custom_price = 0;

                if (!$request->has('customs.' . $j . '.imageProductCustom')) {
                    DB::rollBack();
                    return $this->sendError('Image product custom is required');
                }

                $img_product_custom = $request->input('customs.' . $j . '.imageProductCustom');

                if ($img_product_custom != null) {
                    Log::info('image : ' . $img_product_custom);
                    $extension = pathinfo($img_product_custom)['extension'] ?? '';
                    $cek_img_pc = in_array($extension, $image_type);

                    if ($cek_img_pc == false) {
                        DB::rollBack();
                        return $this->sendError('Image product custom is not the right format', 404);
                    }

                    // $img_file_name = $datas['sku'].'-'.$data['position_side'].'-'.uniqid().'-Image-Custom.'.$image_type[$cek_img_pc-1];
                    $img_file_path = $this->fileTransfer($img_product_custom, 'product-custom');
                    if ($img_file_path['error'] == true) {
                        DB::rollBack();
                        return $this->sendError($img_file_path['message']);
                    }
                    // Storage::disk('s3')->put(substr($img_file_path,1), file_get_contents($img_product_custom));
                }

                for ($i = 0; $i < count($attachments); $i++) {
                    if ($request->has('customs.' . $j . '.customLogos.' . $i . '.data')) {
                        $logo = $request->input('customs.' . $j . '.customLogos.' . $i . '.data');
                        $file = pathinfo($logo);
                        if (isset($file['extension'])) {
                            $extension = pathinfo($logo)['extension'] ?? '';
                            $cek_img_logo = in_array($extension, $image_type);

                            if ($cek_img_logo == false) {
                                DB::rollBack();
                                return $this->sendError('Logo extension is not the right format', 404);
                            }
                            // $logo_file_name = $datas['sku'].'-'.$data['position_side'].'-'.uniqid().'-Logo-Custom.'.$image_type[$cek_img_logo-1];
                            $logo_file_path = $this->fileTransfer($logo, 'logo-custom', false);
                            if ($logo_file_path['error'] == true) {
                                DB::rollBack();
                                return $this->sendError($logo_file_path['message']);
                            }
                            // Storage::disk('s3')->put(substr($logo_file_path,1), file_get_contents($logo));

                            $db_cp = DB::table('custom_price')->where('item', $attachments[$i]['logoMaterial'])
                                ->where('description', $attachments[$i]['logoSize'])
                                ->first();

                            $insert[$i] = [
                                'custom_type' => 'img',
                                'custom_price' => $db_cp->price,
                                'position_x' => $attachments[$i]['positionX'],
                                'position_y' => $attachments[$i]['positionY'],
                                'dimension_width' => $attachments[$i]['dimensionWidth'],
                                'dimension_height' => $attachments[$i]['dimensionHeight'],
                                'file_path' => '/' . $logo_file_path['filepath'],
                                'notes' => $datas['notes'],
                                'size' => $attachments[$i]['logoSize'],
                                'custom_text' => null,
                                'material' => $attachments[$i]['logoMaterial'],
                                'created_date' => now()->format('Y-m-d H:i:s'),
                                'created_by' => auth()->user()->customer->owner_name,
                                'modified_date' => now()->format('Y-m-d H:i:s'),
                                'modified_by' => auth()->user()->customer->owner_name
                            ];

                            $custom_price += $db_cp->price;
                        } else {
                            $db_cp = DB::table('custom_price')->where('item', $attachments[$i]['logoMaterial'])
                                ->where('description', $attachments[$i]['logoSize'])
                                ->first();

                            $insert[$i] = [
                                'custom_type' => 'text',
                                'custom_price' => $db_cp->price,
                                'position_x' => $attachments[$i]['positionX'],
                                'position_y' => $attachments[$i]['positionY'],
                                'dimension_width' => $attachments[$i]['dimensionWidth'],
                                'dimension_height' => $attachments[$i]['dimensionHeight'],
                                'file_path' => null,
                                'notes' => $datas['notes'],
                                'size' => $attachments[$i]['logoSize'],
                                'custom_text' => $attachments[$i]['data'],
                                'material' => $attachments[$i]['logoMaterial'],
                                'created_date' => now()->format('Y-m-d H:i:s'),
                                'created_by' => auth()->user()->customer->owner_name,
                                'modified_date' => now()->format('Y-m-d H:i:s'),
                                'modified_by' => auth()->user()->customer->owner_name
                            ];
                            $custom_price += $db_cp->price;
                        }
                    }
                    foreach ($datas['cart'] as $cart) {
                        $article = Product::where('article', $cart['article'])->first();
                        $total += $db_cp->price * $cart['qty'];
                    }
                }

                $oc = OrderCustom::updateOrCreate([
                    'sku' => $datas['sku'],
                    'reference_name' => 'cart',
                    'reference_id' => $cart_header_id,
                    'position_side' => $data['position_side'],
                ], [
                    'generated_file_path' => $img_product_custom != null ? '/' . $img_file_path['filepath'] : null, // gk bisa di grouping jika object custom dengan position side yang sama ada 2 karena gambar generatednya tidak bisa di gabung, pake yang last inserted
                    'created_by' => auth()->user()->customer->owner_name,
                    'modified_by' => auth()->user()->customer->owner_name
                ]);

                $q_oc = OrderCustom::where([
                    ['sku', '=', $oc->sku],
                    ['article_id', '=', $oc->article_id],
                    ['reference_name', '=', $oc->reference_name],
                    ['reference_id', '=', $oc->reference_id],
                    ['position_side', '=', $oc->position_side],
                    ['generated_file_path', '=', $oc->generated_file_path],
                    ['created_by', '=', $oc->created_by],
                    ['created_date', '=', $oc->created_date]
                ])->first();

                for ($i = 0; $i < count($insert); $i++) {
                    $insert[$i]['order_custom_id'] = $q_oc->id;
                }

                OrderCustomAttachment::insert($insert);
                $j++;
            }

            $this->deletePublicFile(array_values(array_unique($request->input('customs.*.customLogos.*.data'))));
            if ($total < 20000000) {
                DB::rollback();
                return $this->sendError('Pesanan total minimal 20 Juta');
            }

            DB::commit();
            return $this->sendSuccess('Add product custom success');
        } catch (\Exception $e) {
            DB::rollback();
            Log::info('Add product custom failed : ' . $e->getMessage() . ' at Line :' . $e->getLine(), $request->input());
            return $this->sendError('Add product custom failed');
        }
    }

    public function bulkAddToCart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sku_code_c' => 'required',
            'items' => 'required|array',
            'items.*.article' => 'required',
            'items.*.qty' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {
            $custId = auth()->user()->customer->customer_id;
            $customer = Customer::findOrFail($custId);
            if (!$customer->is_verified) {
                return $this->sendError('Customer not verified.', 403);
            }

            DB::beginTransaction();

            $cart = Cart::where('customer_id', $custId)->first();

            if (empty($cart)) {
                Cart::create([
                    'customer_id' => $custId
                ]);
                $cart = Cart::where('customer_id', $custId)->first();
            }


            $cart_header_id = $cart->id;
            RestHelper::syncStock(array_column($request->items, 'article'), $custId);
            $stockData = ProductSku::whereIn('sku_id', array_column($request->items, 'article'))->get(['stock', 'sku_id'])->toArray();

            $mappedItems = collect($request->items)->flatMap(function ($item) use ($stockData, $cart_header_id) {
                $article = $item['article'];
                $qty = $item['qty'];
                $stockInfo = collect($stockData)->firstWhere('sku_id', $article);

                if ($stockInfo['stock'] != 0 && $qty > $stockInfo['stock']) {
                    $entryWithStock = [
                        'cart_id' => $cart_header_id,
                        'article' => $article,
                        'qty' => $stockInfo['stock'], // Set qty to the available stock
                        'is_custom' => 0,
                        'attachment_group_id' => 0,
                        'modified_date' => now()->format('Y-m-d H:i:s'), // Current timestamp
                        'is_available' => 1 // Available
                    ];

                    return [$entryWithStock];
                }

                return [
                    [
                        'cart_id' => $cart_header_id,
                        'article' => $article,
                        'qty' => $qty,
                        'is_custom' => 0,
                        'attachment_group_id' => 0,
                        'modified_date' => now()->format('Y-m-d H:i:s'),
                        'is_available' => $stockInfo['stock'] > 0 ? 1 : 0
                    ]
                ];
            });

            CartDetail::upsert(
                $mappedItems->toArray(),
                ['cart_id', 'article', 'is_custom', 'is_available', 'attachment_group_id'], // Unique columns
                ['qty', 'is_available'] // Columns to update
            );

            DB::commit();
            return $this->sendSuccessCreated("Cart created/updated successfully.", $mappedItems);

        } catch (\Exception $e) {
            DB::rollback();
            Log::info($e->getMessage());
            return $this->sendError("Sorry system can't create/update checkout, " . $e->getMessage(), 500);
        }
    }

    function checkCartBulk(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sku_code_c' => 'required',
            'items' => 'required|array',
            'items.*.article' => 'required',
            'items.*.selected' => 'required|boolean',
        ]);


        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {
            $custId = auth()->user()->customer->customer_id;

            DB::beginTransaction();
            $cart = Cart::where('customer_id', $custId)->first();

            $cart_header_id = $cart->id;
            $articles = array_column($request->items, 'article');

            CartDetail::where('cart_id', $cart_header_id)->whereIn('article', $articles)->update(['selected' => $request->items[0]['selected']]);

            DB::commit();
            return $this->sendSuccessCreated("Cart created/updated successfully.", []);

        } catch (\Exception $e) {
            DB::rollback();
            Log::info($e->getMessage());
            return $this->sendError("Sorry system can't create/update checkout, " . $e->getMessage(), 500);
        }
    }

    public function addToCartFromHistory($order_group_id)
    {
        $orders = Order::with('items:order_detail_id,order_no,article_id,is_custom,qty')
            ->where('order_group_id', $order_group_id)
            // ->where('order_no', $order_group_id)
            ->get();

        try {
            $custId = auth()->user()->customer->customer_id;

            DB::beginTransaction();
            $cart = Cart::updateOrCreate(
                ['customer_id' => $custId],
                ['modified_date' => now()->format('Y-m-d H:i:s')]
            );

            $cart_header_id = $cart->id;
            $items = $orders->pluck('items')->flatten(1);
            $articleIds = $items->pluck('article_id')->toArray();

            // Sync stock from SAP 
            RestHelper::syncStock($articleIds, $custId);

            //Check stock from SAP
            // $sapStocks = RestHelper::stockCache($articleIds, auth()->user()->customer->customer_id ?? null);

            //Check stock from database
            $articleStocks = ProductSku::whereIn('sku_id', $articleIds)
                ->pluck('stock', 'sku_id');

            $existingCartItems = CartDetail::where('cart_id', $cart_header_id)
                ->whereIn('article', $articleIds)
                ->get()
                ->groupBy('article');

            $mappedItems = [];
            foreach ($items as $item) {
                $articleId = $item->article_id;
                $qty = $item->qty;

                $existingQty = isset($existingCartItems[$articleId]) ? $existingCartItems[$articleId]->sum('qty') : 0;
                $totalQty = $existingQty + $qty;

                //Get stock from SAP
                // $stock = RestHelper::searchStock($sapStocks, $articleId)['qty'];

                //Get stock from database
                $stock = $articleStocks[$articleId] ?? 0;

                $availableQty = min($totalQty, $stock);
                $unavailableQty = max(0, $totalQty - $stock);

                if ($availableQty > 0) {
                    $mappedItems[] = [
                        'cart_id' => $cart_header_id,
                        'article' => $articleId,
                        'is_custom' => $item->is_custom,
                        'qty' => $availableQty,
                        'modified_date' => now()->format('Y-m-d H:i:s'),
                        'is_available' => true,
                        'selected' => 1
                    ];
                } else {
                    CartDetail::where('cart_id', $cart_header_id)
                        ->where('article', $articleId)
                        ->where('is_available', true)
                        ->delete();
                }

                if ($unavailableQty > 0) {
                    $mappedItems[] = [
                        'cart_id' => $cart_header_id,
                        'article' => $articleId,
                        'is_custom' => $item->is_custom,
                        'qty' => $unavailableQty,
                        'modified_date' => now()->format('Y-m-d H:i:s'),
                        'is_available' => false,
                        'selected' => 0
                    ];
                } else {
                    CartDetail::where('cart_id', $cart_header_id)
                        ->where('article', $articleId)
                        ->where('is_available', false)
                        ->delete();
                }
            }

            CartDetail::upsert(
                $mappedItems,
                ['cart_id', 'article', 'is_custom', 'is_available'],
                ['qty', 'modified_date']
            );

            DB::commit();
            return $this->sendSuccessCreated("Cart created/updated successfully.", $mappedItems);

        } catch (\Exception $e) {
            DB::rollback();
            Log::info($e->getMessage());
            return $this->sendError("Sorry system can't create/update cart, " . $e->getMessage(), 500);
        }
    }
}