<?php

namespace App\Http\Controllers;

use App\Models\BusinessUnit;
use App\Models\User;
use App\Models\Sales;
use App\Models\UserMatrix;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\CreateUserRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Resources\UserInternalResource;
use App\Http\Resources\UserInternalDetailsResource;
use App\Models\Customer;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Str;

class CrudUserController extends Controller
{
    public $userRepo;

    public function __construct(UserRepository $userRepo)
    {
        $this->userRepo = $userRepo;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $req)
    {
        $users = User::with(['business_units', 'roles', 'sales'])->has('business_units');
        return $this->userRepo->getData($users, $req);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(CreateUserRequest $request): JsonResponse
    {
        $request->validated();

        try {
            $result = DB::transaction(function () use ($request) {
                $sales = null;
                $directTo = null;

                if ($request->sales_id) {
                    $sales = Sales::findOrFail($request->sales_id);
                    $sales->fill([
                        'position_name' => $request->position,
                        'regional' => $request->area,
                        'sales_name' => $request->name,
                        'sap_username' => $request->sap_user,
                        'target_sales' => $request->target,
                        'phone_number' => $request->phone_no,
                    ]);
                    $sales->save();
                } else {
                    $sales = new Sales();
                    $sales->id = mt_rand();
                    $sales->sales_id = (string) random_int(100000, 999999);
                    $sales->fill([
                        'position_name' => $request->position,
                        'regional' => $request->area,
                        'sales_name' => $request->name,
                        'sap_username' => $request->sap_user,
                        'target_sales' => $request->target,
                        'phone_number' => $request->phone_no,
                    ]);
                    $sales->save();
                }

                if (in_array($request->authorization_id, ['0', '1'])) {
                    $directTo = null;
                } else {
                    $directTo = Sales::whereHas('user', function ($query) use ($request) {
                        $query->where('username', $request->direct_to);
                    })->first();

                    if (!$directTo) {
                        throw new ModelNotFoundException("Sales with username {$request->direct_to} not found.");
                    }

                    $sales->assignment()->sync([
                        $directTo->sales_id => [
                            'created_by' => 'INTERNAL',
                            'modified_by' => 'INTERNAL',
                        ]
                    ]);
                }

                $user = new User();
                $user->user_id = Str::uuid();
                $user->name = $request->name;
                $user->username = $request->username;
                $user->email = $request->email;
                $user->password = Hash::make($request->password);
                $user->reference_object = 'sales';
                $user->reference_id = $sales->sales_id;
                $user->is_active = $request->status;
                $user->save();

                foreach ($request->channels as $channelId) {
                    $user->business_units()->attach($channelId, [
                        'roles_id' => $request->roles_id,
                        'tier_level' => $request->authorization_id,
                        'user_id' => $user->user_id,
                    ]);
                }

                return $user;
            });

            return $this->sendSuccess("User created successfully", new UserInternalResource($result));
        } catch (\Exception $e) {
            return $this->sendException('Penyimpanan data gagal!', 422, '', $e);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id, Request $request)
    {
        try {
            $isMasking = filter_var($request->query('is_masking', true), FILTER_VALIDATE_BOOLEAN);
            $user = User::with([
                'business_units',
                'roles',
                'sales.directTo.direct_sales.user',
                'sales.assignedSales.sales',
                'sales.customer_sales'
            ])->findOrFail($id)->setMasking($isMasking);
            return $this->sendSuccess('User retrieved successfully.', new UserInternalDetailsResource($user, $isMasking));
        } catch (ModelNotFoundException) {
            return $this->sendError('There is no user found');
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try {
            $user = User::findOrFail($id);
            return $this->sendSuccess('User retrieved successfully.', new UserInternalDetailsResource($user));
        } catch (ModelNotFoundException) {
            return $this->sendError('There is no user found');
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateUserRequest $request, $id)
    {
        $request->validated();

        try {
            if ($request->filled('sales_id')) {
                $directTo = null;

                if ($request->filled('direct_to') && !in_array($request->authorization_id, ['0', '1'])) {
                    if (in_array($request->authorization_id, ['0', '1'])) {
                        throw new ModelNotFoundException('Tier 0 & 1 tidak dapat mempunyai Direct to!');
                    }

                    $directTo = Sales::whereHas('user', function ($query) use ($request) {
                        $query->where('username', $request->direct_to);
                    })->first();

                    if (!$directTo) {
                        throw new ModelNotFoundException('There is no sales with username ' . $request->direct_to . ' found');
                    }
                }

                if (in_array($request->authorization_id, ['0', '1'])) {
                    $directTo = null;
                }

                $sales = Sales::findOrFail($request->sales_id);

                $sales->position_name = $request->position ?? $sales->getOriginal('position_name');
                $sales->sales_name = $request->name ?? $sales->getOriginal('sales_name');
                $sales->regional = $request->area ?? $sales->getOriginal('regional');
                $sales->target_sales = $request->target ?? $sales->getOriginal('target_sales');
                $sales->phone_number = $request->phone_no ?? $sales->getOriginal('phone_number');
                $sales->sap_username = $request->sap_user ?? $sales->getOriginal('sap_username');
                $sales->save();

                $currentDirectTo = $sales->assignment()->first();

                if (optional($currentDirectTo)->sales_id !== optional($directTo)->sales_id) {
                    $sales->assignment()->sync(
                        $directTo ? [
                            $directTo->sales_id => [
                                'created_by' => 'INTERNAL',
                                'modified_by' => 'INTERNAL',
                            ]
                        ] : []
                    );
                }
            }

            $result = DB::transaction(function () use ($request, $id) {
                $user = User::findOrFail($id);

                if ($request->filled('sales_id')) {
                    $user->reference_id = $request->sales_id;
                    $user->reference_object = 'sales';
                }

                $user->username = $request->username ?? $user->getOriginal('username');
                $user->email = $request->email ?? $user->getOriginal('email');
                $user->name = $request->name ?? $user->getOriginal('name');

                if ($request->filled('password')) {
                    $user->password = Hash::make($request->password);
                    $user->is_change_password = 1;
                }

                $user->is_active = $request->status ?? $user->getOriginal('is_active');
                $user->save();

                $existingRecord = UserMatrix::where('user_id', $user->user_id)->pluck('id');
                if ($existingRecord->isNotEmpty()) {
                    UserMatrix::whereIn('id', $existingRecord)->delete();
                }

                foreach ($request->channels as $channelId) {
                    UserMatrix::create([
                        'user_id' => $user->user_id,
                        'business_unit_id' => $channelId,
                        'roles_id' => $request->roles_id,
                        'tier_level' => $request->authorization_id,
                        'created_by' => 'SYSTEM',
                        'modified_by' => 'SYSTEM'
                    ]);
                }
                return $user;
            });
            return $this->sendSuccess("User updated successfully", new UserInternalResource($result));
        } catch (ModelNotFoundException $e) {
            return $this->sendError([$e->getPrevious(), $e->getMessage()]);
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}