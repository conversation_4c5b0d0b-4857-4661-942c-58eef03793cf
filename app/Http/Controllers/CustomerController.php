<?php

namespace App\Http\Controllers;

use DateTime;
use Exception;
use Carbon\Carbon;
use App\Models\User;
use App\Models\Color;
use App\Models\Order;
use App\Models\Invoice;
use App\Models\Product;
use App\Jobs\MailSender;
use App\Models\Customer;
use Illuminate\Bus\Batch;
use App\Helpers\FileHelper;
use App\Models\BankAccount;
use App\Models\CreditLimit;
use App\Models\Subdistrict;
use Illuminate\Support\Str;
use App\Events\GenericEvent;
use App\Exports\StockExport;
use Illuminate\Http\Request;
use App\Helpers\FormatHelper;
use App\Models\CustomerSales;
use App\Models\CustomerStock;
use App\Models\InvoiceDetail;
use App\Models\MasterAddress;
use App\Jobs\StockUploadQueue;
use App\Mail\MailRegistration;
use App\Models\VirtualAccount;
use App\Models\CustomerHistory;
use App\Models\MasterParameter;
use App\Models\SalesAssignment;
use App\Imports\StockTokoImport;
use App\Models\CustomerShipment;
use App\Models\TransportationZone;
use Illuminate\Support\Facades\DB;
use App\Models\DeliveryOrderDetail;

use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Models\CustomerStockHistory;
use App\Repositories\GetSocialsRepo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\ResellerMasterAddress;
use App\Http\Requests\UpdateKTPRequest;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\UpdateNPWPRequest;
use App\Repositories\CustomerRepository;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use Spatie\SimpleExcel\SimpleExcelWriter;
use App\Http\Resources\DownloadCSVResource;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use App\Http\Requests\CreateCustomerRequest;
use App\Http\Requests\UpdateCustomerRequest;
use Symfony\Component\HttpFoundation\StreamedResponse;

class CustomerController extends Controller
{
    public $cusRepo;
    use FileHelper;

    public function __construct(CustomerRepository $cusRepo)
    {
        $this->cusRepo = $cusRepo;
    }

    public function getBank(Request $reqeuest)
    {
        return $this->sendSuccess('success', BankAccount::active()->get());
    }

    public function getVa(Request $request)
    {
        return $this->sendSuccess('success', VirtualAccount::where('customer_id', $request->user()->customer->customer_id)->get());
    }

    public function getStock(Request $request)
    {
        $customer_id = $request->input('customer_id') ?? $request->user()->customer->customer_id;
        $text = $request->input('text');
        $tokoId = $request->input('toko_id');
        $category = $request->input('category');
        $orderBy = $request->input('order_by');
        $page = $request->input('page');
        $per_page = $request->input('per_page');

        $datas = DB::table('customer_stock')
            ->where('customer_id', '=', $customer_id)
            ->leftJoin('article', 'customer_stock.article_id', '=', 'article.article')
            ->when($text, function ($query) use ($text) {
                // return $query->where('customer_stock.article_id','LIKE','%'.$text.'%')
                //     ->orWhere('customer_stock.product_name','LIKE','%'.$text.'%');
                return $query->where(function ($q) use ($text) {
                    $q->where('customer_stock.article_id', 'like', '%' . $text . '%')
                        ->orWhere('customer_stock.product_name', 'like', '%' . $text . '%');
                });
            })
            ->when($tokoId, function ($query) use ($tokoId) {
                return $query->where('customer_stock.customer_shipment_id', '=', $tokoId);
            })
            ->when($category, function ($query) use ($category) {
                return $this->category($query, $category);
            })
            ->when($orderBy, function ($query) use ($orderBy) {
                return $this->filter($query, $orderBy);
            })
            ->select('article.sku_code_c', 'article.product_name_c')
            ->groupBy('article.sku_code_c', 'article.product_name_c')
            ->paginate($per_page, ['*'], 'page', $page);

        $data_ids = $datas->pluck('sku_code_c');

        $details = DB::table('customer_stock')
            ->leftJoin('article', 'customer_stock.article_id', '=', 'article.article')
            ->leftJoin('master_color', 'customer_stock.product_variant', '=', 'master_color.key')
            ->where('customer_id', '=', $customer_id)
            ->when($text, function ($query) use ($text) {
                return $query->where('customer_stock.article_id', 'like', '%' . $text . '%')
                    ->orWhere('customer_stock.product_name', 'like', '%' . $text . '%');
            })
            ->when($tokoId, function ($query) use ($tokoId) {
                return $query->where('customer_stock.customer_shipment_id', '=', $tokoId);
            })
            ->when($category, function ($query) use ($category) {
                return $this->category($query, $category);
            })
            ->when($orderBy, function ($query) use ($orderBy) {
                return $this->filter($query, $orderBy);
            })
            ->whereIn('article.sku_code_c', $data_ids)
            ->select(
                'customer_stock.*', // Select all columns from customer_stock
                'master_color.value as color_value' // Select master_color.value as color_value
            )
            ->get()
            ->groupBy('sku_code');

        $article = Product::whereIn('sku_code_c', $data_ids)->get()->groupBy('sku_code_c');

        $list = $datas->map(function ($data) use ($details, $article) {
            $article = $article[$data->sku_code_c][0];
            return [
                'sku' => $data->sku_code_c,
                'name' => $data->product_name_c,
                'image' => @$article->mainImageGeneric == null ? null : env('S3_STREAM_URL') . $article->mainImageGeneric->file_path,
                'flag' => @$article->flag() ?? null,
                'detail' => $details[$data->sku_code_c]->map(function ($detail) {
                    return [
                        'id' => $detail->id,
                        'sku' => $detail->article_id,
                        'product_variant' => $detail->color_value,
                        'product_size' => $detail->product_size,
                        'qty' => $detail->qty,
                        'update_date' => $detail->modified_date,
                    ];
                }),
            ];
        });

        $res = [
            'total_data' => $datas->total(),
            'size' => intval($datas->perPage()),
            'active_page' => $datas->currentPage(),
            'total_page' => $datas->lastPage(),
            'data' => $list
        ];

        return $this->sendSuccess('success', $res);
    }

    public function filter($query, $order_by)
    {
        switch ($order_by) {
            case 'highest':
            case 'lowest':
                $orderBy = $order_by == 'highest' ? 'desc' : 'asc';
                return $query->orderBy('customer_stock.qty', $orderBy);
            case 'alphabetA':
            case 'alphabetZ':
                $orderBy = $order_by == 'alphabetZ' ? 'desc' : 'asc';
                return $query->orderBy('customer_stock.product_name', $orderBy);
            case 'newest':
            case 'oldest':
                $orderBy = $order_by == 'newest' ? 'desc' : 'asc';
                return $query->orderBy('customer_stock.modified_date', $orderBy);
        }
    }

    private function category($query, $key)
    {
        switch ($key) {
            case 'bags':
            case 'footwear':
                return $query->where('article.lvl3_description', $key);
            case 'non-bags':
                return $query->whereNotIn('article.lvl3_description', ['bags', 'footwear']);
        }
        return $query;
    }

    public function getCustomerStore(Request $request, $customer_id = null)
    {
        $customer_id = $request->input('customer_id') ?? $request->user()->customer->customer_id;
        $stores = DB::table('customer_stock')
            ->leftJoin('customer_shipment', 'customer_stock.customer_shipment_id', '=', 'customer_shipment.customer_shipment_id')
            ->where('customer_stock.customer_id', '=', $customer_id)
            ->select('customer_stock.customer_shipment_id', 'customer_shipment.name')
            ->groupBy(DB::raw('1,2'))
            ->get();

        $list = [];
        foreach ($stores as $store) {
            $items = [
                'toko_id' => $store->customer_shipment_id,
                'name' => $store->name
            ];
            $list[] = $items;
        }

        return $this->sendSuccess('success', $list);
    }

    public function getHistoryUpdateStock(Request $request)
    {
        $customer_id = $request->input('customer_id') ?? $request->user()->customer->customer_id;
        // dd($customer_id);

        if ($customer_id != auth()->user()->reference_id) {
            return $this->sendError('Anda tidak dapat mengakses halaman ini');
        }

        $page = $request->input('page');
        $per_page = $request->input('per_page');
        $datas = CustomerStockHistory::where('customer_id', $customer_id)
            ->orderBy('created_date', 'desc')
            ->paginate($per_page, ['*'], 'page', $page);

        $list = [];
        foreach ($datas as $data) {
            $items = [
                'date' => $data->created_date,
                'name' => $data->customer->owner_name,
                'toko' => $data->customer_shipment->name,
                'file_name' => $data->file_name,
                'file_url' => env('S3_STREAM_URL') . $data->file_path,
                'size' => $this->formatSizeUnits($data->file_size)
            ];
            $list[] = $items;
        }

        $res = [
            'total_data' => $datas->total(),
            'size' => intval($datas->perPage()),
            'active_page' => $datas->currentPage(),
            'total_page' => $datas->lastPage(),
            'data' => $list
        ];

        return $this->sendSuccess('success', $res);
    }

    public function internalIndex(Request $request)
    {
        $salesId = auth()->user()->reference_id;
        $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
        $salesIdd = !in_array("0", $roles) ?
            array_map(
                function ($i) {
                    return $i->sales_id ?? '0';
                },
                $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())
            )
            : [];
        array_push($salesIdd, $salesId);
        $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
            ->pluck('customer_id')->all();
        $channel = $request->query('channel');
        
        if (strtolower($channel) == 'b2b') {
            $id = $request->query('customer_id');
            $ownerName = $request->query('owner_name');
            $instanceName = $request->query('instance_name');
            $salesName = $request->query('sales_name');
            $status = $request->query('status');
            $type = $request->query('type');
            $order_by = $request->query('order_by') != null ? $request->query('order_by') : 'newest';
            $limit = $request->query('limit') ?? 15;

            $datas = Customer::where('distribution_channel', 'B2B')
                ->withSum('orders', 'total_nett')
                ->when($id, function ($q) use ($id) {
                    $q->where('customer_id', '=', $id);
                })
                ->when($ownerName, function ($q) use ($ownerName) {
                    $q->where('owner_name', 'LIKE', '%' . $ownerName . '%');
                })
                ->when($instanceName, function ($q) use ($instanceName) {
                    $q->where('instance_name', 'LIKE', '%' . $instanceName . '%');
                })
                ->when($salesName, function ($q) use ($salesName) {
                    $q->whereHas('customer_sales_model.sales', function ($q) use ($salesName) {
                        $q->where('sales_name', 'LIKE', '%' . $salesName . '%');
                    });
                })
                ->when($status, function ($q) use ($status) {
                    $statusValues = explode(',', $status);
                    $q->where(function ($query) use ($statusValues) {
                        $query->where(function ($subQuery) use ($statusValues) {
                            foreach ($statusValues as $value) {
                                switch ($value) {
                                    case 'Baru':
                                        $subQuery->orWhere('status', 'Baru');
                                        break;
                                    case 'Partially Approved':
                                        $subQuery->orWhere('status', 'Partially Approved');
                                        break;
                                    case 'Perlu Revisi':
                                        $subQuery->orWhere('status', 'Perlu Revisi');
                                        break;
                                    case 'Ditolak':
                                        $subQuery->orWhere('status', 'Ditolak');
                                        break;
                                    case 'Disetujui':
                                        $subQuery->orWhere('status', 'Disetujui');
                                        break;
                                }
                            }
                        });
                    });
                })
                ->when($order_by, function ($q) use ($order_by) {
                    if ($order_by == 'highest') {
                        $q->orderBy('orders_sum_total_nett', 'desc');
                    } elseif ($order_by == 'lowest') {
                        $q->orderBy('orders_sum_total_nett', 'asc');
                    } elseif ($order_by == 'alphabetA') {
                        $q->orderBy('owner_name', 'asc');
                    } elseif ($order_by == 'alphabetZ') {
                        $q->orderBy('owner_name', 'desc');
                    } elseif ($order_by == 'oldest') {
                        $q->orderBy('created_date', 'asc');
                    } elseif ($order_by == 'newest') {
                        $q->orderBy('created_date', 'desc');
                    }
                })
                ->when(!in_array("0", $roles), function ($query) use ($customerIdList) {
                    $query->whereIn('customer_id', $customerIdList);
                })
                // ->where(function ($query) {
                //     $query->where(function ($subQuery) {
                //         $subQuery->where('is_rejected', '<>', 0)->orWhere('is_active', '<>', 0);
                //     });
                // })
                ->orderBy('created_date', 'desc')
                ->paginate($limit, ['*']);

            $response = [];
            foreach ($datas as $data) {
                $response[] = [
                    'id' => $data->customer_id,
                    'customer_id' => $data->customer_id,
                    'sap_id' => $data->sap_id,
                    'owner_name' => $data->owner_name,
                    'instance_name' => $data->instance_name,
                    'total' => empty($data->orders_sum_total_nett) ? (string) 0 : (string) $data->orders_sum_total_nett,
                    'registration_date' => Carbon::parse($data->created_date)->format('Y-m-d'),
                    'sales_name' => $data->sales->sales_name ?? null,
                    'status' => $data->status,
                    'sumber' => $data->sumber
                ];
            }

            $meta = [
                'current_page' => $datas->currentPage(),
                'last_page' => $datas->lastPage(),
                'per_page' => $datas->perPage(),
                'total' => $datas->total(),
            ];

            $latest = $datas->pluck('modified_date')->sortByDesc('modified_date')->first() ?? date('Y-m-d H:i:s');

            return $this->sendSuccess('Customers retrieved successfully.', ['latest_update' => $latest, 'data' => $response, 'meta' => $meta]);
        } else {
            if (!in_array("0", $roles)) {
                $customers = CustomerShipment::leftJoin('credit_limit', 'credit_limit.customer_external_id', '=', 'customer_shipment.customer_id')
                    ->whereIn('customer_id', $customerIdList);
            } else {
                $customers = CustomerShipment::leftJoin('credit_limit', 'credit_limit.customer_external_id', '=', 'customer_shipment.customer_id')
                    ->where('name', '!=', '');

            }

            return $this->cusRepo->getData($customers, $request);
        }
    }

    public function exportCustomers(Request $request)
    {
        try {
            $salesId = auth()->user()->reference_id;
            $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
            $salesIdd = !in_array("0", $roles) ?
                array_map(
                    function ($i) {
                        return $i->sales_id ?? '0';
                    },
                    $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())
                )
                : [];
            array_push($salesIdd, $salesId);
            $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                ->pluck('customer_id')->all();

            $id = $request->query('customer_id');
            $ownerName = $request->query('owner_name');
            $instanceName = $request->query('instance_name');
            $storeName = $request->query('store_name');
            $salesName = $request->query('sales_name');
            $status = $request->query('status');
            $type = $request->query('type');
            $channel = $request->query('channel');
            $limit = $request->query('credit_limit');


            $master_param = MasterParameter::where('group_key', 'CHANNEL_CODE')->where('value', 'WAB')->first();

            $rows = [];

            if (strtolower($channel) == 'b2b') {
                $order_by = $request->query('order_by') != null ? $request->query('order_by') : 'newest';
                $datas = Customer::where('distribution_channel', 'B2B')
                    ->withSum('orders', 'total_nett')
                    ->when($id, function ($q) use ($id) {
                        $q->where('customer_id', 'LIKE', '%' . $id . '%');
                    })
                    ->when($ownerName, function ($q) use ($ownerName) {
                        $q->where('owner_name', 'LIKE', '%' . $ownerName . '%');
                    })
                    ->when($instanceName, function ($q) use ($instanceName) {
                        $q->where('instance_name', 'LIKE', '%' . $instanceName . '%');
                    })
                    ->when($salesName, function ($q) use ($salesName) {
                        $q->whereHas('customer_sales_model.sales', function ($q) use ($salesName) {
                            $q->where('sales_name', 'LIKE', '%' . $salesName . '%');
                        });
                    })
                    ->when($status, function ($q) use ($status) {
                        $statusValues = explode(',', $status);
                        $q->where(function ($query) use ($statusValues) {
                            foreach ($statusValues as $value) {
                                switch ($value) {
                                    case 'freeze':
                                        $query->where('is_pending_payment', 1);
                                        break;

                                    case 'active':
                                        $query->where('is_active', 1);
                                        break;

                                    case 'non-active':
                                        $query->where('is_active', 0);
                                        break;
                                }
                            }
                        });
                    })
                    ->when($order_by, function ($q) use ($order_by) {
                        if ($order_by == 'highest') {
                            $q->orderBy('orders_sum_total_nett', 'desc');
                        } elseif ($order_by == 'lowest') {
                            $q->orderBy('orders_sum_total_nett', 'asc');
                        } elseif ($order_by == 'alphabetA') {
                            $q->orderBy('owner_name', 'asc');
                        } elseif ($order_by == 'alphabetZ') {
                            $q->orderBy('owner_name', 'desc');
                        } elseif ($order_by == 'oldest') {
                            $q->orderBy('created_date', 'asc');
                        } elseif ($order_by == 'newest') {
                            $q->orderBy('created_date', 'desc');
                        }
                    })
                    ->when(!in_array("0", $roles), function ($query) use ($customerIdList) {
                        $query->whereIn('customer_id', $customerIdList);
                    })
                    ->chunk(100, function ($datas) use (&$rows) {
                        foreach ($datas as $data) {
                            $rows[] = [
                                'customer_id' => $data->customer_id,
                                'owner_name' => $data->owner_name,
                                'instance_name' => $data->instance_name,
                                'total' => empty($data->orders_sum_total_nett) ? (string) 0 : (string) $data->orders_sum_total_nett,
                                'registration_date' => Carbon::parse($data->created_date)->format('Y-m-d'),
                                'sales_name' => $data->sales->sales_name ?? null,
                                'status' => $data->status
                            ];
                        }
                    });

                return $this->sendSuccess('Export Customers ' . strtoupper($channel) . ' Success', $rows);
            } else {
                $order_by = $request->query('order_by');
                $datas = CustomerShipment::
                    leftJoin('customers', 'customer_shipment.customer_id', '=', 'customers.customer_id')
                    ->leftJoin('customer_sales', 'customer_shipment.customer_id', '=', 'customer_sales.customer_id')
                    ->leftJoin('sales', 'customer_sales.sales_id', '=', 'sales.sales_id')
                    ->leftJoin('credit_limit', 'customers.customer_id', '=', 'credit_limit.customer_external_id')
                    ->leftJoin('order_header', function ($j) {
                        $j->on('customer_shipment.customer_shipment_id', '=', 'order_header.customer_shipment_id')
                            ->whereIn('order_header.order_status', ['Baru', 'Diproses', 'Siap Dikirim', 'Pembayaran', 'Dikirim', 'Diterima']);
                    })
                    ->select(
                        'customers.customer_id',
                        'customers.top_days',
                        'customers.owner_name',
                        'customer_shipment.name',
                        'sales.target_sales',
                        'credit_limit.credit_limit_used_percentage',
                        'sales.sales_name',
                        'customers.is_pending_payment',
                        'customers.is_active',
                        'customers.instance_name',
                        'customers.created_date as registered_date',
                        'customers.is_rejected',
                        'customers.is_pending_payment',
                        'customers.is_active'
                    )
                    ->selectRaw('COALESCE(SUM(order_header.total_nett),0) as total')
                    ->orderBy('customer_shipment.created_date', 'desc')
                    ->groupBy('customer_shipment.customer_shipment_id')
                    ->when($id, function ($q) use ($id) {
                        $q->where('customers.customer_id', 'LIKE', '%' . $id . '%');
                    })
                    ->when($ownerName, function ($q) use ($ownerName) {
                        $q->where('customers.owner_name', 'LIKE', '%' . $ownerName . '%');
                    })
                    ->when($storeName, function ($q) use ($storeName) {
                        $q->where('customer_shipment.name', 'LIKE', '%' . $storeName . '%');
                    })
                    ->when($salesName, function ($q) use ($salesName) {
                        $q->where('sales.sales_name', 'LIKE', '%' . $salesName . '%');
                    })
                    ->when($status, function ($q) use ($status) {
                        $statusValues = explode(',', $status);
                        $q->where(function ($query) use ($statusValues) {
                            foreach ($statusValues as $value) {
                                switch ($value) {
                                    case 'freeze':
                                        $query->orWhereHas('customer', function ($query) {
                                            $query->where('is_pending_payment', 1);
                                        });
                                        break;

                                    case 'active':
                                        $query->orWhereHas('customer', function ($query) {
                                            $query->where('is_active', 1);
                                        });
                                        break;

                                    case 'non-active':
                                        $query->orWhereHas('customer', function ($query) {
                                            $query->where('is_active', 0);
                                        });
                                        break;
                                }
                            }
                        });
                    })
                    ->when($type, function ($q) use ($type) {
                        if ($type == 'cash') {
                            $q->where(function ($q) {
                                $q->where('customers.top_days', 'LIKE', '0')
                                    ->orWhereNull('customers.top_days');
                            });
                        } elseif ($type == 'tempo') {
                            $q->where(function ($q) {
                                $q->where('customers.top_days', 'NOT LIKE', '0');
                                // ->orWhereNotNull('customers.top_days');
                            });
                        }
                    })
                    ->when($channel, function ($q) use ($channel, $master_param) {
                        $channel = strtoupper($channel);
                        if (strtolower($channel) == 'WAB') {
                            $q->where('customers.distribution_channel', 'LIKE', $master_param->key);
                        } elseif (strtolower($channel) == 'B2B') {
                            $q->where('customers.distribution_channel', 'LIKE', $channel)->where('is_verified', 1)->Where('is_active', 1)->orWhere('is_rejected', 1);
                        } else {
                            $q->where('customers.distribution_channel', 'LIKE', $channel);
                        }
                    })
                    ->when($limit, function ($q) use ($limit) {
                        $limit = array($limit);
                        foreach ($limit as $l) {
                            $q->whereBetween('credit_limit.credit_limit_used_percentage', explode('-', $l));
                        }
                    })
                    ->when($order_by, function ($q) use ($order_by) {
                        if ($order_by == 'highest') {
                            $q->orderBy('total', 'desc');
                        } elseif ($order_by == 'lowest') {
                            $q->orderBy('total', 'asc');
                        } elseif ($order_by == 'alphabetA') {
                            $q->orderBy('customers.owner_name', 'asc');
                        } elseif ($order_by == 'alphabetZ') {
                            $q->orderBy('customers.owner_name', 'desc');
                        }
                    });

                if (!in_array("0", $roles)) {
                    $datas->whereIn('customer_shipment.customer_id', $customerIdList);
                } else {
                    $datas->where('customer_shipment.name', '!=', '');
                }

                $spreadsheet = new Spreadsheet();
                $sheet = $spreadsheet->getActiveSheet();

                $headers = ['Customer ID', 'Type', 'Owner Name', 'Store Name', 'Target', 'Total', 'Credit Limit', 'Sales Name', 'Status'];
                $sheet->fromArray($headers, null, 'A1');

                $sheet->getStyle('A1:I1')->applyFromArray([
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => ['argb' => '808080']
                    ],
                    'font' => [
                        'bold' => true,
                        'color' => ['argb' => '000000']
                    ],
                ]);

                $rowIndex = 2;

                $datas->chunk(100, function ($datas) use (&$sheet, &$rowIndex) {
                    foreach ($datas as $data) {
                        $sheet->fromArray([
                            $data->customer_id,
                            $data->top_days == '0' || $data->top_days == null ? 'TOKO CASH' : 'TOKO TEMPO',
                            $data->owner_name ?? '-',
                            $data->name,
                            (string) $data->target_sales,
                            (string) $data->total,
                            $data->credit_limit_used_percentage . ' %' ?? '0 %',
                            $data->sales_name ?? '-',
                            $data->is_pending_payment == 1 ? 'FREEZE' : ($data->is_active == 1 ? 'ACTIVE' : 'NON ACTIVE')
                        ], null, "A$rowIndex");
                        $sheet->getStyle('E')->getNumberFormat()->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);
                        $rowIndex++;
                    }
                });

                $highestRow = $sheet->getHighestRow();
                $highestColumn = $sheet->getHighestColumn();
                $sheet->getStyle("A1:$highestColumn$highestRow")->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);

                foreach (range('A', $highestColumn) as $columnID) {
                    $sheet->getColumnDimension($columnID)->setAutoSize(true);
                }

                $writer = new Xls($spreadsheet);
                $response = new StreamedResponse(fn() => $writer->save('php://output'));

                $filename = "EXPORT " . strtoupper($channel) . " CUSTOMERS.xls";
                $response->headers->set('Content-Type', 'application/vnd.ms-excel');
                $response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');
                $response->headers->set('Cache-Control', 'max-age=0');

                return $response;
            }
        } catch (\Exception $e) {
            Log::info('Export Daftar Customer Failed : ' . $e->getMessage() . '. Line : ' . $e->getLine());
            return $this->sendError('Export Daftar Customer Failed');
        }
    }

    public function internalDetail(Request $request, $id)
    {
        return $this->cusRepo->getById($request, $id);
    }

    public function internalOrder(Request $request, $id)
    {
        return $this->cusRepo->getOrders($request, $id);
    }

    public function downloadStock($param)
    {
        $get = Storage::disk('s3')->get('public/stock/' . $param);
        return response()->stream($get, 200, ['Content-Type' => '*/*']);
    }

    public function uploadStockToko(Request $request){
        $custId = $request->customer_id = auth()->user()->customer->customer_id;

        $validator = Validator::make($request->all(), [
            'customer_id' => 'exists:customers,customer_id',
            'file' => ['required', 'string', 'regex:/\.xlsx?$/i'],
            'isChange' => 'required|boolean',
        ]);


        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {
            $s3 = \Storage::disk('s3');
            $client = $s3->getDriver()->getAdapter()->getClient();
            $expiry = "+10 minutes";

            if ($request->isChange == true) {
                $files = \Storage::disk('s3-public')->files('staging/stock-toko/' . $custId);
                if ($files)
                    \Storage::disk('s3-public')->delete($files);
            }

            $file = $request->file;

            $cmd = $client->getCommand('PutObject', [
                'Bucket' => env('AWS_BUCKET_STAGING', 'bucket-public-careorder'),
                'Key' => 'staging/' . 'stock-toko/' . $custId . '/' . $file,
            ]);

            $request = $client->createPresignedRequest($cmd, $expiry);
            $presignedUrl = (string) $request->getUri();

            return $this->sendSuccess('success', [
                "filepath" => $cmd["Key"],
                "s3_url" => $presignedUrl,
            ]);
        } catch (\Exception $e) {

            DB::rollback();
            return response()->json([
                'message' => 'Terjadi Kesalahan saat melakukan import',
            ], 500);
        }

    }

    public function unduhStockToko(Request $request){
        try {
            return Excel::download(new StockExport, 'upload_stock_template.xlsx', \Maatwebsite\Excel\Excel::XLSX);
        } catch (\Exception $e) {
            $errors = $e->getMessage();
            return $this->sendError($errors, 500);
        }
    }

    public function insertStockToko(Request $request){
        $custId = $request->customer_id = auth()->user()->customer->customer_id;

        $file = \Storage::disk('s3-public')->files('staging/stock-toko/' . $custId);
        $latestFile = collect($file)->sortByDesc(function ($file) {
            return \Storage::disk('s3-public')->lastModified($file);
        })->first();

        $tf_file = $this->fileTransfer($latestFile, 'stock-toko/' . $custId, true);
        if ($tf_file['error'] == true) {
            return $this->sendError($tf_file['message']);
        }

        $file_path = $tf_file['filepath'];
        $custId = $request->customer_id = auth()->user()->customer->customer_id;
        $cartImport = new StockTokoImport($custId, null, false);
        Excel::import($cartImport, $file_path, 's3');
        $cartData = $cartImport->result;
        $stock_data = $cartImport->data_stock;

        if ($file_path != '' && !empty($file_path)) {
            $files = \Storage::disk('s3')->files('stock-toko/' . $custId);
            if ($files) {
                \Storage::disk('s3')->delete($files);
            }
        }

        return $this->sendSuccess('data created successfully', []);
    }

    public function checkStockToko(Request $request){
        $tokoId = CustomerShipment::where('customer_id', auth()->user()->customer->customer_id)->first();
        if ($tokoId) {
            $product = CustomerStock::where('customer_shipment_id', $tokoId->customer_shipment_id)->exists();
            if($product){
                return $this->sendSuccess('stock toko tersedia', ["is_available" =>true]);
            }
            return $this->sendSuccess('stock toko tidak tersedia', ["is_available" => false]);
        }else{
            return $this->sendError('toko tidak ditemukan');
        }
    }



    public function uploadStock(Request $request)
    {
        $cus_id = $request->user()->customer->customer_id;
        $email = $request->user()->customer->email;
        $tokoId = $request->input('toko_id');
        Log::info($tokoId);
        if ($tokoId == null || $tokoId == 0) {
            return $this->sendError('no store selected', 404);
        }
        if ($request->has('file')) {
            // $file = Storage::disk('s3')->get($request->file);
            $tf_file = $this->fileTransfer($request->file, 'customer-stock');
            if ($tf_file['error'] == true) {
                return $this->sendError($tf_file['message']);
            }
            $mime_type = Storage::disk('s3')->mimeType($tf_file['filepath']);
            $size = Storage::disk('s3')->size($tf_file['filepath']);
            $extension = pathinfo($tf_file['filepath'])['extension'] ?? '';
            // $mimeTypes = ['text/csv','text/plain','application/csv', 'application/vnd.ms-excel','text/comma-separated-values','text/anytext','application/octet-stream','application/txt'];


            if (!in_array($extension, ['csv', 'ms-excel'])) {
                return $this->sendError('File must be in csv format', 404);
            }



            $adapter = Storage::disk('s3')->getAdapter();
            $client = $adapter->getClient();
            $client->registerStreamWrapper();
            $file_s3 = "s3://" . $adapter->getBucket() . '/' . $tf_file['filepath'];
            if ($this->validateCsv($file_s3) === false) {
                event(new GenericEvent($cus_id, ['error' => true, 'message' => 'Processing Failed', 'total_data' => 0], 'stock.upload'));
                return $this->sendError('CSV Format not matched', 400);
            }

            try {
                $rq = [
                    'customer_id' => $cus_id,
                    'email' => $email,
                    'toko_id' => $tokoId
                ];
                $rows = $this->countCsv($file_s3);
                event(new GenericEvent($cus_id, ['error' => false, 'message' => "Processing $rows Sku", 'total_data' => $rows], 'stock.upload'));
                $items_batch = 100;
                for ($i = 0; $i <= $rows; $i = $i + $items_batch) {
                    $chunk = $this->sliceCsv($file_s3, $i, $items_batch);
                    Log::info('CSV Chunk ' . $cus_id . ' :');
                    Log::info($chunk);
                    StockUploadQueue::dispatch($rq, $chunk, $i)->onConnection('redis')->onQueue('rqueue');
                    // array_push($queue_list, new StockUploadQueue($rq, $chunk, $i));
                }

                // if(count($queue_list) > 0){
                //     Bus::batch($queue_list)->then(function (Batch $batch) use($cus_id){
                //         event(new GenericEvent($cus_id, ['success' => true, 'message' => 'Processed'], 'stock.upload'));
                //     })->onConnection('redis')->onQueue('rqueue')->dispatch();
                // }



                DB::table('customer_stock_history')
                    ->insert([
                        'customer_id' => $cus_id,
                        'customer_shipment_id' => $tokoId,
                        'file_name' => pathinfo($tf_file['filepath'])['filename'] ?? '-',
                        'file_type' => $mime_type,
                        'file_path' => '/' . $tf_file['filepath'] ?? '-',
                        'file_size' => $size,
                        'created_date' => date('Y-m-d H:i:s'),
                        'created_by' => $email,
                        'modified_date' => date('Y-m-d H:i:s'),
                        'modified_by' => $email
                    ]);

                DB::commit();
                return $this->sendSuccess('queueing stock upload.');
            } catch (\Exception $e) {
                DB::rollBack();
                return $this->sendError($e->getMessage() . '. Line : ' . $e->getLine());
            }
        }
        return $this->sendError('no File attached', 404);

    }

    public function approveCustomer($id)
    {
        DB::beginTransaction();
        try {
            $customers = Customer::findOrFail($id);
            $customers->status = 'Disetujui';
            $customers->is_active = 1;
            $customers->is_verified = 1;
            $customers->is_revised = 0;
            $customers->is_rejected = 0;
            $customers->remarks = null;
            $customers->modified_by = Auth::user()->name;
            $customers->save();

            // $user = User::where('reference_id', $id)->first();
            // if ($user) {
            //     $user->is_active = 1;
            //     $user->save();
            // }

            CustomerSales::updateOrCreate(['customer_id' => $id], [
                'customer_id' => $id,
                'sales_id' => Auth::user()->reference_id,
                'created_by' => 'INTEGRATION',
                'modified_by' => 'INTEGRATION',
            ]);

            // $customerShipment = CustomerShipment::where('customer_id', $customers->customer_id)->first();
            // if (!$customerShipment) {
            //     return response()->json([
            //         'error' => true,
            //         'message' => 'Customer shipment data not found'
            //     ], 404);
            // }

            //Mail::to($customers->email)->send(new MailRegistration($customers));

            //get socialmedia
            // $socmed = new GetSocialsRepo();
            // $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
            // $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
            // $twitter = $socmed->getSocialMediaParameters('TWITTER');
            // $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
            // $youtube = $socmed->getSocialMediaParameters('YOUTUBE');
            // $support = $socmed->getSocialMediaParameters('SUPPORT');

            // kirim email
            // $param['data'] = [
            //     'name' => $customers->owner_name,
            //     'instance_name' => $customers->instance_name,
            //     'email' => $customers->email,
            //     'phone' => $customers->phone_number,
            //     'npwp' => $customers->npwp,
            //     'npwp_name' => $customers->npwp_name,
            //     'npwp_address' => $customers->npwp_address,
            //     'address' => $customerShipment->address,
            //     'subdistrict' => $customerShipment->subdistrict,
            //     'district' => $customerShipment->district,
            //     'city' => $customerShipment->city,
            //     'province' => $customerShipment->province,
            //     'zip_code' => $customerShipment->zip_code,

            //     //GET SOCIAL MEDIA ACCOUNTS
            //     'facebook' => $facebook,
            //     'twitter' => $twitter,
            //     'instagram' => $instagram,
            //     'support' => $support,
            //     'tiktok' => $tiktok,
            //     'youtube' => $youtube,
            // ];

            // MailSender::dispatch($customers->email, json_encode($param), 'mail_approve');

            $msg = 'Selamat! Data Anda telah berhasil diverifikasi. Kini Anda dapat menggunakan seluruh layanan yang tersedia.';
            $this->notifStore($id, 'Akun Anda Telah Diverifikasi', 'customer-internal', $msg, null, 'Info', 'B2B', 'success');
           
            DB::commit();

            return response()->json([
                'error' => false,
                'message' => 'Approved Successfully',
                'data' => $customers
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Customer approved failed: ' . $e->getMessage());
            return response()->json([
                'error' => true,
                'message' => 'Approved failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function revisionCustomer($id, Request $request)
    {
        if (!$request->filled('remarks')) {
            return response()->json([
                'error' => true,
                'message' => 'Remarks is required.'
            ], 400);
        }

        DB::beginTransaction();
        try {
            $customers = Customer::findOrFail($id);
            $customers->status = 'Perlu Revisi';
            $customers->is_revised = 1;
            $customers->remarks = $request->input('remarks');
            $customers->modified_by = Auth::user()->name;
            $customers->save();

            CustomerSales::updateOrCreate(['customer_id' => $id], [
                'customer_id' => $id,
                'sales_id' => Auth::user()->reference_id,
                'created_by' => 'INTEGRATION',
                'modified_by' => 'INTEGRATION',
            ]);

            $msg = 'Beberapa informasi dalam akun Anda perlu diperbaiki. Silakan periksa kembali dan lengkapi data sesuai catatan dari tim kami.';
            $this->notifStore($id, 'Perlu Revisi Data Akun', 'customer-internal', $msg, null, 'Info', 'B2B', 'info');
           
            DB::commit();

            return response()->json([
                'error' => false,
                'message' => 'Revised Successfully',
                'data' => $customers
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Customer revised failed: ' . $e->getMessage());
            return response()->json([
                'error' => true,
                'message' => 'Revised failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function rejectCustomer(Request $request, $id)
    {
        if (!$request->filled('remarks')) {
            return response()->json([
                'error' => true,
                'message' => 'Remarks is required.'
            ], 400);
        }

        DB::beginTransaction();
        try {

            $customers = Customer::find($id);
            if (!$customers) {
                return $this->sendError('Customer not found', 404);
            }

            $customers->status = 'Ditolak';
            $customers->is_rejected = 1;
            $customers->remarks = $request->input('remarks');
            $customers->modified_by = Auth::user()->name;
            $customers->save();

            CustomerSales::updateOrCreate(['customer_id' => $id], [
                'customer_id' => $id,
                'sales_id' => Auth::user()->reference_id,
                'created_by' => 'INTEGRATION',
                'modified_by' => 'INTEGRATION',
            ]);
    
            // CustomerHistory::create([
                // 'id' => Str::uuid()->toString(),
                // 'customer_id' => $customers->customer_id,
                // 'owner_name' => $customers->owner_name,
                // 'instance_name' => $customers->instance_name,
                // 'email' => $customers->email,
                // 'address' => $customers->address,
                // 'phone_number' => $customers->phone_number,
                // 'distribution_channel' => $customers->distribution_channel,
                // 'national_id' => $customers->national_id,
                // 'national_id_file' => $customers->national_id_file,
                // 'npwp' => $customers->npwp,
                // 'npwp_name' => $customers->npwp_name,
                // 'npwp_file' => $customers->npwp_file,
                // 'npwp_province' => $customers->npwp_province,
                // 'npwp_province_code' => $customers->npwp_province_code,
                // 'npwp_city' => $customers->npwp_city,
                // 'npwp_city_code' => $customers->npwp_city_code,
                // 'npwp_district' => $customers->npwp_district,
                // 'npwp_district_code' => $customers->npwp_district_code,
                // 'npwp_zip_code' => $customers->npwp_zip_code,
                // 'npwp_address' => $customers->npwp_address,
                // 'tax_type' => $customers->tax_type,
                // 'tax_invoice' => $customers->tax_invoice,
                // 'top' => $customers->top,
                // 'top_days' => $customers->top_days,
                // 'discount_percent' => $customers->discount_percent,
                // 'customer_type' => $customers->customer_type,
                // 'status' => $customers->status,
                // 'is_active' => $customers->is_active,
                // 'is_blocked' => $customers->is_blocked,
                // 'is_pending_payment' => $customers->is_pending_payment,
                // 'registered_sap_at' => $customers->registered_sap_at,
                // 'sumber' => $customers->sumber,
                // 'created_by' => Auth::user()->name,
                // 'modified_by' => Auth::user()->name,
                // 'rejection_reason' => $request->input('remarks'),
            // ]);

            //get socialmedia
            // $socmed = new GetSocialsRepo();
            // $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
            // $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
            // $twitter = $socmed->getSocialMediaParameters('TWITTER');
            // $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
            // $youtube = $socmed->getSocialMediaParameters('YOUTUBE');
            // $support = $socmed->getSocialMediaParameters('SUPPORT');

            // kirim email
            // $param['data'] = [
            //     'name' => $customers->owner_name,
            //     'reason' => $request->rejection_reason,

            //     //GET SOCIAL MEDIA ACCOUNTS
            //     'facebook' => $facebook,
            //     'twitter' => $twitter,
            //     'instagram' => $instagram,
            //     'support' => $support,
            //     'tiktok' => $tiktok,
            //     'youtube' => $youtube,
            // ];

            // MailSender::dispatch($customers->email, json_encode($param), 'mail_reject');

            $msg = 'Maaf, akun Anda tidak dapat diverifikasi karena data yang Anda berikan tidak sesuai. Silakan buat akun baru dengan informasi yang benar.';
            $this->notifStore($id, 'Akun Tidak Dapat Diverifikasi', 'customer-internal', $msg, null, 'Info', 'B2B', 'error');

            DB::commit();

            return response()->json([
                'error' => false,
                'message' => 'Rejected Successfully',
                'data' => $customers
            ], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Customer rejected failed: ' . $e->getMessage());
            return response()->json([
                'error' => true,
                'message' => 'Rejected failed: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function getlist_Customer(Request $request)
    {
        $page = $request->input('page');
        $per_page = $request->input('per_page');
        $sortir = $request->input('sortir');
        $search = $request->input('search');
        $sortby = 'cust.customer_id';
        if ($sortir == "newest") {
            $sortby = 'cust.created_date';
            $sort = 'desc';
        } elseif ($sortir == "oldest") {
            $sortby = 'cust.created_date';
            $sort = 'asc';
        } else {
            $sortby = 'cust.created_date';
            $sort = 'desc';
        }
        ;

        $customers = DB::table('customers as cust')
            ->leftJoin('user as u', 'u.reference_id', '=', 'cust.customer_id')
            ->select('cust.customer_id', 'cust.owner_name', 'cust.instance_name', 'cust.phone_number', 'cust.created_date')
            ->where(function ($query) use ($search) {
                $query->where('cust.owner_name', 'LIKE', '%' . $search . '%')
                    ->orWhere('cust.instance_name', 'LIKE', '%' . $search . '%')
                    ->orWhere('cust.phone_number', 'LIKE', '%' . $search . '%');
            })
            ->where('cust.is_verified', '=', 1)
            ->where('u.is_active', 1)
            ->where('cust.is_active', '=', 0)
            ->where('cust.is_rejected', '=', 0)
            ->orderBy($sortby, $sort)
            ->paginate($per_page, ['*'], 'page', $page);

        return response()->json([
            'data' => $customers,
            'pagination' => [
                'total' => $customers->total(),
                'per_page' => (int) $customers->perPage(),
                'current_page' => $customers->currentPage(),
                'last_page' => $customers->lastPage(),
                'from' => (int) $customers->firstItem(),
                'to' => (int) $customers->lastItem()
            ],
            'error' => 'false',
            'status' => 200
        ], 200);
    }

    public function getdetail_Customer($id)
    {
        $customer = DB::table('customers as cust')
            ->select(
                'cust.owner_name',
                'cust.instance_name',
                'cust.email',
                'cust.phone_number',
                'cs.address',
                'cs.province',
                'cs.city',
                'cs.district',
                'cs.zip_code',
                'cust.npwp_address',
                'cust.national_id',
                'cust.national_id_file',
                'cust.npwp',
                'cust.npwp_file',
                'cust.tax_invoice',
                'cust.created_date',
                'cust.tax_type',
                'cs.country_code',
                'cs.province_code',
                'cs.city_code',
                'cs.zone_code',
                'cust.npwp_name',
                'cs.district_code',
                'cs.subdistrict',
                'cs.zip_code_id'
            )
            ->join('customer_shipment as cs', 'cs.customer_id', '=', 'cust.customer_id')
            ->where('cust.customer_id', $id)
            ->first();

        if ($customer) {
            $envValue = env('S3_STREAM_URL');
            $customer->national_id_file = $envValue . $customer->national_id_file;
            $customer->npwp_file = $envValue . $customer->npwp_file;
            $customer->tax_invoice = $customer->tax_invoice == 'null' ? null : $customer->tax_invoice;
        }

        $city = DB::table('city')
            ->select('id as city_id')
            ->where('code', '=', $customer->city_code)
            ->first();

        // $subdistrict = ResellerMasterAddress::where('sub_district_code',$customer->zip_code_id)->first();
        // $customer->zip_code_id = $subdistrict->sub_district_code ?? null;

        if ($city) {
            $customer->city_id = $city->city_id;
        }

        return response()->json([
            'data' => $customer,
            'error' => 'false',
            'status' => 200
        ], 200);
    }

    public function updateStock(Request $request)
    {
        $customer = $request->user()->customer;
        $data = $request->input();

        // dd($data);
        try {
            DB::beginTransaction();
            foreach ($data as $item) {
                $customer_stock = DB::table('customer_stock')
                    ->where('id', $item['id'])
                    ->first();

                if ($customer_stock == null) {
                    DB::rollBack();
                    return $this->sendError('Customer stock data not found', 404);
                }

                DB::table('customer_stock')
                    ->where('id', '=', $item['id'])
                    ->update([
                        'qty' => $item['qty'],
                        'modified_date' => date('Y-m-d H:i:s'),
                        'modified_by' => $customer->email
                    ]);
            }
            DB::commit();
            return $this->sendSuccess('success update stock');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError($e->getMessage());
        }
    }

    public function downloadTransactionsCustomer(Request $request)
    {
        $salesId = auth()->user()->reference_id;
        Log::channel('stderr')->info($salesId);

        $status = $request->input('status');
        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');
        $order_no = $request->input('order_no');
        $owner_name = $request->input('owner_name');
        $store_type = $request->query('store_type');
        $store_name = $request->input('store_name');
        $ref_order = $request->input('ref_order');
        $sales_name = $request->input('sales_name');
        $channel = $request->query('channel') ?: 'WHOLESALES';
        //dd($channel);

        $data = [];
        $customerIdList = CustomerSales::where('customer_sales.sales_id', $salesId)
            ->pluck('customer_sales.customer_id');

        $Orders = Order::where('order_header.distribution_channel', $channel)
            ->leftJoin('order_detail', 'order_header.order_no', '=', 'order_detail.order_no')
            ->leftJoin('article', 'order_detail.article_id', '=', 'article.article')
            ->leftJoin('customers', 'order_header.customer_id', '=', "customers.customer_id")
            ->leftJoin('customer_shipment', 'customers.customer_id', '=', "customer_shipment.customer_id")
            ->leftJoin('customer_sales', 'order_header.customer_id', '=', 'customer_sales.customer_id')
            ->leftJoin('sales', 'customer_sales.sales_id', '=', 'sales.sales_id')

            ->when($store_type, function ($query) use ($store_type) {
                if ($store_type == 'cash') {
                    $query->where(function ($q) {
                        $q->where('customers.top', 'T001');
                    });
                } elseif ($store_type == 'tempo') {
                    $query->where(function ($q) {
                        $q->where('customers.top', '!=', 'T001');
                    });
                } else {

                }
            })

            ->when($status, function ($query) use ($status) {
                if ($status) {
                    $statusValues = explode(',', $status);

                    $query = $query->where(function ($q) use ($statusValues) {
                        foreach ($statusValues as $value) {
                            switch ($value) {
                                case 'freeze':
                                    $q->orWhereHas('customer', function ($query) {
                                        $query->where('is_pending_payment', 1);
                                    });
                                    break;

                                case 'active':
                                    $q->orWhereHas('customer', function ($query) {
                                        $query->where('is_active', 1);
                                    });
                                    break;

                                case 'non-active':
                                    $q->orWhereHas('customer', function ($query) {
                                        $query->where('is_active', 0);
                                    });
                                    break;
                            }
                        }
                    });
                }
            })

            ->when($dateFrom, function ($query) use ($dateFrom) {
                return $query->whereDate('order_header.created_date', ">=", $dateFrom);
            })
            ->when($dateTo, function ($query) use ($dateTo) {
                return $query->whereDate('order_header.created_date', "<=", $dateTo);
            })
            ->when($ref_order, function ($query) use ($ref_order) {
                return $query->where('order_header.sales_order_no', 'like', '%' . $ref_order . '%');
            })
            ->when($owner_name, function ($query) use ($owner_name) {
                return $query->where('customers.owner_name', 'like', '%' . $owner_name . '%');
            })
            ->when($order_no, function ($query) use ($order_no) {
                return $query->where('order_header.order_no', 'like', '%' . $order_no . '%');
            })
            ->when($store_name, function ($query) use ($store_name) {
                return $query->where('customer_shipment.name', 'like', '%' . $store_name . '%');
            })
            ->when($sales_name, function ($query) use ($sales_name) {
                return $query->where('sales.sales_name', 'like', '%' . $sales_name . '%');
            })
            ->whereIn('order_header.customer_id', $customerIdList)
            ->select('order_header.*', 'customers.top_days as top_days', 'customers.owner_name as owner_name', 'customer_shipment.name as store_name', 'sales.sales_name as sales_name')
            ->groupBy('order_header.order_no')
            ->orderBy('order_header.created_date', 'desc')
            ->get();

        $response = [];
        $i = 0;

        foreach ($Orders as $order) {
            foreach ($order->items as $item) {
                $qty_order = $item->qty;
                $discount = $order->total_discount;

                if ($order->delivery_order != null) {
                    $do_detail = DeliveryOrderDetail::where('delivery_order_no', $order->delivery_order->delivery_order_no)
                        ->where('article', $item->article_id)
                        ->first();

                    $qty_order = $order->delivery_order->good_issue_date != null ? $do_detail->issued_qty : $do_detail->qty;

                    $inv = Invoice::where('delivery_order_no', $order->delivery_order->delivery_order_no)
                        ->first();

                    if ($order->delivery_order->discount != null) {
                        $discount = $order->delivery_order->discount;
                    }

                    if ($inv != null) {
                        $inv_detail = InvoiceDetail::where('invoice_no', $inv->invoice_no)
                            ->where('article', $item->article_id)
                            ->first();

                        $discount = $inv_detail->gross_price * $inv_detail->discount_percent;
                    }
                }

                $article = Product::where('article', $item->article_id)->first();
                //dd($article);

                $response[$i] = [
                    'nama_akun' => $order->customer == null ? null : $order->customer->owner_name,
                    'nama_toko' => $order->store_name,
                    'tanggal_pesan' => Carbon::parse($order->created_date)->format('Y-m-d'),
                    'status_pesanan' => $order->order_status,
                    '#order' => $order->order_no,
                    '#ref_order' => $order->sales_order_no,
                    '#dn' => $order->delivery_order == null ? null : $order->delivery_order->delivery_order_no,
                    '#billing' => $order->invoice == null ? null : $order->invoice->invoice_no,
                    'article' => $item->article_id,
                    'article_description' => $item->product_name . ' ' . $item->product_variant . ' ' . $item->product_size,
                    'price' => (int) $item->price,
                    'qty_order' => (int) $qty_order,
                    'diskon' => (int) $discount,
                    'sub_total' => ((int) $qty_order * (int) $item->price) - $discount,
                    'item_product' => $article->lvl3_description,
                    'product_category' => $article->product_style,
                    'nama_sales' => $order->sales_name,
                    'tanggal_gi' => $order->delivery_order == null ? null : Carbon::parse($order->delivery_order->good_issue_date)->format('Y-m-d'),
                    'tanggal_billing' => $order->invoice == null ? null : Carbon::parse($order->invoice->billing_date)->format('Y-m-d')
                ];
                $i++;
            }
        }

        return $this->sendSuccess(null, $response);
    }

    function formatSizeUnits($bytes)
    {
        if ($bytes >= 1073741824) {
            $bytes = number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            $bytes = number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            $bytes = number_format($bytes / 1024, 2) . ' KB';
        } elseif ($bytes > 1) {
            $bytes = $bytes . ' Bytes';
        } elseif ($bytes == 1) {
            $bytes = $bytes . ' Byte';
        } else {
            $bytes = '0 Bytes';
        }

        return $bytes;
    }

    public function editKTP(UpdateKTPRequest $request, $customerId)
    {
        $request->validated();

        $customer = Customer::where('customer_id', $customerId)->first();
        $customerShipment = CustomerShipment::where('customer_id', $customerId)->first();
        $user = User::where('reference_id', $customerId)->first();

        if (!$customer || !$customerShipment) {
            return $this->sendError('customer not found', 400);
        }

        //upload file
        $photo_ktp = $request->file;
        $ktp_file_path = $this->fileTransfer($photo_ktp, 'ktp');
        if ($ktp_file_path['error'] == true) {
            return $this->sendError($ktp_file_path['message']);
        }

        // customer table update
        $customer->owner_name = $request->name ?: $customer->getOriginal('owner_name');
        $customer->instance_name = $request->instance ?: $customer->getOriginal('instance_name');
        $customer->email = $request->email ?: $customer->getOriginal('email');
        $customer->phone_number = $request->phone ?: $customer->getOriginal('phone_number');
        $customer->national_id = $request->ktp ?: $customer->getOriginal('national_id');
        $customer->national_id_file = '/' . $ktp_file_path['filepath'];

        // shipment table
        $customerShipment->address = $request->address ?: $customerShipment->getOriginal('address');
        $customerShipment->city = ResellerMasterAddress::where('city_code', $request->city_code)->first()->city_name;
        $customerShipment->city_code = $request->city_code ?: $customerShipment->getOriginal('city_code');
        $customerShipment->province = ResellerMasterAddress::where('region_code', $request->province_code)->first()->region_name;
        $customerShipment->province_code = $request->province_code ?: $customerShipment->getOriginal('province_code');
        $customerShipment->district = ResellerMasterAddress::where('district_code', $request->district_code)->first()->district_name;
        $customerShipment->district_code = $request->district_code ?: $customerShipment->getOriginal('district_code');
        $customerShipment->zip_code = ResellerMasterAddress::where('sub_district_code', $request->zip_code_id)->first()->zip_code;
        $customerShipment->zip_code_id = $request->zip_code_id ?: $customerShipment->getOriginal('zip_code_id');
        $customerShipment->subdistrict = ResellerMasterAddress::where('sub_district_code', $request->zip_code_id)->first()->sub_district_code;
        $customerShipment->name = $request->name ?: $customerShipment->getOriginal('name');

        // user table update
        $user->email = $request->email ?: $customer->getOriginal('email');

        $customer->update();
        $customerShipment->update();
        $user->update();

        return $this->sendSuccess('data has been updated');

    }

    public function editNPWP(UpdateNPWPRequest $request, $customerId)
    {

        $customer = Customer::where('customer_id', $customerId)->first();
        if (!$customer) {
            return $this->sendError('customer not found', 400);
        }

        //upload file
        $photo_npwp = $request->file;
        $npwp_file_path = $this->fileTransfer($photo_npwp, 'ktp');
        if ($npwp_file_path['error'] == true) {
            return $this->sendError($npwp_file_path['message']);
        }

        $customer->npwp_name = $request->name ?: $customer->getOriginal('npwp_name');
        $customer->npwp = $request->npwp ?: $customer->getOriginal('npwp');
        $customer->npwp_address = $request->address ?: $customer->getOriginal('npwp_address');
        $customer->tax_invoice = $request->tax_invoice ?? null;
        $customer->npwp_file = '/' . $npwp_file_path['filepath'];
        $customer->tax_type = $request->tax_type ?? null;

        $customer->save();

        return $this->sendSuccess('data has been updated');
    }

    public function exportCustomerOrders(Request $request, $customerId)
    {
        try {
            $filters = [
                'invoice_no'        => $request->input('invoice_no'),
                'order_no'          => $request->input('order_no'),
                'order_status'      => $request->input('order_status'),
                'invoice_status'    => $request->input('invoice_status'),
                'dateFrom'          => $request->input('date_from'),
                'dateTo'            => $request->input('date_to'),
            ];
            
            if (empty($filters['dateFrom']) || empty($filters['dateTo'])) {
                throw new Exception('Filter date diperlukan. Harap berikan parameter date_from dan date_to');
            }

            $start = new DateTime($filters['dateFrom']);
            $end = new DateTime($filters['dateTo']);
            if ($start->diff($end)->days > 90) {
                throw new Exception('Rentang tanggal tidak boleh melebihi 90 hari');
            }

            $query = DB::table('wholesales_download_csv')->where('customer_id', $customerId)
                ->when($filters['invoice_no'], fn($q) => $q->where('no_billing', 'like', "%{$filters['invoice_no']}%"))
                ->when($filters['order_no'], fn($q) => $q->where('no_order', 'like', "%{$filters['order_no']}%"))
                ->when($filters['dateFrom'], fn($q) => $q->whereDate('tanggal_pesan', ">=", $filters['dateFrom']))
                ->when($filters['dateTo'], fn($q) => $q->whereDate('tanggal_pesan', "<=", $filters['dateTo']))
                ->when($filters['order_status'], function ($q) use ($filters) {
                    $order_statuses = array_map(fn($s) => str_replace('_', ' ', $s), explode(',', $filters['order_status']));
                    return $q->whereIn('status_pesanan', $order_statuses);
                })
                ->when($filters['invoice_status'], function ($q) use ($filters) {
                    $invoice_statuses = array_map(fn($s) => str_replace('_', ' ', $s), explode(',', $filters['invoice_status']));
                    return $q->whereIn('status_tagihan', $invoice_statuses);
                })
                ->get()->toArray();

            $data = DownloadCSVResource::collection($query);

            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            $headers = [
                'Nama Akun', 'Nama Toko', 'Tanggal Pesan', 'Status Pesanan',
                'Order No', 'Ref Order', 'DN', 'Billing', 'Article',
                'Article Description', 'Price', 'Qty Order', 'Diskon',
                'Item Product', 'Product Category', 'Nama Sales',
                'Tanggal GI', 'Tanggal Billing', 'Gross', 'Nett'
            ];

            $sheet->fromArray([$headers], null, 'A1');

            $sheet->getStyle('A1:T1')->applyFromArray([
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => '808080']
                ],
                'font' => [
                    'bold' => true,
                    'color' => ['argb' => '000000']
                ],
            ]);

            $row = 2;
            foreach ($data as $item) {
                $sheet->fromArray([
                $item->nama_akun, $item->nama_toko, $item->tanggal_pesan, $item->status_pesanan,
                $item->no_order, $item->ref_order, $item->no_dn, $item->no_billing, '',
                $item->article_description, $item->price, $item->qty_order, $item->diskon,
                $item->item_product, $item->product_category, $item->nama_sales,
                $item->tanggal_gi, $item->tanggal_billing, $item->gross, $item->nett
                ], null, "A$row");

                $sheet->setCellValueExplicit("I$row", $item->article, DataType::TYPE_STRING);
                $row++;
            }

            $highestRow = $sheet->getHighestRow();
            $highestColumn = $sheet->getHighestColumn();
            $sheet->getStyle("A1:$highestColumn$highestRow")->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        'color' => ['argb' => '000000'],
                    ],
                ],
            ]);

            foreach (range('A', $highestColumn) as $columnID) {
                $sheet->getColumnDimension($columnID)->setAutoSize(true);
            }

            $writer = new Xls($spreadsheet);
            $response = new StreamedResponse(fn() => $writer->save('php://output'));

            $response->headers->set('Content-Type', 'application/vnd.ms-excel');
            $response->headers->set('Content-Disposition', 'attachment; filename="Export Wholesales Customer Orders.xls"');
            $response->headers->set('Cache-Control', 'max-age=0');

            return $response;

        } catch (Exception $e){
            Log::error($e->getMessage());
            return $this->sendError($e->getMessage(), 400);
        }
    }

    public function createCustomer(CreateCustomerRequest $request)
    {
        // do {
        //     $cust_id = 'TMP' . random_int(100000, 999999);
        // } while (Customer::where('customer_id', $cust_id)->exists());
                
        $status = $request->input('status');

        $customerDraft = Customer::with(['shipments', 'creditLimit'])
            ->where('created_by', Auth::user()->name)
            ->where('status', 'Draft')
            ->first();

        if ($customerDraft) {
            $customer = $customerDraft;
            $cust_id = $customerDraft->customer_id;
                
            $customerShipment = $customerDraft->shipments->first();
            $creditLimit = $customerDraft->creditLimit;
        } else {
            $customer = new Customer;
            $cust_id = 'TMP'.strval(random_int(100000, 999999));
            $customer->customer_id = $cust_id;

            $customerShipment = new CustomerShipment;
            $creditLimit = new CreditLimit();
        }

        // $npwp_file['filepath'] = null;
        
        // if ($status === 'Disetujui') {
            // $npwp = Storage::disk('s3-public')->files('staging/npwp-dev/' . $cust_id);
            // $latestFile = collect($npwp)->sortDesc()->first();
            $tf_file = $this->fileTransfer($request->npwp_file, 'npwp-dev', true);
            if ($tf_file['error'] == true) {
                return $this->sendError($tf_file['message']);
            }
        // }
            
        // $presignedUrl = null;
        // if ($request->hasFile('npwp_file') && $status === 'Disetujui') {
        //     try {
        //         $s3 = \Storage::disk('s3');
        //         $client = $s3->getDriver()->getAdapter()->getClient();
        //         $expiry = "+10 minutes";

        //         $files = \Storage::disk('s3-public')->files('staging/npwp-dev/' . $cust_id);
        //         if ($files)
        //             \Storage::disk('s3-public')->delete($files);

        //         // $envPrefix = app()->environment('production') ? '' : 'staging/';
        //         // $key = $envPrefix . 'npwp-dev/' . $cust_id . '/' . $file->getClientOriginalName();
        
        //         $file = $request->file('npwp_file');
        //         $cmd = $client->getCommand('PutObject', [
        //             'Bucket' => env('AWS_BUCKET_STAGING', 'bucket-public-careorder'),
        //             'Key' => 'staging/' . 'npwp-dev/' . $cust_id . '/' . $file->getClientOriginalName(),
        //         ]);

        //         $presignedRequest = $client->createPresignedRequest($cmd, $expiry);
        //         $presignedUrl = (string) $presignedRequest->getUri();
        //         $npwpFilePath = $cmd["Key"];
        //     } catch (\Exception $e) {
        //         return response()->json([
        //             'message' => 'Failed to upload file to S3',
        //         ], 500);
        //     }
        // }

        DB::beginTransaction();
        try {
            // insert customer
            $customer->owner_name              = $request->owner_name;
            $customer->instance_name           = $request->instance_name;
            $customer->email                   = $request->email;
            $customer->address                 = $request->shipment_address;
            $customer->phone_number            = $request->phone_number;
            $customer->distribution_channel    = 'B2B';
            // $customer->npwp                    = FormatHelper::formatNpwp($request->npwp);
            $customer->npwp                    = $request->npwp;
            $customer->npwp_name               = $request->npwp_name;
            $customer->npwp_file               = '/' . $tf_file['filepath'] ?? null;
            $customer->npwp_province           = $request->npwp_province;
            $customer->npwp_province_code      = $request->npwp_province_code;
            $customer->npwp_city               = $request->npwp_city;
            $customer->npwp_city_code          = $request->npwp_city_code;
            $customer->npwp_district           = $request->npwp_district;
            $customer->npwp_district_code      = $request->npwp_district_code;
            $customer->npwp_zip_code           = $request->npwp_zip_code;
            $customer->npwp_address            = $request->npwp_address;
            $customer->tax_type                = $request->tax_type;
            $customer->tax_invoice             = $request->tax_invoice;
            
            if ($request->top_days === '7 Days') {
                $customer->top = 'T008';
            } elseif ($request->top_days === '14 Days') {
                $customer->top = 'T015';
            } else {
                $customer->top = 'T001';
            }

            $customer->top_days                = $request->top_days;
            $customer->discount_percent        = $request->discount_percent;
            $customer->customer_type           = 'Z4';
            // $customers->customer_type           = 'W3';
            $customer->status                  = $status;
            $customer->is_active               = 1;
            $customer->is_verified             = 1;
            $customer->sumber                  = 'Oleh Admin';
            // $customer->remarks                 = 'Verifikasi akun Anda sedang diproses. Silakan tunggu beberapa saat.';
            $customer->created_by              = Auth::user()->name;
            $customer->modified_by             = Auth::user()->name;
            if ($customerDraft) {
                $customer->update();
            } else {
                $customer->save();
            }
            Log::info("Create Customer Successfully");

            // insert customer shipment
            $customerShipment->customer_id      = $cust_id;
            $customerShipment->name             = $request->owner_name;
            $customerShipment->address          = $request->shipment_address;
            $customerShipment->country          = 'Indonesia';
            $customerShipment->country_code     = 'ID';
            $customerShipment->province         = $request->shipment_province;  
            $customerShipment->province_code    = $request->shipment_province_code;
            $customerShipment->city             = $request->shipment_city;
            $customerShipment->city_code        = $request->shipment_city_code;        
            $customerShipment->district         = $request->shipment_district;
            $customerShipment->district_code    = $request->shipment_district_code;
            $customerShipment->subdistrict      = $request->shipment_subdistrict ?? '';
            // $customerShipment->subdistrict_code = $request->shipment_subdistrict_code ?? '-'; 
            $customerShipment->zip_code         = $request->shipment_zip_code;
            $customerShipment->phone_number     = $request->phone_number;

            $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->district . '%')->first();
            if (!$zone_code) {
                $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->city . '%')->first();
            }
            $customerShipment->zone_code        = $zone_code->zone_code ?? null;

            $customerShipment->created_by       = Auth::user()->name;
            $customerShipment->modified_by      = Auth::user()->name;
            $customerShipment->save();
            Log::info("Create Customer Shipment Successfully");

            // insert credit limit
            $creditLimit->customer_external_id          = $cust_id;
            $creditLimit->credit_limit                  = $request->credit_limit;
            $creditLimit->credit_limit_used             = 0;
            $creditLimit->credit_limit_remaining        = $request->credit_limit;
            $creditLimit->credit_limit_used_percentage  = 0;
            $creditLimit->currency                      = 'IDR';
            $creditLimit->created_by                    = Auth::user()->name;
            $creditLimit->modified_by                   = Auth::user()->name;                
            $creditLimit->save();
            Log::info("Create Credit Limit Successfully");

            if ($status === 'Disetujui') {
                // insert user
                $user                       = new User;
                $user->reference_id         = $cust_id;
                $user->reference_object     = 'customer';
                $user->username             = $request->email;
                $user->email                = $request->email;
                $user->name                 = $request->owner_name;
                $user->password             = Hash::make($request->password);
                $user->is_change_password   = 0;
                $user->is_active            = 1;

                // $token = Str::random(64);
                // $user->activation_token     = $token;
                // $user->activation_token_expired_at = now()->addDays(1);
                
                $user->created_by           = Auth::user()->name;
                $user->modified_by          = Auth::user()->name;
                $user->save();
                Log::info("Create User Successfully");

                CustomerSales::updateOrCreate(['customer_id' => $cust_id], [
                    'customer_id' => $cust_id,
                    'sales_id' => Auth::user()->reference_id,
                    'created_by' => 'INTEGRATION',
                    'modified_by' => 'INTEGRATION',
                ]);
                Log::info("Create Customer Sales Successfully");

                // get socialmedia
                // $socmed     = new GetSocialsRepo();
                // $instagram  = $socmed->getSocialMediaParameters('INSTAGRAM');
                // $facebook   = $socmed->getSocialMediaParameters('FACEBOOK');
                // $twitter    = $socmed->getSocialMediaParameters('TWITTER');
                // $tiktok     = $socmed->getSocialMediaParameters('TIKTOK');
                // $youtube    = $socmed->getSocialMediaParameters('YOUTUBE');

                // send email
                // $param['data'] = [
                //     'name'      => $request->owner_name,
                //     'email' => $request->email,

                    // get socialmedia account
                    // 'facebook'  => $facebook,
                    // 'twitter'   => $twitter,
                    // 'instagram' => $instagram,
                    // 'tiktok'    => $tiktok,
                    // 'youtube'   => $youtube,
                // ];
                
                // MailSender::dispatch($request->email, $param['data'], 'mail_verification', $token);
                
                // $sales_all = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'B2B')->first();
                // $msg = 'Terdapat pengajuan Customer Baru atas nama '.$user->name.', klik disini untuk melihat detail';
                // $this->notifStore($sales_all->value, 'CUSTOMER BARU', 'customer-internal', $msg, null, 'Info', 'B2B', 'info');
            }

            DB::commit();

            return $this->sendSuccess('Customer ' . ($status === 'Draft' ? 'saved as draft' : 'registered') . ' successfully', [
                'customer' => $customer,
                'customer_shipment' => $customerShipment,
                'user' => $user ?? null,
                'credit_limit' => $creditLimit,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError("An error occurred while creating the customer.", 500);
        }
    }

    public function getDraftCustomer(Request $request)
    {
        $customerDraft = Customer::with(['shipments', 'creditLimit'])
            ->where('created_by', $request->user()->name)
            ->where('status', 'Draft')
            ->first();

        if (!$customerDraft) {
            return response()->json([
                'draft' => false,
                'data' => null,
            ]);
        }

        return response()->json([
            'draft' => true,
            'data' => $customerDraft,
        ]);
    }

    public function updateCustomer(UpdateCustomerRequest $request, $id)
    {
        $customer = Customer::where('customer_id', $id)->first();
        if (!$customer) {
            return $this->sendError("Customer not found", 404);
        }

        $envValue = env('S3_STREAM_URL');

        if ($request->npwp_file !== $envValue . $customer->npwp_file) {
            // $npwp = Storage::disk('s3-public')->files('staging/npwp-dev/' . $id);
            // $latestFile = collect($npwp)->sortDesc()->first();
            if ($customer->npwp_file) {
                \Storage::disk('s3')->delete(ltrim($customer->npwp_file, '/'));
            }
            
            $tf_file = $this->fileTransfer($request->npwp_file, 'npwp-dev', true);
            if ($tf_file['error'] == true) {
                return $this->sendError($tf_file['message']);
            }
        }

        // if ($request->hasFile('npwp_file')) {
        //     try {
        //         $s3 = \Storage::disk('s3');
        //         $client = $s3->getDriver()->getAdapter()->getClient();
        //         $expiry = "+10 minutes";

        //         $files = \Storage::disk('s3-public')->files('staging/npwp-dev/' . $id);
        //         if ($files)
        //             \Storage::disk('s3-public')->delete($files);

        //         // $envPrefix = app()->environment('production') ? '' : 'staging/';
        //         // $key = $envPrefix . 'npwp-dev/' . $cust_id . '/' . $file->getClientOriginalName();
        
        //         $file = $request->file('npwp_file');
        //         $cmd = $client->getCommand('PutObject', [
        //             'Bucket' => env('AWS_BUCKET_STAGING', 'bucket-public-careorder'),
        //             'Key' => 'staging/' . 'npwp-dev/' . $id . '/' . $file->getClientOriginalName(),
        //         ]);

        //         $presignedRequest = $client->createPresignedRequest($cmd, $expiry);
        //         $presignedUrl = (string) $presignedRequest->getUri();
        //         $npwpFilePath = $cmd["Key"];
        //     } catch (\Exception $e) {
        //         return response()->json([
        //             'message' => 'Failed to upload file to S3',
        //         ], 500);
        //     }
        // }
        
        DB::beginTransaction();
        try {
            // update customer data
            $customer->sap_id              = $request->sap_id;
            $customer->owner_name          = $request->owner_name;
            $customer->instance_name       = $request->instance_name;
            $customer->email               = $request->email;
            $customer->address             = $request->shipment_address;
            $customer->phone_number        = $request->phone_number;
            // $customer->npwp                = FormatHelper::formatNpwp($request->npwp);
            $customer->npwp                = $request->npwp;
            $customer->npwp_name           = $request->npwp_name;

            if ($request->npwp_file !== $envValue . $customer->npwp_file) {
                $customer->npwp_file       = '/' . $tf_file['filepath'];
            }

            $customer->npwp_province       = $request->npwp_province;
            $customer->npwp_province_code  = $request->npwp_province_code;
            $customer->npwp_city           = $request->npwp_city;
            $customer->npwp_city_code      = $request->npwp_city_code;
            $customer->npwp_district       = $request->npwp_district;
            $customer->npwp_district_code  = $request->npwp_district_code;
            $customer->npwp_zip_code       = $request->npwp_zip_code;
            $customer->npwp_city           = $request->npwp_city;
            $customer->npwp_address        = $request->npwp_address;
            $customer->tax_type            = $request->tax_type;
            $customer->tax_invoice         = $request->tax_invoice;
            $customer->top_days            = $request->top_days;

            if ($customer->top_days === '7 Days') {
                $customer->top = 'T008';
            } elseif ($customer->top_days === '14 Days') {
                $customer->top = 'T015';
            } else {
                $customer->top = 'T001';
            }

            // if ($customer->status === 'Baru') {
            //     $customer->status = 'Perlu Revisi';
            // }
            
            $customer->discount_percent    = $request->discount_percent;
            $customer->modified_by         = Auth::user()->name;
            $customer->update();
            Log::info("Update Customer Successfully");

            // if($request->status === 'Approved') {
            //     CustomerSales::updateOrCreate(['customer_id' => $id], [
            //         'customer_id' => $id,
            //         'sales_id' => auth()->user()->sales->sales_id,
            //         'created_by' => 'INTEGRATION',
            //         'modified_by' => 'INTEGRATION',
            //     ]);
            // }

            // update customer shipment data
            $customerShipment = customerShipment::where('customer_id', $id)->first();
            if ($customerShipment) {
                $customerShipment->name             = $request->owner_name;
                $customerShipment->address          = $request->shipment_address;
                $customerShipment->province         = $request->shipment_province;  
                $customerShipment->province_code    = $request->shipment_province_code;
                $customerShipment->city             = $request->shipment_city;
                $customerShipment->city_code        = $request->shipment_city_code;
                $customerShipment->district         = $request->shipment_district;
                $customerShipment->district_code    = $request->shipment_district_code;
                // $customerShipment->subdistrict      = $request->shipment_subdistrict ?? '';
                // $customerShipment->subdistrict_code = $request->shipment_subdistrict_code ?? '';                
                $customerShipment->zip_code         = $request->shipment_zip_code;
                $customerShipment->phone_number     = $request->phone_number;

                $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->district . '%')->first();
                if (!$zone_code) {
                    $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->city . '%')->first();
                }
                $customerShipment->zone_code        = $zone_code->zone_code ?? null;

                $customerShipment->modified_by      = Auth::user()->name;
                $customerShipment->update();
            }
            Log::info("Update Customer Shipment Successfully");

            // update user data
            $user = User::where('reference_id', $id)->first();
            if ($user) {
                $user->username     = $request->email;
                $user->email        = $request->email;
                $user->name         = $request->owner_name;

                if ($request->filled('password')) {
                    $user->password = Hash::make($request->password);
                    $user->is_change_password = 1;
                }
                
                $user->modified_by  = Auth::user()->name;
                $user->update();
            }
            Log::info("Update User Successfully");

            // update credit limit
            $creditLimit = CreditLimit::where('customer_external_id', $id)->first();
            if ($creditLimit) {
                $creditLimit->credit_limit                  = $request->credit_limit;
                $creditLimit->credit_limit_remaining        = $creditLimit->credit_limit - $creditLimit->getOriginal('credit_limit_used');
                if ($creditLimit->credit_limit > 0) {
                    $creditLimit->credit_limit_used_percentage = round(($creditLimit->getOriginal('credit_limit_used') / $creditLimit->credit_limit) * 100, 2);
                } else {
                    $creditLimit->credit_limit_used_percentage = 0;
                }
                $creditLimit->modified_by                   = Auth::user()->name;  
                $creditLimit->update();
            }
            Log::info("Update Credit Limit Successfully");

            $socmed     = new GetSocialsRepo();
            $instagram  = $socmed->getSocialMediaParameters('INSTAGRAM');
            $facebook   = $socmed->getSocialMediaParameters('FACEBOOK');
            $twitter    = $socmed->getSocialMediaParameters('TWITTER');
            $tiktok     = $socmed->getSocialMediaParameters('TIKTOK');
            $youtube    = $socmed->getSocialMediaParameters('YOUTUBE');
            $support    = $socmed->getSocialMediaParameters('SUPPORT');

            $param['data'] = [
                'name'      => $request->owner_name ?? $customer->getOriginal('owner_name'),
                'facebook'  => $facebook,
                'twitter'   => $twitter,
                'instagram' => $instagram,
                'support'   => $support,
                'tiktok'    => $tiktok,
                'youtube'   => $youtube,
            ];
            
            MailSender::dispatch($request->email, $param['data'], 'mail_update', null);

            $sales_all = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'B2B')->first();
            $msg = 'Terdapat pengajuan Update Customer Baru atas nama '.$user->name.', klik disini untuk melihat detail';
            $this->notifStore($sales_all->value, 'UPDATE CUSTOMER BARU', 'customer-internal', $msg, null, 'Info', 'B2B', 'info');

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError("An error occurred while updating customer.", 500);
        }
                
        return $this->sendSuccess('Updated Successfully', [
            'customer' => $customer,
            'customer_shipment' => $customerShipment,
            'user' => $user,
            'credit_limit' => $creditLimit
        ]);
    }

    public function getCustomerB2b(Request $request)
    {
        $search = $request->input('search');
        $limit = $request->input('limit');

        // $salesId = auth()->user()->reference_id;
        // $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
        // $salesIdd = !in_array("0", $roles) ?
        //     array_map(
        //         function ($i) {
        //             return $i->sales_id ?? '0';
        //         },
        //         $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())
        //     )
        //     : [];
        // array_push($salesIdd, $salesId);
        // $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
        //     ->pluck('customer_id')->all();

        $datas = Customer::with('shipments')
            // ->whereIn('customer_id', $customerIdList)
            ->where('distribution_channel', 'B2B')
            ->where('status', 'Disetujui')
            ->when($search, function ($q) use ($search) {
                $q->where(function ($query) use ($search) {
                    $query->where('owner_name', 'LIKE', '%' . $search . '%')
                        ->orWhere('customer_id', 'LIKE', '%' . $search . '%');
                });
            })
            ->orderBy('owner_name', 'asc')
            ->when($limit, function ($q) use ($limit) {
                $q->limit((int) $limit);
            })
            ->get(['customer_id', 'owner_name', 'discount_percent']);

        return $this->sendSuccess('Customers retrieved successfully.', $datas);
    }
}