<?php

namespace App\Http\Controllers;

use App\Models\CustomerSales;
use App\Models\Proforma;
use App\Models\SalesAssignment;
use App\Models\User;
use App\Models\Invoice;
use App\Models\Customer;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Order;
use App\Models\OrderItem;
use App\Http\Resources\DashboardOrderResource;
use App\Http\Resources\SalesSummaryResource;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Support\Facades\DB as FacadesDB;

class DashboardController extends Controller
{

    public function summary(Request $req){
        try{
            $salesId = auth()->user()->reference_id;
            $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
            $salesIdd = !in_array("0", $roles) ? 
            array_map(function($i) {return $i->sales_id??'0';}, $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id',$salesId)->first())
            )
            : [];
            array_push($salesIdd, $salesId);
            $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                                ->pluck('customer_id')->all();
            $customeridStrings = implode(",", $customerIdList);

            $this->periodQuery($req, $start, $to);
            
            $typeSales = "WHOLESALES";
            if($req->query("type")=='b2b'){
                $typeSales = ["B2B", "W3", "RE", "RD"];
            } elseif (($req->query("type")=='wholesales')){
                $typeSales = ["WHOLESALES"];
            }
            $jsonTypeSales = json_encode($typeSales);

            if (!in_array("0", $roles) && $customeridStrings) {
                $query = DB::select('call get_summary_customers(?,?,?,?)', [$start, $to, $jsonTypeSales, $customeridStrings]);
            } else {
                $query = DB::select('call get_summary(?,?,?)', [$start, $to, $jsonTypeSales]);
            }
    
            return $this->sendSuccess('Success', SalesSummaryResource::make($query));
        } catch (Exception $e){
            return $this->sendError('Fail', 422, $e->getMessage());
        }

    }

    public function orders (Request $req)
    {
        $salesId = auth()->user()->reference_id;
        $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
        $salesIdd = !in_array("0", $roles) ? 
        array_map(function($i) {return $i->sales_id??'0';}, $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id',$salesId)->first())
        )
        : [];
        array_push($salesIdd, $salesId);
        $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                            ->pluck('customer_id')->all();
        $this->periodQuery($req, $start, $to);

        $userid = $req->user()->user_id;
        $user = User::select('u.user_id', 'u.email', 'r.name', 'bu.name', 'r.tier', 's.sales_id')
        ->from('user as u')
        ->leftJoin('user_matrix as um', 'u.user_id', '=', 'um.user_id')
        ->leftJoin('business_unit as bu', 'bu.id', '=', 'um.business_unit_id')
        ->leftJoin('roles as r', 'r.id', '=', 'um.roles_id')
        ->leftJoin('sales as s', 's.sales_id', '=', 'u.reference_id')
        ->where('u.user_id', $userid)
        ->where('u.reference_object', 'sales')
        ->first();

        $sales_id = [$user->sales_id];
        $tier = $user->tier;

        $typeSales = ["WHOLESALES"];
        if($req->query("type")=='b2b'){
            $typeSales = ["B2B", "W3", "RE", "RD"];
        } elseif (($req->query("type")=='wholesales')){
            $typeSales = ["WHOLESALES"];
        }

        $limit = 5;
        if ($req->has('limit')) {
            $limit = $req->query('limit');
        }
        $orders = DB::table('order_header as OH')
        ->leftJoin('customers as C', 'OH.customer_id', '=', 'C.customer_id')
        //->leftJoin('customer_shipment as CS', 'OH.customer_id', '=', 'CS.customer_id')
        ->leftJoin('delivery_order as DO', 'OH.sales_order_no', '=', 'DO.sales_order_no')
        ->leftJoin('invoice as I', 'OH.sales_order_no', '=', 'I.sales_order_no')
        ->whereDate('OH.created_date', '>=', $start)
        ->whereDate('OH.created_date', '<=', $to)
        ->whereIn('OH.distribution_channel', $typeSales)
        ->whereIn('OH.order_status', ['Baru', 'Diproses', 'Pembayaran', 'Siap Dikirim', 'Dikirim', 'Batal'])
        ->when(!in_array("0", $roles), function ($query) use ($customerIdList) {
            return $query->whereIn('OH.customer_id', $customerIdList);
        })
        ->select('OH.created_date', 'DO.created_date as do_date', 'OH.order_no','OH.total_nett','I.nett_price', 'C.owner_name', 'C.instance_name', 'OH.order_status')
        ->orderBy('OH.created_date', 'desc')
        ->limit($limit)
        ->get();

        return $this->sendSuccess('', DashboardOrderResource::collection($orders));
    }

    public function customers (Request $req)
    {
        $salesId = auth()->user()->reference_id;
        $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
        $salesIdd = !in_array("0", $roles) ? 
        array_map(function($i) {return $i->sales_id??'0';}, $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id',$salesId)->first())
        )
        : [];
        array_push($salesIdd, $salesId);
        $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                            ->pluck('customer_id')->all();
        $this->periodQuery($req, $start, $to);

        $userid = $req->user()->user_id;
        $user = User::select('u.user_id', 'u.email', 'r.name', 'bu.name', 'r.tier', 's.sales_id')
        ->from('user as u')
        ->leftJoin('user_matrix as um', 'u.user_id', '=', 'um.user_id')
        ->leftJoin('business_unit as bu', 'bu.id', '=', 'um.business_unit_id')
        ->leftJoin('roles as r', 'r.id', '=', 'um.roles_id')
        ->leftJoin('sales as s', 's.sales_id', '=', 'u.reference_id')
        ->where('u.user_id', $userid)
        ->where('u.reference_object', 'sales')
        ->first();

        $sales_id = [$user->sales_id];
        $tier = $user->tier;

        $typeSales = ["WHOLESALES"];
        if($req->query("type")=='b2b'){
            $typeSales = ["B2B", "W3", "RE", "RD"];
            $limit = 5;
            if ($req->has('limit')) {
                $limit = $req->query('limit');
            }
            $customers = json_decode(DB::table('order_header as OH')
            ->leftJoin('customers as C', 'OH.customer_id', '=', 'C.customer_id')
            ->leftJoin('credit_limit as CL','OH.customer_id', '=', 'CL.customer_external_id')
            ->leftJoin('delivery_order as DO', 'OH.sales_order_no', '=', 'DO.sales_order_no')
            ->whereDate('OH.created_date', '>=', $start)
            ->whereDate('OH.created_date', '<=', $to)  
            ->whereIn('OH.distribution_channel', $typeSales)
            ->whereIn('OH.order_status', ['Baru', 'Diproses', 'Pembayaran', 'Siap Dikirim', 'Dikirim'])
            ->when(!in_array("0", $roles), function ($query) use ($customerIdList) {
                return $query->whereIn('OH.customer_id', $customerIdList);
            })
            ->select('C.owner_name as customer_name','C.customer_id',DB::raw('SUM(OH.total) AS total_transaksi'),'CL.credit_limit_used as credit_used','OH.created_date as last_date_order')
            ->groupBy('OH.customer_id')
            ->orderBy('OH.created_date', 'desc')
            ->limit($limit)
            ->get(),true);

            $res = array_map(function ($i) use ($customers) {
                
                $customer = Customer::where('customer_id', $i['customer_id'])->first();
                if ($customer) {
                    //proforma
                    $proformaSum = 0;
                    $orderPayments = Order::where('customer_id', $i['customer_id'])->where('order_status','Pembayaran')->get();
                    foreach ($orderPayments as $orderPayment) {
                        $proformaSum += Proforma::where('po_no', $orderPayment->order_no)->sum('nett_price');
                    }
                    return [
                        ...$i,
                        'running_invoice' => (string)(Invoice::where('customer_id', $i['customer_id'])
                            ->whereIn('status', ['BELUM DIBAYAR', 'BELUM LUNAS'])
                            ->sum('nett_price') + $proformaSum),
                        'register_date' => Carbon::parse($customer->created_date)->format('Y-m-d')
                    ];
                } else {
                    return [
                        ...$i,
                        'running_invoice' => (string)0,
                        'register_date' => '-' 
                    ];
                }
            }, $customers);

            return $this->sendSuccess('', $res);

        } elseif (($req->query("type")=='wholesales')){
            $typeSales = ["WHOLESALES"];
        }

        $limit = 5;
        if ($req->has('limit')) {
            $limit = $req->query('limit');
        }
        $customers = json_decode(DB::table('order_header as OH')
        ->leftJoin('customers as C', 'OH.customer_id', '=', 'C.customer_id')
        ->leftJoin('credit_limit as CL','OH.customer_id', '=', 'CL.customer_external_id')
        ->leftJoin('delivery_order as DO', 'OH.sales_order_no', '=', 'DO.sales_order_no')
        ->leftJoin('invoice as I', function ($join) 
        {
            $join->on('DO.delivery_order_no', '=', 'I.delivery_order_no');
            $join->on('I.status', '!=', DB::RAW("'PAID'"));
        })
        ->whereDate('OH.created_date', '>=', $start)
        ->whereDate('OH.created_date', '<=', $to)  
        ->whereIn('OH.distribution_channel', $typeSales)
        ->whereIn('OH.order_status', ['Baru', 'Diproses', 'Pembayaran', 'Siap Dikirim', 'Dikirim'])
        ->when(!in_array("0", $roles), function ($query) use ($customerIdList) {
            return $query->whereIn('OH.customer_id', $customerIdList);
        })
        ->select('C.owner_name as customer_name', 'C.instance_name as store_name','C.customer_id',DB::raw('SUM(OH.total) AS total_transaksi'),'CL.credit_limit_used as credit_used','OH.created_date as last_date_order','CL.credit_limit_used_percentage as credit_limit')
        ->groupBy('OH.customer_id')
        ->orderBy('OH.created_date', 'desc')
        ->limit($limit)
        ->get(),true);

      
        $res = array_map(fn ($i) => [...$i, 'last_date_order' => Order::select('created_date')->where('customer_id', $i['customer_id'])->orderBy('created_date', 'desc')->first()->created_date], $customers);

        usort($res, function ($a, $b) {
            return strtotime($b['last_date_order']) <=> strtotime($a['last_date_order']);
        });
        
        return $this->sendSuccess('', $res);
    }

    public function periodQuery($req, &$start, &$to)
    {
        if ($req->has('due_date_from') && $req->has('due_date_to')) {
            $start = $req->query('due_date_from');
            $to = $req->query('due_date_to');
        } elseif ($req->has('due_date_from') && !$req->has('due_date_to')) {
            $start = $req->query('due_date_from');
            $to = now()->format('Y-m-d');
        } elseif (!$req->has('due_date_from') && $req->has('due_date_to')) {
            $start = now()->format('Y-m-d');
            $to = $req->query('due_date_to');
        } else {
            $start = now()->subDays(30)->format('Y-m-d');
            $to = now()->format('Y-m-d');
        }
    }
}