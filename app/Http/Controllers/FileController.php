<?php

namespace App\Http\Controllers;

use App\Http\Requests\FileRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Validator;
use Webpatser\Uuid\Uuid;


class FileController extends Controller
{
    public function presignedUpload(Request $request)
    {
        $s3 = Storage::disk('s3');
        $client = $s3->getDriver()->getAdapter()->getClient();
        $expiry = "+10 minutes";
        
        if (!isset(pathinfo($request->name)['extension'])) {
            return $this->sendError('File extension is required!');
        }

        $cmd = $client->getCommand('PutObject', [
            'Bucket' => env('AWS_BUCKET_STAGING', 'bucket-public-careorder'),
            'Key' => 'staging/' . $request->name        
        ]);

        $presignedRequest  = $client->createPresignedRequest($cmd, $expiry);
        $presignedUrl = (string)$presignedRequest->getUri();

        return $this->sendSuccess('success', [
            "filepath" => $cmd["Key"],
            "s3_url" => $presignedUrl
        ]);
    }

    public function fileUpload(FileRequest $request)
    {
        $request->validated();
        
        //  check if the module exists
        // $userId = auth()->user()->user_id;

            // extension checking
            $file = $request->file('file');
            $originalName = $file->getClientOriginalName();
            $whitelist = array('jpg', 'png', 'gif', 'jpeg', 'svg', 'txt','jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx', 'mp3', 'mp4', 'avi', 'json', 'pdf');
            $explFilename = explode('.', $originalName);
            $fileExtension = strtolower(end($explFilename));
            if(!in_array($fileExtension, $whitelist))
            {
                return $this->sendError($fileExtension.' is invalid file extension. Allowed extensions: '.implode(', ', $whitelist));
            }

            $content = file_get_contents($file);
            $filename = (string) Uuid::generate(4).".".$fileExtension;
            $file_path = '/'.$request->input('module').'/'.$filename;
            Storage::disk('s3')->put(substr($file_path,1), $content);

            
            return $this->sendSuccess('success', ['path' => $file_path]);
    }

}
