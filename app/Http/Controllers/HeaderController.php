<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Interfaces\ProductInterface;

class HeaderController extends Controller
{
    private $menu;

    public function __construct(ProductInterface $prodInterface)
    {
        $this->menu = $prodInterface;
    }

    public function getMenus(Request $request)
    {
        // var_dump(auth()->user());die();
        $custId = isset(auth()->user()->customer->customer_id) ? auth()->user()->customer->customer_id : null;
        $cid = $custId == null ? 99999 : $custId;
        return $this->menu->getMenus($request, $cid);
    }
}
