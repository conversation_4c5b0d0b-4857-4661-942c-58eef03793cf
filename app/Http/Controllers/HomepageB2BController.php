<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductSku;
use App\Helpers\RestHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class HomepageB2BController extends Controller
{
  public function productb2b(Request $request)
  {

    $cte = DB::table('article_skus')
      ->select('sku_code_c', DB::raw('SUM(stock) as total_stock'))
      ->groupBy('sku_code_c');

    $productb2b = Product::where('is_b2b', 1)
      ->joinSub($cte, 'stock_data', function ($join) {
        $join->on('article.sku_code_c', '=', 'stock_data.sku_code_c');
      })
      ->select('article.*', 'stock_data.total_stock')
      ->groupBy('article.sku_code_c')
      ->orderBy('stock_data.total_stock', 'desc')

      ->where('b2b_published_date', '<=', now()->format('Y-m-d'))
      ->doesnthave('exclusion')
      ->orderBy('b2b_published_date', 'desc')
      ->orderBy('product_name_c', 'asc')
      ->when($request->query('isPopular'), function ($query) {
        // If is_popular exists
        $popularArticles = DB::table('order_detail')
          ->select('article_id')
          ->groupBy('article_id')
          ->orderByRaw('COUNT(*) DESC')
          ->limit(10)
          ->pluck('article_id');

        return $query->whereIn('article', $popularArticles);
      }, function ($query) {
        return $query->where('is_custom_logo', 1)
          ->orWhere('is_custom_size', 1);
      })
      ->with(['price', 'mainImageGeneric'])
      ->limit(6)->get();

    $datas = [];
    $i = 0;
    // $stock_collection = RestHelper::stockCache($productb2b->pluck('article')->toArray());
    foreach ($productb2b as $item) {
      $flag = $this->getFlag($item->article);
      $image = isset($item->mainImageGeneric->file_path) ? env('S3_STREAM_URL') . $item->mainImageGeneric->file_path : 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp';
      $datas[$i] = [
        'product_name_c' => $item->product_name_c,
        'product_description' => $item->product_description,
        'article_description' => $item->article_description,
        'sku_code_c' => $item->sku_code_c,
        'amount' => $item->price ? $item->price->amount : 0,
        'currency' => $item->price ? $item->price->currency : 0,
        'flag' => $flag,
        'main_image' => $image,
        'stock' => (int) ProductSku::where('sku_code_c', $item->sku_code_c)->sum('stock') ?? 0
      ];

      $i++;
    }

    $datas['data'] = collect(data_get($datas, 'data'))->values()->all();

    $res = [
      'error' => 'false',
      'status' => 200,
      'data' => $datas
    ];

    return $res;
  }

  public function getFlag($article)
  {
    $flag = [];
    $date = now()->format('Y-m-d');
    $articleData = DB::table('article')->Where('article', $article)->first();
    if (!empty($articleData)) {
      if ($articleData->transfer_date <= $date && $articleData->expired_date >= $date) {
        array_push($flag, 'NEW');
      }
      if ($articleData->is_custom_logo) {
        array_push($flag, 'CUSTOM');
      }
      array_push($flag, $articleData->lvl4_description);
    }
    return $flag;
  }

  public function faq()
  {
    $faq = DB::table('faq')
      ->select('faq.*')
      ->where('faq.is_active', 1)
      ->orderBy('sequence_no', 'asc')
      ->get();

    return response()->json([
      'data' => $faq,
      'error' => 'false',
      'status' => 200
    ], 200);
  }

  public function tnc()
  {
    $termsAndConditions = DB::table('tnc_registration')
      ->select('id', 'section_name', 'sequence_no', 'value')
      ->where('is_active', '=', 1)
      ->orderBy('sequence_no', 'asc')
      ->get()
      ->toArray();

    return $this->sendSuccess('GET Terms and Conditions Success', $termsAndConditions);
  }
}