<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Invoice;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class HomepageWholesalesController extends Controller
{
    public function jumlah_product()
    {
        date_default_timezone_set('Asia/Jakarta');
        $total_product = fn(array $arr) => array_sum(array_map(function ($i) {
            $sum = is_null($i['delivery_order']) ? 
            array_reduce($i['items'], function($total, $order_items)
            {
                return $total += $order_items['qty'] ?? 0;
            }, 0) :
            array_reduce($i['delivery_order']['items'], function($total, $order_items)
            {
                return $total += is_null($order_items['issued_qty']) ? $order_items['qty'] : $order_items['issued_qty'];
            }, 0);
            return $sum;
        }, $arr));
        $customer_id = auth()->user()->customer->customer_id;

        $from = new Carbon('first day of this month');
        $to = new Carbon('last day of this month');
        $from_compare = new Carbon('first day of last month');
        $to_compare = new Carbon('last day of last month');

        $qty_current = $total_product(Order::with(['items', 'delivery_order.items'])
        ->where('customer_id', $customer_id)
        ->whereIn('order_status', ['Baru','Pembayaran', 'Diproses', 'Siap Dikirim', 'Dikirim', 'Selesai'])
        ->whereBetween('created_date',[$from, $to])->get()->toArray());

        $qty_last = $total_product(Order::with(['items', 'delivery_order.items'])
        ->where('customer_id', $customer_id)
        ->whereIn('order_status', ['Baru', 'Pembayaran', 'Diproses', 'Siap Dikirim', 'Dikirim', 'Selesai'])
        ->whereBetween('created_date',[$from_compare, $to_compare])->get()->toArray());

        $qty_compare = $qty_current - $qty_last;
        if ($qty_last != 0) {
            $qty_percentage = round((abs($qty_compare) / $qty_last) * 100, 0);
            $qty_percentage = min($qty_percentage, 100);
        } else {
            $qty_percentage = $qty_current > 0 ? 100 : 0;
        }
        $increase = $qty_compare > 0 ? true : false;

        return response()->json([
            'qty_berjalan' => $qty_current,
            'qty_sebelumnya'=> $qty_last,
            'qty_compare'=> abs($qty_compare),
            'qty_percentage' => $qty_percentage,
            'is_increase' => $increase,
            'error' => false,
            'status' => 200
        ], 200);
    }
    
    public function jumlah_transaksi()
    {   
        $customer_id = auth()->user()->customer->customer_id;     
        $from = new Carbon('first day of this month');
        $to = new Carbon('last day of this month');
        $from_compare = new Carbon('first day of last month');
        $to_compare = new Carbon('last day of last month');

        $sub_total_now = Order::where('customer_id', $customer_id)
        ->whereIn('order_status', ['Baru', 'Pembayaran', 'Diproses', 'Siap Dikirim', 'Dikirim', 'Selesai'])
        ->whereBetween('created_date',[$from, $to])->count();

        $sub_total_last = Order::where('customer_id', $customer_id)
        ->whereIn('order_status', ['Baru', 'Pembayaran', 'Diproses', 'Siap Dikirim', 'Dikirim', 'Selesai'])
        ->whereBetween('created_date',[$from_compare, $to_compare])->count();

        $sub_total_compare = $sub_total_now - $sub_total_last;
        if ($sub_total_last != 0) {
            $sub_total_percentage = round((abs($sub_total_compare) / $sub_total_last) * 100, 0);
            $sub_total_percentage = min($sub_total_percentage, 100);
        } else {
            $sub_total_percentage = $sub_total_now > 0 ? 100 : 0;
        }
        $increase = $sub_total_compare > 0 ? true : false;

        return response()->json([
            'sub_total_berjalan' => $sub_total_now,
            'sub_total_sebelumnya' => $sub_total_last,
            'sub_total_compare' => abs($sub_total_compare),
            'sub_total_percentage' => $sub_total_percentage,
            'is_increase' => $increase,
            'error' => false,
            'status' => 200
        ], 200);
    }

    public function total_transaksi()
    {
        $total_product = fn(array $arr) => array_sum(array_map(function ($i) {
            return is_null($i['invoice']) ? $i['total_nett'] : $i['invoice']['nett_price'];
        }, $arr));
        
        $customer_id = auth()->user()->customer->customer_id;     
        $from = new Carbon('first day of this month');
        $to = new Carbon('last day of this month');
        $from_compare = new Carbon('first day of last month');
        $to_compare = new Carbon('last day of last month');

        $sub_total_now = $total_product(Order::with('invoice')
        ->where('customer_id', $customer_id)
        ->whereIn('order_status', ['Baru', 'Pembayaran', 'Diproses', 'Siap Dikirim', 'Dikirim', 'Selesai'])
        ->whereBetween('created_date',[$from, $to])->get()->toArray());

        $sub_total_last = $total_product(Order::with('invoice')
        ->where('customer_id', $customer_id)
        ->whereIn('order_status', ['Baru', 'Pembayaran', 'Diproses', 'Siap Dikirim', 'Dikirim', 'Selesai'])
        ->whereBetween('created_date',[$from_compare, $to_compare])->get()->toArray());

        $sub_total_compare = $sub_total_now - $sub_total_last;
        if ($sub_total_last != 0) {
            $sub_total_percentage = round((abs($sub_total_compare) / $sub_total_last) * 100, 0);
            $sub_total_percentage = min($sub_total_percentage, 100);
        } else {
            $sub_total_percentage = $sub_total_now > 0 ? 100 : 0;
        }
        $increase = $sub_total_compare > 0 ? true : false;

        return response()->json([
            'sub_total_berjalan' => $sub_total_now,
            'sub_total_sebelumnya' => $sub_total_last,
            'sub_total_compare' => abs($sub_total_compare),
            'sub_total_percentage' => $sub_total_percentage,
            'is_increase' => $increase,
            'error' => false,
            'status' => 200
        ], 200);
    }

    public function pesanan_berjalan()
    {
        $customer_id = auth()->user()->customer->customer_id;
        $data_pesanan = DB::table('order_header as oh')
            ->select('oh.created_date', 'oh.order_no', 'oh.total_nett','oh.total','oh.total_discount', 'oh.order_status as status', 'oh.order_group_id'
            // ,
            //  DB::raw("CASE
            //              WHEN i.status = 'PAID' THEN 'Lunas'
            //              ELSE 'Belum Lunas'
            //              END AS payment_status")
                         )
            ->leftJoin('delivery_order as do', 'oh.sales_order_no', '=', 'do.sales_order_no')
            // ->leftJoin('invoice as i', 'do.delivery_order_no', '=', 'i.delivery_order_no')
            ->where('oh.customer_id', $customer_id)
            ->orderBy('oh.created_date', 'desc')
            ->limit(5)
            ->get();

            foreach ($data_pesanan as $item) {
                $psn_berjalan = DB::table('order_header as oh')
                ->select('oh.created_date', 'oh.order_no', 'oh.total_nett', 'oh.order_status', 'i.status as statusku', 'i.invoice_no', 'i.nett_price','oh.sales_order_no')
                ->leftJoin('invoice as i', 'oh.sales_order_no', '=', 'i.sales_order_no')
                ->where('oh.order_no', $item->order_no)
                ->orderBy('oh.created_date', 'asc')
                ->limit(3)
                ->get();

                $invoice_no = Invoice::select('invoice_no')->find($item->order_no)->invoice_no ?? '-';
                $end_total = $item->total - $item->total_discount;
                // dd($item->status);
                // dd($psn_berjalan);
                
                // if($item->status == 'Menunggu Konfirmasi' || $item->status == 'Pending' || $item->status == 'On Hold' || $item->status == 'Diproses' || $item->status == 'Siap Dikirim'  || $item->status == 'Baru'){
                //     $stts_payment = '-';
                // }
                // if(isset($psn_berjalan->statusku) == 'PAID'){
                //     $stts_payment = 'Lunas';
                // }
                // if(isset($psn_berjalan->statusku) == 'UNPAID'){
                //     $stts_payment = 'Belum Lunas';
                // }

                if ($item->status == 'Menunggu Konfirmasi' || $item->status == 'Pending' || $item->status == 'On Hold' || $item->status == 'Diproses' || $item->status == 'Siap Dikirim' || $item->status == 'Baru') {
                    $stts_payment = '-';
                } else {
                    $stts_payment = '-';
                    foreach ($psn_berjalan as $psn) {
                        if ($psn->statusku === 'LUNAS') {
                            $end_total = $psn->nett_price;
                            $invoice_no = $psn->invoice_no;
                            $stts_payment = 'Lunas';
                            break;
                        } elseif ($psn->statusku === 'BELUM LUNAS') {
                            $invoice_no = $psn->invoice_no;
                            $end_total = $psn->nett_price;
                            $stts_payment = 'Belum Lunas';
                            break;
                        } elseif ($psn->statusku === 'BELUM DIBAYAR') {
                            $end_total = $psn->nett_price;
                            $invoice_no = $psn->invoice_no;
                        $stts_payment = 'Belum Dibayar';
                        break;
                    }
                    }
                }

                $end_total = $item->total - $item->total_discount;
                $pesanan_berjalan[] = [
                    'created_date' => $item->created_date,
                    'order_no' => $item->order_no,
                    'total' => $end_total,
                    'order_status' => $item->status == 'Dikirim' && ($stts_payment == 'Belum Dibayar' || $stts_payment == 'Belum Lunas') ? 'Pembayaran' : $item->status,
                    'order_group_id' => $item->order_group_id,
                    'payment_status' => $stts_payment ?? '-',
                    'invoice_no' => $invoice_no

                ];
            }

        return response()->json([
            'pesanan_berjalan'=>  isset($pesanan_berjalan) ? $pesanan_berjalan : [],
            'error' => 'false',
            'status' => 200
        ], 200);
    }
    
    public function get_banner()
    {
        $banners = DB::table('banner as b')
        ->select('b.*')
        ->whereNotNull('sequence_no')
        ->where('start_period', '<=', Carbon::now())
        ->where('end_period', '>=', Carbon::now())
        ->orderBy('sequence_no', 'ASC')->limit(5)
        ->get()->toArray();

        $banners = array_map(function ($b) {
            $b->thumbnail = filter_var($b->thumbnail, FILTER_VALIDATE_URL) ? $b->thumbnail : env('S3_STREAM_URL').$b->thumbnail;
            return $b;
        }, $banners);

        return response()->json([
            'banners'=>  $banners,
            'error' => 'false',
            'status' => 200
        ], 200);
    }
}