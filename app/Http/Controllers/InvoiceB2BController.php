<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Order;
use App\Models\Invoice;
use App\Models\Product;
use App\Models\Customer;
use App\Models\CompanyBank;
use App\Models\ImageGeneric;
use Illuminate\Http\Request;
use App\Models\DeliveryOrder;
use App\Models\InvoiceDetail;
use Illuminate\Http\Response;
use App\Models\CustomerShipment;
use Illuminate\Support\Facades\DB;
use App\Models\DeliveryOrderDetail;
use App\Models\OrderCustom;
use App\Models\OrderCustomAttachment;
use App\Models\OrderItem;
use App\Models\Proforma;
use Illuminate\Support\Facades\Log;
use App\Repositories\InvoiceRepository;

class InvoiceB2BController extends Controller
{
    public function index(Request $request)
    {
        $cari = $request->input('cari');
        $date_from = $request->input('date_from');
        $date_to = $request->input('date_to');
        $status = $request->input('status');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 12);
        $customer_id = auth()->user()->customer->customer_id;

        $data = [];
        $invoices = DB::table('order_header')
            ->leftJoin('invoice as i_bill',function ($j){
                $j->on('i_bill.sales_order_no','=','order_header.sales_order_no')
                    ->where('i_bill.invoice_type','BILLING');
            })
            ->leftJoin('invoice as i_dp',function ($j){
                $j->on('i_dp.sales_order_no','=','order_header.sales_order_no')
                    ->where('i_dp.invoice_type','DOWN PAYMENT');
            })
            ->leftJoin('proforma_invoice as pi', 'pi.sales_order_no', '=', 'order_header.sales_order_no')
            ->leftJoin('delivery_order', 'delivery_order.sales_order_no', '=', 'order_header.sales_order_no')
            ->select('order_header.dp_amount','i_bill.invoice_no as bill_inv_no','i_dp.invoice_no as dp_invoice_no',
            'order_header.created_date','order_header.bill_to','i_bill.due_date as bill_due_date', 'i_dp.due_date as dp_due_date',
            'i_bill.gross_price as bill_total','i_dp.gross_price as dp_total','order_header.sales_name','i_bill.status as bill_status',
            'i_dp.status as dp_status','order_header.order_no','order_header.total_nett as oh_total_nett','delivery_order.created_date as do_date',
            'pi.sales_order_no as pi_so', 'pi.due_date as pi_due_date', 'pi.gross_price as pi_total')
            ->selectRaw('CASE WHEN ((order_header.dp_amount IS NULL OR order_header.dp_amount = 0)
                        AND i_bill.invoice_no IS NULL) OR (order_header.dp_amount > 0 AND i_dp.invoice_no
                        IS NULL) THEN "BELUM DIBAYAR" ELSE NULL END AS oh_inv_status')
            ->whereIn('order_header.distribution_channel', ['B2B', 'W3', 'RE', 'RD'],)
            ->whereNotIn('order_header.order_status',['Menunggu Konfirmasi', 'Menunggu Verifikasi', 'Baru', 'Batal', 'On Hold', 'Pending', 'Cancel', 'Reject'])
            ->when($cari, function ($q) use ($cari){
                $q->where(function($q) use ($cari){
                    $q->where('i_bill.invoice_no','LIKE','%'.$cari.'%')
                    ->orWhere('i_dp.invoice_no','LIKE','%'.$cari.'%')
                    ->orWhere('order_header.order_no','LIKE','%'.$cari.'%');
                });
            })
            // ->when($status, function ($q) use ($status,$customer_id){
            //     if ($status == 'Semua') {
            //         $q->where(function ($q){
            //             $q->whereIn('i_bill.status',['Belum Dibayar','Lunas'])
            //             ->orWhereIn('i_dp.status',['Belum Dibayar','DP Lunas']);
            //         });
            //     } else {
            //         $q->where(function ($q) use ($status){
            //             $q->where('i_bill.status',$status)
            //             ->orWhere('i_dp.status',$status);
            //         });
            //     }
            // if ($status == 'Semua' || $status == 'Belum Dibayar') {
            //     $ohs = Order::where('customer_id',$customer_id)->pluck('order_no')->toArray();
            //     $q->whereIn('order_header.order_no',$ohs)
            //         ->where(function ($q){
            //         $q->whereIn('i_bill.status',['Belum Dibayar','Lunas'])
            //         ->orWhereIn('i_dp.status',['Belum Dibayar','DP Lunas']);
            //     });
            // } else {
            //     $ohs = Order::where('customer_id',$customer_id)->pluck('order_no')->toArray();
            //     $q->whereNotIn('order_header.order_no',$ohs)
            //         ->where(function ($q) use($status,$ohs){
            //         $q->where('i_bill.status',$status)
            //         ->orWhere('i_dp.status',$status);
            //     });
            // }
            // })
            ->when($date_from, function ($q) use ($date_from){
                $order_date_from = Carbon::parse($date_from)->format('Y-m-d H:i:s');
                $q->whereDate('delivery_order.created_date','>=',$order_date_from);
            })
            ->when($date_to, function ($q) use ($date_to){
                $order_date_to = Carbon::parse($date_to)->format('Y-m-d H:i:s');
                $q->whereDate('delivery_order.created_date','<=',$order_date_to);
            })
            ->where('order_header.customer_id', $customer_id)
            ->where(function ($q) {
                $q->whereNotNull('i_bill.invoice_no')
                    ->orWhereNotNull('i_dp.invoice_no')
                    ->orWhereNotNull('pi.sales_order_no');
            })
            ->orderBy('delivery_order.created_date', 'desc')
            // ->paginate(6,['*'],'page',$page);
            ->chunk(100, function ($dd) use (&$data) {
                foreach ($dd as $d) {
                    $data[] = $d;
                }
            });
        
        $data = collect($data);
        if ($status != null) {
            $status = strtolower($status);
            if ($status == 'lunas') {
                $data = $data->filter(function ($data) use ($status) {
                    $lowerSBill = strtolower($data->bill_status);
                    $lowerSDP = strtolower($data->dp_status);

                    return $lowerSBill == $status
                        || $lowerSDP == $status;
                })->values()->all();

                $data = collect($data);
            }
            if ($status == 'belum dibayar') {
                $data = $data->filter(function ($data) use ($status) {
                    $lowerSBill = strtolower($data->bill_status);
                    $lowerSDP = strtolower($data->dp_status);
                    $lowerOHI = strtolower($data->oh_inv_status);

                    return $lowerSBill == $status
                        || $lowerSDP == $status
                        || $lowerOHI == $status;
                })->values()->all();

                $data = collect($data);
            } else {
                $data = collect($data);
            }
        }
        
        $data = $data->transform(function ($data) {
            // if ($data->dp_invoice_no != null || $data->bill_inv_no != null) {
                $invoice_no = $data->bill_inv_no != null ? $data->bill_inv_no : ($data->dp_invoice_no ?? $data->pi_so);
                $i_model = Invoice::where('invoice_no', $invoice_no)->first();
                if ($i_model) {
                    $product_name = $i_model ? InvoiceDetail::where('invoice_no', $i_model->invoice_no)->first() : '';
                    $image = ImageGeneric::where('sku_code_c', @$i_model->detail[0]->product->sku_code_c)
                        ->where('is_main_image', 1)->where('sequence_no', 0)->first();
                } else {
                    $pi_model = Proforma::where('sales_order_no', $invoice_no)->first();
                    $product_name = $i_model ? InvoiceDetail::where('proforma_invoice_id', $pi_model->id)->first() : '';
                    $image = ImageGeneric::where('sku_code_c', @$pi_model->detail[0]->product->sku_code_c)
                        ->where('is_main_image', 1)->where('sequence_no', 0)->first();
                }

                return [
                    'invoice_no' => $invoice_no,
                    'product_name' => $product_name ? $product_name->product_name : '-',
                    'order_date' => Carbon::parse($data->created_date)->format('Y-m-d H:i:s'),
                    'do_date' => Carbon::parse($data->do_date)->format('Y-m-d H:i:s'),
                    'invoice_status' => $data->bill_inv_no != null ? $data->bill_status : (($data->dp_status == 'LUNAS' ? 'DP ' . $data->dp_status : $data->dp_status) ?? 'BELUM DIBAYAR'),
                    'jatuh_tempo' => $data->bill_inv_no != null ? $data->bill_due_date : ($data->dp_due_date ?? $data->pi_due_date),
                    'total_produk' => count($i_model->detail) ?? count($pi_model->detail),
                    'image' => @$image->file_path == null ? null : env('S3_STREAM_URL') . $image->file_path,
                    'total_tagihan' => $data->bill_inv_no != null ? $data->bill_total : ($data->dp_total ?? $data->pi_total)
                ];
            // } else {
            //     $invoice_no = $data->order_no;
            //     $oh_model = Order::where('order_no', $invoice_no)->first();
            //     $oc_exs = OrderCustom::where('reference_id', $oh_model->order_no)->first();
            //     if ($oc_exs) {
            //         $article = Product::where('article', $oc_exs->article_id)->first();
            //         $orderItem = $oc_exs;
            //         $productName = $article->product_name_c;
            //     } else {
            //         $orderItem = OrderItem::where('order_no', $oh_model->order_no)->first();
            //         $article = Product::where('article', $orderItem->article_id)->first();
            //         $productName = $orderItem->product_name;
            //     }

            //     $image = ImageGeneric::where('sku_code_c', $article->sku_code_c)
            //         ->where('is_main_image', 1)
            //         ->where('sequence_no', 0)
            //         ->first();

            //     return [
            //         'invoice_no' => $invoice_no,
            //         'product_name' => $productName ?? '-',
            //         'order_date' => Carbon::parse($data->order_date)->format('Y-m-d H:i:s'),
            //         'invoice_status' => 'Belum Dibayar',
            //         'jatuh_tempo' => null,
            //         'total_produk' => optional($oh_model->items)->count() ?? 0,
            //         'image' => optional($image)->file_path ? env('S3_STREAM_URL') . $image->file_path : null,
            //         'total_tagihan' => $data->oh_total_nett
            //     ];
            // }
        });

        $paginated_data = collect($data)->paginate($perPage, null, $page, 'page');

        $data_invoice = [
            'total_data' => $paginated_data->total(),
            'size' => intval($paginated_data->perPage()),
            'active_page' => $paginated_data->currentPage(),
            'total_page' => $paginated_data->lastPage(),
            'data' => $paginated_data->items()
        ];

        // $i = 0;
        // foreach ($invoices as $invoice) {
        //     if ($invoice->dp_invoice_no != null || $invoice->bill_inv_no != null) {
        //         $invoice_no = $invoice->bill_inv_no != null ? $invoice->bill_inv_no : $invoice->dp_invoice_no;
        //         $i_model = Invoice::where('invoice_no',$invoice_no)->first();
        //         $image = ImageGeneric::where('sku_code_c',@$i_model->detail[0]->product->sku_code_c)
        //                             ->where('is_main_image',1)->where('sequence_no',0)->first();

        //         $data_invoice['data'][$i] = [
        //             'invoice_no' => $invoice_no,
        //             'order_date' => Carbon::parse($invoice->order_date)->format('Y-m-d H:i:s'),
        //             'invoice_status' => $invoice->bill_inv_no != null ? $invoice->bill_status : ($invoice->dp_status == 'LUNAS' ? 'DP '.$invoice->dp_status : $invoice->dp_status),
        //             'jatuh_tempo' => $invoice->bill_inv_no != null ? $invoice->bill_due_date : $invoice->dp_due_date,
        //             'total_produk' => count($i_model->detail),
        //             'image' => @$image->file_path == null ? null : env('S3_STREAM_URL').$image->file_path,
        //             'total_tagihan' => $invoice->bill_inv_no != null ? $invoice->bill_total : $invoice->dp_total
        //         ];
        //         $i++;
        //     }  else {
        //         $invoice_no = $invoice->order_no;
        //         $oh_model = Order::where('order_no',$invoice_no)->first();
        //         $image = ImageGeneric::where('sku_code_c',@$oh_model->items[0]->product->sku_code_c)
        //                             ->where('is_main_image',1)->where('sequence_no',0)->first();

        //         $data_invoice['data'][$i] = [
        //             'invoice_no' => $invoice_no,
        //             'order_date' => Carbon::parse($invoice->order_date)->format('Y-m-d H:i:s'),
        //             'invoice_status' => 'Belum Dibayar',
        //             'jatuh_tempo' => null,
        //             'total_produk' => count($oh_model->items),
        //             'image' => @$image->file_path == null ? null : env('S3_STREAM_URL').$image->file_path,
        //             'total_tagihan' =>$invoice->oh_total_nett
        //         ];
        //         $i++;
        //     }
        // }

        return $this->sendSuccess(null, $data_invoice);

    }

    public function getInvoiceDetail(Request $request, $invoice_no)
    {
        $page = $request->input('page');

        $invoice = Invoice::where('invoice_no', $invoice_no)->first();

        $order = Order::find($invoice->order_no);

        $delivery_order = DeliveryOrder::where('delivery_order_no', $invoice->delivery_order_no)->first();

        $invoice_details = InvoiceDetail::where('invoice_no', $invoice_no)
            ->paginate(10, ['*'], 'page', $page);

        $item = [];

        for ($i = 0; $i < count($invoice_details); $i++) {
            $item[$i] = [
                'nama_barang' => $invoice_details[$i]->product_name,
                'sku' => $invoice_details[$i]->article,
                'ukuran' => $invoice_details[$i]->product_size,
                'kuantiti' => !is_null($invoice_details[$i]->qty) ? $invoice_details[$i]->qty : 'Tidak Ada',
                'harga_satuan' => $invoice_details[$i]->price,
                'sub-total' => $invoice_details[$i]->gross_price,
                'potongan_harga' => (int) ($invoice_details[$i]->gross_price * $invoice_details[$i]->discount_percent / 100),
                'nett' => $invoice_details[$i]->nett_price
            ];
        }

        // $dn = [];
        // $gi_date = [];
        // $no_resi = [];
        // for ($i=0; $i < count($delivery_order) ; $i++) { 
        //     $dn[$i] = $delivery_order[$i]->delivery_order_no;
        //     $gi_date[$i] = $delivery_order[$i]->good_issue_date;
        //     $no_resi[$i] = $delivery_order[$i]->delivery_no;
        // }

        $data_invoice = [
            'order_status' => $order->order_status,
            'order' => $invoice->order_no,
            'ref' => $order->sales_order_no,
            'DN' => $invoice->delivery_order_no ?? null,
            'billing' => $invoice->invoice_no,
            'tanggal_pesan' => Carbon::parse($order->created_date)->format('Y-m-d H:i:s'),
            'tanggal_gi' => $delivery_order->good_issue_date ?? null,
            'jatuh_tempo_tagihan' => $invoice->due_date,
            'pengiriman' => [
                'name' => $order->bill_to, #cust
                'phone_number' => $order->bill_to_phone_number,
                'email' => $order->bill_to_email, #cust
                'alamat' => $order->ship_to_address #cshipment
            ],
            'payment_status' => $invoice->status,
            'sub_total' => $invoice->gross_price,
            'diskon' => $order->total_discount, #orderheader
            'total' => $invoice->nett_price,
            'item' => $item
        ];

        $response = [
            'total_data' => count($invoice_details),
            'size' => intval($invoice_details->perPage()),
            'active_page' => $invoice_details->currentPage(),
            'total_page' => $invoice_details->lastPage(),
            'invoice' => $data_invoice
        ];

        return $this->sendSuccess(null, $response);

    }

    public function paymentInvoiceDetail($invoice_no)
    {
        $invoice = Invoice::find($invoice_no);

        if ($invoice == null) {
            return $this->sendError('Invoice not found', Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $banks = CompanyBank::where('is_active', 1)->get();

        $data_bank = [];
        $i = 0;
        foreach ($banks as $bank) {
            $data_bank[$i] = [
                'logo' => isset($bank->logo) ? env('S3_STREAM_URL') . $bank->logo : null,
                'bank_name' => $bank->bank_name,
                'bank_account_name' => $bank->bank_account_name,
                'bank_account_no' => $bank->bank_account_no
            ];
            $i++;
        }

        $data = [
            'invoice_no' => $invoice_no,
            'no_order' => $invoice->order_no,
            'total_payment' => $invoice->invoice_type == 'BILLING' ? $invoice->nett_price : $invoice->due_payment,
            'bank_transfer' => $data_bank
        ];

        return $this->sendSuccess(null, $data);
    }

    public function downloadInvoiceDetailDP($invoice_no, Request $request)
    {
        $invoice = Invoice::find($invoice_no);

        $order = Order::where('sales_order_no', $invoice->sales_order_no)->first();

        if ($invoice == null) {
            return $this->sendError('Invoice not found', Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $customer = Customer::find($invoice->customer_id);

        $invoice_details = InvoiceDetail::where('invoice_no', $invoice_no)->get();

        $ocs_exs = OrderCustom::where('reference_id', $order->order_no)->exists();

        $item = [];

        $total_qty = 0;
        $total_bruto = 0;
        $total_netto = 0;

        for ($i = 0; $i < count($invoice_details); $i++) {
            $article = Product::where('article', $invoice_details[$i]->article)->first();
            $custom_price = 0;
            if ($ocs_exs == true) {
                $ocs = OrderCustom::where('reference_id', $order->order_no)->where('sku', $article->sku_code_c)->pluck('id')->toArray();
                $custom_price = OrderCustomAttachment::where('order_custom_id', $ocs)->sum('custom_price');
            }
            $item[$i] = [
                'article_no' => $invoice_details[$i]->article,
                'description' => $invoice_details[$i]->article_description,
                // 'unit_price' => $invoice_details[$i]->price+$custom_price,
                'unit_price' => $invoice_details[$i]->price,
                'qty' => !is_null($invoice_details[$i]->qty) ? $invoice_details[$i]->qty : 'Tidak Ada',
                'bruto' => $invoice_details[$i]->gross_price,
                'disc' => $invoice_details[$i]->discount_percent,
                'netto' => $invoice_details[$i]->nett_price
            ];
            $total_qty += !is_null($invoice_details[$i]->qty) ? $invoice_details[$i]->qty : 0;
            $total_bruto += $invoice_details[$i]->gross_price;
            $total_netto += $invoice_details[$i]->nett_price;
        }

        $data = [
            'customer_number' => $invoice->customer_id,
            'customer_name' => $invoice->customer_name,
            'customer_address' => $order->ship_to_address,
            'NPWP' => $customer->npwp,
            'invoice_no' => $invoice_no,
            'invoice_date' => $invoice->billing_date,
            'due_date' => $invoice->due_date,
            'delivery_no' => $invoice->delivery_order_no,
            'sales_order_no' => $invoice->sales_order_no,
            'po_number' => $invoice->po_no,
            'items' => $item,
            'total_qty' => $total_qty,
            'total_bruto' => $total_bruto,
            'total_netto' => $total_netto,
            'dp' => (int) $invoice->down_payment,
            'due for payment' => (int) $invoice->due_payment,
            'dpp' => (int) $invoice->dpp,
            'ppn' => (int) $invoice->tax_amount
        ];

        return $this->sendSuccess(null, $data);
    }

    public function downloadInvoiceDetailLunas($invoice_no, Request $request)
    {
        $invoice = Invoice::find($invoice_no);

        if ($invoice == null) {
            return $this->sendError('Invoice not found', Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $customer = Customer::find($invoice->customer_id);
        $order = Order::where('order_no', $invoice->po_no)->first();

        $invoice_details = InvoiceDetail::where('invoice_no', $invoice_no)->get();

        $item = [];

        $total_qty = 0;
        $total_bruto = 0;
        $total_netto = 0;

        for ($i = 0; $i < count($invoice_details); $i++) {
            $item[$i] = [
                'article_no' => $invoice_details[$i]->article,
                'description' => $invoice_details[$i]->article_description,
                'unit_price' => $invoice_details[$i]->price,
                'qty' => !is_null($invoice_details[$i]->qty) ? $invoice_details[$i]->qty : 'Tidak Ada',
                'bruto' => $invoice_details[$i]->gross_price,
                'disc' => $invoice_details[$i]->discount_percent,
                'netto' => $invoice_details[$i]->nett_price
            ];
            $total_qty += !is_null($invoice_details[$i]->qty) ? $invoice_details[$i]->qty : 0;
            $total_bruto += $invoice_details[$i]->gross_price;
            $total_netto += $invoice_details[$i]->nett_price;
        }

        $data = [
            'customer_number' => $invoice->customer_id,
            'customer_name' => $invoice->customer_name,
            'customer_address' => $order->ship_to_address,
            'NPWP' => $customer->npwp,
            'invoice_no' => $invoice_no,
            'invoice_date' => $invoice->billing_date,
            'due_date' => $invoice->due_date,
            'delivery_no' => $invoice->delivery_order_no,
            'po_number' => $invoice->po_no,
            'items' => $item,
            'total_qty' => (int) $total_qty,
            'total_bruto' => (int) $total_bruto,
            'total_netto' => (int) $total_netto,
            'dp' => (int) $invoice->down_payment,
            'due for payment' => (int) $invoice->due_payment,
            'dpp' => (int) $invoice->dpp,
            'ppn' => (int) $invoice->tax_amount
        ];

        return $this->sendSuccess(null, $data);
    }

    public function downloadFakturPajak($invoice_no)
    {

        $inv = Invoice::find($invoice_no);

        if ($inv == null) {
            return $this->sendError('invoice not found', 404);
        }
        //        $path = public_path('faktur/blocked_user_empty_balance.csv');
        $path = storage_path('app/public/faktur/') . $inv->tax_invoice_file_name;
        // Log::info($path);
        if (file_exists($path)) {
            $name = $inv->tax_invoice_file_name;
            $fileType = $inv->tax_invoice_file_type;
            return $this->sendDownloadResponse($path, $name, $fileType);
        }
        return $this->sendError('file not found');
    }

    public function getDetailItems($invoice_no, Request $request)
    {
        $page = $request->input('page');
        $per_page = $request->input('per_page');

        Log::info('$per_page = ' . $per_page);
        $inv = Invoice::where('invoice_no', $invoice_no)->where('invoice_type', 'BILLING')->first();

        if ($inv != null) {
            $items = InvoiceDetail::query()
                ->where('invoice_no', '=', $invoice_no)
                ->select('*')
                ->paginate($per_page, ['*'], 'page', $page);

            $datas = [];
            foreach ($items as $item) {
                $datas[] = [
                    'product_name' => $item->product_name,
                    //'sku' => $article->sku_code_c,
                    'sku' => $item->article,
                    'product_size' => $item->product_size,
                    'qty' => $item->qty,
                    'price' => $item->price,
                    // 'discount' => intval($item->discount_percent/100*$item->gross_price),
                    'sub_total' => $item->gross_price,
                    'discount' => $potongan = intval($item->discount_percent / 100 * $item->gross_price),
                    'net_price' => $item->gross_price - $potongan
                ];
            }
        } else {
            $inv_dp = Invoice::where('invoice_no', $invoice_no)->where('invoice_type', 'DOWN PAYMENT')->first();
            if ($inv_dp != null) {
                $oh = Order::where('sales_order_no', $inv_dp->sales_order_no)->first();
            } else {
                $oh = Order::where('order_no', $invoice_no)->first();
            }
            $oc_exs = OrderCustom::where('reference_id', $oh->order_no)->exists();
            $do = DeliveryOrder::where('sales_order_no', $oh->sales_order_no)->first();

            if ($oc_exs) {
                $items = OrderCustom::query()
                    ->where('reference_id', '=', $oh->order_no)
                    ->select('*')
                    ->paginate($per_page, ['*'], 'page', $page);
            } else {
                $items = OrderItem::query()
                    ->where('order_no', '=', $oh->order_no)
                    ->select('*')
                    ->paginate($per_page, ['*'], 'page', $page);
            }

            $datas = [];
            $i = 0;
            foreach ($items as $item) {
                $custom_price = 0;
                if ($oc_exs) {
                    $article = Product::where('article', $item->article_id)->first();

                    $ocs = OrderCustom::where('reference_id', $item->reference_id)
                        ->where('sku', $article->sku_code_c)
                        ->where('attachment_group_id', $item->attachment_group_id)
                        ->pluck('attachment_group_id')->first();

                    $custom_price = OrderCustomAttachment::where('order_custom_id', $ocs)->sum('custom_price');
                    $base_price = $article->price->amount;
                } else {
                    $base_price = $item->price;
                }

                if ($do != null) {
                    $qty = !empty($do->items[$i]->issued_qty) ? $do->items[$i]->issued_qty : $do->items[$i]->qty;
                    $discount_percent = $do->issued_discount != 0 ? $do->issued_discount / (($base_price + $custom_price) * $qty) : $do->discount / (($base_price + $custom_price) * $qty);
                } else {
                    $qty = $item->qty;
                    $discount_percent = $oh->total_discount / $oh->total;
                }

                $gross_price = $qty * ($base_price + $custom_price);
                $datas[] = [
                    'product_name' => $oc_exs ? $article->product_name_c : $item->product_name,
                    //'sku' => $article->sku_code_c,
                    'sku' => $item->article_id,
                    'product_size' => $oc_exs ? $article->product_size_c : $item->product_size,
                    'qty' => $qty,
                    'price' => $base_price,
                    // 'discount' => intval($item->discount_percent/100*$item->gross_price),
                    'sub_total' => $gross_price,
                    'discount' => $gross_price * $discount_percent,
                    'net_price' => $gross_price - ($gross_price * $discount_percent)
                ];
                $i++;
            }
        }

        $res = [
            'total_data' => $items->total(),
            'size' => intval($items->perPage()),
            'active_page' => $items->currentPage(),
            'total_page' => $items->lastPage(),
            //            'from' => $items->firstItem(),
//            'to' => $items->lastItem(),
            'data' => $datas
        ];

        return $this->sendSuccess("list item retrieved successfully.", $res);
    }

    public function getDetailHeader($invoice_no, Request $request)
    {
        $data = Invoice::where('invoice_no', $invoice_no)->first();
        $oh_inv = Order::where('order_no', $invoice_no)->first();

        if ($data == null && $oh_inv == null) {
            return $this->sendError('Invoice tidak ditemukan', 404);
        }

        $oh = $data != null ? Order::where('sales_order_no', $data->sales_order_no)->first() : $oh_inv;

        // $nomorresi = DB::table('delivery_number')
        // ->leftJoin('invoice', 'invoice.invoice_no' ,'=','delivery_number.invoice_no')
        // ->where('invoice.invoice_no', $invoice_no)
        // ->select('delivery_number.delivery_no as noresi','delivery_number.delivery_name as namaresi')
        // ->get()
        // ->toArray();

        $oc_exs = OrderCustom::where('reference_id', $oh->order_no)
            ->exists();

        $do = DeliveryOrder::where('sales_order_no', $oh->sales_order_no)
            ->first();

        // $company = Company::query()
        //     ->where('npwp','=',$cust->npwp)
        //     ->first();

        $data_dp = Invoice::where('sales_order_no', $oh->sales_order_no)->where('invoice_type', 'DOWN PAYMENT')->first();
        $data_bill = Invoice::where('sales_order_no', $oh->sales_order_no)->where('invoice_type', 'BILLING')->first();
        $qty_items = 0;
        $sub_total = 0;
        $discount = 0;
        $total = 0;
        $dp = 0;

        $qty_items = OrderItem::where('order_no', $oh->order_no)->sum('qty');
        $sub_total = $oh->total;
        $discount = $oh->total_discount;
        $total = $oh->total_nett;
        $dp = $oh->dp_amount;

        if ($do != null) {
            $do_qty_items = 0;
            $do_sub_total = 0;
            foreach ($do->items as $item) {
                $article = Product::where('article', $item->article)->first();
                $do_qty_items += $item->issued_qty != null ? $item->issued_qty : $item->qty;
                $custom_price = 0;
                if ($oc_exs == true) {
                    $ocs = OrderCustom::where('reference_id', $oh->order_no)->pluck('id')->toArray();
                    $custom_price = OrderCustomAttachment::where('order_custom_id', $ocs)->sum('custom_price');
                }
                $do_sub_total += $item->issued_qty != null ? $item->issued_qty * ($article->price->amount + $custom_price) : $item->qty * ($article->price->amount + $custom_price);
            }

            $sub_total = $do_sub_total;
            $qty_items = $do_qty_items;
            $discount = !empty($do->issued_discount) ? $do->issued_discount : $do->discount;
            $total = $sub_total - $discount;
            $dp = $oh->dp_amount ?? 0;
        }

        if ($data_bill != null) {
            $det = InvoiceDetail::query()
                ->where('invoice_no', '=', $data_bill->invoice_no)
                ->select(
                    DB::raw('sum(gross_price) as subtotal'),
                    DB::raw('sum(discount_percent/100*gross_price) as discount'),
                    DB::raw('sum(nett_price) as total')
                )
                ->first();

            $qty_items = InvoiceDetail::where('invoice_no', $data_bill->invoice_no)->sum('qty');
            $sub_total = $det->subtotal;
            $discount = $det->discount;
            $total = $det->total;
            $dp = $oh->down_payment ?? 0;
        }

        $attachments = [
            'tax_url' => $data_bill != null ? ($data_bill->tax_invoice_file_path != '' ? env('S3_STREAM_URL') . $data_bill->tax_invoice_file_path : null) : null,
            'invoice_url' => $data_bill->invoice_file_path ?? null,
            'tax_url_dp' => $data_dp != null ? ($data_dp->tax_invoice_file_path != '' ? env('S3_STREAM_URL') . $data_dp->tax_invoice_file_path : null) : null,
            'invoice_url_dp' => $data_dp->invoice_file_path ?? null
        ];

        $res = [
            'order' => [
                'order_no' => $oh->order_no,
                'ref' => $oh->sales_order_no,
                'dn' => $do->delivery_order_no ?? null,
                'delivery_status' => $oh->order_status,
                'billing' => $data_bill->invoice_no ?? null,
                'invoice_no_dp' => $data_dp->invoice_no ?? null,
                'order_date' => $oh->created_date,
                'gi_date' => $do->good_issue_date ?? null,
                'due_date' => $data_bill != null ? $data_bill->due_date : ($oh->dp_due_date ?? null),
                'qty_items' => (int) $qty_items,
                'sub_total' => (int) $sub_total,
                'discount' => (int) $discount,
                'total' => (int) $total,
                'down_payment' => (int) $dp,
                'status' => $data_bill != null ? $data_bill->status : ($data_dp != null ? $data_dp->status : 'BELUM DIBAYAR'),
                'customCheck' => $oc_exs,
                'invoice_type' => $data->invoice_type ?? 'ORDER'
            ],
            'shipment' => [
                'nomor_resi' => [],
                'address' => [
                    'name' => $oh->bill_to,
                    'company_name' => $oh->ship_to,
                    'phone' => $oh->bill_to_phone_number,
                    'address' => $oh->ship_to_address
                ]
            ],
            'attachment' => $attachments
        ];

        return $this->sendSuccess("success", $res);
    }
}