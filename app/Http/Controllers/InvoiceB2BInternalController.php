<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Order;
use App\Models\Invoice;
use App\Models\Customer;
use App\Models\OrderItem;
use App\Models\OrderCustom;
use Illuminate\Http\Request;
use App\Models\CustomerSales;
use App\Models\DeliveryOrder;
use App\Models\InvoiceDetail;
use Illuminate\Http\Response;
use App\Models\SalesAssignment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\OrderCustomAttachment;
use Symfony\Component\HttpFoundation\StreamedResponse;
use App\Helpers\FileHelper;
use App\Models\Product;
use DateTime;
use Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;

class InvoiceB2BInternalController extends Controller
{
    use FileHelper;
    public function getInvoices(Request $request){
        $no_billing = $request->input('no_billing');
        $nama_akun = $request->input('acc_name');
        $nama_sales = $request->input('sales_name');
        $order_date_to = $request->input('order_date_to');
        $order_date_from = $request->input('order_date_from');
        $status = $request->input('status');
        // $tipe_toko = $request->input('toko_type');
        // $kredit_limit = $request->input('credit_limit');
        $due_date_to = $request->input('due_date_to');
        $due_date_from = $request->input('due_date_from');
        $page = $request->input('page', 1);
        $per_page = $request->input('per_page', 12);

        $salesId = auth()->user()->reference_id;
        $roles = @auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
        Log::info('sales id = '.$salesId);

        $salesIdd = !in_array("0", $roles)
        ? array_map(function($i) {
            return $i->sales_id??'0';
        }, $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id',$salesId)->first()))
        : [];

        array_push($salesIdd, $salesId);
        
        $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                            ->pluck('customer_id')->all();

        $data = [];
        $datas = DB::table('order_header')
                        ->leftJoin('invoice as i_bill',function ($j){
                            $j->on('i_bill.sales_order_no','=','order_header.sales_order_no')
                                ->where('i_bill.invoice_type','BILLING');
                        })
                        ->leftJoin('invoice as i_dp',function ($j){
                            $j->on('i_dp.sales_order_no','=','order_header.sales_order_no')
                                ->where('i_dp.invoice_type','DOWN PAYMENT');
                        })
                        ->leftJoin('proforma_invoice as pi', 'pi.sales_order_no', '=', 'order_header.sales_order_no')
                        ->leftJoin('delivery_order', 'delivery_order.sales_order_no', '=', 'order_header.sales_order_no')
                        ->select('order_header.dp_amount','i_bill.invoice_no as bill_inv_no','i_dp.invoice_no as dp_invoice_no',
                        'order_header.created_date','order_header.bill_to','i_bill.due_date as bill_due_date', 'i_dp.due_date as dp_due_date',
                        'i_bill.gross_price as bill_total','i_dp.gross_price as dp_total','order_header.sales_name','i_bill.status as bill_status',
                        'i_dp.status as dp_status','order_header.order_no','order_header.total_nett as oh_total_nett','delivery_order.created_date as do_date',
                        'pi.sales_order_no as pi_so', 'pi.due_date as pi_due_date', 'pi.gross_price as pi_total')
                        ->selectRaw('CASE WHEN ((order_header.dp_amount IS NULL OR order_header.dp_amount = 0)
                                     AND i_bill.invoice_no IS NULL) OR (order_header.dp_amount > 0 AND i_dp.invoice_no
                                     IS NULL) THEN "BELUM DIBAYAR" ELSE NULL END AS oh_inv_status')
                        ->whereIn('order_header.distribution_channel', ['B2B', 'W3', 'RE', 'RD'],)
                        ->whereNotIn('order_header.order_status',['Menunggu Konfirmasi', 'Menunggu Verifikasi', 'Baru', 'Batal', 'On Hold', 'Pending', 'Cancel', 'Reject'])
                        ->when($no_billing, function ($q) use ($no_billing){
                            $q->where(function($q) use ($no_billing){
                                $q->where('i_bill.invoice_no','LIKE','%'.$no_billing.'%')
                                ->orWhere('i_dp.invoice_no','LIKE','%'.$no_billing.'%')
                                ->orWhere('order_header.order_no','LIKE','%'.$no_billing.'%');
                            });
                        })
                        ->when($nama_akun, function ($q) use ($nama_akun){
                            $q->where('order_header.bill_to','LIKE','%'.$nama_akun.'%');
                        })
                        ->when($nama_sales, function ($q) use ($nama_sales){
                            $q->where('order_header.sales_name','LIKE','%'.$nama_sales.'%');
                        })
                        ->when($order_date_to, function ($q) use ($order_date_to){
                            $order_date_to = Carbon::parse($order_date_to)->format('Y-m-d H:i:s');
                            $q->whereDate('delivery_order.created_date','<=',$order_date_to);
                        })
                        ->when($order_date_from, function ($q) use ($order_date_from){
                            $order_date_from = Carbon::parse($order_date_from)->format('Y-m-d H:i:s');
                            $q->whereDate('delivery_order.created_date','>=',$order_date_from);
                        })
                        // ->when($status, function ($q) use ($status,$roles,$customerIdList){
                        //     $status = explode(',',$status);
                        //     $q->where(function ($q) use ($status){
                        //         $q->whereIn('i_bill.status',$status)
                        //         ->orWhereIn('i_dp.status',$status);
                        //     });
                            // if (!in_array('0',$roles)) {
                            //     $ohs = Order::where('customer_id',$customerIdList)->pluck('order_no')->toArray();
                            // } else {
                            //     $ohs = Order::pluck('order_no')->toArray();
                            // }

                            // if (in_array('belum dibayar',$status)) {
                            //     $q->whereIn('order_header.order_no',$ohs)
                            //     ->where(function($q) use ($status){
                            //         $q->whereIn('i_bill.status',$status)
                            //         ->orWhereIn('i_dp.status',$status);
                            //     });
                            // } else {
                            //     $q->whereNotIn('order_header.order_no',$ohs)
                            //     ->where(function($q) use ($status){
                            //         $q->whereIn('i_bill.status',$status)
                            //         ->orWhereIn('i_dp.status',$status);
                            //     });
                            // }
                        // })
                        ->when($due_date_to, function ($q) use ($due_date_to){
                            $due_date_to = Carbon::parse($due_date_to)->format('Y-m-d H:i:s');
                            $q->where(function($q) use ($due_date_to){
                                $q->whereDate('i_bill.due_date','<=',$due_date_to)
                                ->orWhereDate('i_dp.due_date','<=',$due_date_to);
                            });
                        })
                        ->when($due_date_from, function ($q) use ($due_date_from){
                            $due_date_from = Carbon::parse($due_date_from)->format('Y-m-d H:i:s');
                            $q->where(function ($q) use ($due_date_from){
                                $q->whereDate('i_bill.due_date','>=',$due_date_from)
                                ->orWhereDate('i_dp.due_date','>=',$due_date_from);
                            });
                        })
                        ->when(!in_array("0", $roles),function($query) use($customerIdList){
                            $query->whereIn('order_header.customer_id', $customerIdList);
                        })
                        ->where(function ($q) {
                            $q->whereNotNull('i_bill.invoice_no')
                                ->orWhereNotNull('i_dp.invoice_no')
                                ->orWhereNotNull('pi.sales_order_no');
                        })
                        // ->where('i_bill.invoice_no','!=',null)
                        ->orderBy('delivery_order.created_date','desc')
                        // ->paginate($per_page, ['*'], 'page', $page);
                        ->chunk(100,function($dd) use (&$data){
                            foreach ($dd as $d) {
                                $data[] = $d;
                            }
                        });
        
        $data = collect($data);
        if ($status != null) {
            $status = explode(',',strtolower($status));
            if (!in_array('belum dibayar',$status)) {
                $data = $data->filter(function ($data) use($status){
                    $lowerSBill = strtolower($data->bill_status);
                    $lowerSDP = strtolower($data->dp_status);

                    return in_array($lowerSBill,$status)
                        || in_array($lowerSDP,$status);
                })->values()->all();
            } else {
                $data = $data->filter(function ($data) use($status){
                    $lowerSBill = strtolower($data->bill_status);
                    $lowerSDP = strtolower($data->dp_status);
                    $lowerOHI = strtolower($data->oh_inv_status);

                    return in_array($lowerSBill,$status)
                        || in_array($lowerSDP,$status)
                        || in_array($lowerOHI,$status);
                })->values()->all();
            }
            $data = collect($data);
        }


        $data = $data->transform(function ($d) {
            // if ($d->bill_inv_no != null || $d->dp_invoice_no != null) {
                return [
                    'billing' => $d->bill_inv_no != null ? $d->bill_inv_no : ($d->dp_invoice_no ?? $d->pi_so),
                    'order_date' => Carbon::parse($d->created_date)->format('Y-m-d H:i:s'),
                    'toko_type' => null,
                    'acc_name' => $d->bill_to,
                    'due_date' => $d->bill_inv_no != null ? $d->bill_due_date : ($d->dp_due_date ?? $d->pi_due_date),
                    'do_date' => Carbon::parse($d->do_date)->format('Y-m-d H:i:s'),
                    'total_bill' => $d->bill_inv_no != null ? $d->bill_total : ($d->dp_total ?? $d->pi_total),
                    'credit_limit' => null,
                    'sales_name' => $d->sales_name,
                    'status' => $d->bill_inv_no != null ? $d->bill_status : ($d->dp_status ?? 'BELUM DIBAYAR'),
                    'order_no' => $d->order_no
                ];
            // }

            // return [
            //     'billing' => $d->order_no,
            //     'order_date' => Carbon::parse($d->created_date)->format('Y-m-d H:i:s'),
            //     'toko_type' => null,
            //     'acc_name' => $d->bill_to,
            //     'due_date' => $d->dp_due_date??null,
            //     'do_date' => Carbon::parse($d->do_date)->format('Y-m-d H:i:s'),
            //     'total_bill' => $d->oh_total_nett,
            //     'credit_limit' => null,
            //     'sales_name' => $d->sales_name,
            //     'status' => 'BELUM DIBAYAR',
            //     'order_no' => $d->order_no
            // ];
        });

        $paginated_data = collect($data)->paginate($per_page,null,$page,'page');

        $res = [
            'total_data' => $paginated_data->total(),
            'size' => intval($paginated_data->perPage()),
            'active_page' => $paginated_data->currentPage(),
            'total_page' => $paginated_data->lastPage(),
            'data' => $paginated_data->items()
        ];

        return $this->sendSuccess(null,$res);
    }

    public function getDetailItems($invoice_no,Request $request){
        $page = $request->input('page');
        $per_page = $request->input('per_page');

        Log::info('$per_page = '.$per_page);
        $inv = Invoice::where('invoice_no',$invoice_no)->where('invoice_type','BILLING')->first();
        if ($inv != null) {
            $items = InvoiceDetail::query()
            ->where('invoice_no','=', $invoice_no)
            ->select('*')
            ->paginate($per_page,['*'],'page',$page);

            $datas = [];
            foreach ($items as $item){
                $datas[] = [
                    'product_name' => $item->product_name,
                    //'sku' => $article->sku_code_c,
                    'sku' => $item->article,
                    'product_size' => $item->product_size,
                    'qty' => $item->qty,
                    'price' => $item->price,
                    // 'discount' => intval($item->discount_percent/100*$item->gross_price),
                    'sub_total' => $item->gross_price,
                    'discount' => $potongan = intval($item->discount_percent/100*$item->gross_price),
                    'net_price' => $item->gross_price-$potongan
                ];
            }
        } else {
            $inv_dp = Invoice::where('invoice_no',$invoice_no)->where('invoice_type','DOWN PAYMENT')->first();

            if ($inv_dp != null) {
                $oh = Order::where('sales_order_no',$inv_dp->sales_order_no)->first();
            } else {
                $oh = Order::where('sales_order_no',$invoice_no)->first();
            }
            
            $oc_exs = OrderCustom::where('reference_id',$oh->order_no)->exists();
            $do = DeliveryOrder::where('sales_order_no',$oh->sales_order_no)->first();

            if ($oc_exs) {
                $items = OrderCustom::query()
                    ->where('reference_id', '=', $oh->order_no)
                    ->select('*')
                    ->paginate($per_page, ['*'], 'page', $page);
            } else {
                $items = OrderItem::query()
                    ->where('order_no', '=', $oh->order_no)
                    ->select('*')
                    ->paginate($per_page, ['*'], 'page', $page);
            }

            $datas = [];
            $i = 0;
            foreach ($items as $item){
                $custom_price = 0;
                if ($oc_exs) {
                    $article = Product::where('article', $item->article_id)->first();

                    $ocs = OrderCustom::where('reference_id', $item->reference_id)
                        ->where('sku', $article->sku_code_c)
                        ->where('attachment_group_id', $item->attachment_group_id)
                        ->pluck('attachment_group_id')->first();

                    $custom_price = OrderCustomAttachment::where('order_custom_id', $ocs)->sum('custom_price');
                    $base_price = $article->price->amount;
                } else {
                    $base_price = $item->price;
                }

                if ($do != null) {
                    $qty = !empty($do->items[$i]->issued_qty) ? $do->items[$i]->issued_qty : $do->items[$i]->qty;
                    $discount_percent = $do->issued_discount != 0 ? $do->issued_discount / (($base_price + $custom_price) * $qty) : $do->discount / (($base_price + $custom_price) * $qty) ;
                } else {
                    $qty = $item->qty;
                    $discount_percent = $oh->total_discount/$oh->total;
                }
                $gross_price = $qty * ($base_price + $custom_price);
                $datas[] = [
                    'product_name' => $oc_exs ? $article->product_name_c  : $item->product_name,
                    //'sku' => $article->sku_code_c,
                    'sku' => $item->article_id,
                    'product_size' => $oc_exs ? $article->product_size_c : $item->product_size,
                    'qty' => $qty,
                    'price' => $base_price,
                    // 'discount' => intval($item->discount_percent/100*$item->gross_price),
                    'sub_total' => $gross_price,
                    'discount' => $gross_price * $discount_percent,
                    'net_price' => $gross_price - ($gross_price * $discount_percent)
                ];
            }
        }

        $res = [
            'total_data' => $items->total(),
            'size' => intval($items->perPage()),
            'active_page' => $items->currentPage(),
            'total_page' => $items->lastPage(),
//            'from' => $items->firstItem(),
//            'to' => $items->lastItem(),
            'data'=>$datas
        ];

        return $this->sendSuccess("list item retrieved successfully.", $res);
    }

    public function getInvoiceDetails(Request $request,$invoice_no){
        $page = $request->input('page');

        $invoice = Invoice::where('invoice_no',$invoice_no)->first();

        $order = Order::find($invoice->order_no);

        $delivery_order = DeliveryOrder::where('delivery_order_no',$invoice->delivery_order_no)->first();

        $invoice_details = InvoiceDetail::where('invoice_no',$invoice_no)
                                ->paginate(10,['*'],'page',$page);

        $item = [];

        for ($i=0; $i < count($invoice_details); $i++) { 
            $item[$i] = [
                'nama_barang' => $invoice_details[$i]->product_name,
                'sku' => $invoice_details[$i]->article,
                'ukuran' => $invoice_details[$i]->product_size,
                'kuantiti' => !is_null($invoice_details[$i]->qty) ? $invoice_details[$i]->qty : 'Tidak Ada',
                'harga_satuan' => $invoice_details[$i]->price,
                'sub_total' => $invoice_details[$i]->gross_price,
                'potongan_harga' => (int)($invoice_details[$i]->gross_price * $invoice_details[$i]->discount_percent/100),
                'nett' => $invoice_details[$i]->nett_price
            ];
        }

        // $dn = [];
        // $gi_date = [];
        // $no_resi = [];
        // for ($i=0; $i < count($delivery_order) ; $i++) { 
        //     $dn[$i] = $delivery_order[$i]->delivery_order_no;
        //     $gi_date[$i] = $delivery_order[$i]->good_issue_date;
        //     $no_resi[$i] = $delivery_order[$i]->delivery_no;
        // }

        $data_invoice = [
            'order_status' => $order->order_status,
            'order' => $invoice->order_no,
            'ref' => $order->sales_order_no,
            'DN' => $invoice->delivery_order_no,
            'billing' => $invoice->invoice_no,
            'tanggal_pesan' => Carbon::parse($order->created_date)->format('Y-m-d H:i:s'),
            'tanggal_gi' => !is_null($delivery_order) ? $delivery_order->good_issue_date : null,
            'jatuh_tempo_tagihan' => $invoice->due_date,
            'invoice_type' => $invoice->invoice_type,
            'pengiriman' => [
                'no_resi' => $invoice->no_resi,
                'name' => $order->bill_to, #cust
                'phone_number' => $order->bill_to_phone_number,
                'email' => $order->bill_to_email, #cust
                'alamat' => $order->ship_to_address #cshipment
            ],
            'payment_status' => $invoice->status,
            'sub_total' => $invoice->gross_price,
            'diskon' => $order->total_discount, #orderheader
            'total' => $invoice->nett_price,
            'item' => $item
        ];
        
        $response = [
            'total_data' => $invoice_details->total(),
            'size' => intval($invoice_details->perPage()),
            'active_page' => $invoice_details->currentPage(),
            'total_page' => $invoice_details->lastPage(),
            'invoice' => $data_invoice
        ];

        return $this->sendSuccess(null,$response);
    }

    public function downloadInvoicesNew(Request $request){
        try {
            $no_billing = $request->input('no_billing');
            $nama_akun = $request->input('acc_name');
            $nama_sales = $request->input('sales_name');
            $order_date_to = $request->input('order_date_to');
            $order_date_from = $request->input('order_date_from');
            $status = $request->input('status');
            // $tipe_toko = $request->input('toko_type');
            // $kredit_limit = $request->input('credit_limit');
            $due_date_to = $request->input('due_date_to');
            $due_date_from = $request->input('due_date_from');

            if (empty($order_date_from) || empty($order_date_to)) {
                throw new Exception('Filter date diperlukan. Harap berikan parameter date from dan date to');
            }
        
            $start = new DateTime($order_date_from);
            $end = new DateTime($order_date_to);
            if ($start->diff($end)->days > 90) {
                throw new Exception('Rentang tanggal tidak boleh melebihi 90 hari');
            }

            $salesId = auth()->user()->reference_id;
            $roles = @auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
            Log::info('sales id = '.$salesId);

            $salesIdd = !in_array("0", $roles) ? 
            array_map(function($i) {return $i->sales_id??'0';}, $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id',$salesId)->first())
            )
            : [];

            array_push($salesIdd, $salesId);
            
            $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                                ->pluck('customer_id')->all();

            $rows = [];

            $datas = Order::query()
                ->leftJoin('invoice as i_bill',function ($j){
                    $j->on('i_bill.sales_order_no','=','order_header.sales_order_no')
                        ->where('i_bill.invoice_type','BILLING');
                })
                ->leftJoin('invoice as i_dp',function ($j){
                    $j->on('i_dp.sales_order_no','=','order_header.sales_order_no')
                        ->where('i_dp.invoice_type','DOWN PAYMENT');
                })
                ->leftJoin('proforma_invoice as pi', 'pi.sales_order_no', '=', 'order_header.sales_order_no')
                ->leftJoin('delivery_order', 'delivery_order.sales_order_no', '=', 'order_header.sales_order_no')
                ->when($no_billing, function ($q) use ($no_billing){
                    $q->where(function($q) use ($no_billing){
                        $q->where('i_bill.invoice_no','LIKE','%'.$no_billing.'%')
                        ->orWhere('i_dp.invoice_no','LIKE','%'.$no_billing.'%')
                        ->orWhere('order_header.order_no','LIKE','%'.$no_billing.'%');
                    });
                })
                ->when($nama_akun, function ($q) use ($nama_akun){
                    $q->where('order_header.bill_to','LIKE','%'.$nama_akun.'%');
                })
                ->when($nama_sales, function ($q) use ($nama_sales){
                    $q->where('order_header.sales_name','LIKE','%'.$nama_sales.'%');
                })
                ->when($order_date_from, function ($q) use ($order_date_from){
                    $order_date_from = Carbon::parse($order_date_from)->format('Y-m-d H:i:s');
                    $q->whereDate('delivery_order.created_date','>=',$order_date_from);
                })
                ->when($order_date_to, function ($q) use ($order_date_to){
                    $order_date_to = Carbon::parse($order_date_to)->format('Y-m-d H:i:s');
                    $q->whereDate('delivery_order.created_date','<=',$order_date_to);
                })
                // ->when($status, function ($q) use ($status){
                //     $status = explode(',',$status);
                //     $q->where(function($q) use ($status){
                //         $q->whereIn('i_bill.status',$status)
                //         ->orWhereIn('i_dp.status',$status);
                //     });
                // })
                ->when($due_date_to, function ($q) use ($due_date_to){
                    $due_date_to = Carbon::parse($due_date_to)->format('Y-m-d H:i:s');
                    $q->where(function($q) use ($due_date_to){
                        $q->whereDate('i_bill.due_date','<=',$due_date_to)
                        ->orWhereDate('i_dp.due_date','<=',$due_date_to);
                    });
                })
                ->when($due_date_from, function ($q) use ($due_date_from){
                    $due_date_from = Carbon::parse($due_date_from)->format('Y-m-d H:i:s');
                    $q->where(function ($q) use ($due_date_from){
                        $q->whereDate('i_bill.due_date','>=',$due_date_from)
                        ->orWhereDate('i_dp.due_date','>=',$due_date_from);
                    });
                })
                ->when(!in_array("0", $roles),function($query) use($customerIdList){
                    $query->whereIn('order_header.customer_id', $customerIdList);
                })
                ->whereIn('order_header.distribution_channel', ['B2B', 'W3', 'RE', 'RD'],)
                ->where(function ($q) {
                    $q->whereNotNull('i_bill.invoice_no')
                        ->orWhereNotNull('i_dp.invoice_no')
                        ->orWhereNotNull('pi.sales_order_no');
                })
                ->select('order_header.dp_amount','i_bill.invoice_no as bill_inv_no','i_dp.invoice_no as dp_invoice_no',
                'order_header.created_date','order_header.bill_to','i_bill.due_date as bill_due_date', 'i_dp.due_date as dp_due_date',
                'i_bill.gross_price as bill_total','i_dp.gross_price as dp_total','order_header.sales_name','i_bill.status as bill_status',
                'i_dp.status as dp_status','order_header.order_no','order_header.total_nett as oh_total_nett','delivery_order.created_date as do_date',
                'pi.sales_order_no as pi_so', 'pi.due_date as pi_due_date', 'pi.gross_price as pi_total')
                ->selectRaw('CASE WHEN ((order_header.dp_amount IS NULL OR order_header.dp_amount = 0)
                            AND i_bill.invoice_no IS NULL) OR (order_header.dp_amount > 0 AND i_dp.invoice_no
                            IS NULL) THEN "BELUM DIBAYAR" ELSE NULL END AS oh_inv_status')
                ->orderBy('delivery_order.created_date', 'desc')
                ->chunk(100, function ($data) use (&$rows){
                    foreach ($data as $d) {
                        $rows[] = $d;
                    }
                });

            $data = collect($rows);
            if ($status != null) {
                $status = explode(',',strtolower($status));
                if (!in_array('belum dibayar',$status)) {
                    $data = $data->filter(function ($data) use($status){
                        $lowerSBill = strtolower($data->bill_status);
                        $lowerSDP = strtolower($data->dp_status);
    
                        return in_array($lowerSBill,$status)
                            || in_array($lowerSDP,$status);
                    })->values()->all();
                } else {
                    $data = $data->filter(function ($data) use($status){
                        $lowerSBill = strtolower($data->bill_status);
                        $lowerSDP = strtolower($data->dp_status);
                        $lowerOHI = strtolower($data->oh_inv_status);
    
                        return in_array($lowerSBill,$status)
                            || in_array($lowerSDP,$status)
                            || in_array($lowerOHI,$status);
                    })->values()->all();
                }
                $data = collect($data);
            }
    
    
            $data = $data->transform(function ($d) {
                // if ($d->bill_inv_no != null || $d->dp_invoice_no != null) {
                    return [
                        'billing' => $d->bill_inv_no != null ? $d->bill_inv_no : ($d->dp_invoice_no ?? $d->pi_so),
                        'order_date' => Carbon::parse($d->created_date)->format('Y-m-d H:i:s'),
                        'toko_type' => '-',
                        'acc_name' => $d->bill_to,
                        'due_date' => $d->bill_inv_no != null ? $d->bill_due_date : ($d->dp_due_date ?? $d->pi_due_date),
                        'do_date' => Carbon::parse($d->do_date)->format('Y-m-d H:i:s'),
                        'total_bill' => $d->bill_inv_no != null ? $d->bill_total : ($d->dp_total ?? $d->pi_total),
                        'credit_limit' => '-',
                        'sales_name' => $d->sales_name,
                        'status' => $d->bill_inv_no != null ? $d->bill_status : ($d->dp_status ?? 'BELUM DIBAYAR'),
                        'order_no' => $d->order_no
                    ];
                // }
    
                // return [
                //     'billing' => $d->order_no,
                //     'order_date' => Carbon::parse($d->created_date)->format('Y-m-d H:i:s'),
                //     'toko_type' => null,
                //     'acc_name' => $d->bill_to,
                //     'due_date' => $d->dp_due_date??null,
                //     'do_date' => Carbon::parse($d->do_date)->format('Y-m-d H:i:s'),
                //     'total_bill' => $d->oh_total_nett,
                //     'credit_limit' => null,
                //     'sales_name' => $d->sales_name,
                //     'status' => 'BELUM DIBAYAR',
                //     'order_no' => $d->order_no
                // ];
            });
        
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            $headers = ['Billing', 'Order Date', 'Toko Type', 'Account Name', 'Due Date', 'Delivery Order Date', 'Total Bill', 'Credit Limit', 'Sales Name', 'Status', 'Order No'];
            $sheet->fromArray($headers, null, 'A1');

            $sheet->getStyle('A1:K1')->applyFromArray([
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => '808080']
                ],
                'font' => [
                    'bold' => true,
                    'color' => ['argb' => '000000']
                ],
            ]);
                
            $rowIndex = 2;
            foreach ($data as $index => $item) {
                $sheet->fromArray([
                    $item['billing'],
                    $item['order_date'],
                    $item['toko_type'],
                    $item['acc_name'],
                    $item['due_date'],
                    $item['do_date'],
                    $item['total_bill'],
                    $item['credit_limit'],
                    $item['sales_name'],
                    $item['status'],
                    $item['order_no']
                ], null, 'A' . $rowIndex);
    
                $rowIndex++;
            }

            $highestRow = $sheet->getHighestRow();
            $highestColumn = $sheet->getHighestColumn();
            $sheet->getStyle("A1:$highestColumn$highestRow")->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        'color' => ['argb' => '000000'],
                    ],
                ],
            ]);

            foreach (range('A', $highestColumn) as $columnID) {
                $sheet->getColumnDimension($columnID)->setAutoSize(true);
            }

            $writer = new Xls($spreadsheet);
            $response = new StreamedResponse(fn() => $writer->save('php://output'));

            $response->headers->set('Content-Type', 'application/vnd.ms-excel');
            $response->headers->set('Content-Disposition', 'attachment; filename="Export B2B Invoices.xls"');
            $response->headers->set('Cache-Control', 'max-age=0');

            return $response;
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return $this->sendError($e->getMessage(), 400);
        }
    }
    
    public function downloadInvoices(Request $request){
        $no_billing = $request->input('no_billing');
        $nama_akun = $request->input('acc_name');
        $nama_sales = $request->input('sales_name');
        $order_date_to = $request->input('order_date_to');
        $order_date_from = $request->input('order_date_from');
        $status = $request->input('status');
        // $tipe_toko = $request->input('toko_type');
        // $kredit_limit = $request->input('credit_limit');
        $due_date_to = $request->input('due_date_to');
        $due_date_from = $request->input('due_date_from');

        $salesId = auth()->user()->reference_id;
        $roles = @auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
        Log::info('sales id = '.$salesId);

        $salesIdd = !in_array("0", $roles) ? 
        array_map(function($i) {return $i->sales_id??'0';}, $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id',$salesId)->first())
        )
        : [];

        array_push($salesIdd, $salesId);
        
        $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                            ->pluck('customer_id')->all();
        
        $col = ['Billing', 'Order Date', 'Toko Type', 'Acc Name', 'Due Date', 'Total Bill',
                'Credit Limit', 'Sales Name', 'Status'];
        
        $handle = fopen('Export Invoices B2B (Internal).csv','w');

        fputcsv($handle,$col);

        $invoices = DB::table('invoice')
                            ->leftJoin('order_header','invoice.order_no','=','order_header.order_no')
                            ->leftJoin('customers', 'invoice.customer_id','=','customers.customer_id')
                            ->leftJoin('customer_sales','customers.customer_id','=', 'customer_sales.customer_id')
                            ->leftJoin('sales','customer_sales.sales_id','=','sales.sales_id')
                            ->where('order_header.distribution_channel', 'B2B')
                            ->when($no_billing, function ($q) use ($no_billing){
                                $q->where('invoice.invoice_no','LIKE','%'.$no_billing.'%');
                            })
                            ->when($nama_akun, function ($q) use ($nama_akun){
                                $q->where('order_header.bill_to','LIKE','%'.$nama_akun.'%');
                            })
                            ->when($nama_sales, function ($q) use ($nama_sales){
                                $q->where('order_header.sales_name','LIKE','%'.$nama_sales.'%');
                            })
                            ->when($order_date_to, function ($q) use ($order_date_to){
                                $order_date_to = Carbon::parse($order_date_to)->format('Y-m-d H:i:s');
                                $q->whereDate('order_header.created_date','<=',$order_date_to);
                            })
                            ->when($order_date_from, function ($q) use ($order_date_from){
                                $order_date_from = Carbon::parse($order_date_from)->format('Y-m-d H:i:s');
                                $q->whereDate('order_header.created_date','>=',$order_date_from);
                            })
                            ->when($status, function ($q) use ($status){
                                $status = explode(',',$status);
                                $q->whereIn('invoice.status',$status);
                            })
                            // ->when($tipe_toko, function ($q) use ($tipe_toko){
                            //     if ($tipe_toko == 'cash') {
                            //         return $q->where('customers.top','=','T001');
                            //     } else {
                            //         return $q->where('customers.top','!=','T001');
                            //     }
                            // })
                            ->when($due_date_to, function ($q) use ($due_date_to){
                                $due_date_to = Carbon::parse($due_date_to)->format('Y-m-d H:i:s');
                                $q->whereDate('invoice.due_date','<=',$due_date_to);
                            })
                            ->when($due_date_from, function ($q) use ($due_date_from){
                                $due_date_from = Carbon::parse($due_date_from)->format('Y-m-d H:i:s');
                                $q->whereDate('invoice.due_date','>=',$due_date_from);
                            })
                            ->when(!in_array("0", $roles),function($query) use($customerIdList){
                                return $query->whereIn('order_header.customer_id', $customerIdList);
                            });

        // if($kredit_limit){
        //     $kredit_limit = array($kredit_limit);
        //     foreach($kredit_limit as $data){
        //         $invoices = $invoices->whereBetween('customers.credit_limit_used_percentage', explode('-',$data));
        //     }
        // }

        $invoices = $invoices->select('invoice.invoice_no','order_header.created_date as order_date','customers.top','order_header.bill_to as owner_name',
            'invoice.due_date','invoice.gross_price as total','customers.credit_limit_used_percentage','order_header.sales_name', 'invoice.status')
            ->orderBy('order_header.created_date','desc')
            ->chunk(100, function ($datas) use ($handle){
                foreach ($datas as $data) {
                    $row = [
                        'billing' => $data->invoice_no,
                        'order_date' => date('Y-m-d H:i:s',strtotime($data->order_date)),
                        'toko_type' => $data->top == 'T001' ? 'CASH' : 'TEMPO',
                        'acc_name' => $data->owner_name,
                        'due_date' => $data->due_date,
                        'total_bill' => (int)$data->total,
                        'credit_limit' => (float)$data->credit_limit_used_percentage,
                        'sales_name' => $data->sales_name,
                        'status' => $data->status
                    ];
                    fputcsv($handle,$row);
                }
            });
        
        fclose($handle);

        return response()->download('Export Invoices B2B (Internal).csv')->deleteFileAfterSend(true);
    }

    public function downloadInvoiceDetail($invoice_no, Request $request)
    {
        $invoice = Invoice::find($invoice_no);

        if ($invoice == null) {
            return $this->sendError('Invoice not found',Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $customer = Customer::find($invoice->customer_id);

        $invoice_details = InvoiceDetail::where('invoice_no',$invoice_no)->get();

        $item = [];

        $total_qty = 0;
        $total_bruto = 0;
        $total_netto = 0;

        for ($i=0; $i < count($invoice_details); $i++) { 
            $item[$i] = [
                'article_no' => $invoice_details[$i]->article,
                'description' => $invoice_details[$i]->article_description,
                'unit_price' => $invoice_details[$i]->price,
                'qty' => !is_null($invoice_details[$i]->qty) ? $invoice_details[$i]->qty : 'Tidak Ada',
                'bruto' => $invoice_details[$i]->gross_price,
                'disc' => $invoice_details[$i]->discount_percent,
                'netto' => $invoice_details[$i]->nett_price
            ];
            $total_qty+=!is_null($invoice_details[$i]->qty) ? $invoice_details[$i]->qty : 0;
            $total_bruto+=$invoice_details[$i]->gross_price;
            $total_netto+=$invoice_details[$i]->nett_price;
        }

        $data = [
            'customer_number' => $invoice->customer_id,
            'customer_name' => $customer->owner_name,
            'customer_address' => $customer->npwp_address, 
            'NPWP' => $customer->npwp,
            'invoice_no' => $invoice_no,
            'invoice_date' => $invoice->billing_date, 
            'due_date' => $invoice->due_date,
            'delivery_no' => $invoice->delivery_order_no,
            'po_number' => $invoice->po_no,
            'items' => $item,
            'total_qty' => $total_qty,
            'total_bruto' => $total_bruto,
            'total_netto' => $total_netto,
            'dp' => $invoice->down_payment,
            'due for payment' => $invoice->due_payment,
            'dpp' => $invoice->dpp,
            'ppn' => $invoice->tax_amount
        ];

        return $this->sendSuccess(null,$data);
    }

    public function uploadFaktur(Request $request, $invoice_no){
        // dd($request->file('file'));
        // Log::info($invoice_no);
        $inv = DB::table('invoice')
            ->where('invoice_no','=',$invoice_no)
            ->first();

        if($inv == null){
            return $this->sendError('invoice not found', 404);
        }

        try{
            DB::beginTransaction();
            if ($request->has('file')) {
                $file = $request->input('file');
                
                $faktur_file_path = $this->fileTransfer($file, 'faktur');
                if ($faktur_file_path['error'] == true) {
                    return $this->sendError($faktur_file_path['message']);
                }

                $path = '/'.$faktur_file_path['filepath'];

                DB::table('invoice')
                    ->where('invoice_no','=',$invoice_no)
                    ->update([
                        'tax_invoice_file_path' => $path,
                        'tax_invoice_file_name' => $file->getClientOriginalName(),
                        'tax_invoice_file_type' => $file->getType(),
                    ]);
            }
            DB::commit();
            return $this->sendSuccess('success', $path);
        }catch (\Exception $e){
            DB::rollBack();
            return $this->sendError($e->getMessage());
        }
    }
}
