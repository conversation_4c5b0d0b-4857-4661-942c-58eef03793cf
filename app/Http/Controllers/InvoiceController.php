<?php

namespace App\Http\Controllers;

use PDF;
use URL;
use App\Models\Order;
use App\Models\Company;
use App\Models\Invoice;
use App\Models\Product;
use App\Models\Customer;
use App\Models\Proforma;
use App\Models\OrderItem;
use App\Models\CompanyBank;
use App\Models\OrderCustom;
use Illuminate\Http\Request;
use App\Models\CustomerSales;
use App\Models\DeliveryOrder;
use App\Models\InvoiceDetail;
use Illuminate\Http\Response;
use App\Models\ProformaDetail;
use App\Models\CustomerShipment;
use Illuminate\Support\Facades\DB;
use App\Models\DeliveryOrderDetail;
use Illuminate\Support\Facades\Log;
use App\Interfaces\InvoiceInterface;
use App\Models\OrderCustomAttachment;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use App\Models\CustomerVirtualAccount;
use App\Repositories\InvoiceRepository;
use Illuminate\Support\Facades\Storage;

class InvoiceController extends Controller
{
    private $invoice;

    /**
     * @param InvoiceInterface $invoice
     */
    public function __construct(InvoiceInterface $invoice)
    {
        $this->invoice = $invoice;
    }


    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index(Request $request)
    {
        $cus_id = !isset($request->user()->customer->customer_id) ? CustomerSales::where('sales_id', $request->user()->sales->sales_id)->get()->pluck('customer_id') : $request->user()->customer->customer_id;
        return $this->invoice->getData($request, $cus_id);
    }

    public function getStatuses()
    {
        return $this->invoice->getStatuses();
    }

    public function getDetailItems($invoice_no, Request $request)
    {
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 12);
        $searchText = $request->input('search');

        $items = null;
        
        $inv = Invoice::where('invoice_no', $invoice_no)->first();
        if ($inv) {
            $items = InvoiceDetail::query()
                ->where('invoice_no', $inv->invoice_no)
                ->when($searchText, function ($query, $searchText) {
                    return $query->where(function ($q) use ($searchText) {
                        $q->where('product_name', 'like', '%' . $searchText . '%')
                          ->orWhere('article', 'like', '%' . $searchText . '%');
                    });
                })
                ->paginate($perPage, ['*'], 'page', $page);
        } else {
            $inv = Proforma::where('sales_order_no', $invoice_no)->first();
            if ($inv) {
                $items = ProformaDetail::query()
                    ->where('proforma_invoice_id', $inv->id)
                    ->when($searchText, function ($query, $searchText) {
                        return $query->where(function ($q) use ($searchText) {
                            $q->where('product_name', 'like', '%' . $searchText . '%')
                              ->orWhere('article', 'like', '%' . $searchText . '%');
                        });
                    })
                    ->paginate($perPage, ['*'], 'page', $page);
            }
        }

        if (!$items) {
            $items = new \Illuminate\Pagination\LengthAwarePaginator([], 0, $perPage, $page);
        }
        
        $data = [];
        foreach ($items as $item) {
            $discount = intval($item->discount_percent / 100 * $item->gross_price);
            $data[] = [
                'product_name' => $item->product_name,
                'sku' => $item->article,
                'product_size' => $item->product_size,
                'qty' => $item->qty,
                'price' => $item->price,
                'sub_total' => $item->gross_price,
                'discount' => $discount,
                'net_price' => $item->gross_price - $discount
            ];
        }

        $res = [
            'total_data' => $items->total(),
            'size' => intval($items->perPage()),
            'active_page' => $items->currentPage(),
            'total_page' => $items->lastPage(),
            'data' => $data
        ];

        return $this->sendSuccess("Item list retrieved successfully.", $res);
    }

    public function getDetailHeader($invoice_no, Request $request)
    {
        $data = Invoice::where('invoice_no', $invoice_no)->first();
        if (empty($data)) {
            $data = Proforma::where(function ($query) use ($invoice_no) {
                $query->where('id', $invoice_no)
                      ->orWhere('sales_order_no', $invoice_no)
                      ->orWhere('po_no', $invoice_no);
            })->first();
            $order = DeliveryOrder::where('sales_order_no', $data->sales_order_no)->first();
            $data->delivery_order_no = $order ? $order->delivery_order_no : null;
            $data->customer_id = $data->customer_external_id;
            $data->invoice_no = $invoice_no;
            $data->order_no = $data->po_no;
        }

        $oh_inv = Order::where('order_no', $invoice_no)->first();
        if ($data == null && $oh_inv == null) {
            return $this->sendError('Invoice tidak ditemukan');
        }

        $oh = $data != null ? Order::where('sales_order_no', $data->sales_order_no)->first() : $oh_inv;
        if ($oh->distribution_channel == 'WHOLESALES') {
            $status_order = DB::table('invoice')
                ->leftJoin('delivery_order', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
                ->leftJoin('order_header', 'order_header.sales_order_no', '=', 'invoice.sales_order_no')
                ->where('invoice.invoice_no', '=', $invoice_no)
                ->select('order_header.order_status', 'delivery_order.good_issue_date', 'order_header.created_date', 'order_header.total_discount', 'order_header.distribution_channel')
                ->first();

            $nomorresi = DB::table('delivery_number')
                ->leftJoin('invoice', 'invoice.invoice_no', '=', 'delivery_number.invoice_no')
                ->where('invoice.invoice_no', $invoice_no)
                ->select('delivery_number.delivery_no as noresi', 'delivery_number.delivery_name as namaresi')
                ->get()
                ->toArray();

            if ($data == null) {
                return $this->sendError('detail from invoice ' . $invoice_no . ' not found', 404);
            }

            $cust = Customer::query()
                ->where('customer_id', '=', $data->customer_id)
                ->first();

            $det = InvoiceDetail::query()
                ->where('invoice_no', '=', $invoice_no)
                ->select(
                    DB::raw('sum(gross_price) as subtotal'),
                    DB::raw('sum(discount_percent/100*gross_price) as discount'),
                    DB::raw('sum(nett_price) as total'),
                    DB::raw('sum(qty) as total_barang')
                )
                ->first();

            if (collect($det->toArray())->every(fn($value) => is_null($value))) {
                $det = ProformaDetail::query()
                    ->where('proforma_invoice_id', '=', $data->id)
                    ->select(
                        DB::raw('sum(gross_price) as subtotal'),
                        DB::raw('sum(discount/100*gross_price) as discount'),
                        DB::raw('sum(nett_price) as total'),
                        DB::raw('sum(qty) as total_barang')
                    )
                    ->first();
            }

            $perhitungan_diskon = InvoiceDetail::query()
                ->where('invoice_no', '=', $invoice_no)
                ->select(DB::raw('sum(gross_price-nett_price) as total_discount'))
                ->first();

            if(is_null($perhitungan_diskon->total_discount)){
                $perhitungan_diskon = ProformaDetail::query()
                ->where('proforma_invoice_id', '=', $data->id)
                ->select(DB::raw('sum(gross_price-nett_price) as total_discount'))
                ->first();
            }
            
            $do_det = DeliveryOrderDetail::query()
                ->where('delivery_order_no', '=', $data->delivery_order_no)
                ->select(DB::raw('sum(issued_qty) as qty'))
                ->first();

            $order = DeliveryOrder::query()
                ->where('delivery_order_no', '=', $data->delivery_order_no)
                ->first();

            if ($order == null) {
                return $this->sendError("order " . $data->delivery_order_no . " not found ", 404);
            }

            $shipment = CustomerShipment::query()
                ->where('customer_shipment_id', '=', $order->customer_shipment_id)
                ->first();

            $res = [
                'order' => [
                    'order_no' => $data->order_no,
                    'ref' => $data->sales_order_no,
                    'dn' => $data->delivery_order_no,
                    'delivery_status' => $status_order->order_status ?? $oh->order_status,
                    'billing' => $data->invoice_no,
                    'proforma_invoice_no' => $data->id ?? null,
                    'order_date' => $status_order ?? $oh->created_date,
                    'gi_date' => $status_order->good_issue_date ?? $order->good_issue_date,
                    'due_date' => $data->due_date,
                    'do_date' =>  $order->created_date,
                    'qty_items' => $do_det->qty,
                    'sub_total' => $det->subtotal,
                    'total_barang' => $det->total_barang,
                    'discount' => $perhitungan_diskon->total_discount,
                    'total' => $data->status == InvoiceRepository::BelumLunas ? $det->total - $data->down_payment : $det->total,
                    'down_payment' => $data->down_payment,
                    'status' => $data->status ?? 'Belum Lunas'
                ],
                'shipment' => [
                    'nomor_resi' => $nomorresi,
                    'address' => [
                        'name' => $shipment->name,
                        'company_name' => $cust->owner_name,
                        'phone' => $shipment->phone_number,
                        'address' => $shipment->address
                    ]
                ],
                'attachment' => [
                    'tax_url' => $data->tax_invoice_file_path !== '' ? env('S3_STREAM_URL') . $data->tax_invoice_file_path : '',
                    'invoice_url' => $data->invoice_file_path
                ]
            ];
        }

        if (in_array($oh->distribution_channel, ['B2B', 'W3', 'RE', 'RD'])) {
            // $nomorresi = DB::table('delivery_number')
            // ->leftJoin('invoice', 'invoice.invoice_no' ,'=','delivery_number.invoice_no')
            // ->where('invoice.invoice_no', $invoice_no)
            // ->select('delivery_number.delivery_no as noresi','delivery_number.delivery_name as namaresi')
            // ->get()
            // ->toArray();

            $oc_exs = OrderCustom::where('reference_id', $oh->order_no)
                ->exists();

            $do = DeliveryOrder::where('sales_order_no', $oh->sales_order_no)
                ->first();

            // $company = Company::query()
            //     ->where('npwp','=',$cust->npwp)
            //     ->first();

            $data_dp = Invoice::where('sales_order_no', $oh->sales_order_no)->where('invoice_type', 'DOWN PAYMENT')->first();
            $data_bill = Invoice::where('sales_order_no', $oh->sales_order_no)->where('invoice_type', 'BILLING')->first();
            $qty_items = 0;
            $sub_total = 0;
            $discount = 0;
            $total = 0;
            $dp = 0;

            $qty_items = OrderItem::where('order_no', $oh->order_no)->sum('qty');
            $sub_total = $oh->total;
            $discount = $oh->total_discount;
            $total = $oh->total_nett;
            $dp = $oh->dp_amount;

            if ($do != null) {
                $do_qty_items = 0;
                $do_sub_total = 0;
                foreach ($do->items as $item) {
                    $article = Product::where('article', $item->article)->first();
                    $do_qty_items += $item->issued_qty != null ? $item->issued_qty : $item->qty;
                    $custom_price = 0;
                    if ($oc_exs == true) {
                        $ocs = OrderCustom::where('reference_id', $oh->order_no)->pluck('id')->toArray();
                        $custom_price = OrderCustomAttachment::where('order_custom_id', $ocs)->sum('custom_price');
                    }
                    $do_sub_total += $item->issued_qty != null ? $item->issued_qty * ($article->price->amount + $custom_price) : $item->qty * ($article->price->amount + $custom_price);
                }

                $sub_total = $do_sub_total;
                $qty_items = $do_qty_items;
                $discount = !empty($do->issued_discount) ? $do->issued_discount : $do->discount;
                $total = $sub_total - $discount;
                $dp = $oh->dp_amount ?? 0;
            }

            if ($data_bill != null) {
                $det = InvoiceDetail::query()
                    ->where('invoice_no', '=', $data_bill->invoice_no)
                    ->select(
                        DB::raw('sum(gross_price) as subtotal'),
                        DB::raw('sum(discount_percent/100*gross_price) as discount'),
                        DB::raw('sum(nett_price) as total')
                    )
                    ->first();

                $qty_items = InvoiceDetail::where('invoice_no', $data_bill->invoice_no)->sum('qty');
                $sub_total = $det->subtotal;
                $discount = $det->discount;
                $total = $det->total;
                $dp = $oh->down_payment ?? 0;
            }

            $attachments = [
                'tax_url' => $data_bill != null ? ($data_bill->tax_invoice_file_path != '' ? env('S3_STREAM_URL') . $data_bill->tax_invoice_file_path : null) : null,
                'invoice_url' => $data_bill->invoice_file_path ?? null,
                'tax_url_dp' => $data_dp != null ? ($data_dp->tax_invoice_file_path != '' ? env('S3_STREAM_URL') . $data_dp->tax_invoice_file_path : null) : null,
                'invoice_url_dp' => $data_dp->invoice_file_path ?? null
            ];

            $res = [
                'order' => [
                    'order_no' => $oh->order_no,
                    'ref' => $oh->sales_order_no,
                    'dn' => $do->delivery_order_no ?? null,
                    'delivery_status' => $oh->order_status,
                    'billing' => $data_bill->invoice_no ?? null,
                    'invoice_no_dp' => $data_dp->invoice_no ?? null,
                    'proforma_invoice_no' => $data->id ?? null,
                    'order_date' => $oh->created_date,
                    'gi_date' => $do->good_issue_date ?? null,
                    'due_date' => $data_bill != null ? $data_bill->due_date : ($oh->dp_due_date ?? null),
                    'qty_items' => (int) $qty_items,
                    'sub_total' => (int) $sub_total,
                    'discount' => (int) $discount,
                    'total' => (int) $total,
                    'down_payment' => (int) $dp,
                    'status' => $data_bill != null ? $data_bill->status : ($data_dp != null ? $data_dp->status : 'BELUM DIBAYAR'),
                    'customCheck' => $oc_exs,
                    'invoice_type' => $data->invoice_type ?? 'ORDER'
                ],
                'shipment' => [
                    'nomor_resi' => [],
                    'address' => [
                        'name' => $oh->bill_to,
                        'company_name' => $oh->ship_to,
                        'phone' => $oh->bill_to_phone_number,
                        'address' => $oh->ship_to_address
                    ]
                ],
                'attachment' => $attachments
            ];
        }

        return $this->sendSuccess("success", $res);
    }

    public function listPayment($invoice_no, Request $request)
    {
        $cus_id = $request->user()->customer->customer_id;
        $data = Invoice::query()
            ->leftJoin('invoice_detail', 'invoice.invoice_no', '=', 'invoice_detail.invoice_no')
            //            ->leftJoin('delivery_order','invoice.delivery_order_no','=','delivery_order.delivery_order_no')
//            ->leftJoin('order','order.order_no', '=','invoice.order_no')
            ->where('invoice.invoice_no', '=', $invoice_no)
            ->where('invoice.customer_id', '=', $cus_id)
            ->select('invoice.*')
            ->first();

        if ($data == null) {
            return $this->sendError('list payment from invoice ' . $invoice_no . ' not found', 404);
        }

        $vas = CustomerVirtualAccount::query()
            ->where('customer_id', '=', $data->customer_id)
            ->get();
        $user_va = [];
        foreach ($vas as $va) {
            $uva = [
                'bank_name' => $va->bank_name,
                'va_no' => $va->virtual_account_no,
            ];
            $user_va[] = $uva;
        }

        $npwp = $request->user()->customer->npwp;
        $cbs = Company::query()
            ->leftJoin('company_bank', 'company.company_id', '=', 'company_bank.company_id')
            ->where('company.npwp', '=', $npwp)
            ->where('is_active', '=', '1')
            ->select('company_bank.*')
            ->get();

        $comp_bank = [];
        foreach ($cbs as $cb) {
            $ucb = [
                'bank_name' => $cb->bank_name,
                'account_name' => $cb->bank_account_name,
                'account_number' => $cb->bank_account_no,
            ];
            $comp_bank[] = $ucb;
        }

        $det = InvoiceDetail::query()
            ->where('invoice_no', '=', $invoice_no)
            ->select(
                DB::raw('sum(gross_price) as subtotal'),
                DB::raw('sum(discount_percent/100*gross_price) as discount'),
                DB::raw('sum(nett_price) as total')
            )
            ->first();

        $res = [
            'order_no' => $data->order_no,
            'invoice_no' => $data->invoice_no,
            'total' => $data->status == InvoiceRepository::BelumLunas ? $det->total - $data->down_payment : $det->total,
            'va' => $user_va,
            'bank_transfer' => $comp_bank
        ];

        return $this->sendSuccess('success', $res);
    }

    /**
     * FUNCTION GENERATE PDF PROFORMA INVOICE
     */
    public function downloadProforma($invoice_no, Request $request)
    {
        $data['proforma_invoice'] = DB::table('proforma_invoice as pinv')
            ->join('customers as cst', 'cst.customer_id', '=', 'pinv.customer_external_id')
            ->join('customer_shipment as cs', 'cs.customer_id', '=', 'pinv.customer_external_id')
            ->where('pinv.po_no', $invoice_no)
            ->select('pinv.*', 'cst.owner_name', 'cst.phone_number', 'cst.npwp', 'cs.address', 'cs.city', 'cs.province', 'cs.zip_code')
            ->first();

        $data['proforma_detail'] = ProformaDetail::query()
            ->join('proforma_invoice as pinv', 'pinv.id', '=', 'proforma_invoice_detail.proforma_invoice_id')
            ->where('pinv.po_no', $invoice_no)
            ->select('proforma_invoice_detail.*')
            ->get();

        $data['summary'] = ProformaDetail::query()
            ->join('proforma_invoice as pinv', 'pinv.id', '=', 'proforma_invoice_id')
            ->where('pinv.po_no', '=', $invoice_no)
            ->select(
                DB::raw('sum(proforma_invoice_detail.gross_price) as subtotal'),
                DB::raw('sum(proforma_invoice_detail.nett_price) as total'),
                DB::raw('sum(qty) as qty'),
                'pinv.down_payment',
                'pinv.down_payment_percentage'
            )
            ->first();

        $data['distribution_channel'] = Order::where('order_no', $invoice_no)->pluck('distribution_channel')->first();

        $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('pdf_proforma', $data);

        $name_file = 'Proforma_' . $data['proforma_invoice']->po_no . '.pdf';
        Storage::put('public/' . $name_file, $pdf->output());
        Cache::set($name_file, true, now()->addSeconds(10));
        return response(URL::to('/api/proforma/file/' . $name_file));
    }

    public function downloadInvoice($invoice_no, Request $request)
    {
        //         $cus_id = $request->user()->customer->customer_id;
        //         $inv = Invoice::query()
        //             ->where('invoice.invoice_no','=',$invoice_no)
        //             ->where('invoice.customer_id','=', $cus_id)
        //             ->select('invoice.*')
        //             ->first();

        //         if ($inv == null){
        //             return $this->sendError("invoice ".$invoice_no." not found ", 404);
        //         }

        //         $cust = $request->user()->customer;
        //         $order = DeliveryOrder::query()
        //             ->where('delivery_order_no', '=',$inv->delivery_order_no)
        //             ->first();
        //         if ($order == null){
        //             return $this->sendError("order ".$inv->delivery_order_no." not found ", 404);
        //         }
        //         $shipment = CustomerShipment::query()
        //             ->where('customer_shipment_id','=', $order->customer_shipment_id)
        //             ->first();

        //         $npwp = $cust->npwp;
        //         $company = Company::query()
        //             ->where('npwp','=',$npwp)
        //             ->first();

        //         $inv_det = Invoice::query()
        //             ->leftJoin('invoice_detail','invoice.invoice_no','=','invoice_detail.invoice_no')
        //             ->leftJoin('article', 'invoice_detail.article', '=','article.article')
        //             ->leftJoin('delivery_order_detail','invoice.delivery_order_no','=',
        //                 'delivery_order_detail.delivery_order_no')
        //             ->where('invoice.invoice_no','=',$invoice_no)
        //             ->select('invoice_detail.product_name','invoice_detail.product_variant',
        //                 'article.sku_code_c','invoice_detail.discount_percent',
        //                 DB::raw('sum(invoice_detail.price) as price'),
        //                 DB::raw('sum(invoice_detail.gross_price) as gross_price'),
        //                 DB::raw('sum(invoice_detail.nett_price) as net_price'),
        //                 DB::raw('sum(delivery_order_detail.qty) as qty')
        //             )
        //             ->groupBy(DB::raw('invoice_detail.product_name,invoice_detail.product_variant,
        //             invoice_detail.product_size,article.sku_code_c,invoice_detail.discount_percent'))
        //             ->get();

        //         $articles = [];
        //         $grand_tot_qty = 0;
        //         $grand_tot_bruto = 0;
        //         $grand_tot_netto = 0;
        //         foreach ($inv_det as $detail) {
        //             $det_qty = Invoice::query()
        //                 ->leftJoin('invoice_detail','invoice.invoice_no','=','invoice_detail.invoice_no')
        //                 ->leftJoin('article', 'invoice_detail.article', '=','article.article')
        //                 ->leftJoin('delivery_order_detail','invoice.delivery_order_no','=',
        //                     'delivery_order_detail.delivery_order_no')
        //                 ->where('invoice.invoice_no','=',$invoice_no)
        //                 ->where('invoice_detail.product_name', '=',$detail->product_name)
        //                 ->where('invoice_detail.product_variant','=', $detail->product_variant)
        //                 ->select('invoice_detail.product_size', 'delivery_order_detail.qty')
        //                 ->get();

        //             $product_name = $detail->sku_code_c.' '.$detail->product_name.' '.$detail->product_variant.':';
        //             $tot_gty = 0;
        //             foreach ($det_qty as $qty){
        //                 $product_name = $product_name.$qty->product_size.'/'.$qty->qty.' ';
        // //                $pss = [
        // //                    'size' => $qty->product_size,
        // //                    'qty' => ,
        // //                ];
        // //                $pqty[] = $pss;
        //                 $tot_gty = $tot_gty + $qty->qty;
        //             }
        //             Log::channel('stderr')->info('test = '.$det_qty);
        //             $article = [
        // //                'sku' => $detail->sku_code_c,
        // //                'product_name' => $detail->product_name,
        //                 'product_name' => $product_name,
        // //                'product_variant' => $detail->product_variant,
        //                 'qty' => $tot_gty,
        //                 'price' => $detail->price,
        //                 'bruto' => $detail->gross_price,
        //                 'discount_percentage' => $detail->discount_percent,
        //                 'nett_price' => $detail->net_price,
        //             ];
        //             $grand_tot_qty = $grand_tot_qty + $tot_gty;
        //             $grand_tot_bruto = $grand_tot_bruto + $detail->gross_price;
        //             $grand_tot_netto = $grand_tot_netto + $detail->net_price;
        //             $articles[] = $article;
        //         }

        //         $res['ikan'] = [
        //             'customer_name'=>$cust->owner_name,
        //             'customer_no' => '',
        //             'customer_address' => $shipment->address,
        //             'npwp' => $npwp,
        //             'company_name' => $company == null ? "":$company->company_name,
        //             'invoice_no' => $invoice_no,
        //             'invoice_date' => $inv->billing_date,
        //             'due_date' => $inv->due_date,
        //             'delivery_no' => $inv->delivery_order_no,
        //             'po_no' => $inv->order_no,
        //             'dp' => $inv->down_payment,
        //             'due_for_payment' => $inv->gross_price,
        //             'dpp' => $inv->dpp,
        //             'ppn' => $inv->tax_amount,
        //             'items' => $articles,
        //             'grand_total' => [
        //                 'qty' => $grand_tot_qty,
        //                 'bruto' => $grand_tot_bruto,
        //                 'netto' => $grand_tot_netto
        //             ]
        //         ];
        // ================================================================

        $data['invoice'] = DB::table('invoice')
        ->leftjoin('customers as cst', 'cst.customer_id', '=', 'invoice.customer_id')
        ->leftjoin('customer_shipment as cs', 'cs.customer_id', '=', 'cst.customer_id')
        ->leftjoin('order_header as oh', 'invoice.order_no', '=', 'oh.order_no')
        ->leftjoin('delivery_number as dn', 'invoice.invoice_no', '=', 'dn.invoice_no')
        ->leftjoin('delivery_order as do', 'invoice.delivery_order_no', '=', 'do.delivery_order_no')
        ->where('invoice.invoice_no', $invoice_no)
        ->select('invoice.*', 'cst.owner_name', 'cst.customer_id' ,'cst.npwp', 'cs.phone_number', 'cs.address as shipment_address', 'cs.city', 'cs.province', 'cs.zip_code', 'oh.distribution_channel', 'oh.shipping_charges', 'oh.order_status', 'oh.created_date as order_date', 'dn.delivery_no as delivery_order_no', 'do.good_issue_date')
        ->first();

        $data['invoice_detail'] = DB::table('invoice_detail as invd')
            ->leftjoin('invoice as inv', 'inv.invoice_no', '=', 'invd.invoice_no')
            ->leftjoin('article as art', 'art.article', '=', 'invd.article')
            ->leftJoin('master_color as mc', 'art.product_variant_c', '=', 'mc.key')
            ->where('inv.invoice_no', $invoice_no)
            ->select('invd.*', 'art.article_description', 'mc.value')
            ->get();

        $data['grand_total'] = InvoiceDetail::query()
            ->join('invoice as inv', 'inv.invoice_no', '=', 'invoice_detail.invoice_no')
            ->where('invoice_detail.invoice_no', '=', $invoice_no)
            ->select(
                DB::raw('sum(invoice_detail.gross_price) as subtotal'),
                DB::raw('sum(invoice_detail.discount_percent/100*invoice_detail.gross_price) as discount'),
                DB::raw('sum(invoice_detail.nett_price) as total'),
                DB::raw('sum(invoice_detail.qty) as qty'),
                'inv.down_payment'

            )
            ->first();

        if (empty($data['invoice'])) {
            $data['invoice'] = DB::table('proforma_invoice as pinv')
                ->join('customers as cst', 'cst.customer_id', '=', 'pinv.customer_external_id')
                ->join('customer_shipment as cs', 'cs.customer_id', '=', 'pinv.customer_external_id')
                ->join('delivery_order as do', 'pinv.sales_order_no', '=', 'do.sales_order_no')
                ->where(function ($query) use ($invoice_no) {
                    $query->where('pinv.id', $invoice_no)
                          ->orWhere('pinv.sales_order_no', $invoice_no)
                          ->orWhere('pinv.po_no', $invoice_no);
                })
                ->select('pinv.*', 'pinv.created_date as billing_date', 'pinv.sales_order_no as invoice_no', 'cst.owner_name', 'cst.customer_id' ,'cst.phone_number', 'cst.npwp', 'cs.address', 'cs.city', 'cs.province', 'cs.zip_code', 'do.delivery_order_no')
                ->first();

            $data['invoice_detail'] = ProformaDetail::query()
                ->join('proforma_invoice as pinv', 'pinv.id', '=', 'proforma_invoice_detail.proforma_invoice_id')
                ->where('proforma_invoice_id', $data['invoice']->id)
                ->select('proforma_invoice_detail.*')
                ->get();

            $data['grand_total'] = ProformaDetail::query()
                ->join('proforma_invoice as pinv', 'pinv.id', '=', 'proforma_invoice_id')
                ->where('proforma_invoice_id', $data['invoice']->id)
                ->select(
                    DB::raw('sum(proforma_invoice_detail.gross_price) as subtotal'),
                    DB::raw('sum(proforma_invoice_detail.nett_price) as total'),
                    DB::raw('sum(proforma_invoice_detail.discount/100*proforma_invoice_detail.gross_price) as discount'),
                    DB::raw('sum(qty) as qty'),
                    'pinv.down_payment',
                    'pinv.down_payment_percentage'
                )
                ->first();
        }

        if ($data['invoice'] && $data['invoice']->npwp) {
            try {
                $data['invoice']->npwp = Crypt::decrypt($data['invoice']->npwp);
            } catch (\Exception $e) {
                // Handle invalid decryption gracefully
                // $data['invoice']->npwp = $e->getMessage();
            }
        }

        $data['due_for_payment'] =  $data['grand_total']->total - $data['grand_total']->down_payment;
        
        // return PDF::loadView('pdf_invoice', $data)->setPaper('a4', 'potrait')->download();

        $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('pdf_invoice_new', $data)->setPaper('a4', 'potrait');

        $name_file = 'invoice_' . $data['invoice']->invoice_no . '.pdf';
        Storage::put('public/' . $name_file, $pdf->output());

        // $invoice_file  = Invoice::where('invoice_no', $invoice_no)->first();
        // // $invoice_file->invoice_file_path  = URL::.'/api/invoice/file/'.$name_file   ;
        // $invoice_file->invoice_file_path  = URL::to('/api/invoice/file/'.$name_file);
        // $invoice_file->update();

        Cache::set($name_file, true, now()->addSeconds(10));

        return response(env('APP_URL') . '/api/invoice/file/' . $name_file);
    }

    public function downloadDPInvoice($invoice_no, Request $request)
    {

        // ================================================================
        $data['invoice'] = DB::table('invoice')
            ->join('customers as cst', 'cst.customer_id', '=', 'invoice.customer_id')
            ->join('customer_shipment as cs', 'cs.customer_id', '=', 'cst.customer_id')
            ->where('invoice_no', $invoice_no)
            ->select('invoice.*', 'cst.owner_name', 'cst.phone_number', 'cst.npwp', 'cs.address', 'cs.city', 'cs.province', 'cs.zip_code')
            ->first();

        $data['invoice_detail'] = DB::table('invoice_detail as invd')
            ->join('invoice as inv', 'inv.invoice_no', '=', 'invd.invoice_no')
            ->join('article as art', 'art.article', '=', 'invd.article')
            ->where('inv.invoice_no', $invoice_no)
            ->select('invd.*', 'art.article_description')
            ->get();

        $data['grand_total'] = InvoiceDetail::query()
            ->where('invoice_no', '=', $invoice_no)
            ->select(
                DB::raw('sum(gross_price) as subtotal'),
                DB::raw('sum(discount_percent/100*gross_price) as discount'),
                DB::raw('sum(nett_price) as total'),
                DB::raw('sum(qty) as qty')
            )
            ->first();

        if (!$data['invoice']) {
            return $this->sendError('invoice not found');
        }


        $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('pdf_dp_invoice', $data);

        $name_file = 'invoice_' . $data['invoice']->invoice_no . '.pdf';
        Storage::put('public/' . $name_file, $pdf->output());

        // $invoice_file  = Invoice::where('invoice_no', $invoice_no)->first();
        // $invoice_file->invoice_file_path  = URL::.'/api/invoice/file/'.$name_file   ;
        // $invoice_file->invoice_file_path  = URL::to('/api/invoice/file/'.$name_file);
        // $invoice_file->update();

        Cache::set($name_file, true, now()->addSeconds(10));

        return response(URL::to('/api/invoice/file/' . $name_file));
    }

    public function url_download_invoice($file_name)
    {
        if (!Cache::has($file_name)) {
            return $this->sendError('URL Expired/Unauthorized', 403, 'failed');
        }
        return response()->download(storage_path('/app/public/' . $file_name));
    }

    public function url_download_proforma($file_name)
    {
        if (!Cache::has($file_name)) {
            return $this->sendError('URL Expired/Unauthorized', 403, 'failed');
        }
        return response()->file(storage_path('/app/public/' . $file_name));
    }
}