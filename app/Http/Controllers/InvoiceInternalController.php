<?php

namespace App\Http\Controllers;

use App\Models\CustomerSales;
use App\Models\SalesAssignment;
use Carbon\Carbon;
use App\Models\Invoice;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;
use App\Helpers\FileHelper;
use App\Models\Order;
use DateTime;
use Exception;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;

class InvoiceInternalController extends Controller
{
    use FileHelper;

    public function index(Request $request)
    {
        $billingNo = $request->input('billing_no');
        $accName = $request->input('acc_name');
        $salesName = $request->input('sales_name');
        $orderDateFrom = $request->input('order_date_from');
        $orderDateTo = $request->input('order_date_to');
        $status = $request->input('status');
        $tokoType = $request->input('toko_type');
        $creditLimit = $request->input('credit_limit');
        $dueDateFrom = $request->input('due_date_from');
        $dueDateTo = $request->input('due_date_to');
        $page = $request->input('page', 1);
        $perPage = $request->input('limit', 12);

        $salesId = auth()->user()->reference_id;
        $roles = @auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];

        $salesIdd = !in_array("0", $roles) ?
            array_map(
                function ($i) {
                    return $i->sales_id ?? '0'; },
                $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())
            )
            : [];

        array_push($salesIdd, $salesId);

        $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
            ->pluck('customer_id')->all();
           
        $datas = Order::query()
            ->leftJoin('invoice', 'order_header.sales_order_no','=','invoice.sales_order_no')
            ->leftJoin('proforma_invoice', 'order_header.sales_order_no','=','proforma_invoice.sales_order_no')
            ->leftJoin('delivery_order', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
            ->leftJoin('customers', 'order_header.customer_id', '=', 'customers.customer_id')
            ->leftJoin('customer_sales', 'customers.customer_id', '=', 'customer_sales.customer_id')
            ->leftJoin('credit_limit', 'customers.customer_id', '=', 'credit_limit.customer_external_id')
            ->leftJoin('sales', 'customer_sales.sales_id', '=', 'sales.sales_id')
            ->where('order_header.distribution_channel', 'WHOLESALES')
            ->whereNotIn('order_header.order_status',['Menunggu Konfirmasi', 'Menunggu Verifikasi', 'Baru', 'Batal', 'On Hold', 'Pending', 'Cancel', 'Reject'])
            ->where(function ($q) {
                $q->whereNotNull('invoice.invoice_no')
                    ->orWhereNotNull('proforma_invoice.sales_order_no');
            })
            ->when($billingNo, function ($query) use ($billingNo) {
                return $query->where('invoice.invoice_no', 'like', '%' . $billingNo . '%')
                    ->orWhere('proforma_invoice.sales_order_no', 'like', '%' . $billingNo . '%')
                    ->orWhere('order_header.order_no','LIKE','%'.$billingNo.'%');
            })
            ->when($accName, function ($query) use ($accName) {
                return $query->where('order_header.bill_to', 'like', '%' . $accName . '%');
            })
            ->when($salesName, function ($query) use ($salesName) {
                return $query->where('order_header.sales_name', 'like', '%' . $salesName . '%');
            })
            ->when($orderDateFrom, function ($q) use ($orderDateFrom) {
                $orderDateFrom = Carbon::parse($orderDateFrom)->format('Y-m-d H:i:s');
                $q->whereDate('delivery_order.created_date', '>=', $orderDateFrom);
            })
            ->when($orderDateTo, function ($q) use ($orderDateTo) {
                $orderDateTo = Carbon::parse($orderDateTo)->format('Y-m-d H:i:s');
                $q->whereDate('delivery_order.created_date', '<=', $orderDateTo);
            })
            ->when($status, function ($query) use ($status) {
                return $query->whereIn('invoice.status', explode(',', $status));
            })
            ->when($tokoType, function ($query) use ($tokoType) {
                if ($tokoType == 'cash') {
                    return $query->where('customers.top', '=', 'T001');
                } else {
                    return $query->where('customers.top', '!=', 'T001');
                }
            })
            ->when($dueDateFrom, function ($q) use ($dueDateFrom) {
                $dueDateFrom = Carbon::parse($dueDateFrom)->format('Y-m-d H:i:s');
                $q->whereDate('invoice.due_date', '>=', $dueDateFrom);
            })
            ->when($dueDateTo, function ($q) use ($dueDateTo) {
                $dueDateTo = Carbon::parse($dueDateTo)->format('Y-m-d H:i:s');
                $q->whereDate('invoice.due_date', '<=', $dueDateTo);
            })
            ->when(!in_array("0", $roles),function($query) use($customerIdList){
                $query->whereIn('order_header.customer_id', $customerIdList);
            });
            // ->when($creditLimitFrom, function ($query) use($creditLimitFrom, $creditLimitTo){
            //     return $query->whereBetween('customers.credit_limit_used_percentage',[$creditLimitFrom, $creditLimitTo]);
            // })

        // if (!in_array("0", $roles)) {
        //     $datas = $datas->whereIn('order_header.customer_id', $customerIdList);
        // }

        if ($creditLimit) {
            $creditLimit = str_replace('-', ',', $creditLimit);
            $arrLimit = explode(',', $creditLimit);
            $param = [min($arrLimit), max($arrLimit)];
            $datas = $datas->whereBetween('credit_limit.credit_limit_used_percentage', $param);
        }
        
        $latest = $datas->pluck('invoice.modified_date')->sortByDesc('invoice.modified_date')->first() ?? date('Y-m-d H:i:s');

        $datas = $datas->select(
            'invoice.invoice_no',
            'proforma_invoice.sales_order_no as pi_so',
            'order_header.sales_order_no as oh_so',
            'order_header.created_date',
            'customers.top',
            'order_header.bill_to as owner_name',
            'invoice.due_date as i_date',
            'proforma_invoice.due_date as pi_date',
            'invoice.gross_price as i_total',
            'proforma_invoice.gross_price as pi_total',
            'order_header.total as oh_total',
            'credit_limit.credit_limit_used_percentage',
            'order_header.sales_name',
            'invoice.status as i_status',
            'order_header.payment_status as oh_status',
            'delivery_order.created_date as do_date',
        )
            ->orderBy('delivery_order.created_date','desc')
            ->paginate($perPage, ['*'], 'page', $page);

        $list = [];
        foreach ($datas as $data) {
            $item = [
                'billing' => $data->invoice_no ? $data->invoice_no : ($data->pi_so ?? $data->oh_so),
                'oder_date' => date('Y-m-d H:i:s', strtotime($data->created_date)),
                'toko_type' => $data->top == 'T001' ? 'CASH' : 'TEMPO',
                'acc_name' => $data->owner_name,
                'due_date' => $data->i_date ? $data->i_date : ($data->pi_date ?? ''),
                'do_date' => date('Y-m-d H:i:s', strtotime($data->do_date)),
                'total_bill' => $data->i_total ? (int) $data->i_total : ((int) $data->pi_total ?? (int) $data->oh_total),
                'credit_limit' => (float) $data->credit_limit_used_percentage,
                'sales_name' => $data->sales_name,
                'status' => $data->i_status ? $data->i_status : ($data->oh_status ?? 'Belum Dibayar')
            ];

            $list[] = $item;
        }

        $res = [
            'latest_update' => $latest,
            'total_data' => $datas->total(),
            'size' => intval($datas->perPage()),
            'active_page' => $datas->currentPage(),
            'total_page' => $datas->lastPage(),
            'data' => $list
        ];

        return $this->sendSuccess('Invoice list retrieved successfully', $res);
    }

    public function downloadInvoicesNew(Request $request)
    {
        try {
            $status = $request->input('status');
            $orderDateFrom = $request->input('order_date_from');
            $orderDateTo = $request->input('order_date_to');
            $billingNo = $request->input('billing_no');
            $accName = $request->input('acc_name');
            $salesName = $request->input('sales_name');
            $tokoType = $request->input('toko_type');
            $creditLimits = $request->input('credit_limit');
            $dueDateFrom = $request->input('due_date_from');
            $dueDateTo = $request->input('due_date_to');

            if (empty($orderDateFrom) || empty($orderDateTo)) {
                throw new Exception('Filter date diperlukan. Harap berikan parameter date from dan date to');
            }
        
            $start = new DateTime($orderDateFrom);
            $end = new DateTime($orderDateTo);
            if ($start->diff($end)->days > 90) {
                throw new Exception('Rentang tanggal tidak boleh melebihi 90 hari');
            }

            $salesId = auth()->user()->reference_id;
            $roles = @auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
            Log::info('sales id = ' . $salesId);

            $salesIdd = !in_array("0", $roles) ?
                array_map(
                    function ($i) {
                        return $i->sales_id ?? '0'; },
                    $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())
                )
                : [];

            array_push($salesIdd, $salesId);

            $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                ->pluck('customer_id')->all();

            $datas = Order::query()
                ->leftJoin('invoice', 'order_header.sales_order_no','=','invoice.sales_order_no')
                ->leftJoin('proforma_invoice', 'order_header.sales_order_no','=','proforma_invoice.sales_order_no')
                ->leftJoin('delivery_order', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
                ->leftJoin('customers', 'order_header.customer_id', '=', 'customers.customer_id')
                ->leftJoin('customer_sales', 'customers.customer_id', '=', 'customer_sales.customer_id')
                ->leftJoin('credit_limit', 'customers.customer_id', '=', 'credit_limit.customer_external_id')
                ->leftJoin('sales', 'customer_sales.sales_id', '=', 'sales.sales_id')
                ->where('order_header.distribution_channel', 'WHOLESALES')
                ->whereNotIn('order_header.order_status',['Menunggu Konfirmasi', 'Menunggu Verifikasi', 'Baru', 'Batal', 'On Hold', 'Pending', 'Cancel', 'Reject'])
                ->where(function ($q) {
                    $q->whereNotNull('invoice.invoice_no')
                        ->orWhereNotNull('proforma_invoice.sales_order_no');
                })
                ->when($status, function ($query) use ($status) {
                    return $query->whereIn('invoice.status', explode(',', $status));
                })
                ->when($billingNo, function ($query) use ($billingNo) {
                    return $query->where('invoice.invoice_no', 'like', '%' . $billingNo . '%')
                        ->orWhere('proforma_invoice.sales_order_no', 'like', '%' . $billingNo . '%')
                        ->orWhere('order_header.order_no','LIKE','%'.$billingNo.'%');
                })
                ->when($accName, function ($query) use ($accName) {
                    return $query->where('order_header.bill_to', 'like', '%' . $accName . '%');
                })
                ->when($salesName, function ($query) use ($salesName) {
                    return $query->where('order_header.sales_name', 'like', '%' . $salesName . '%');
                })
                ->when($tokoType, function ($query) use ($tokoType) {
                    if ($tokoType == 'cash') {
                        return $query->where('customers.top', '=', 'T001');
                    } else {
                        return $query->where('customers.top', '!=', 'T001');
                    }
                })
                ->when($orderDateFrom, function ($q) use ($orderDateFrom) {
                    $orderDateFrom = Carbon::parse($orderDateFrom)->format('Y-m-d H:i:s');
                    $q->whereDate('delivery_order.created_date', '>=', $orderDateFrom);
                })
                ->when($orderDateTo, function ($q) use ($orderDateTo) {
                    $orderDateTo = Carbon::parse($orderDateTo)->format('Y-m-d H:i:s');
                    $q->whereDate('delivery_order.created_date', '<=', $orderDateTo);
                })
                ->when($dueDateFrom, function ($q) use ($dueDateFrom) {
                    $dueDateFrom = Carbon::parse($dueDateFrom)->format('Y-m-d H:i:s');
                    $q->whereDate('invoice.due_date', '>=', $dueDateFrom);
                })
                ->when($dueDateTo, function ($q) use ($dueDateTo) {
                    $dueDateTo = Carbon::parse($dueDateTo)->format('Y-m-d H:i:s');
                    $q->whereDate('invoice.due_date', '<=', $dueDateTo);
                })
                ->when(!in_array("0", $roles),function($query) use($customerIdList){
                    $query->whereIn('order_header.customer_id', $customerIdList);
                });

            if ($creditLimits) {
                $creditLimits = array($creditLimits);
                foreach ($creditLimits as $creditLimit) {
                    $datas = $datas->whereBetween('credit_limit.credit_limit_used_percentage', explode('-', $creditLimit));
                }
            }

            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            $headers = ['Billing', 'Order Date', 'Toko Type', 'Account Name', 'Due Date', 'Total Bill', 'Credit Limit', 'Sales Name', 'Status'];
            $sheet->fromArray($headers, null, 'A1');

            $sheet->getStyle('A1:I1')->applyFromArray([
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => '808080']
                ],
                'font' => [
                    'bold' => true,
                    'color' => ['argb' => '000000']
                ],
            ]);
                
            $rowIndex = 2;

            $datas = $datas->select(
                'invoice.invoice_no',
                'proforma_invoice.sales_order_no as pi_so',
                'order_header.sales_order_no as oh_so',
                'order_header.created_date',
                'customers.top',
                'order_header.bill_to as owner_name',
                'invoice.due_date as i_date',
                'proforma_invoice.due_date as pi_date',
                'invoice.gross_price as i_total',
                'proforma_invoice.gross_price as pi_total',
                'order_header.total as oh_total',
                'credit_limit.credit_limit_used_percentage',
                'order_header.sales_name',
                'invoice.status as i_status',
                'order_header.payment_status as oh_status',
                'delivery_order.created_date as do_date',
            )
                ->orderBy('delivery_order.created_date', 'desc')
                ->chunk(100, function ($datas) use (&$sheet, &$rowIndex) {
                    foreach ($datas as $data) {
                        $sheet->fromArray([
                            $data->invoice_no ? $data->invoice_no : ($data->pi_so ?? $data->oh_so),
                            date('Y-m-d H:i:s', strtotime($data->do_date)),
                            $data->top == 'T001' ? 'CASH' : 'TEMPO',
                            $data->owner_name,
                            $data->i_date ? $data->i_date : ($data->pi_date ?? ''),
                            (string) ($data->i_total ? (int) $data->i_total : ((int) $data->pi_total ?? (int) $data->oh_total)),
                            (string) $data->credit_limit_used_percentage . ' %',
                            $data->sales_name,
                            $data->i_status ? $data->i_status : ($data->oh_status ?? 'Belum Dibayar')
                        ], null, "A$rowIndex");
                        $rowIndex++;
                    }
                });

            $highestRow = $sheet->getHighestRow();
            $highestColumn = $sheet->getHighestColumn();
            $sheet->getStyle("A1:$highestColumn$highestRow")->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        'color' => ['argb' => '000000'],
                    ],
                ],
            ]);

            foreach (range('A', $highestColumn) as $columnID) {
                $sheet->getColumnDimension($columnID)->setAutoSize(true);
            }

            $writer = new Xls($spreadsheet);
            $response = new StreamedResponse(fn() => $writer->save('php://output'));

            $response->headers->set('Content-Type', 'application/vnd.ms-excel');
            $response->headers->set('Content-Disposition', 'attachment; filename="Export Wholesales Invoices.xls"');
            $response->headers->set('Cache-Control', 'max-age=0');

            return $response;
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return $this->sendError($e->getMessage(), 400);
        }
    }

    public function downloadInvoices(Request $request)
    {
        $status = $request->input('status');
        $orderDateFrom = $request->input('order_date_from');
        $orderDateTo = $request->input('order_date_to');
        $billingNo = $request->input('billing_no');
        $accName = $request->input('acc_name');
        $salesName = $request->input('sales_name');
        $tokoType = $request->input('toko_type');
        $creditLimits = $request->input('credit_limit');
        $dueDateFrom = $request->input('due_date_from');
        $dueDateTo = $request->input('due_date_to');

        $salesId = auth()->user()->reference_id;
        $roles = @auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
        Log::info('sales id = ' . $salesId);

        $salesIdd = !in_array("0", $roles) ?
            array_map(
                function ($i) {
                    return $i->sales_id ?? '0'; },
                $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())
            )
            : [];

        array_push($salesIdd, $salesId);

        $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
            ->pluck('customer_id')->all();

        $col = [
            'Billing',
            'Order Date',
            'Toko Type',
            'Acc Name',
            'Due Date',
            'Total Bill',
            'Credit Limit',
            'Sales Name',
            'Status'
        ];

        $handle = fopen('Export Invoices Wholesales (Internal).csv', 'w');

        fputcsv($handle, $col);

        $datas = DB::table('invoice')
            ->leftJoin('order_header', 'invoice.order_no', '=', 'order_header.order_no')
            ->leftJoin('customers', 'invoice.customer_id', '=', 'customers.customer_id')
            ->leftJoin('customer_sales', 'customers.customer_id', '=', 'customer_sales.customer_id')
            ->leftJoin('credit_limit', 'customers.customer_id', '=', 'credit_limit.customer_external_id')
            ->leftJoin('sales', 'customer_sales.sales_id', '=', 'sales.sales_id')
            ->where('order_header.distribution_channel', 'WHOLESALES')
            ->when($status, function ($query) use ($status) {
                return $query->whereIn('invoice.status', explode(',', $status));
            })
            ->when($billingNo, function ($query) use ($billingNo) {
                return $query->where('invoice.invoice_no', 'like', '%' . $billingNo . '%');
            })
            ->when($accName, function ($query) use ($accName) {
                return $query->where('order_header.bill_to', 'like', '%' . $accName . '%');
            })
            ->when($salesName, function ($query) use ($salesName) {
                return $query->where('order_header.sales_name', 'like', '%' . $salesName . '%');
            })
            ->when($tokoType, function ($query) use ($tokoType) {
                if ($tokoType == 'cash') {
                    return $query->where('customers.top', '=', 'T001');
                } else {
                    return $query->where('customers.top', '!=', 'T001');
                }
            })
            ->when($orderDateFrom, function ($q) use ($orderDateFrom) {
                $orderDateFrom = Carbon::parse($orderDateFrom)->format('Y-m-d H:i:s');
                $q->whereDate('order_header.created_date', '>=', $orderDateFrom);
            })
            ->when($orderDateTo, function ($q) use ($orderDateTo) {
                $orderDateTo = Carbon::parse($orderDateTo)->format('Y-m-d H:i:s');
                $q->whereDate('order_header.created_date', '>=', $orderDateTo);
            })
            ->when($dueDateFrom, function ($q) use ($dueDateFrom) {
                $dueDateFrom = Carbon::parse($dueDateFrom)->format('Y-m-d H:i:s');
                $q->whereDate('invoice.due_date', '>=', $dueDateFrom);
            })
            ->when($dueDateTo, function ($q) use ($dueDateTo) {
                $dueDateTo = Carbon::parse($dueDateTo)->format('Y-m-d H:i:s');
                $q->whereDate('invoice.due_date', '<=', $dueDateTo);
            });
        // ->when($orderDateFrom, function ($query) use($orderDateFrom, $orderDateTo){
        //     Log::info($orderDateFrom.' + '.$orderDateTo);
        //     return $query->whereBetween('order_header.created_date',[$orderDateFrom, $orderDateTo]);
        // })
        // ->when($dueDateFrom, function ($query) use($dueDateFrom, $dueDateTo){
        //     return $query->whereBetween('invoice.due_date',[$dueDateFrom, $dueDateTo]);
        // })
        // ->when($creditLimitFrom, function ($query) use($creditLimitFrom, $creditLimitTo){
        //     return $query->whereBetween('customers.credit_limit_used_percentage',[$creditLimitFrom, $creditLimitTo]);
        // })
        if (!in_array("0", $roles)) {
            $datas = $datas->whereIn('order_header.customer_id', $customerIdList);
        }

        if ($creditLimits) {
            $creditLimits = array($creditLimits);
            foreach ($creditLimits as $creditLimit) {
                $datas = $datas->whereBetween('credit_limit.credit_limit_used_percentage', explode('-', $creditLimit));
            }
        }

        $datas = $datas->select(
            'invoice.invoice_no',
            'order_header.created_date',
            'customers.top',
            'order_header.bill_to as owner_name',
            'invoice.due_date',
            'invoice.gross_price as total',
            'credit_limit.credit_limit_used_percentage',
            'order_header.sales_name',
            'invoice.status'
        )
            ->orderBy('order_header.created_date', 'desc')
            ->chunk(100, function ($datas) use ($handle) {
                foreach ($datas as $data) {
                    $row = [
                        'billing' => $data->invoice_no,
                        'order_date' => date('Y-m-d H:i:s', strtotime($data->created_date)),
                        'toko_type' => $data->top == 'T001' ? 'CASH' : 'TEMPO',
                        'acc_name' => $data->owner_name,
                        'due_date' => $data->due_date,
                        'total_bill' => (int) $data->total,
                        'credit_limit' => (float) $data->credit_limit_used_percentage,
                        'sales_name' => $data->sales_name,
                        'status' => $data->status
                    ];
                    fputcsv($handle, $row);
                }
            });

        fclose($handle);

        return response()->download('Export Invoices Wholesales (Internal).csv')->deleteFileAfterSend(true);
    }

    public function download($invoice_no)
    {
        $datas = Invoice::query()
            ->leftJoin('order_header', 'invoice.order_no', '=', 'order_header.order_no')
            ->leftJoin('invoice_detail', 'invoice.invoice_no', '=', 'invoice_detail.invoice_no')
            ->where('invoice.invoice_no', '=', $invoice_no)
            ->select(
                'order_header.created_date',
                'invoice.sales_order_no',
                'invoice_detail.article',
                'invoice_detail.product_name',
                'invoice_detail.product_variant',
                'invoice_detail.product_size'
            )
            ->get();

        $list = [];
        foreach ($datas as $data) {
            $item = [
                'so_doc_date' => date('Y-m-d H:i:s', strtotime($data->created_date)),
                'so_number' => $data->sales_order_no,
                'article_no' => $data->article,
                'article_desc' => $data->product_name . ' ' . $data->product_variant . ' ' . $data->product_size
            ];
            $list[] = $item;
        }

        return $this->sendSuccess('success', $list);
    }

    public function deleteFaktur(Request $request, $invoice_no){
        $invoice = Invoice::where('invoice_no', $invoice_no)->first();

        if(empty($invoice)){
            return $this->sendError('invoice not found', 404);
        }

        if($invoice->tax_invoice_file_path == null){
            return $this->sendError('tidak ada file yang dihapus', 404);
        }

        $files = \Storage::disk('s3')->exists($invoice->tax_invoice_file_path);

        if ($files) {
            \Storage::disk('s3')->delete($invoice->tax_invoice_file_path);
            $invoice->update([
                'tax_invoice_file_path' => null,
                'tax_invoice_file_name' => null,
                'tax_invoice_file_type' => null,
            ]);
        } else {
            return $this->sendError('file not founc', 404);
        }

        return $this->sendSuccess('faktur pajak berhasil dihapus!', $invoice);
    }
    public function uploadFaktur(Request $request, $invoice_no)
    {
        Log::info($invoice_no);
        $inv = DB::table('invoice')
            ->where('invoice_no', '=', $invoice_no)
            ->first();

        if ($inv == null) {
            return $this->sendError('invoice not found', 404);
        }

        try {
            DB::beginTransaction();
            if ($request->has('file')) {

                $file = $request->file;


                $originalName = $file->getClientOriginalName();
                $newFileName = 'staging/' . env('S3_FAKTUR_FOLDER', 'customer-faktur-dev/'). $invoice_no . '-' . $request->no_faktur_pajak . '-faktur-'  . $originalName;

                // return $newFileName;

                $faktur_file_path = $this->fileTransfer($newFileName, 'faktur');
                if ($faktur_file_path['error'] == true) {
                    return $this->sendError($faktur_file_path['message']);
                }
                // $finfo = finfo_open(FILEINFO_MIME_TYPE);

                // $mime_type = finfo_file($finfo,$file);

                // finfo_close($finfo);

                // $allowedMimeTypes = array(
                //     'application/pdf'
                // );

                // if (!in_array($mime_type, $allowedMimeTypes)) {
                //     return $this->sendError('pdf files only', 400);
                // }

                // if($file->getSize()> 2097152){
                //     return $this->sendError('uploaded file too large', 400);
                // }


                // $file_name = $invoice_no.'-faktur-'.uniqid().'.pdf';
                // $file_path = env('S3_FAKTUR_FOLDER').$file_name;
                // Storage::disk('s3')->put(substr($file_path,1), file_get_contents($file));
                DB::table('invoice')
                    ->where('invoice_no', '=', $invoice_no)
                    ->update([
                        'tax_invoice_file_path' => '/' . $faktur_file_path['filepath'],
                        'tax_invoice_file_name' => pathinfo($file)['basename'],
                        'tax_invoice_file_type' => 'file',
                    ]);
            }
            DB::commit();
            return $this->sendSuccess('success', $file);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError($e->getMessage());
        }
    }

    public function downloadFaktur($invoice_no)
    {

        $inv = DB::table('invoice')
            ->where('invoice_no', '=', $invoice_no)
            ->first();

        if ($inv == null) {
            return $this->sendError('invoice not found', 404);
        }
        $path = env('S3_STREAM_URL') . $inv->tax_invoice_file_path;
        Log::info($path);
        if ($path) {
            return $this->sendSuccess('get link success', $path);
        }
        return $this->sendError('file not found');
    }
}