<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class MenuController extends Controller
{
    public function getMenu(Request $req)
    {
        $menus = config('menu');
        $sidemenu = [];
        foreach ($menus as $i => $attributes) {
            foreach ($attributes as $ii => $attribute) {
                if ($ii == 'childs') {
                    foreach ($attributes['childs'] as $iii => $submenu) {
                        if (!in_array($iii, $req->user()->menus)) {
                            continue;
                        }
                        $sidemenu[$i]['childs'][$iii] = $submenu;
                    }
                }else {
                    $sidemenu[$i][$ii] = $attribute;
                    $sidemenu[$i]['childs'] = [];
                }
            }
        }

        return response()->json($sidemenu);
    }
}
