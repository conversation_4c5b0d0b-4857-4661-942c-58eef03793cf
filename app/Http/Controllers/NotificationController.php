<?php

namespace App\Http\Controllers;

use App\Helpers\RestHelper;
use Illuminate\Http\Request;
use App\Models\CustomerSales;
use App\Models\MasterParameter;
use App\Models\UserNotification;
use Illuminate\Support\Facades\Cache;
use App\Http\Resources\User\NotificationResource;


class NotificationController extends Controller
{
    public function getNotification(Request $request)
    {
        $user = $request->user();
        $reference_id = null;
        $data = UserNotification::query();

        if ($user->reference_object == 'customer') {
            $reference_id = $user->customer->customer_id;
            $category = $request->input('category');

            $data->where('user_id', $reference_id);
            if (in_array($category, ['Transaksi', 'Info'])) {
                $data->where('category', $category);
            }
        } else if ($user->reference_object == 'sales') {
            $reference_id = $user->sales->sales_id;
            $channel = strtoupper($request->input('channel'));

            $sales_all = [];

            if (in_array($channel, ['WHOLESALES', 'B2B'])) {
                $sales_all = MasterParameter::where('group_key', 'SALES_NOTIF')
                    ->where('key', $channel)
                    ->pluck('value')
                    ->toArray();
                $customersSales = CustomerSales::where('sales_id', $reference_id)->pluck('customer_id')->toArray();
                array_merge([$reference_id], $customersSales);
                $data->whereIn('user_id', array_merge([$reference_id], $sales_all))
                    ->where('channel', $channel);
            } else {
                $channelKeys = array_unique(array_map('strtoupper', $user->business_units()->pluck('name')->toArray()));
                $sales_all = MasterParameter::where('group_key', 'SALES_NOTIF')
                    ->whereIn('key', $channelKeys)
                    ->pluck('value')
                    ->toArray();
                $data->whereIn('user_id', array_merge([$reference_id], $sales_all));
            }
        }

        $data = $data->orderBy('is_read')->orderByDesc('created_date')->pager($request);

        return $this->sendSuccess(null, $data, null, false, NotificationResource::class);
    }

    public function readAll(Request $request)
    {
        $reference_id = '';
        $user = $request->user();

        if ($user->reference_object == 'customer') {
            $reference_id = $user->customer->customer_id;
        } else if ($user->reference_object == 'sales') {
            $reference_id_array = [];
            $bu = $user->business_units()->pluck('name')->toArray();

            // if(in_array('Reseller',$bu)){
            //     $reference_id = MasterParameter::where('group_key','SALES_NOTIF')->where('key','RESELLER')->first()->value;
            //     array_push($reference_id_array, $reference_id);
            // }

            if (in_array('B2B', $bu)) {
                $reference_id = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'B2B')->first()->value;
                array_push($reference_id_array, $reference_id);
            }

            // if(in_array('WAB',$bu)){
            //     $reference_id = MasterParameter::where('group_key','SALES_NOTIF')->where('key','WAB')->first()->value;
            //     array_push($reference_id_array, $reference_id);
            // }

            // if(in_array('Wholesales',$bu)){
            //     $reference_id = MasterParameter::where('group_key','SALES_NOTIF')->where('key','WHOLESALES')->first()->value;
            //     array_push($reference_id_array, $reference_id);
            // }

            $reference_id = $user->sales->sales_id;
            array_push($reference_id_array, $reference_id);
            UserNotification::whereIn('user_id', $reference_id_array)
                ->update(['is_read' => 1]);
            return $this->sendSuccess('All notification marked as read');
        }

        UserNotification::where('user_id', $reference_id)
            ->update(['is_read' => 1]);
        return $this->sendSuccess('All notification marked as read');
    }

    public function newNotifs(Request $request)
    {
        return $this->doStore(UserNotification::class, $request, [], false, true);
    }

    public function getDetailNotification(Request $request, $notifId)
    {
        $notification = UserNotification::find($notifId);

        if (!$notification) {
            return $this->sendError('Notification not found', [], 404);
        }

        return $this->sendSuccess(
            'Notification detail retrieved successfully',
            new NotificationResource($notification)
        );
    }

    public function readNotification(Request $request, $notification_id)
    {
        $user = $request->user();
        $reference_id = '';

        if ($user->reference_object == 'customer') {
            $reference_id = $user->customer->customer_id;
        } else if ($user->reference_object == 'sales') {
            $reference_id_array = [];
            $bu = $user->business_units()->pluck('name')->toArray();

            if (in_array('B2B', $bu)) {
                $reference_id = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'B2B')->first()->value;
                array_push($reference_id_array, $reference_id);
            }

            // if (in_array('Wholesales', $bu)) {
            //     $reference_id = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'WHOLESALES')->first()->value;
            //     array_push($reference_id_array, $reference_id);
            // }

            $reference_id = $user->sales->sales_id;
            array_push($reference_id_array, $reference_id);

            $notification = UserNotification::whereIn('user_id', $reference_id_array)
                ->where('id', $notification_id)
                ->first();

            if ($notification) {
                $notification->update(['is_read' => 1]);
                return $this->sendSuccess('Notification marked as read');
            } else {
                return $this->sendError('Notification not found');
            }
        }

        $notification = UserNotification::where('user_id', $reference_id)
            ->where('id', $notification_id)
            ->first();

        if ($notification) {
            $notification->update(['is_read' => 1]);
            return $this->sendSuccess('Notification marked as read');
        } else {
            return $this->sendError('Notification not found');
        }
    }

    public function deleteNotification(Request $request, $notifId)
    {
        $notification = UserNotification::find($notifId);

        if (!$notification) {
            return $this->sendError('Notification not found', [], 404);
        }

        $notification->delete();

        return $this->sendSuccess(
            'Notification deleted successfully',
            []
        );
    }

    public function getNotificationCount(Request $request)
    {
        $user = $request->user();
        $reference_id = null;
        $query = UserNotification::query();

        if ($user->reference_object === 'customer') {
            $reference_id = $user->customer->customer_id;
            $query->where('user_id', $reference_id);
        } else if ($user->reference_object === 'sales') {
            $reference_id = $user->sales->sales_id;
            $channel = strtoupper($request->input('channel'));

            if (in_array($channel, ['WHOLESALES', 'B2B'])) {
                $sales_all = MasterParameter::where('group_key', 'SALES_NOTIF')
                    ->where('key', $channel)
                    ->pluck('value')
                    ->toArray();
                $query->whereIn('user_id', array_merge([$reference_id], $sales_all))
                    ->where('channel', $channel);
            } else {
                $channelKeys = array_unique(array_map('strtoupper', $user->business_units()->pluck('name')->toArray()));
                $sales_all = MasterParameter::where('group_key', 'SALES_NOTIF')
                    ->whereIn('key', $channelKeys)
                    ->pluck('value')
                    ->toArray();
                $query->whereIn('user_id', array_merge([$reference_id], $sales_all));
            }
        }

        $baseQuery = $query->where('is_read', false);

        $total = (clone $baseQuery)->count();
        $info = (clone $baseQuery)->where('category', 'Info')->count();
        $transaksi = (clone $baseQuery)->where('category', 'Transaksi')->count();

        return $this->sendSuccess(null, [
            'Semua' => $total,
            'Info' => $info,
            'Transaksi' => $transaksi,
        ]);
    }
}