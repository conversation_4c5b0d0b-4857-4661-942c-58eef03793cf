<?php

namespace App\Http\Controllers;

use App\Models\CustomPrice;
use App\Models\MasterParameter;
use App\Models\Roles;
use App\Models\Position;
use App\Models\BusinessUnit;
use App\Models\Sales;
use App\Models\TransportationZone;
use App\Models\User;
use Illuminate\Http\Request;
use App\Interfaces\ParameterInterface;
use App\Models\Product;

class ParameterController extends Controller
{
    const footer = 'FOOTER';
    protected $param;

    public function __construct(ParameterInterface $parameter)
    {
        $this->param = $parameter;
    }

    public function getFooter(){
        return $this->param->getParameter(self::footer);
    }

    public function getBusinessUnits()
    {
        $data = BusinessUnit::whereIn('name', ['Wholesales', 'B2B'])
            ->select('id', 'name')
            ->pluck('name', 'id')
            ->toArray();

        return $this->sendSuccess('Business unit retrieved successfully.', $data);
    }

    public function getRoles()
    {
        $data = Roles::select('id', 'name')->pluck('name', 'id')->toArray();
        return $this->sendSuccess('Roles retrieve successfully.', $data);
    }

    public function getPositions()
    {
        $data = Position::select('id', 'position_name')->pluck('position_name', 'id')->toArray();
        return $this->sendSuccess('Positions retrieve successfully.', $data);
    }

    public function getSales()
    {
        $data = Sales::select('sales_id', 'sales_name')
        ->leftJoin('user', function ($join) {
            $join->on('sales.sales_id', '=', 'user.reference_id')
                ->where('user.reference_object', '=', 'sales');
        })
        ->whereNull('user.reference_id')
        ->get();
    
        return $this->sendSuccess('Sales Data retrieved successfully.', $data);
    }

    public function getDirectTos()
    {
        $data = User::select('username')->where('reference_object','sales')->pluck('username')->toArray();
        return $this->sendSuccess('Direct To data retrieved successfully.', $data);
    }

    
    public function getAuths()
    {
        $data = MasterParameter::select('value')->where('group_key','MATRIX_LEVEL')->pluck('value')->toArray();
        return $this->sendSuccess('authorization data retrieved successfully.', $data);
    }

    public function getTiers()
    {
        return $this->param->getParameter('MATRIX_LEVEL');
    }

    public function getTaxTypes()
    {
        $data = MasterParameter::select('value')->where('group_key','B2B_REGISTER')->where('key','TAX_TYPE')->pluck('value')->toArray();
        return $this->sendSuccess('tax types data retrieved successfully.', $data);
    }

    public function getTransportZones()
    {
        $data = TransportationZone::select('zone_code','description')->orderBy('description','asc')->pluck('zone_code','description')->toArray();
        return $this->sendSuccess('transportation zone data retrieved successfully.', $data);
    }

    public function getCustomPrices()
    {
        $data = CustomPrice::select('item','description','size_from','size_to','price')->get();
        return $this->sendSuccess('custom pricedata retrieved successfully.', $data);
    }

    public function getProductsB2b(Request $request)
    {
        $search = $request->input('search');
        $limit = $request->input('limit');

        $datas = Product::select('article', 'article_description', 'article_price.amount')
            ->where('is_b2b', true)
            ->leftJoin('article_price', 'article_price.sku_code_c', '=', 'article.sku_code_c')
            ->when($search, function ($q) use ($search) {
                    $q->where('article', 'LIKE', '%' . $search . '%');
                })
            ->when($limit, function ($q) use ($limit) {
                $q->limit((int) $limit);
            })
            ->get();

        return $this->sendSuccess('Product retrieved successfully.', $datas);
    }

    public function getConfigurationB2B()
    {
        $data = MasterParameter::select('key', 'value')->where('group_key','B2B_CONFIG')->pluck('value', 'key')->toArray();
        $data = array_map('intval', $data);
        return $this->sendSuccess('B2B config data retrieved successfully.', $data);
    }
}
