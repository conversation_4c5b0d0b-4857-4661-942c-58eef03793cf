<?php

namespace App\Http\Controllers;

use App\Http\Resources\CustomResource;
use App\Http\Resources\InternalProductResellerDetailCollection;
use App\Jobs\ResellerArticleSorter;
use App\Models\CustomTag;
use App\Models\PublicProduct;
use App\Models\ResellerArticleStock;
use Illuminate\Support\Facades\Cache;
use ResponseAPI;
use App\Models\Color;
use App\Models\Product;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Interfaces\ProductInterface;
use App\Helpers\ApiClient;
use App\Http\Requests\IndexPublicProductRequest;
use App\Http\Resources\ProductResource;
use App\Repositories\GetStokRepo;

use Illuminate\Support\Facades\DB;

class ProductController extends Controller
{

    private $limit = 12;
    protected $getStockURI;

    private $product;

    public function __construct(ProductInterface $prodInterface)
    {
        $this->product = $prodInterface;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        Log::info('START QUERY PRODUCT');

        $customer_type = $request->user()->customer->customer_type ?? null;

        $cte = DB::table('article_skus')
        ->select('sku_code_c', DB::raw('SUM(stock) as total_stock'))
        ->groupBy('sku_code_c');

        $product = is_null($customer_type)
        ? Product::with(['mainImageGeneric', 'price', 'skuStock'])
            ->joinSub($cte, 'stock_data', function ($join) {
                $join->on('article.sku_code_c', '=', 'stock_data.sku_code_c');
            })
            ->whereNull('deletion_flag')
            ->select('article.*', 'stock_data.total_stock')
            ->groupBy('article.sku_code_c')
            ->orderBy('stock_data.total_stock', 'desc')
        : Product::with(['mainImageGeneric', 'price', 'skuStock'])
        ->joinSub($cte, 'stock_data', function ($join) {
            $join->on('article.sku_code_c', '=', 'stock_data.sku_code_c');
        })
        ->whereNull('deletion_flag')
        ->select('article.*', 'stock_data.total_stock')
        ->groupBy('article.sku_code_c')
        ->orderBy('stock_data.total_stock', 'desc')
        ->exclude($customer_type);
    
        $products = $this->product->getData($product, $request, true, true);
        Log::info('END QUERY PRODUCT');
        return $products;
    }
    public function indexB2B(Request $request)
    {
        Log::info('START QUERY PRODUCT');

        $cte = DB::table('article_skus')
        ->select('sku_code_c', DB::raw('SUM(stock) as total_stock'))
        ->groupBy('sku_code_c');

        $product =  Product::with(['mainImageGeneric', 'price', 'skuStock'])
            ->joinSub($cte, 'stock_data', function ($join) {
                $join->on('article.sku_code_c', '=', 'stock_data.sku_code_c');
            })
            ->select('article.*', 'stock_data.total_stock')
            ->groupBy('article.sku_code_c')
            ->orderBy('stock_data.total_stock', 'desc')
            ->where('is_b2b', 1);
      
        $products = $this->product->getData($product, $request, true, true, true);
        Log::info('END QUERY PRODUCT');
        return $products;
    }

    public function indexPublic(Request $request)
    {
        // if($request->has('site')){
        //     $query_stock = DB::select('SELECT *.article, (SELECT SUM(available_stock) FROM rsl_article_stock where article LIKE CONCAT("%",article.sku_code_c"%") as total_stock FROM article ORDER BY total_stock DESC GROUP BY article.sku_code_c');
        //     $product = PublicProduct::hydrate($query_stock);
        // }
        $site = $request->input('site', '0000');
        ResellerArticleSorter::dispatch($site);
        $product = Cache::has("reseller_article_list_$site") ?
            PublicProduct::whereIn('article.article', Cache::get("reseller_article_list_$site"))
                ->orderByRaw("field(article.article," . implode(',', Cache::get("reseller_article_list_$site")) . ")")
            : PublicProduct::groupBy('article.sku_code_c');
        // dd($product->get());
        return $this->product->getData($product, $request);
    }

    public function getDetailPublic(Request $request, $sku)
    {
        $data = $this->product->getDetailPublic($sku);
        return $data;
    }

    public function getDetailPublicInternalReseller(Request $request, $sku)
    {
        $product = Product::doesntHave('exclusion')->where('article.sku_code_c', $sku);
        $data = $this->product->getData($product, $request, false, true);
        return $data;
    }

    public function getDetailPublicInternalResellerNew(Request $request, $sku)
    {
        try {
            // $product = Product::select('article.article', 'article.sku_code_c','article_description', 'product_variant_c', 'product_size_c','location_code', 'lvl3_description', 'article.modified_date','rsl_master_site.name')
            // ->selectRaw('SUM(rsl_article_stock.available_stock) as available_stock')
            // ->leftJoin('rsl_article_stock', 'article.article', '=', 'rsl_article_stock.article')
            // ->leftJoin('rsl_master_site', 'rsl_article_stock.location_code', '=', 'rsl_master_site.code')
            // ->where('article.sku_code_c', $sku)
            // ->where('rsl_master_site.is_active', true)
            // ->groupBy('rsl_article_stock.article', 'rsl_article_stock.location_code')
            // ->orderBy('rsl_article_stock.location_code', 'ASC')
            // ->orderBy('rsl_article_stock.article', 'ASC')
            // ->paginate();

            $product = collect(DB::table('view_article_article_stock_site as v')
                ->select('v.*', 'a.*', 'r.name')->leftJoin('article as a', 'v.article', '=', 'a.article')->leftJoin('rsl_master_site as r', 'v.location_code', '=', 'r.code')->
                where('a.sku_code_c', $sku)->where('r.is_active', true)->orderBy('location_code')->orderByDesc('available_stock')->get()->toArray());

            return $this->sendSuccess('get data success', new InternalProductResellerDetailCollection($product));
        } catch (\Exception $e) {
            return $this->sendException('Exception occured', 500, 'error', $e);
        }
    }

    public function getDetailPublicInternal(Request $request, $sku)
    {
        $data = $this->product->getDetailPublic($sku);
        return $data;
    }

    public function customerStock(Request $request)
    {
        $customer_id = $request->user()->customer->customer_id;
        $articleCustomer = DB::table('customer_stock')->where('customer_id', $customer_id);
        if ($tokoId = $request->query('toko_id')) {
            $articleCustomer = $articleCustomer->where('customer_shipment_id', $tokoId);
        }
        if ($text = $request->query('text')) {
            $articleCustomer = $articleCustomer->where('product_name', 'like', "%$text%");
        }

        $articleCustomer = $articleCustomer->pluck('article_id')->toArray();
        $product = Product::whereIn('article', $articleCustomer)->groupBy('article.sku_code_c');
        return $this->product->getData($product, $request, false);
    }

    public function checkStock(Request $request)
    {

        if (!$request->has('article')) {
            return $this->sendError('article param required.', 400);
        }
        try {
            $resp_final = '';
            $article_map = array_map(function ($b) {
                return [
                    'source' => 'CAREOM',
                    'destination' => 'STK',
                    'article' => $b,
                    //  'site' => '1200'
                ];
            }, $request['article']);

            $article_chunk = array_chunk($article_map, 15, true);

            foreach ($article_chunk as $i) {

                $data = [
                    "source" => "CAREOM",
                    "destination" => "STK",
                    "detail" => $article_map
                ];

                $getStokRepo = new GetStokRepo();
                $resp = $getStokRepo->getStock($data);
                $new_resp = $getStokRepo->getData($resp);
                if ($resp_final == '') {
                    $resp_final = $new_resp->success == true ? $new_resp : $resp_final;
                } else {
                    $resp_final->data = array_push($new_resp->data, $resp_final->data);
                }

            }





            return $this->sendSuccess('success', $new_resp, 200);
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage(), 500);
        }
    }

    public function getSubCategories(Request $req)
    {
        $categoryId = $req->get('category');
        return $this->product->getFilter(Product::getSubCategories($req));
    }

    public function getActivities(Request $req)
    {
        $categoryId = $req->get('category');
        $subCategoryId = $req->get('subcategory');
        return $this->product->getFilter(Product::getActivities($req));
    }

    public function getColors(Request $request)
    {
        $colors = Product::getColors($request);
        $data = Color::select('key', 'value')->whereIn('key', $colors)->whereNotNull('value')->orderBy('value')->get();
        return $this->product->getFilter($data, true);
    }

    public function getSizes(Request $request)
    {
        return $this->product->getFilter(Product::getSizes($request));
    }

    public function getDetail(Request $request, $sku)
    {
        
        $customer_type = $request->user() ? @$request->user()->customer->customer_type : 'B2B';
        
        return $this->product->getDetailNew($sku, $customer_type);
    }

    public function getDetailInternal(Request $request, $sku)
    {
        $customer_type = @$request->user()->customer->customer_type;

        return $this->product->getDetailNewInternal($sku);
    }

    public function getDetailVariant(Request $request, $sku, $color)
    {
        $color = Color::where('value', $color)->first();
        $customer_type =  $request->user() ? @$request->user()->customer->customer_type : 'B2B';
        return $this->product->getDetailVariantNew($sku, $color, $customer_type);
    }

    public function getDetailVariantCustomer(Request $request, $sku, $color)
    {
        $color = Color::where('value', $color)->first();
        $customer_type = @$request->user()->customer->customer_type;
        $customer_id = @$request->user()->customer->customer_id;
        return $this->product->getDetailVariantNewCustomer($sku, $color, $customer_type, $customer_id);
    }

    public function getProductDetail($sku)
    {
        return $this->product->getDetail($sku);
    }

    public function getProductVariant($id)
    {
        return $this->product->getDetailVariant($id);
    }

    public function internalIndex(Request $request)
    {
        $cte = DB::table('article_skus')
            ->select('sku_code_c', DB::raw('SUM(stock) as total_stock'))
            ->groupBy('sku_code_c');

        $product = Product::doesntHave('exclusion')
            ->from('article')
            ->joinSub($cte, 'stock_data', function ($join) {
                $join->on('article.sku_code_c', '=', 'stock_data.sku_code_c');
            })
            ->select('article.*', 'stock_data.total_stock')
            ->groupBy('article.sku_code_c')
            ->orderBy('stock_data.total_stock', 'desc');

        return $this->product->getData($product, $request, true, true);
    }

    public function internalResellerIndex(Request $request)
    {
        $product = Product::doesntHave('exclusion')->groupBy('article.sku_code_c');
        $jsonData = $this->product->getData($product, $request, false, false);

        return $jsonData;
    }

    public function internalDetail(Request $request, $sku)
    {
        $product = Product::doesntHave('exclusion')->where('article.sku_code_c', $sku);
        return $this->product->getData($product, $request);
    }

    public function getCustomTags($sku)
    {
        $lv4desc = Product::where('sku_code_c', $sku)->first();
        if (!$lv4desc) {
            return $this->sendError('SKU not found', 400);
        }
        $customTags = CustomTag::where('product_group', $lv4desc->lvl4_description)->get();
        foreach ($customTags as &$item) {
            $item['coordinate'] = json_decode($item['coordinate'], true);
        }

        return $this->sendSuccess("success", $customTags);
    }

    public function getProductsRecommended(Request $request)
    {
        $type = $request->query('type');
        $search = $request->query('search');

        $categories = Product::when($type, function ($query, $type) {
            return match ($type) {
                'wholesales' => $query->where('is_wholesales', 1),
                'b2b' => $query->where('is_b2b', 1),
                default => $query
            };
        })->when($search, function ($query, $search) {
            return $query->where(function ($subQuery) use ($search) {
                $subQuery->where('article_description', 'like', "%$search%")
                    ->orWhere('sku_code_c', 'like', "%$search%")
                    ->orWhere('lvl4_description', 'like', "%$search%");
            });
        })->distinct()->pluck('lvl4_description');

        if ($categories->isEmpty()) {
            return response()->json([
                'status' => 'success',
                'data' => []
            ]);
        }

        $products = Product::select('sku_code_c', 'product_name_c')
            ->whereIn('lvl4_description', $categories)
            ->distinct()
            ->limit(10)
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $products
        ]);
    }

    public function getProductsRecommendedBySku(Request $request, $sku_code_c)
    {
        $type = $request->query('type');
        $limit = $request->query('limit', 4);

        $categories = Product::where('sku_code_c', $sku_code_c)
        ->when($type, function ($query, $type) {
            return match ($type) {
                'wholesales' => $query->where('is_wholesales', 1),
                'b2b' => $query->where('is_b2b', 1),
                default => $query
            };
        })->distinct()->pluck('lvl4_description');

        if ($categories->isEmpty()) {
            return response()->json([
                'status' => 'success',
                'data' => []
            ]);
        }

        $products = Product::whereIn('lvl4_description', $categories)
            ->select('article', 'sku_code_c', 'product_name_c', 'transfer_date')
            ->groupBy('sku_code_c')
            ->orderBy('transfer_date', 'desc')
            ->paginate($limit);

        return response()->json([
            'status' => 'success',
            'data' => ProductResource::collection($products),
            'meta' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'total' => $products->total(),
                'per_page' => $products->perPage(),
            ]
        ]);
    }

    public function getB2BProductsRecommended(Request $request)
    {
        $search = $request->query('search');

        $categories = Product::where('is_b2b', 1)
            ->when($search, function ($query, $search) {
                return $query->where(function ($subQuery) use ($search) {
                    $subQuery->where('article_description', 'like', "%$search%")
                        ->orWhere('sku_code_c', 'like', "%$search%")
                        ->orWhere('lvl4_description', 'like', "%$search%");
                });
            })->distinct()->pluck('lvl4_description');

        if ($categories->isEmpty()) {
            return response()->json([
                'status' => 'success',
                'data' => []
            ]);
        }

        $products = Product::select('sku_code_c', 'product_name_c')
            ->where('is_b2b', 1)
            ->whereIn('lvl4_description', $categories)
            ->distinct()
            ->limit(10)
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $products
        ]);
    }
}