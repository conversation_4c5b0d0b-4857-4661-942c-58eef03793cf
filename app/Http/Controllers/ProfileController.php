<?php

namespace App\Http\Controllers;


use App\Models\User;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Repositories\CustomerRepository;


class ProfileController extends Controller
{
    

  
    public function detail(Request $request,$custId)
    {
        $custId = auth()->user()->customer->customer_id;
        //$custId = $id;
        $customer = DB::table('customers as cus')
        ->select('cus.*')
        ->where('cus.customer_id', $custId)
        ->first();

        $transaksi = DB::table('customers as cus')
         ->join('order_header', 'order_header.customer_id', '=', 'cus.customer_id')
         ->selectRaw('SUM(order_header.total) as total, COUNT(order_header.customer_id) as jumlah_transaksi,order_header.order_date as tanggal_terakhir')
         ->where('cus.customer_id', $custId)
         ->orderBy('order_header.order_date','desc')
         ->first();

         $total= $transaksi->total;
         $jumlahTrans= $transaksi->jumlah_transaksi;
         $rataRata = $jumlahTrans > 0 ? $total / $jumlahTrans : 0;
       

         $totalTransaksi = $transaksi->total;
         $rataRataTransaksi = $rataRata;
         $tanggal=$transaksi->tanggal_terakhir;
        return response()->json([
            'data'=> [
              
              'customer'=>$customer,
              'total_transaksi' => $totalTransaksi,
              'rata_rata_transaksi' => $rataRataTransaksi,
              'tanggal_terakhir'=>$tanggal

            ],
            'error' => 'false',
            'status' => 200
             ],200);
    }

    public function listdaftarcustomer(Request $request)
{
    
    $sorting = $request->input('sorting');
    $status = $request->input('status');
    
    $query = DB::table('customers as cus')
        ->select('cus.customer_id as customerId', 'cus.owner_name as nama_customer', 'cus.instance_name as nama_instansi', 'oh.created_date as tglRegistrasi', 's.sales_name as nama_sales', 'cus.is_active as status_akun','cus.is_pending_payment', 'oh.total as total_transaksi')
        ->join('order_header as oh', 'oh.customer_id', '=', 'cus.customer_id')
        ->join('sales as s', 's.sales_id', '=', 'oh.sales_id')
        ->where('oh.distribution_channel','B2B');

        
        if ($status) {
            $statusArray = explode(',', $status);
        
            $query->where(function ($query) use ($statusArray) {
                if (in_array('active', $statusArray)) {
                    $query->orWhere('cus.is_active', 1);
                }
                if (in_array('non-active', $statusArray)) {
                    $query->orWhere('cus.is_active', 0);
                }
                if (in_array('freeze', $statusArray)) {
                    $query->orWhere('cus.is_pending_payment', 1);
                }
            });
        }
        
    // $status = $request->input('status', 'active');
    // if(!empty($status)){
    //     if ($status === 'active') {
    //         $query->where('cus.is_active', 1);
    //     }else if ($status === 'non-active') {
    //         $query->where('cus.is_active', 0);

    //     }else if ($status === 'freeze') {
    //         $query->where('cus.is_pending_payment', 1);
    //     }
    // }
   
    
   

    $searchId = $request->input('searchId');
    if (!empty($searchId)) {
        $query->where(function ($q) use ($searchId) {
            $q->where('cus.customer_id', 'like', "%$searchId%");
                
        });
    }
    $searchName = $request->input('searchName');
    if (!empty($searchName)) {
        $query->where(function ($q) use ($searchName) {
            $q->where('cus.owner_name', 'like', "%$searchName%");
        });
    }
    $searchInstance = $request->input('searchInstance');
    if (!empty($searchInstance)) {
        $query->where(function ($q) use ($searchInstance) {
            $q->where('cus.instance_name', 'like', "%$searchInstance%");
                
        });
    }
    $searchSales = $request->input('searchSales');
    if (!empty($searchSales)) {
        $query->where(function ($q) use ($searchSales) {
            $q->where('s.sales_name', 'like', "%$searchSales%");
        });
    }

    // Proses urutan berdasarkan input order
    if ($sorting) {
        switch ($sorting) {
            case 'lowest':
                $query->orderBy('oh.total', 'asc');
                break;
            case 'highest':
                $query->orderBy('oh.total', 'desc');
                break;
            case 'alfabetikalA':
                $query->orderBy('cus.owner_name', 'asc');
                break;
            case 'alfabetikalZ':
                $query->orderBy('cus.owner_name', 'desc');
                break;
            case 'oldest':
                $query->orderBy('cus.created_date', 'asc');
                break;
            case 'newest':
                $query->orderBy('cus.created_date', 'desc');
                break;
        }
    }


    $perPage = $request->input('per_page', 10);
    $page = $request->input('page', 1);

    $totalData = $query->count();

    $customers = $query->skip(($page - 1) * $perPage)
        ->take($perPage)
        ->get();

    $listCust = [];

    foreach ($customers as $customer) {
        $customerId = $customer->customerId;
        $customerName = $customer->nama_customer;
        $customerInstance = $customer->nama_instansi;
        $totalTransaksi = $customer->total_transaksi;
        $tglRegist = $customer->tglRegistrasi;
        $namaSales =$customer->nama_sales;
        $statusAkun = $customer->status_akun;
        if ($statusAkun ==1){
            $statusResp ='active';
        }elseif($statusAkun == 0){
            $statusResp ='Non-active';
        }elseif($customer->is_pending_payment==1){
            $statusResp='Freeze';
        }else{
            $statusResp="";
        }

        $listCust[] = [
            'customer_id' => $customerId,
            'customer_name' => $customerName,
            'instansi_name' => $customerInstance,
            'total_transaction'=>$totalTransaksi,
            'date' => $tglRegist,
            'sales_name' =>$namaSales,
            'status' =>$statusResp,
            
        ];
    }
    if ($totalData === 0) {
        return response()->json([
            'message' => '0 Hasil Pencarian',
            'error' => false,
            'status' => 200
        ], 200);
    } else {
        return response()->json([
            'data' => [
                'total' => $totalData,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($totalData / $perPage),
                'from' => ($page - 1) * $perPage + 1,
                'to' => ($page - 1) * $perPage + count($customers),
                'data' => $listCust
            ],
            'error' => false,
            'status' => 200
        ], 200);
    }
}

public function getdetailcustPesanan(Request $request, $id)
{
    $customerId = $id;
    $filterStatusPesanan = $request->input('filterStatusPesanan');
    $filterTagihan = $request->input('filterTagihan');
    
    $invoices = DB::table('invoice as in')
        ->select('in.invoice_no as billing', 'oh.order_no as no_order', 'oh.order_date as tgl_pesanan','oh.total as total_transaction', 'oh.order_status as status_pesanan', 'in.status as status_tagihan')
        ->join('order_header as oh', 'oh.order_no', '=', 'in.order_no')
        ->where('oh.distribution_channel', 'B2B')
        ->where('oh.customer_id', $customerId);


    if ($filterStatusPesanan) {
        $invoices->whereIn('oh.order_status',explode(',',$filterStatusPesanan) );
    }

    if ($filterTagihan) {
        $invoices->whereIn('in.status', explode(',',$filterTagihan));
    } 

//Filter date
    if ($request->has('date_from') && !$request->has('date_to'))
    {
        $invoices = $invoices->whereDate('order_date', '>=', $request->query('date_from'))->whereDate('order_date', '<=', now()->format('Y-m-d'));
    } 
    elseif (!$request->has('date_from') && $request->has('date_to')) 
    {
        $invoices = $invoices->whereDate('order_date', '>=', now()->format('Y-m-d'))->whereDate('order_date', '<=', $request->query('date_to'));
    } 
    elseif ($request->has('date_from') && $request->has('date_to')) 
    {
    $invoices = $invoices->whereDate('order_date', '>=', $request->query('date_from'))->whereDate('order_date', '<=', $request->query('date_to'));
    }

    $perPage = $request->input('per_page', 10);
    $page = $request->input('page', 1);

    $totalData = $invoices->count();
    $customers = $invoices->skip(($page - 1) * $perPage)
    ->take($perPage)
    ->get();
  $listPesanan = [];
    foreach ($customers as $row) {
        $billing = $row->billing;
        $no_order = $row->no_order;
        $tgl_pesanan = $row->tgl_pesanan;
        $total_transaction = $row->total_transaction;
        $status_pesanan = $row->status_pesanan;
        $status_tagihan =$row->status_tagihan;

        $listPesanan[] = [
            'no_billing' => $billing,
            'no_order' => $no_order,
            'tanggal_pesanan' => $tgl_pesanan,
            'total_transaction'=>$total_transaction,
            'status_pesanan' => $status_pesanan,
            'status_tagihan' => $status_tagihan,
        
    ];
    }
    if ($totalData === 0) {
        return response()->json([
            'message' => '0 Hasil Pencarian',
            'error' => false,
            'status' => 200
        ], 200);
    } else {
        return response()->json([
            'data' => [
                'total' => $totalData,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil($totalData / $perPage),
                'from' => ($page - 1) * $perPage + 1,
                'to' => ($page - 1) * $perPage + count($customers),
                'data' => $listPesanan
            ],
            'error' => false,
            'status' => 200
        ], 200);
    }
}
}
