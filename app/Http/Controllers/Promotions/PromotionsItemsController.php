<?php

namespace App\Http\Controllers\Promotions;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Promotions\PromotionItems;
use App\Http\Resources\Promotions\PromotionsResource;
class PromotionsItemsController extends Controller
{
    private $m = PromotionItems::class;
    private $pk = 'id';

    public function index(Request $request)
    {

        $data = $this->m::pager($request);
        return $this->sendSuccess('success', $data, '200 OK', false);

    }
    public function store(Request $request)
    {
        return $this->doStore($this->m, $request, $this->pk);

    }
    public function show($id)
    {
            $data = $this->m::where($this->pk, $id)->first();
            return $this->sendSuccess('get data success', $data);

    }
    public function update(Request $request, $id)
    {
        $model = $this->m::find($id);
        return $this->doUpdate($this->m, $model, $request->all(), $this->pk);
    }


    public function destroy($id)
    {
        $objcomp = $this->m::where('id', $id)->first();
        if($objcomp){
            return $this->doDestroy($objcomp);
        }
    }
    

}
