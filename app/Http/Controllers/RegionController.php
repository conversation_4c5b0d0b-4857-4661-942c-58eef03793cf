<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\RegionService;
use App\Traits\ResponseAPI;

class RegionController extends Controller
{
    protected $regionService;

    public function __construct(RegionService $regionService) 
    {
        $this->regionService = $regionService;
    }

    public function getRegions(Request $request) 
    {
        $page = (int)($request->input('page')??1);
        $perPage = (int)($request->input('per_page')??10);
        $search = $request->input('search')??'';

        try {
            $result['data'] = $this->regionService->getRegions($page, $perPage, $search);
        } catch (RequestException $ex) {
            // return response()->json([
            //     'status' => 400,
            //     'message' => 'Bad request',
            // ]);
            return $this->sendSuccess($ex->getMessage(), []);
        }

        if (!$result['data']->items()) {
            return $this->sendSuccess("Data not found!", []);
        }

        $data = response()->json([
            'data' => $result['data']->items(),
            'pagination' => [
                'total' => $result['data']->total(),
                'per_page' => (int)$result['data']->perPage(),
                'current_page' => $result['data']->currentPage(),
                'last_page' => $result['data']->lastPage(),
                'from' => (int)$result['data']->firstItem(),
                'to' => (int)$result['data']->lastItem()
            ],
        ]);

        return $this->sendSuccess("Get regions success", $data);
    }

    public function getRegionCity (Request $request) 
    {
        $page = (int)($request->input('page') ?? 1);
        $perPage = (int)($request->input('per_page') ?? 10);
        $search = $request->input('search') ?? '';
        $regionCode = $request->input('region_code') ?? null;

        if (!$regionCode) {
            return $this->sendError("Region Code is Required!", 400);
        }

        try {
            $result['data'] = $this->regionService->getRegionCity($page, $perPage, $search, $regionCode);
        } catch (RequestException $ex) {
            return $this->sendSuccess($ex->getMessage(), []);
        }

        if (!$result['data']->items()) {
            return $this->sendSuccess("Data not found!", []);
        }

        $data = response()->json([
            'data' => $result['data']->items(),
            'pagination' => [
                'total' => $result['data']->total(),
                'per_page' => (int)$result['data']->perPage(),
                'current_page' => $result['data']->currentPage(),
                'last_page' => $result['data']->lastPage(),
                'from' => (int)$result['data']->firstItem(),
                'to' => (int)$result['data']->lastItem()
            ],
        ]);

        return $this->sendSuccess("Get city success", $data);
    }

    public function getRegionDistrict (Request $request) 
    {
        $page = (int)($request->input('page') ?? 1);
        $perPage = (int)($request->input('per_page') ?? 10);
        $search = $request->input('search') ?? '';
        $cityCode = $request->input('city_code') ?? $request->input('city_id') ?? null;

        if (!$cityCode) {
            return $this->sendError("City Code is Required!", 400);
        }

        try {
            $result['data'] = $this->regionService->getRegionDistrict($page, $perPage, $search, $cityCode);
        } catch (RequestException $ex) {
            return $this->sendSuccess($ex->getMessage(), []);
        }

        if (!$result['data']->items()) {
            return $this->sendSuccess("Data not found!", []);
        }

        $data = response()->json([
            'data' => $result['data']->items(),
            'pagination' => [
                'total' => $result['data']->total(),
                'per_page' => (int)$result['data']->perPage(),
                'current_page' => $result['data']->currentPage(),
                'last_page' => $result['data']->lastPage(),
                'from' => (int)$result['data']->firstItem(),
                'to' => (int)$result['data']->lastItem()
            ],
        ]);

        return $this->sendSuccess("Get district success", $data);
    }

    public function getRegionSubdistrict (Request $request) 
    {
        $page = (int)($request->input('page') ?? 1);
        $perPage = (int)($request->input('per_page') ?? 10);
        $search = $request->input('search') ?? '';
        $districtCode = $request->input('district_code') ?? null;

        if (!$districtCode) {
            return $this->sendError("District Code is Required!", 400);
        }

        try {
            $result['data'] = $this->regionService->getRegionSubdistrict($page, $perPage, $search, $districtCode);
        } catch (RequestException $ex) {
            return $this->sendSuccess($ex->getMessage(), []);
        }

        if (!$result['data']->items()) {
            return $this->sendSuccess("Data not found!", []);
        }

        $data = response()->json([
            'data' => $result['data']->items(),
            'pagination' => [
                'total' => $result['data']->total(),
                'per_page' => (int)$result['data']->perPage(),
                'current_page' => $result['data']->currentPage(),
                'last_page' => $result['data']->lastPage(),
                'from' => (int)$result['data']->firstItem(),
                'to' => (int)$result['data']->lastItem()
            ],
        ]);

        return $this->sendSuccess("Get subdistrict success", $data);
    }

    public function getRegionPostalCode (Request $request) 
    {
        $page = (int)($request->input('page') ?? 1);
        $perPage = (int)($request->input('per_page') ?? 10);
        $search = $request->input('search') ?? '';
        $subDistrictCode = $request->input('sub_district_code') ?? null;

        if (!$subDistrictCode) {
            return $this->sendError("District Code is Required!", 400);
        }

        try {
            $result['data'] = $this->regionService->getRegionPostalCode($page, $perPage, $search, $subDistrictCode);
        } catch (RequestException $ex) {
            return $this->sendSuccess($ex->getMessage(), []);
        }

        if (!$result['data']->items()) {
            return $this->sendSuccess("Data not found!", []);
        }

        $data = response()->json([
            'data' => $result['data']->items(),
            'pagination' => [
                'total' => $result['data']->total(),
                'per_page' => (int)$result['data']->perPage(),
                'current_page' => $result['data']->currentPage(),
                'last_page' => $result['data']->lastPage(),
                'from' => (int)$result['data']->firstItem(),
                'to' => (int)$result['data']->lastItem()
            ],
        ]);

        return $this->sendSuccess("Get postal code success", $data);
    }


}
