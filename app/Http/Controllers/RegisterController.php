<?php

namespace App\Http\Controllers;

use App\Helpers\FormatHelper;
use App\Jobs\MailSender;
use App\Models\MasterParameter;
use App\Repositories\GetSocialsRepo;
use Illuminate\Http\Request;
use App\Models\Customer;
use App\Models\CustomerShipment;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use App\Mail\MailRegistration;
use App\Mail\MailExist;
use App\Mail\MailForgot;
use App\Mail\SendMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Image;
use File;
use App\Helpers\FileHelper;
use App\Http\Requests\RegisterRequest;
use App\Models\CreditLimit;
use App\Models\TransportationZone;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;

class RegisterController extends Controller
{
    // function forgotPassword() {
    //     $data = array(
    //         'to' => '<EMAIL>',
    //         'subject' => 'test',
    //         'message' => 'test'
    //     );

    //     Mail::to('<EMAIL>')->send(new SendMail($data));
    // }
    use FileHelper;

    public function forgotPassword(Request $request)
    {
        $email = $request->email;
        $customers = Customer::where('email', $email)->first();

        if ($customers !== null) {
            Mail::to($email)->send(new MailForgot($customers));
            return response([
                'error' => 'false',
                'status' => '200',
                'data' => $customers
            ]);
        } else if ($customers == null) {
            return response([
                'error' => 'true',
                'status' => '500',
                'data' => 'data not found'
            ]);
        }
    }

    public function updatePassword(Request $request)
    {
        $email = base64_decode($request->token);
        $users = User::where('email', $email)->first();
        if ($users == null) {
            return response([
                'error' => 'true',
                'status' => '500',
                'data' => 'data not found'
            ]);
        }
        $users->password = Hash::make($request->password);
        $users->is_change_password = 1;

        if ($users->update()) {
            return response([
                'error' => 'false',
                'status' => '200',
                'data' => $users
            ]);
        } else {
            return response([
                'error' => 'true',
                'status' => '500',
                'data' => 'Gagal update'
            ]);
        }
    }

    public function checkCustomer(Request $request)
    {
        $emailToCheck = $request->email;
        $customers = Customer::whereNotNull('email')->get();
        $check = $customers->first(function ($customer) use ($emailToCheck) {
            try {
                return Crypt::decryptString($customer->email) === $emailToCheck;
            } catch (\Exception $e) {
                return $customer->email === $emailToCheck;
            }
        });

        if ($check) {
            return response()->json([
                'error' => true,
                'status' => '409 CONFLICT',
                'message' => 'Email telah terdaftar',
                'data' => [],
            ], 409);
        }
    
        return response()->json([
            'error' => false,
            'status' => '200 OK',
            'message' => 'Validasi berhasil',
            'data' => [],
        ], 200);
    }

    public function register(RegisterRequest $request)
    {
        // // validate uploaded files
        // $finfo = finfo_open(FILEINFO_MIME_TYPE);

        // $mime_type_ktp = Storage::disk('s3')->mimeType($request->file_identity);
        // $mime_type_npwp =  Storage::disk('s3')->mimeType($request->file_npwp);

        // // finfo_close($finfo);

        // $allowedMimeTypes = array(
        //     'image/png',
        //     'image/jpeg',
        //     'image/jpg'
        // );

        // if (!in_array($mime_type_ktp, $allowedMimeTypes) || !in_array($mime_type_npwp, $allowedMimeTypes)) {
        //     return $this->sendError('png, jpg, jpeg files only', 400);
        // }

        // //upload ktp to S3
        // $photo_ktp = $request->file('file_identity');
        // $name = uniqid();
        // $content = file_get_contents($photo_ktp);
        // $filename = $name.'.'.$photo_ktp->extension();
        // Storage::disk('s3')->put(substr($ktp_file_name,1), $content);

        // // $photo_npwp = $request->file('file_npwp');
        // // $path_npwp = $photo_npwp->storeAs('npwp', uniqid().'.'.$photo_npwp->extension(), ['disk' => 'public']);
        // // $npwp_file_name=url('/').'/'.'storage/'.$path_npwp;
        // $ktp_file_name = $request->file_identity??'';
        // $is_double = $request->file_identity ==  $request->file_npwp;
        // $ktp_file_path = $this->fileTransfer($request->file_identity,'ktp', !$is_double);
        // if ($ktp_file_path['error'] == true) {
        //     return $this->sendError($ktp_file_path['message']);
        // }

        // //upload NPWP to S3
        // $photo_npwp = $request->file('file_npwp');
        // $name = uniqid();
        // $content = file_get_contents($photo_npwp);
        // $filename = $name.'.'.$photo_npwp->extension();
        // $npwp_file_name = env('S3_NPWP_FOLDER').$filename;
        // Storage::disk('s3')->put(substr($npwp_file_name,1), $content);
        // // ================================================================================================
        // // ================================================================================================
        // $npwp_file_name = $request->file_npwp??'';
        // $npwp_file_path = $this->fileTransfer($request->npwp_file, 'npwp');
        // if ($npwp_file_path['error'] == true) {
        //     return $this->sendError($npwp_file_path['message']);
        // }

        $cust_id = 'TMP' . strval(random_int(100000, 999999));

        // if ($request->npwp_file_path) {
            // $npwp = Storage::disk('s3-public')->files('staging/npwp-dev/' . $cust_id);
            // $latestFile = collect($npwp)->sortDesc()->first();
            $tf_npwp_file = $this->fileTransfer($request->npwp_file, 'npwp-dev', true);
            if ($tf_npwp_file['error'] == true) {
                return $this->sendError($tf_npwp_file['message']);
            }
        // }

        if ($request->ktp_file) {
            // $ktp = Storage::disk('s3-public')->files('staging/ktp-dev/' . $cust_id);
            // $latestFile = collect($ktp)->sortDesc()->first();
            $tf_ktp_file = $this->fileTransfer($request->ktp_file, 'ktp-dev', true);
            if ($tf_ktp_file['error'] == true) {
                return $this->sendError($tf_ktp_file['message']);
            }
        }

        // if ($request->hasFile('npwp_file')) {
        //     try {
        //         $s3 = \Storage::disk('s3');
        //         $client = $s3->getDriver()->getAdapter()->getClient();
        //         $expiry = "+10 minutes";

        //         $npwp_existing_file = \Storage::disk('s3-public')->files('staging/npwp-dev/' . $cust_id);
        //         if ($npwp_existing_file)
        //             \Storage::disk('s3-public')->delete($npwp_existing_file);

        //         $npwp_file = $request->file('npwp_file');
        //         $npwp_cmd = $client->getCommand('PutObject', [
        //             'Bucket' => env('AWS_BUCKET_STAGING', 'bucket-public-careorder'),
        //             'Key' => 'staging/' . 'npwp-dev/' . $cust_id . '/' . $npwp_file->getClientOriginalName(),
        //         ]);

        //         $npwpPresignedRequest = $client->createPresignedRequest($npwp_cmd, $expiry);
        //         $npwpPresignedUrl = (string) $npwpPresignedRequest->getUri();
        //         $npwpFilePath = $npwp_cmd["Key"];
        //     } catch (\Exception $e) {
        //         return response()->json([
        //             'message' => 'Failed to upload file NPWP to S3',
        //         ], 500);
        //     }
        // }

        // if ($request->hasFile('ktp_file')) {
        //     try {
        //         $s3 = \Storage::disk('s3');
        //         $client = $s3->getDriver()->getAdapter()->getClient();
        //         $expiry = "+10 minutes";

        //         $ktp_existing_file = \Storage::disk('s3-public')->files('staging/ktp-dev/' . $cust_id);
        //         if ($ktp_existing_file)
        //             \Storage::disk('s3-public')->delete($ktp_existing_file);

        //         $ktp_file = $request->file('ktp_file');
        //         $ktp_cmd = $client->getCommand('PutObject', [
        //             'Bucket' => env('AWS_BUCKET_STAGING', 'bucket-public-careorder'),
        //             'Key' => 'staging/' . 'ktp-dev/' . $cust_id . '/' . $ktp_file->getClientOriginalName(),
        //         ]);

        //         $ktpPresignedRequest = $client->createPresignedRequest($ktp_cmd, $expiry);
        //         $ktpPresignedUrl = (string) $ktpPresignedRequest->getUri();
        //         $ktpFilePath = $ktp_cmd["Key"];
        //     } catch (\Exception $e) {
        //         return response()->json([
        //             'message' => 'Failed to upload file KTP to S3',
        //         ], 500);
        //     }
        // }

        // check if username already existed
        // $checkexistinguser = User::where('username',$request->email)->first();
        // if($checkexistinguser){
        //     return $this->sendError("Username entered already existed", 400);
        // }

        DB::beginTransaction();
        try {
            // insert customer
            $customer = new Customer;
            $customer->customer_id = $cust_id;
            $customer->owner_name = $request->owner_name;
            $customer->instance_name = $request->instance_name;
            $customer->email = $request->email;
            $customer->address = $request->shipment_address;
            $customer->phone_number = $request->phone_number;
            $customer->distribution_channel = 'B2B';
            // $customer->npwp = FormatHelper::formatNpwp($request->npwp);
            $customer->npwp = $request->npwp;
            $customer->npwp_name = $request->npwp_name;
            $customer->npwp_file = '/' . $tf_npwp_file['filepath'] ?? null;
            $customer->npwp_province = $request->npwp_province;
            $customer->npwp_province_code = $request->npwp_province_code;
            $customer->npwp_city = $request->npwp_city;
            $customer->npwp_city_code = $request->npwp_city_code;
            $customer->npwp_district = $request->npwp_district;
            $customer->npwp_district_code = $request->npwp_district_code;
            $customer->npwp_zip_code = $request->npwp_zip_code;
            $customer->npwp_address = $request->npwp_address;
            $customer->tax_type = $request->tax_type;
            $customer->tax_invoice = $request->tax_invoice;
            $customer->national_id = $request->ktp;
            $customer->national_id_file = isset($tf_ktp_file['filepath']) ? '/' . $tf_ktp_file['filepath'] : null;
            $customer->customer_type = 'Z4';
            // $customer->customer_type           = 'W3';
            $customer->top = 'T001';
            $customer->top_days = 'Cash';
            $customer->status = 'Baru';
            $customer->sumber = 'Mandiri';
            $customer->remarks = 'Verifikasi akun Anda sedang diproses. Silakan tunggu beberapa saat.';
            $customer->save();
            Log::info("Create Customer Successfully");

            // insert customer shipment
            $customerShipment = new CustomerShipment;
            $customerShipment->customer_id = $cust_id;
            $customerShipment->name = $request->owner_name;
            $customerShipment->address = $request->shipment_address;
            $customerShipment->country = 'Indonesia';
            $customerShipment->country_code = 'ID';
            $customerShipment->province = $request->shipment_province;
            $customerShipment->province_code = $request->shipment_province_code;
            $customerShipment->city = $request->shipment_city;
            $customerShipment->city_code = $request->shipment_city_code;
            $customerShipment->district = $request->shipment_district;
            $customerShipment->district_code = $request->shipment_district_code;
            $customerShipment->subdistrict = $request->shipment_subdistrict ?? '';
            // $customerShipment->subdistrict_code = $request->shipment_subdistrict_code ?? '-'; 
            $customerShipment->zip_code = $request->shipment_zip_code;
            
            $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->district . '%')->first();
            if (!$zone_code) {
                $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->city . '%')->first();
            }
            $customerShipment->zone_code = $zone_code->zone_code ?? null;
            
            $customerShipment->phone_number = $request->phone_number;
            $customerShipment->save();
            Log::info("Create Customer Shipment Successfully");

            // insert credit limit
            $creditLimit = new CreditLimit;
            $creditLimit->customer_external_id          = $cust_id;
            $creditLimit->credit_limit                  = 0;
            $creditLimit->credit_limit_used             = 0;
            $creditLimit->credit_limit_remaining        = 0;
            $creditLimit->credit_limit_used_percentage  = 0;
            $creditLimit->currency                      = 'IDR';             
            $creditLimit->save();
            Log::info("Create Credit Limit Successfully");

            // insert user
            $user = new User;
            $user->reference_id = $cust_id;
            $user->reference_object = 'customer';
            $user->username = $request->email;
            $user->email = $request->email;
            $user->name = $request->owner_name;
            $user->password = Hash::make($request->password);
            $user->is_change_password = 0;
            $user->is_active = 0;

            $token = Str::random(64);
            $user->activation_token = $token;
            $user->activation_token_expired_at = now()->addDays(1);

            $user->save();
            Log::info("Create User Successfully");

            // get socialmedia
            $socmed = new GetSocialsRepo();
            $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
            $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
            $twitter = $socmed->getSocialMediaParameters('TWITTER');
            $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
            $youtube = $socmed->getSocialMediaParameters('YOUTUBE');

            // send email
            $param['data'] = [
                'name' => $request->owner_name,
                'email' => $request->email,

                // get socialmedia account
                'facebook' => $facebook,
                'twitter' => $twitter,
                'instagram' => $instagram,
                'tiktok' => $tiktok,
                'youtube' => $youtube,
            ];

            MailSender::dispatch($request->email, $param['data'], 'mail_verification', $token);

            $sales_all = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'B2B')->first();
            $msg = 'Terdapat pengajuan Customer Baru atas nama '.$user->name.', klik disini untuk melihat detail';
            $this->notifStore($sales_all->value, 'CUSTOMER BARU', 'customer-internal', $msg, null, 'Info', 'B2B', 'info');

            DB::commit();

            return $this->sendSuccess('Registered Successfully', [
                'customer' => $customer,
                'customer_shipment' => $customerShipment,
                'user' => $user,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Register Error: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->sendError("An error occurred while registered.", 500);
        }
    }

    public function activateUser($email)
    {
        $user = User::where('username', $email)->first();
        if (!$user) {
            return $this->sendError("User not found", 404);
        }
        
        if ($user->is_active == 1) {
            return $this->sendSuccess('User already activated');
        }

        $customer = Customer::where('customer_id', $user->reference_id)->first();
        if (!$customer) {
            return $this->sendError("Customer not found", 404);
        }

        $customerShipment = CustomerShipment::where('customer_id', $user->reference_id)->first();

        // $customer->is_verified = 0;
        // $customer->save();
        $user->is_active = 1;
        $user->save();

        //get socialmedia
        $socmed = new GetSocialsRepo();
        $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
        $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
        $twitter = $socmed->getSocialMediaParameters('TWITTER');
        $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
        $youtube = $socmed->getSocialMediaParameters('YOUTUBE');
        $support = $socmed->getSocialMediaParameters('SUPPORT');

        // kirim email
        $param['data'] = [
            'name' => $customer->owner_name,
            'instance_name' => $customer->instance_name,
            'email' => $customer->email,
            'phone' => $customer->phone_number,
            'npwp' => $customer->npwp,
            'npwp_name' => $customer->npwp_name,
            'npwp_address' => $customer->npwp_address,
            'address' => $customerShipment->address,
            'subdistrict' => $customerShipment->subdistrict,
            'district' => $customerShipment->district,
            'city' => $customerShipment->city,
            'province' => $customerShipment->province,
            'zip_code' => $customerShipment->zip_code,


            //GET SOCIAL MEDIA ACCOUNTS
            'facebook' => $facebook,
            'twitter' => $twitter,
            'instagram' => $instagram,
            'support' => $support,
            'tiktok' => $tiktok,
            'youtube' => $youtube
        ];

        MailSender::dispatch($email, json_encode($param), 'mail_verification_b2b', null);

        //send notif to all sales
        // $sales_all = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'B2B')->first();
        // $msg = 'Terdapat pengajuan Customer Baru atas nama '.$user->name.', klik disini untuk melihat detail';
        // $this->notifStore($sales_all->value, 'CUSTOMER BARU', 'customer-internal', $msg, null, null, 'B2B', 'info');

        return $this->sendSuccess('User activated successfully');
    }

    public function validateNPWPKTP(Request $request)
    {
        $tocheck = strtoupper($request->type);
        $numberToCheck = $request->number;
        $check = null;
    
        if ($request->type === 'ktp') {
            $check = Customer::whereNotNull('national_id')->get()->first(function ($customer) use ($numberToCheck) {
                try {
                    return Crypt::decryptString($customer->national_id) === $numberToCheck;
                } catch (\Exception $e) {
                    return $customer->national_id === $numberToCheck;
                }
            });
        } elseif ($request->type === 'npwp') {
            $check = Customer::whereNotNull('npwp')->get()->first(function ($customer) use ($numberToCheck) {
                try {
                    return Crypt::decryptString($customer->npwp) === $numberToCheck;
                } catch (\Exception $e) {
                    return $customer->npwp === $numberToCheck;
                }
            });
        }
    
        if ($check) {
            return response()->json([
                'error' => true,
                'status' => '400 BAD REQUEST',
                'message' => "$tocheck sudah terdaftar",
                'data' => [],
            ], 400);
        }
    
        return response()->json([
            'error' => false,
            'status' => '200 OK',
            'message' => 'Validasi berhasil',
            'data' => [],
        ], 200);
    }      

    public function validateNPWPKTPUpdate(Request $request)
    {
        $custId = auth()->user()->customer->getRawOriginal('customer_id');
        $tocheck = strtoupper($request->type);
        $numberToCheck = $request->number;
        $check = null;
    
        if ($request->type === 'ktp') {
            $check = Customer::whereNotNull('national_id')->where('customer_id', '!=', $custId)->get()->first(function ($customer) use ($numberToCheck) {
                try {
                    return Crypt::decryptString($customer->getRawOriginal('national_id')) === $numberToCheck;
                } catch (\Exception $e) {
                    return $customer->getRawOriginal('national_id') === $numberToCheck;
                }
            });
        } elseif ($request->type === 'npwp') {
            $check = Customer::whereNotNull('npwp')->where('customer_id', '!=', $custId)->get()->first(function ($customer) use ($numberToCheck) {
                try {
                    return Crypt::decryptString($customer->getRawOriginal('npwp')) === $numberToCheck;
                } catch (\Exception $e) {
                    return $customer->getRawOriginal('npwp') === $numberToCheck;
                }
            });
        }
    
        if ($check) {
            return response()->json([
                'error' => true,
                'status' => '400 BAD REQUEST',
                'message' => "$tocheck sudah terdaftar",
                'data' => [],
            ], 400);
        }
    
        return response()->json([
            'error' => false,
            'status' => '200 OK',
            'message' => 'Validasi berhasil',
            'data' => [],
        ], 200);
    }      

    public function getUpdateUserData()
    {
        $i = DB::table('customers as cust')
            // ->select('cust.owner_name','cust.instance_name','cust.tax_type','cust.npwp_name','cust.email','cust.phone_number','cs.address','cs.province','cs.city','cs.district','cs.zip_code','cust.npwp_address','cust.national_id','cs.province_code',
            // 'cust.national_id_file','cust.npwp','cust.npwp_file','cust.tax_invoice','cust.created_date')
            ->join('customer_shipment as cs', 'cs.customer_id', '=', 'cust.customer_id')
            ->where('cust.customer_id', auth()->user()->customer->customer_id)
            ->first();



        $resp = [
            "owner_name" => $i->owner_name,
            "phone_number" => $i->phone_number,
            "instance_name" => $i->instance_name,
            "identity_number" => $i->national_id,
            "file_identity" => $i->national_id_file ? env('S3_STREAM_URL') . $i->national_id_file : null,
            "npwp" => $i->npwp,
            "file_npwp" => $i->npwp_file ? env('S3_STREAM_URL') . $i->npwp_file : null,
            "tax_invoice_type" => $i->tax_invoice,
            "npwp_city" => $i->city,
            "npwp_name" => $i->npwp_name,
            "npwp_address" => $i->npwp_address,
            "address_shipment" => $i->address,
            "city_shipment" => $i->city,
            "province_shipment" => $i->province,
            "zip_code" => $i->zip_code,
            "phone_shipment" => $i->phone_number,
            "district" => $i->district,
            "province_code" => $i->province_code,
            "tax_type" => $i->tax_type,
            "city_code" => $i->city_code,
            "district_code" => $i->district_code,
            "subdistrict" => $i->subdistrict,
        ];

        return $this->sendSuccess('success', $resp);
    }



    public function updateUserData(Request $request)
    {
        // validate uploaded files
        // $finfo = finfo_open(FILEINFO_MIME_TYPE);

        // $mime_type_ktp = finfo_file($finfo,$request->file('file_identity'));
        // $mime_type_npwp = finfo_file($finfo,$request->file('file_npwp'));
        $mime_type_ktp = Storage::disk('s3')->mimeType($request->file_identity);
        $mime_type_npwp = Storage::disk('s3')->mimeType($request->file_npwp);
        // finfo_close($finfo);

        $allowedMimeTypes = array(
            'image/png',
            'image/jpeg',
            'image/jpg'
        );

        if (!in_array($mime_type_ktp, $allowedMimeTypes) || !in_array($mime_type_npwp, $allowedMimeTypes)) {
            return $this->sendError('png, jpg, jpeg files only', 400);
        }

        // //upload ktp to S3
        // $photo_ktp = $request->file('file_identity');
        // $name = uniqid();
        // $content = file_get_contents($photo_ktp);
        // $filename = $name.'.'.$photo_ktp->extension();
        // $ktp_file_name = env('S3_KTP_FOLDER').$filename;
        // Storage::disk('s3')->put(substr($ktp_file_name,1), $content);


        // //upload NPWP to S3
        // $photo_npwp = $request->file('file_npwp');
        // $name = uniqid();
        // $content = file_get_contents($photo_npwp);
        // $filename = $name.'.'.$photo_npwp->extension();
        // $npwp_file_name = env('S3_NPWP_FOLDER').$filename;
        // Storage::disk('s3')->put(substr($npwp_file_name,1), $content);
        // // ================================================================================================
        // ================================================================================================

        // check if username already existed
        $ktp_file_name = $request->file_identity ?? '';
        $npwp_file_name = $request->file_npwp ?? '';


        $customer_id = auth()->user()->customer->customer_id;

        $customers = Customer::where('customer_id', $customer_id)->first();

        $customers->owner_name = $request->owner_name ?? $customers->getOriginal('owner_name');
        $customers->phone_number = $request->phone_number ?? $customers->getOriginal('phone_number');
        $customers->instance_name = $request->instance_name ?? $customers->getOriginal('instance_name');
        $customers->national_id = $request->identity_number ?? $customers->getOriginal('national_id');
        $customers->national_id_file = $ktp_file_name;
        $customers->npwp = FormatHelper::formatNpwp($request->npwp);
        $customers->npwp_name = $request->npwp_name;
        $customers->npwp_file = $npwp_file_name;
        $customers->tax_invoice = $request->tax_invoice_type ?? $customers->getOriginal('tax_invoice');
        $customers->npwp_city = $request->npwp_city ?? $customers->getOriginal('npwp_city');
        $customers->npwp_address = $request->npwp_address ?? $customers->getOriginal('npwp_address');
        $customers->address = $request->address_shipment ?? $customers->getOriginal('address');
        $customers->tax_type = $request->tax_type ?? $customers->getOriginal('tax_type');
        $customers->is_rejected = 0;

        if ($customers->update()) {
            // insert shipment=====================
            $CustomerShipment = CustomerShipment::where('customer_id', $customer_id)->first();
            $CustomerShipment->name = $request->owner_name ?? $CustomerShipment->getOriginal('name');
            $CustomerShipment->address = $request->address_shipment ?? $CustomerShipment->getOriginal('address');
            $CustomerShipment->city = $request->city_shipment ?? $CustomerShipment->getOriginal('city');
            $CustomerShipment->province = $request->province_shipment ?? $CustomerShipment->getOriginal('province');
            $CustomerShipment->zip_code = $request->zip_code ?? $CustomerShipment->getOriginal('zip_code');
            $CustomerShipment->phone_number = $request->phone_shipment ?? $CustomerShipment->getOriginal('phone_number');
            $CustomerShipment->district = $request->district ?? $CustomerShipment->getOriginal('district');
            $CustomerShipment->province_code = $request->province_code ?? $CustomerShipment->getOriginal('province_code');
            $CustomerShipment->country = $CustomerShipment->getOriginal('country');
            $CustomerShipment->country_code = $CustomerShipment->getOriginal('country_code');
            $CustomerShipment->city_code = $request->city_code ?? $CustomerShipment->getOriginal('city_code');
            $CustomerShipment->district_code = $request->district_code ?? $CustomerShipment->getOriginal('district_code');
            $CustomerShipment->subdistrict = $request->subdistrict ?? $CustomerShipment->getOriginal('subdistrict');
            $CustomerShipment->update();
            // ======================================

            // insert user =================
            $user = User::where('reference_id', $customer_id)->first();
            $user->name = $request->owner_name ?? $user->getOriginal('name');
            $user->update();
            // =========================

            //get socialmedia
            $socmed = new GetSocialsRepo();
            $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
            $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
            $twitter = $socmed->getSocialMediaParameters('TWITTER');
            $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
            $youtube = $socmed->getSocialMediaParameters('YOUTUBE');
            $support = $socmed->getSocialMediaParameters('SUPPORT');

            // kirim email
            $param['data'] = [
                'name' => $request->owner_name,
                'email' => $user->email,
                'phone' => $request->phone_number,
                'npwp' => $request->npwp,
                'address' => $request->address_shipment,

                //GET SOCIAL MEDIA ACCOUNTS
                'facebook' => $facebook,
                'twitter' => $twitter,
                'instagram' => $instagram,
                'support' => $support,
                'tiktok' => $tiktok,
                'youtube' => $youtube,
            ];

            //MailSender::dispatch($request->email, json_encode($param),'mail_activate');

            //send notif to all sales
            $sales_all = MasterParameter::where('group_key', 'B2B_NOTIF')->where('key', 'ALL_SALES_NOTIF')->first();
            $msg = 'Terdapat pengajuan Update Customer Baru atas nama ' . $user->name . ', klik disini untuk melihat detail';
            $this->notifStore($sales_all->value, 'UPDATE CUSTOMER BARU', 'customer-internal', $msg, null, 'Info', 'B2B', 'info');

            return response()->json(
                [
                    'error' => false,
                    'status' => '200 OK',
                    'message' => 'Updated Succesfully'
                ],
                200,
                [],
                JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT
            );
        } else {
            return $this->sendError("Sorry system can't Update", 500);
        }
    }

    public function termsAndConditions(Request $request)
    {
        $rslTnc = DB::table('rsl_tnc')
            ->select('id', 'section_name', 'sequence_no', 'value')
            ->where('is_active', '=', 1)
            ->orderBy('sequence_no', 'asc')
            ->get()
            ->toArray();

        $rslTncDetail = DB::table('rsl_tnc_detail')
            ->select('id', 'tnc_id', 'sequence_no', 'value')
            ->where('is_active', '=', 1)
            ->orderBy('sequence_no', 'asc')
            ->get()
            ->toArray();

        $rslTncPoint = DB::table('rsl_tnc_point')
            ->select('id', 'tnc_detail_id', 'sequence_no', 'value')
            ->where('is_active', '=', 1)
            ->orderBy('sequence_no', 'asc')
            ->get()
            ->toArray();

        $result = [];
        $detailPoint = [];

        //combine points and details
        foreach ($rslTncDetail as $tncDetail) {
            $detailPoint[$tncDetail->id] = [
                "tnc_id" => $tncDetail->tnc_id,
                "detail_sequence" => $tncDetail->sequence_no,
                "detail_value" => $tncDetail->value,
                "tnc_point" => []
            ];
            foreach ($rslTncPoint as $tncPoint) {
                if ($tncPoint->tnc_detail_id == $tncDetail->id) {
                    array_push(
                        $detailPoint[$tncDetail->id]["tnc_point"],
                        [
                            "point_sequence" => $tncPoint->sequence_no,
                            "point_value" => $tncPoint->value,
                        ]
                    );
                }
            }
        }

        //combine details with points and main sections
        foreach ($rslTnc as $tnc) {
            $result[$tnc->section_name] = [
                "section_name" => $tnc->section_name,
                "sequence_no" => $tnc->sequence_no,
                "value" => $tnc->value,
                "tnc_detail" => []
            ];

            foreach ($detailPoint as $tncDetail) {
                if ($tncDetail['tnc_id'] == $tnc->id) {
                    array_push(
                        $result[$tnc->section_name]['tnc_detail'],
                        [
                            "sequence_no" => $tncDetail['detail_sequence'],
                            "detail_value" => $tncDetail['detail_value'],
                            "tnc_point" => $tncDetail['tnc_point']
                        ]
                    );
                }
            }
        }

        $footer = DB::table('master_parameter')
            ->select('value')
            ->where('group_key', '=', 'RESELLER_TNC')
            ->Where('key', '=', 'FOOTER')
            ->get()->first();

        $result["footer"] = $footer->value;

        return $this->sendSuccess('GET Terms and Conditions Success', $result);
    }

    public function activateUserByToken($activation_token, Request $request)
    {
        try {
            $user = User::where('activation_token', $activation_token)->first();

            if (!$user) {
                if ($request->has('email')) {
                    $user = User::where('email', $request->email)
                        ->where('is_active', 1)
                        ->first();
    
                    if ($user) {
                        return response()->json([
                            'status' => 'info',
                            'message' => 'Your account is already activated.',
                            'redirect_to' => config('app.b2b_app_url') . '/authentication/login'
                        ], 200);
                    }
                }
    
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid activation token.',
                    'redirect_to' => null
                ], 400);
            }   
            
            if ($user->activation_token_expired_at < now()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Activation token has expired. Please request a new activation email.',
                    'redirect_to' => null
                ], 410);
            }
    
            $user->update([
                'is_active' => 1,
                'activation_token' => null,
                'activation_token_expired_at' => null,
            ]);
    
            // Get social media info
            // $socmed = new GetSocialsRepo();
            // $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
            // $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
            // $twitter = $socmed->getSocialMediaParameters('TWITTER');
            // $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
            // $youtube = $socmed->getSocialMediaParameters('YOUTUBE');
            // $support = $socmed->getSocialMediaParameters('SUPPORT');
    
            // $param['data'] = [
            //     'name' => $customer->owner_name ?? '',
            //     'facebook' => $facebook,
            //     'twitter' => $twitter,
            //     'instagram' => $instagram,
            //     'support' => $support,
            //     'tiktok' => $tiktok,
            //     'youtube' => $youtube
            // ];
    
            // MailSender::dispatch($user->email, $param['data'], 'mail_activate', null);
    
            return response()->json([
                'status' => 'success',
                'message' => 'Your account has been activated',
                'redirect_to' => null
            ], 200);
    
        } catch (\Exception $e) {
            Log::error('Activation Failed: ' . $e->getMessage() . ' | Line: ' . $e->getLine());
    
            return response()->json([
                'status' => 'error',
                'message' => 'Activation failed',
                'redirect_to' => null
            ], 500);
        }
    }    

    public function resendActivateLink(Request $request)
    {
        $request->validate([
            'email' => 'required|email'
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user) {
            return $this->sendError("User not found", 404);
        }

        if ($user->is_active) {
            return $this->sendSuccess("Your account already activated.");
        }

        if ($user->activation_token && $user->activation_token_expired_at >= now()) {
            return $this->sendSuccess("An activation link has already been sent. Please check your email.");
        }

        $token = Str::random(64);
        $user->update([
            'activation_token' => $token,
            'activation_token_expired_at' => now()->addHours(24)
        ]);

        $socmed = new GetSocialsRepo();
        $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
        $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
        $twitter = $socmed->getSocialMediaParameters('TWITTER');
        $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
        $youtube = $socmed->getSocialMediaParameters('YOUTUBE');

        $param['data'] = [
            'name' => $user->name,
            'email' => $user->email,
            'facebook' => $facebook,
            'twitter' => $twitter,
            'instagram' => $instagram,
            'tiktok' => $tiktok,
            'youtube' => $youtube
        ];

        MailSender::dispatch($user->email, $param['data'], 'mail_verification', $token);

        return $this->sendSuccess("Activation link has been resent to your email.");
    }
}