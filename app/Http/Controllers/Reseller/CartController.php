<?php

namespace App\Http\Controllers\Reseller;

use App\Http\Controllers\Controller;
use App\Models\ResellerCart;
use App\Models\ResellerCartDetail;
use App\Http\Resources\Cart\RslCartListResource;
use Illuminate\Http\Request;
use App\Http\Requests\CartRequest;
use Illuminate\Support\Facades\Cache;

class CartController extends Controller
{
    public function getCart (CartRequest $request) 
    {
        $request->validated();
        // $cart = ResellerCart::Where('customer_id', '=', $customerId)
        // ->OrWhere('link_id', '=', $linkId)
        // ->first();
        
        // if (!$cart) {
        //     return $this->sendError("Cart data not found", 404);
        // }
        
        // $articles = ResellerCartDetail::with('article')->where('cart_id', '=', $cart->id)->orderBy('created_date','DESC')
        // ->get()
        // ->toArray();
        
        // if (!$articles) {
        //     return $this->sendError("Cart detail data not found", 404);
        // }
        // dd($request->customer_id);die();

        return $this->sendSuccess("Get Cart List Success", new RslCartListResource(ResellerCart::fetch($request->customer_id, $request->link_id)->first()));
    }
    
    public function addCart (Request $request) 
    {
        $customerId = $request->input('customer_id'); // ?? condition jika customer tidak login
        $linkId = $request->input('link_id');
        $articleId = $request->input('article_id');
        $quantity = $request->input('quantity');

        $cart = ResellerCart::Where('customer_id', '=', $customerId)
        ->Where('link_id', '=', $linkId)
        ->first();

        if (!$cart) {
            try {
                $createCart = [
                    "link_id" => $linkId,
                    "customer_id" => $customerId
                ];
                ResellerCart::create($createCart);
    
                $cart = ResellerCart::Where('customer_id', '=', $customerId)
                ->Where('link_id', '=', $linkId)
                ->first();
            } catch (\Exception $e) {
                return $this->sendError("Constraint violation", 500);
            }
        }

        $cartDetail = [
            "cart_id" => $cart->id,
            "article_id" => $articleId,
            "qty" => $quantity
        ];

        try {
            ResellerCartDetail::updateOrCreate(['cart_id' => $cart->id, 'article_id' => $articleId], $cartDetail);
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }

        return $this->sendSuccess("Add to Cart Success", $cartDetail);
    }
    
    public function deleteCart (Request $request) 
    {
        $cartDetailId = $request->input('cart_detail_id');
        $article = ResellerCartDetail::where('id', $cartDetailId)->first();

        if(!$article) {
            return $this->sendError("Article in Cart Not Found", 404);
        }

        $article->delete();

        return $this->sendSuccess("Article in cart deleted", $article);
    }

    public function deleteAllCart (Request $request) 
    {
        $cartId = $request->input('cart_id');
        
        try {
            $articles = ResellerCartDetail::Where('cart_id', $cartId)->delete();
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }

        return $this->sendSuccess("Articles in cart deleted", $articles);
    }
}

