<?php

namespace App\Http\Controllers\Reseller;

use App\Http\Requests\MutationVARequest;
use DB;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Helpers\DanamonHelper;
use App\Http\Requests\{CreateVARequest, TransferRequest, InquiryVARequest, InquiryBankRequest, InquiryBankDanamonRequest, TransferVAInquiryRequest, TransferVARequest};
use App\Models\Reseller;
use App\Models\ResellerVA;

class DanamonController extends Controller
{
  private $dn_helper;
  public function __construct()
  {
    $this->dn_helper = new DanamonHelper;
    
  }

  public function store(CreateVARequest $request)
  {
    $request->validated();
    $rq = $this->MappingCreateVA(Reseller::Where('reseller_id', '=', $request->input('reseller_id'))->first());
    // $this->dn_helper->queueDispatch($request,'create_va_debit');
    //cth queue ^
    return $this->dn_helper->createVADebit($rq);
  }

  public function aioFunction(TransferRequest $request)
  {
    $request->validated();
    $payload = $request->transform();
    // $this->dn_helper->queueDispatch($request,'create_va_debit');
    //cth queue ^
    return $this->dn_helper->doAction($request->module??'', $payload??[]);
  }


  public function inquiryVA(InquiryVARequest $request)
  {
    $request->validated();
    return $this->dn_helper->vaInquiry($request->transform());
  }

  public function mutationVA(MutationVARequest $request)
  {
    $request->validated();
    return $this->dn_helper->mutationInquiry($request->transform());
  }
  
  public function inquiryBank(InquiryBankRequest $request)
  {
    $request->validated();
    if($request['bank_code'] == '011'){
      return $this->dn_helper->bankDanamonInquiry($request->transformDanamon());
    } else {
      return $this->dn_helper->bankInquiry($request->transform());
    }
  }

  public function inquiryBankDanamon(InquiryBankDanamonRequest $request)
  {
    $request->validated();
    return $this->dn_helper->bankDanamonInquiry($request->transform());
  }

  public function inquiryBalanceDanamon(InquiryBankDanamonRequest $request)
  {
    $request->validated();
    return $this->dn_helper->casaInquiry($request->transform());
  }

  public function topupInquiry(TransferVAInquiryRequest $request)
  {
    $request->validated();
    return $this->dn_helper->topupInquiry($request->transform($this->dn_helper->getOperationalAccount()));
  }

  public function transfer(TransferRequest $request)
  {
    $request->validated();
    return $this->dn_helper->topupInquiry($request->transform($this->dn_helper->getOperationalAccount()));
  }

  public function topupTransfer(TransferVARequest $request)
  {
    $request->validated();
    return $this->dn_helper->topupTransfer($request->transform($this->dn_helper->getOperationalAccount()));
  }
}
