<?php

namespace App\Http\Controllers\Reseller;

use App\Http\Controllers\Controller;
use App\Services\LoggerIntegration;
use Illuminate\Http\Request;
use App\Http\Requests\JNERequest; 
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;

class JNEController extends Controller
{
    public function checkFee(JNERequest $request)
    {
    $request->validated();
    $payload = $request->transform();
    try {
        $response = Http::asForm()
        ->withHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded'
        ])->post('http://apiv2.jne.co.id:10101/tracing/api/pricedev',$payload);
        (new LoggerIntegration)->InsertLogger([
            'module' => 'JNE',
            'name' => 'Cek Tarif JNE',
            'input-from' => $payload['from'],
            'input-thru' => $payload['thru'],
            'type'=> 'Outbound',
            'status' => 'success',
            'description' => json_encode([
                'payload' => $payload,
                'response' => 'Integrasi sukses'
            ])
        ]);
        return $this->sendSuccess('Check Rate Success',json_decode($response));
        } catch(\Exception $e){
            (new LoggerIntegration)->InsertLogger([
                'module' => 'JNE',
                'name' => 'Cek Tarif JNE',
                'input-from' => $payload['from'],
                'input-thru' => $payload['thru'],
                'type'=> 'Outbound',
                'status' => 'failed',
                'description' => json_encode([
                    'payload' => $payload,
                    'response' => $e->getMessage()
                ])
            ]);
        DB::rollback();
        return $this->sendError(['message' => $e->getMessage()], 500);
        }
    }
}

