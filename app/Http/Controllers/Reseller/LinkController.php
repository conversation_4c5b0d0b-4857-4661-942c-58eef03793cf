<?php

namespace App\Http\Controllers\Reseller;

use Carbon\Carbon;
use App\Models\User;
use Valitron\Validator;
use App\Models\Reseller;
use App\Helpers\LinkHelper;
use App\Models\ResellerLink;
use Illuminate\Http\Request;
use App\Models\ResellerLinkHistory;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use App\Http\Requests\GenerateLinkRequest;
use App\Models\OrderReseller;
use App\Models\ResellerToken;

class LinkController extends Controller
{

  use LinkHelper;
  public function generateLink(GenerateLinkRequest $request)
  {
    $request->validated();
    $cleanData = $request->transform();

    if ($cleanData == false) return $this->sendError("Bad JSON String", 500);
    try {
      $resellerId = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();
      if($resellerId->is_active == Reseller::RESELLER_REJECTED){
        return $this->sendError("reseller not allowed to do this action",400);
      }
      $cleanData["reseller_id"] = $resellerId->id;
      switch ($request->input("type")) {
        case "LIST":
          if (Cache::has($cleanData['reseller_id'] . '-List')) {
            $cleanData['metadata'] = json_encode(Cache::get($cleanData['reseller_id'] . '-List'));
          }
          break;
        case "DETAIL":
          if (Cache::has($cleanData['reseller_id'] . '-Detail')) {
            $cleanData['metadata'] = json_encode(Cache::get($cleanData['reseller_id'] . '-Detail'));
          }
          break;
        case "STORE":
          $checkExistingStore = ResellerLink::Where("reseller_id", "=", $cleanData['reseller_id'])->Where("type", "=", "STORE")->first();
          
          if ($checkExistingStore) {
            ResellerLink::where('reseller_id', $cleanData['reseller_id'])->Where("type", "=", "STORE")
              ->update(['identifier' => $cleanData['identifier']]);

            return $this->sendSuccess(
              "Reseller STORE Link Updated",
              ['reseller_link' => env('RESELLER_APP_URL', 'https://reseller-dev.eigerindo.co.id/') . $cleanData['identifier']]
            );
          }
          break;
      }
      ResellerLink::create($cleanData);

      return $this->sendSuccess('Generate link Success', ["reseller_link" => env('RESELLER_APP_URL', 'https://reseller-dev.eigerindo.co.id/') . $cleanData['identifier']]);
    } catch (\Exception $e) {
      return $this->sendError("Generate link failed", 500, '', ['message' => $e->getMessage()]);
    }
  }

  public function getIdentifier(Request $request, $identifier)
  {
    if (!$identifier) {
      return $this->sendError("identifier is required", 400);
    }

    $link = ResellerLink::Where('identifier', '=', $identifier)->first();

    if (!$link) {
      return $this->sendError("identifier data not found", 404);
    }

    $response = [
      "id" => $link->id,
      "reseller_id" => $link->reseller_id,
      "type" => $link->type,
      "metadata" => $link->metadata,
      'is_reseller_active' => $link->reseller->is_active
    ];

    if (!empty(request()->header('X-AUTH-DEVICE'))) {
      $customer_id = ResellerToken::customerID(request()->header('X-AUTH-DEVICE'));
      $order = OrderReseller::where('reseller_id',$link->reseller_id)->where('customer_id',$customer_id)
                            ->latest()->first();
      
      $response['order_no'] = $order->order_no??null;
    }

    try {
      $linkHistory = new ResellerLinkHistory;
      $linkHistory->link_id = $link->id;
      $linkHistory->save();
    } catch (\Exception $e) {
      return $this->sendError("Failed to save link history", 500, '', ['message' => $e->getMessage()]);
    }

    return $this->sendSuccess('Get Link Success', $response);
  }

  public function getStoreLink(Request $request)
  {
    $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();
    $resellerId = $reseller->id;
    $getStoreLink = ResellerLink::Where("reseller_id", "=", $resellerId)->Where("type", "=", "STORE")->first();

    if (!$getStoreLink) {
      return $this->sendError("No store link found");
    }

    return $this->sendSuccess("Get store link success", $getStoreLink);
  }

  public function getAllLinks(Request $request)
  {
    $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();
    $resellerId = $reseller->id;
    $getStoreLink = ResellerLink::Where("reseller_id", "=", $resellerId)->get()->toArray();

    if (!$getStoreLink) {
      return $this->sendError("No store link found");
    }
    $activeLinks = [];

    foreach ($getStoreLink as $link) {
      $activeLinks[] = [
        "link" => env('RESELLER_APP_URL', 'https://reseller-dev.eigerindo.co.id/') . $link['identifier'],
        "metadata" => $link['metadata'],
        "type" => $link['type']
      ];
    }
    return $this->sendSuccess("Get all link success", $activeLinks);
  }

  private function randomStrings($length_of_string)
  {
    $str_result = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';

    return substr(str_shuffle($str_result), 0, $length_of_string);
  }

  public function countHistoryLink(Request $request)
  {
    $dataNow = ResellerLink::where('reseller_id', $request->user()->reseller->id)
      ->withCount([
        'history' => fn ($q) => $q->monthPeriod(Carbon::now())
      ])
      ->get()->toArray();

    $dataBfr = ResellerLink::where('reseller_id', $request->user()->reseller->id)
      ->withCount([
        'history' => fn ($q) => $q->monthPeriod(Carbon::now()->subMonth())
      ])
      ->get()->toArray();

    $res = $this->countingHistoryLink($dataBfr, $dataNow);

    return $this->sendSuccess('Count reseller history link success', $res);
  }
}
