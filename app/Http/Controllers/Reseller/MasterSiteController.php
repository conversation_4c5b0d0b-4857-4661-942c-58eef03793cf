<?php

namespace App\Http\Controllers\Reseller;

use App\Http\Controllers\Controller;
use App\Models\ResellerMasterSite;
use Illuminate\Http\Request;

class MasterSiteController extends Controller
{
    public function index(Request $request)
    {
        $search = $request->query('search');

        try {
            $data = ResellerMasterSite::where('is_active',1)
                                        ->when($search,function($q)use($search){
                                            $q->where(function($q)use($search){
                                                $q->whereHas('ma',function ($q) use($search){
                                                    $q->where('region_name','LIKE',"%$search%");
                                                })->orWhere('code','LIKE',"%$search%")
                                                ->orWhere('name','LIKE',"%$search%");
                                            });
                                        })
                                        // ->with('ma')
                                        ->get()
                                        ->map(function($m){
                                            return [
                                                'code' => $m->code,
                                                'site' => $m->name.' - '.@$m->ma->region_name??null
                                            ];
                                        });

            if ($data->isEmpty()) {
                return $this->sendError('Master Site not found');
            }
            
            return $this->sendSuccess('Get master site success',$data);
        } catch (\Exception $e) {
            return $e->getMessage().' - '.$e->getLine();
        }
    }
}
