<?php

namespace App\Http\Controllers\Reseller;

use GuzzleHttp\Client;
use App\Jobs\MailSender;
use Illuminate\Http\Request;
use App\Interfaces\OTPInterface;
use App\Http\Requests\OTPRequest;
use Illuminate\Support\Facades\DB;
use App\Services\LoggerIntegration;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use App\Http\Requests\OTPVerifyRequest;
use App\Models\User;

class OTPController extends Controller
{
  private $mode, $response, $log;
  public function __construct()
  {
    /*
      1. Botika
      2. Email (CareOM)
      3. Mock 123456
      */
      $this->mode = getenv('OTP_MODE', '1');
      $this->response = [
        'result' => 'success',
        'message' => 'OTP Sent'
      ];
      $this->log = [
        'reference_no' => '',
        'module' => 'Botika',
        'name' => 'Request OTP',
        'type' => 'Outbound',
        'status' => '',
        'description' => [
            'payload' => '',
            'response' => ''
        ]
        ];
      
  }

  public function getOTP($payload){
    try{
      $this->log['reference_no'] = $payload['phone']??'';
      $this->log['description']['payload'] = $payload;
      $result = $this->response;
      if($this->mode == '1'){
        $urlRequestOTP = 'https://client.botika.online/eiger/api/otp/create';
        $response = Http::withToken(getenv('TOKEN_OTP'))->post(
          $urlRequestOTP,
          $payload
        );
  
        $result = json_decode($response->getBody()->getContents());
  
      }
      if($this->mode == '2'){
        $otp_key = 'otp-'.$payload['name'];
        if(!Cache::has($otp_key)){
          $number = mt_rand(100000, 999999);
          Cache::set($otp_key, $number, now()->addMinutes(1));
          MailSender::dispatch($payload['name'], json_encode(strval($number)), 'mail_otp');
          $result = $this->response;
        }
        else{
          $this->response['result'] = 'error';
          $this->response['message'] = 'You still have active otp code, please wait for a moment';
          $result = $this->response;
        }
        
      }
      (new LoggerIntegration())->InsertLogger([
        ...$this->log,
        'description' => [
            'payload' => $payload,
            'response' => $result
        ]
    ]);
      return $this->sendSuccess('OTP Sent', $result);

    }
    catch(\Exception $e){
      return $this->sendException('Request OTP Error', 500, '',$e);
    }
   
  }

  public function requestOTP(OTPRequest $request)
  {
    try {
      $request->validated();
      $payload = $request->transform();
      return $this->getOTP($payload);
    } catch (\Exception $e) {
      return $this->sendException('Request OTP Error',500,'',$e);
    }
  }

  public function verifyOTP(OTPVerifyRequest $request)
  {
    try {
      $request->validated();
      $payload = $request->transform();
      if($this->mode == '1'){
        return $this->verifyBotika($payload);
      }
      if($this->mode == '2'){
        return $this->verifyEmail($payload);
      }
      else{
      return $this->verifyMock($payload['code']??'0');
      }
     
    } catch (\Exception $e) {
      return $this->sendException('Verify OTP Error',500,'',$e);
    }
  }

  public function verifyEmail($payload){
    try{
      $otp_key = 'otp-'.$payload['name'];
      if(!Cache::has($otp_key)){
        return response()->json([
              'result'   => "error",
              'message' => 'OTP is Expired'
          ], 400);
      }
      if(Cache::get($otp_key) !== $payload['code']){
        return response()->json([
          'result'   => "error",
          'message' => 'OTP is Invalid'
      ], 400);
      }
      Cache::forget($otp_key);
      // $this->UpdateVerify();
      return response()->json([
        'result'   => "success",
        'message' => 'OTP is Valid'
    ], 200);
    }
    catch(\Exception $e){
      return $this->sendException('Verify OTP Error',500,'',$e);
    }
       
  }

  public function verifyMock($otp){
    if ($otp != "123456") {
              return response()->json([
                  'result'   => "error",
                  'message' => 'OTP is Invalid'
              ], 400
              );
          }
          // $this->UpdateVerify();
          return response()->json([
              'result'   => "success",
              'message' => 'OTP is Valid'
          ], 200
          );
  }

  public function verifyBotika($request){
    $urlRequestOTP = 'https://client.botika.online/eiger/api/otp/verify';
    $payload = $request;
    $response = Http::withToken(getenv('TOKEN_OTP'))->post(
      $urlRequestOTP,
      $payload
    );

    if ($response->failed()) {
      (new LoggerIntegration())->InsertLogger([
          'reference_no' => $payload['phone'],
          'module' => 'Botika',
          'name' => 'Verify OTP',
          'type' => 'Outbound',
          'status' => 'failed',
          'description' => [
              'payload' => $payload,
              'response' => $response->json()
          ]
      ]);

      return $this->sendError('Please retry again later, verify otp is temporary unavailable.',503);
    }

    $result = json_decode($response->getBody()->getContents());

    (new LoggerIntegration())->InsertLogger([
        'reference_no' => $payload['phone'],
        'module' => 'Botika',
        'name' => 'Verify OTP',
        'type' => 'Outbound',
        'status' => 'success',
        'description' => [
            'payload' => $payload,
            'response' => $response->json()
        ]
    ]);

    if (isset($result->status) && $result->status != true) {
      return $this->sendError('OTP code format is wrong. (The code must be a number and 6 digits max)', 500);
    }

    switch ($result->message) {
      case "OTP is Invalid":
        return response()->json(
          [
            'result' => "error",
            'message' => 'OTP is Invalid'
          ],
          400
        );
        // break;
      case "OTP is expired":
        return response()->json(
          [
            'result' => "error",
            'message' => 'OTP is expired'
          ],
          400
        );
        // break;
    }

    // $this->UpdateVerify();
    return response()->json(
      [
        'result' => "success",
        'message' => 'OTP is Valid'
      ],
      200
    );
  }

  public function UpdateVerify()
  {
    DB::beginTransaction();
    try {
      $user = User::where('email',request()->input()['email'])->where('reference_object','reseller')->first();
      $user->update([
        'verified_otp_date' => now()
      ]);
    } catch (\Exception $e) {
      DB::rollBack();
      return $this->sendException('Verify OTP failed',500,'',$e);
    }
    DB::commit();
  }


  // public function requestOTP(OTPRequest $request)
  // {
  //     $request->validated();
  //     $request = $request->transform();
  //     //kebutuhan development sementara di matikan
  //     // $urlRequestOTP = 'https://client.botika.online/eiger/api/otp/create';
  //     // $response = Http::withToken(getenv('TOKEN_OTP'))->post($urlRequestOTP, 
  //     // [
  //     //     'userid' => $request['userid'],
  //     //     'name'   => $request['name'],
  //     //     'phone'  => $request['phone'],
  //     //     'source' => $request['source']
  //     // ]);

  //     // $result = json_decode($response->getBody()->getContents());

  //     //blocker, perlu di test lagi code di line 30 ini. 
  //     //case: ketika hit OTP dengan email kantor akan terjadi internal server error 500 dari botika
  //     //akan tetapi, OTP tetap terkirim, dan akan invalid ketika di verify.
  //     //perlu test lebih lanjut pada pengecekan nilai dari json_decode($response->getBody()->getContents() 
  //     //karna dengan email pribadi terkadang juga menghasilkan value null sama seperti saat menggunakan email kantor 


  //     // switch ($result->message) {
  //     //     case "You still have active otp code, please wait for a moment":
  //     //         return $this->sendError('You still have active otp code, please wait for a moment', 400);
  //     //         break;
  //     //     case null:
  //     //         return $this->sendError('Internal Server Error when requesting OTP', 500);
  //     //         break;
  //     // }

  //     $result = [
  //         "code" => "123456"
  //     ];

  //     return $this->sendSuccess("OTP Sent", $result, 200);
  // }

  // public function verifyOTP(OTPRequest $request)
  // {
  //     // $request->validated();
  //     // $urlRequestOTP = 'https://client.botika.online/eiger/api/otp/verify';
      
  //     // $response = Http::withToken(getenv('TOKEN_OTP'))->post($urlRequestOTP, 
  //     // [
  //     //     'userid' => $request->phone,
  //     //     'code'   => $request->code,
  //     //     'email'  => $request->email,
  //     //     'source' => 'CAREOM-Reseller'
  //     // ]);
  //     // $result = json_decode($response->getBody()->getContents());
      
  //     // if (isset($result->status) && $result->status != true)  {
  //     //     return $this->sendError('OTP code format is wrong. (The code must be a number and 6 digits max)', 500);
  //     // }

  //     // switch ($result->message) {
  //     //     case "OTP is Invalid":
  //     //         return response()->json([
  //     //             'result'   => "error",
  //     //             'message' => 'OTP is Invalid'
  //     //         ], 400
  //     //         );
  //     //         break;
  //     //     case "OTP is expired":
  //     //         return response()->json([
  //     //             'result'   => "error",
  //     //             'message' => 'OTP is expired'
  //     //         ], 400
  //     //         );
  //     //         break;
  //     // }
  //     $phone = $request->input("phone");
  //     $otp = $request->input("code");
  //     $email = $request->input("email");
      
  //     if ($otp != "123456") {
  //         return response()->json([
  //             'result'   => "error",
  //             'message' => 'OTP is Invalid'
  //         ], 400
  //         );
  //     }

  //     return response()->json([
  //         'result'   => "success",
  //         'message' => 'OTP is Valid'
  //     ], 200
  //     );
  // }
}