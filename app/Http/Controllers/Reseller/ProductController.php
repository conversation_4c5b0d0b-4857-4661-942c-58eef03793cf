<?php
namespace App\Http\Controllers\Reseller;
use Illuminate\Http\Request;
use App\Models\PublicProduct;
use App\Jobs\ResellerArticleSorter;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;
use App\Services\Reseller\ProductService;
use App\Interfaces\Reseller\ProductInterface;

class ProductController extends Controller
{
    protected $productInterface;

    public function __construct(ProductInterface $productInterface)
    {
        $this->productInterface = $productInterface;
    }

    /**
     * @return mixed
     */
    public function index(Request $request)
    {
        // $param = $request->collect();
        // return $this->successResponse($this->productService->fetchProducts($param));
        $site = $request->input('order_by', null);
        $product = \DB::table($site == 'promo'? 'view_reseller_article_promo as article' : 'view_reseller_article AS article');
        
        return $this->productInterface->getData($product, $request);
    }

    public function indexCustomer(Request $request)
    {
        $site = $request->input('order_by', null);

        $product = \DB::table($site == 'promo'? 'view_reseller_article_promo as article' : 'view_reseller_article AS article');
        // dd($product->get());
        return $this->productInterface->getData($product, $request);
    }

    public function getDetailPublic(Request $request, $sku)
    {
        $data = $this->productInterface->getDetailPublic($sku, $request);
        return $data;
    }
    /**
     * @param $product
     *
     * @return mixed
     */
    public function show($product)
    {
        // return $this->successResponse($this->productService->fetchProduct($product));
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function store(Request $request)
    {
        // return $this->successResponse($this->productService->createProduct($request->all()));
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param                          $product
     *
     * @return mixed
     */
    public function update(Request $request, $product)
    {
        // return $this->successResponse($this->productService->updateProduct($product, $request->all()));
    }

    /**
     * @param $product
     *
     * @return mixed
     */
    public function destroy($product)
    {
        // return $this->successResponse($this->productService->deleteProduct($product));
    }
}
