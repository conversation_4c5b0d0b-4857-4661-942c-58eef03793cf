<?php

namespace App\Http\Controllers\Reseller;

use App\Helpers\DanamonHelper;
use App\Http\Requests\DeactivateCustomerResellerRequest;
use App\Http\Requests\DetailApplicantRequest;
use App\Http\Requests\ListApplicantsHistoryRequest;
use App\Http\Requests\ListApplicantsRequest;
use App\Http\Requests\ResellerRegisterRequest;
use App\Http\Requests\VerifyResellerRequest;
use App\Http\Resources\ApplicantDetailResource;
use App\Http\Resources\ApplicantsDashboardResource;
use App\Http\Resources\ListApplicantsHistoryResource;
use App\Http\Resources\ListApplicantsResource;
use App\Models\City;
use App\Models\District;
use App\Models\Region;
use App\Models\ResellerCommission;
use App\Models\ResellerCustomer;
use App\Models\ResellerVA;
use App\Models\Subdistrict;
use App\Services\RegisterService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use App\Models\Reseller;
use App\Jobs\MailSender;
use App\Models\ResellerRegistration;
use App\Models\Customer;
use App\Models\User;
use App\Models\MasterParameter;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\Controller;
use App\Repositories\GetSocialsRepo;
use Valitron\Validator;
use App\Helpers\FormatHelper;

class RegisterController extends Controller
{
  protected $registerService;
  private $dn_helper;
  public function __construct(RegisterService $registerService)
  {
    $this->registerService = $registerService;
    $this->dn_helper = new DanamonHelper;
  }
  public function registerAsReseller(ResellerRegisterRequest $request)
  {
    $validatedRequest = $request->transform();
    if(gettype($validatedRequest) != 'array') return $validatedRequest;
    $regist = new ResellerRegistration();
    $regist->fill($validatedRequest);
    $regist->save();
    if ($regist) {
      $socialMedia = new GetSocialsRepo();
      $regist->support = $socialMedia->getSocialMediaParameters('SUPPORT');
      $regist->facebook = $socialMedia->getSocialMediaParameters('FACEBOOK');
      $regist->youtube = $socialMedia->getSocialMediaParameters('YOUTUBE');
      $regist->twitter = $socialMedia->getSocialMediaParameters('TWITTER');
      $regist->instagram = $socialMedia->getSocialMediaParameters('INSTAGRAM');
      $regist->tiktok = $socialMedia->getSocialMediaParameters('TIKTOK');
      $regist->linkedin = $socialMedia->getSocialMediaParameters('LINKEDIN');
      $regist->registration_date = date("d-m-Y");

      $param['data'] = $regist;
      // Send Mail Successfully registered as reseller
      MailSender::dispatch($request->email, json_encode($param), 'mail_eigerpreneur_regist');

      $param = [
        'data' => [
          'reseller_id' => $regist->reseller_id,
          'registration_date' => date("d-m-Y"),
          'name' => $regist->name,
          'phone_no' => $regist->phone_number,
          'email' => $regist->email,
          'address' => $regist->address,

          'facebook' => $regist->facebook,
          'twitter' => $regist->twitter,
          'instagram' => $regist->instagram,
          'linkedin' => $regist->linkedin,
          'youtube' => $regist->youtube,
          'support' => $regist->support,
          'tiktok' => $regist->tiktok,
        ]
      ];
      $internal = DB::table('user as u')
        ->leftJoin('sales as s', 's.sales_id', '=', 'u.reference_id')
        ->leftJoin('user_matrix as um', 'um.user_id', '=', 'u.user_id')
        ->leftJoin('business_unit as bu', 'bu.id', '=', 'um.business_unit_id')
        ->Select('u.email')
        ->Where('bu.name', 'Reseller')
        ->whereIn('um.tier_level',[4,5])
        ->get()
        ->toArray();

      $notificationMesssage = 'Ada pendaftar reseller baru atas nama ' . $regist->name . ' yang memerlukan verifikasi Anda. Klik pesan ini untuk melihat detail.';
      $salesAll = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'RESELLER')->first();
      $this->notifStore($salesAll->value, 'Pendaftar Reseller Baru', 'reseller-registration', $notificationMesssage, $regist->reseller_id);

      #Send Mail to internal
      foreach ($internal as $admin) {
        MailSender::dispatch($admin->email, json_encode($param), 'mail_reseller_registration');
      }

      return $this->sendSuccess('Registered Succesfully');
    } else {
      return $this->sendError("Register failed", 400);
    }
  }

  public function validateExistingPhoneNumber(Request $request)
  {
    $phoneNumber = $request->input('phoneNumber');

    $validator = new Validator(['phoneNumber' => $phoneNumber]);
    $validator->rule('required', 'phoneNumber')->message('{field} is required')->label("Phone Number");
    $validator->rule('numeric', 'phoneNumber')->message('{field} has to be numeric')->label("Phone Number");
    $validator->rule('lengthBetween', 'phoneNumber', 10, 14)->message('{field} must be between 10 - 14 digits')->label("Phone Number");

    $phoneNumber = FormatHelper::formatPhoneNo($phoneNumber);
    // check if phone number already exist
    $checkExistingEmail = Reseller::where('phone_number', $phoneNumber)->first();

    if ($checkExistingEmail) {
      return $this->sendError("Phone Number already exist as a Reseller", 400);
    }

    return $this->sendSuccess('This Phone Number is available for use', $phoneNumber);
  }

  public function validateExistingEmail(Request $request)
  {
    $email = $request->input('email');

    $validator = new Validator(['email' => $email]);
    $validator->rule('required', 'email')->message('{field} is required')->label("email");

    if (!$validator->validate()) {
      $errors = collect($validator->errors());
      $messages = $errors->flatten();
      return $this->sendError($messages, 400);
    }
    // check if email already exist
    $checkExistingEmail = Reseller::where('email', $email)->first();
    if ($checkExistingEmail) {
      return $this->sendError("Email already registered as Reseller", 400, '');
    }

    return $this->sendSuccess('This Email is available for use', $email);
  }

  public function faq(Request $request)
  {
    $faq = DB::table('rsl_faq')
      ->select('question', 'answer')
      ->where('is_active', true)
      ->orderby('sequence_no', 'asc')
      ->get()
      ->toArray();

    if (!$faq) {
      $faq = [];
    }

    return $this->sendSuccess('GET FAQ Success', $faq);
  }

  public function termsAndConditions(Request $request)
  {
    $rslTnc = DB::table('rsl_tnc')
      ->select('id', 'section_name', 'sequence_no', 'value')
      ->where('is_active', '=', 1)
      ->orderBy('sequence_no', 'asc')
      ->get()
      ->toArray();

    $rslTncDetail = DB::table('rsl_tnc_detail')
      ->select('id', 'tnc_id', 'sequence_no', 'value')
      ->where('is_active', '=', 1)
      ->orderBy('sequence_no', 'asc')
      ->get()
      ->toArray();

    $rslTncPoint = DB::table('rsl_tnc_point')
      ->select('id', 'tnc_detail_id', 'sequence_no', 'value')
      ->where('is_active', '=', 1)
      ->orderBy('sequence_no', 'asc')
      ->get()
      ->toArray();

    $result = [];
    $detailPoint = [];

    //combine points and details
    foreach ($rslTncDetail as $tncDetail) {
      $detailPoint[$tncDetail->id] = [
        "tnc_id" => $tncDetail->tnc_id,
        "detail_sequence" => $tncDetail->sequence_no,
        "detail_value" => $tncDetail->value,
        "tnc_point" => []
      ];
      foreach ($rslTncPoint as $tncPoint) {
        if ($tncPoint->tnc_detail_id == $tncDetail->id) {
          array_push(
            $detailPoint[$tncDetail->id]["tnc_point"],
            [
              "point_sequence" => $tncPoint->sequence_no,
              "point_value" => $tncPoint->value,
            ]
          );
        }
      }
    }

    //combine details with points and main sections
    foreach ($rslTnc as $tnc) {
      $result[$tnc->section_name] = [
        "section_name" => $tnc->section_name,
        "sequence_no" => $tnc->sequence_no,
        "value" => $tnc->value,
        "tnc_detail" => []
      ];

      foreach ($detailPoint as $tncDetail) {
        if ($tncDetail['tnc_id'] == $tnc->id) {
          array_push(
            $result[$tnc->section_name]['tnc_detail'],
            [
              "sequence_no" => $tncDetail['detail_sequence'],
              "detail_value" => $tncDetail['detail_value'],
              "tnc_point" => $tncDetail['tnc_point']
            ]
          );
        }
      }
    }

    $footer = DB::table('master_parameter')
      ->select('value')
      ->where('group_key', '=', 'RESELLER_TNC')
      ->Where('key', '=', 'FOOTER')
      ->get()->first();

    $result["footer"] = $footer->value;

    return $this->sendSuccess('GET Terms and Conditions Success', $result);
  }

  public function listResellerApplicants(ListApplicantsRequest $request)
  {
    $validatedData = $request->validated();
    $orderBy = $validatedData['order_by'];
    $sortValue = $validatedData['sort_value'];
    $search = $validatedData['search'];
    $perPage = $validatedData['per_page'];

    $applicants = ResellerRegistration::where(function ($query) {
      $query->where('status', ResellerRegistration::REGISTRATION_CREATED);
    })
      ->when(!empty($search), function ($query) use ($search) {
        $query->where(function ($subQuery) use ($search) {
          $subQuery->where('name', 'LIKE', '%' . $search . '%')
            ->orWhere('phone_number', 'LIKE', '%' . $search . '%');
        });
      })
      ->when($orderBy, function ($query) use ($orderBy, $sortValue) {
        $query->orderBy($orderBy, $sortValue);
      })
      ->paginate($perPage);

    if (!$applicants) {
      return $this->sendError("No applicant data found", 404);
    }

    return $this->sendSuccess('get data applicants success', $this->pagedResponse(ListApplicantsResource::collection($applicants), $applicants->currentPage(), $perPage));
  }

  public function listResellerApplicantsHistory(ListApplicantsHistoryRequest $request)
  {

    $validatedData = $request->validated();
    $orderBy = $validatedData['order_by'];
    $sortValue = $validatedData['sort_value'];
    $search = $validatedData['search'];
    $perPage = $validatedData['per_page'];

    $applicants = ResellerRegistration::where(function ($query) {
      $query->where('status', ResellerRegistration::REGISTRATION_APPROVED)
        ->orWhere('status', ResellerRegistration::REGISTRATION_REJECTED);
    })
      ->when(!empty($search), function ($query) use ($search) {
        $query->where(function ($subQuery) use ($search) {
          $subQuery->where('name', 'LIKE', '%' . $search . '%')
            ->orWhere('phone_number', 'LIKE', '%' . $search . '%');
        });
      })
      ->when($orderBy, function ($query) use ($orderBy, $sortValue) {
        $query->orderBy($orderBy, $sortValue);
      })
      ->paginate($perPage);


    if (!$applicants) {
      return $this->sendError("No applicant data found", 404);
    }

    return $this->sendSuccess('get data applicants history success', $this->pagedResponse(ListApplicantsHistoryResource::collection($applicants), $applicants->currentPage(), $perPage));
  }

  public function ResellerApplicantsDashboard()
  {
    $result = $this->registerService->getRegisterDashboardData();
    return $this->sendSuccess('get data applicants success', new ApplicantsDashboardResource($result));
  }

  public function detailResellerApplicants(DetailApplicantRequest $request)
  {
    $resellerId = $request->validated();

    $applicant = ResellerRegistration::Where('reseller_id', '=', $resellerId)
      // ->Where('status', '=', ResellerRegistration::REGISTRATION_CREATED)
      ->first();

    if (!$applicant) {
      return $this->sendError("No applicant data found", 404);
    }

    return $this->sendSuccess('GET Reseller Detail Applicant Succesfully', new ApplicantDetailResource($applicant));
  }

  public function verifyReseller(VerifyResellerRequest $request)
  {
    $validatedData = $request->validated();
    $resellerId = $validatedData['reseller_id'];
    $type = $validatedData['type'];
    $actionNotes = $validatedData['action_notes'];

    $checkRegisteredById = ResellerRegistration::where('reseller_id', $resellerId)->first();

    //TODO
    if (!$checkRegisteredById) {
      return $this->sendError("Registered ID not found", 400);
    }
    switch ($checkRegisteredById->status) {
      case ResellerRegistration::REGISTRATION_APPROVED:
        return $this->sendError("Your Registration is already approved!", 400);
      case ResellerRegistration::REGISTRATION_REJECTED:
        return $this->sendError("Your Registration is rejected!", 400);
    }

    if ($type == "approve") {
      try {
        //hit danamon create VA
        // end hit danamon (mocked)
        $rq = $request->MappingCreateVA($checkRegisteredById);
        $hit = $this->dn_helper->createVADebit($rq);
        $jsonContent = $hit->getContent();
        $data = json_decode($jsonContent, true);
        if ($data['message'] == 'success') {
          $dataArray = $data['data'];
          $vaNumber = $dataArray['VirtualAccountNumber'];
        } else {
          return $this->sendError('Terjadi Kendala pada proses, Hubungi Admin - ID Laporan : ' . $data['data']['log_id'] ?? '-', 400);
        }
        // end hit danamon (mocked)

        //Update status registrar to approved
        DB::beginTransaction();
        DB::table('rsl_registration')
          ->where('reseller_id', '=', $resellerId)
          ->update([
            'status' => ResellerRegistration::REGISTRATION_APPROVED,
            'action_by' => auth()->user()->sales->sales_id,
            'action_notes' => $actionNotes,
            'action_date' => date('Y-m-d H:i:s'),
            'modified_date' => date('Y-m-d H:i:s'),
            'modified_by' => auth()->user()->sales->sales_id
          ]);
        DB::commit();
      } catch (\Exception $e) {
        DB::rollBack();
        return $this->sendError($e->getMessage());
      }

      try {
        $transformed = $request->transform($checkRegisteredById);
        $resellerApproved = new Reseller;
        $resellerApproved->fill($transformed);

        if ($resellerApproved->save()) {

          $resellerNew = Reseller::where('reseller_id', $checkRegisteredById->reseller_id)->first();
          $fillVA = $request->transformStoreVA($resellerNew, $dataArray);
          $resellerVa = new ResellerVA;
          $resellerVa->fill($fillVA);
          $resellerVa->save();
          $fillCommission = $request->createCommission($resellerNew);
          $regist = new ResellerCommission();
          $regist->fill($fillCommission);
          $regist->save();

          $User = new User;
          $transformedUser = $request->transformUser($checkRegisteredById);
          $User->fill($transformedUser);
          $User->save();
        }
      } catch (\Exception $e) {
        return $this->sendError($e, 400);
      }

      $socialMedia = new GetSocialsRepo();
      $province = Region::select('name')->where('code', $checkRegisteredById->province_code)->first()->name ?? '-';
      $city = City::select('name')->where('id', $checkRegisteredById->city_code)->first()->name ?? '-';
      $district = District::select('name')->where('code', $checkRegisteredById->district_code)->first()->name ?? '-';
      $zipCode = Subdistrict::select('postal_code')->where('code', $checkRegisteredById->zip_code)->first()->postal_code ?? $checkRegisteredById->zip_code;
      $address = $checkRegisteredById->address . ', ' . $district . ', ' . $city . ', ' . $province . ', ' . $zipCode;
      $param['data'] = [
        'reseller_id' => $checkRegisteredById->reseller_id,
        'created_date' => Carbon::parse($checkRegisteredById->created_date)->format('d-m-Y'),
        'name' => $checkRegisteredById->name,
        'phone_number' => $checkRegisteredById->phone_number,
        'address' => $address,
        'national_id' => $checkRegisteredById->national_id,
        'national_id_file' => env('S3_STREAM_URL') . $checkRegisteredById->national_id_file,
        'npwp' => $checkRegisteredById->npwp,
        'npwp_file' => env('S3_STREAM_URL') . $checkRegisteredById->npwp_file,
        'modified_date' => Carbon::parse($checkRegisteredById->modified_date)->format('d-m-Y'),

        //GET SOCIAL MEDIA ACCOUNTS
        'facebook' => $socialMedia->getSocialMediaParameters('FACEBOOK'),
        'twitter' => $socialMedia->getSocialMediaParameters('TWITTER'),
        'instagram' => $socialMedia->getSocialMediaParameters('INSTAGRAM'),
        'support' => $socialMedia->getSocialMediaParameters('SUPPORT'),
        'tiktok' => $socialMedia->getSocialMediaParameters('TIKTOK'),
        'linkedin' => $socialMedia->getSocialMediaParameters('LINKEDIN'),
      ];
      // Send Mail Successfully approved as reseller
      MailSender::dispatch($checkRegisteredById->email, json_encode($param), 'mail_eigerpreneur_approved');

      return $this->sendSuccess('Reseller Verified Succesfully');
    } else if ($type == "reject") {

      try {
        //Update status registrar to approved
        DB::beginTransaction();
        DB::table('rsl_registration')
          ->where('reseller_id', '=', $resellerId)
          ->update([
            'status' => ResellerRegistration::REGISTRATION_REJECTED,
            'action_by' => auth()->user()->sales->sales_id,
            'action_notes' => $actionNotes,
            'action_date' => date('Y-m-d H:i:s'),
            'modified_date' => date('Y-m-d H:i:s'),
            'modified_by' => auth()->user()->sales->sales_id
          ]);
        DB::commit();
      } catch (\Exception $e) {
        DB::rollBack();
        return $this->sendError("Register failed", 400);
      }
      $socialMedia = new GetSocialsRepo();
      $province = Region::select('name')->where('code', $checkRegisteredById->province_code)->first()->name ?? '-';
      $city = City::select('name')->where('id', $checkRegisteredById->city_code)->first()->name ?? '-';
      $district = District::select('name')->where('code', $checkRegisteredById->district_code)->first()->name ?? '-';
      $zipCode = Subdistrict::select('postal_code')->where('code', $checkRegisteredById->zip_code)->first()->postal_code ?? $checkRegisteredById->zip_code;
      $address = $checkRegisteredById->address . ', ' . $district . ', ' . $city . ', ' . $province . ', ' . $zipCode;
      $param['data'] = [
        'reseller_id' => $checkRegisteredById->reseller_id,
        'created_date' => Carbon::parse($checkRegisteredById->created_date)->format('Y-m-d'),
        'name' => $checkRegisteredById->name,
        'phone_number' => $checkRegisteredById->phone_number,
        'address' => $address,
        'national_id' => $checkRegisteredById->national_id,
        'national_id_file' => env('S3_STREAM_URL') . $checkRegisteredById->national_id_file,
        'npwp' => $checkRegisteredById->npwp,
        'npwp_file' => env('S3_STREAM_URL') . $checkRegisteredById->npwp_file,
        'modified_date' => Carbon::parse($checkRegisteredById->modified_date)->format('Y-m-d'),
        'action_notes' => $actionNotes,

        //GET SOCIAL MEDIA ACCOUNTS
        'facebook' => $socialMedia->getSocialMediaParameters('FACEBOOK'),
        'twitter' => $socialMedia->getSocialMediaParameters('TWITTER'),
        'instagram' => $socialMedia->getSocialMediaParameters('INSTAGRAM'),
        'support' => $socialMedia->getSocialMediaParameters('SUPPORT'),
        'tiktok' => $socialMedia->getSocialMediaParameters('TIKTOK'),
        'linkedin' => $socialMedia->getSocialMediaParameters('LINKEDIN'),
      ];

      // Send Mail Rejected as reseller
      MailSender::dispatch($checkRegisteredById->email, json_encode($param), 'mail_eigerpreneur_rejected');

      return $this->sendSuccess('Reseller Registration Rejected');
    }

    return $this->sendError("Type not found", 400);
  }

  public function deactivateCustomerReseller(DeactivateCustomerResellerRequest $request, $id)
  {

    $reseller = ResellerRegistration::Where("reseller_id", $id)->first();
    if (!$reseller) {
      return $this->sendError('applicant not found', 404);
    }
    $checkUser = User::where("username", $reseller->email)->where('reference_object', 'rsl_customers')->first();
    if ($checkUser) {
      $checkCustomer = ResellerCustomer::where('id', $checkUser->reference_id)->where('is_active', 1)->first();
    } else {
      $checkCustomer = null;
    }
    if (!$checkCustomer) {
      return $this->sendError('customer not found');
    }
    $checkCustomer->fill($request->transform());
    if ($checkCustomer->update()) {
      return $this->sendSuccess('deactivated Succesfully');
    } else {
      return $this->sendError("update failed", 400);
    }
  }
}
