<?php
namespace App\Http\Controllers\Reseller;

use App\Models\User;
use App\Jobs\MailSender;
use App\Models\Reseller;
use App\Helpers\RestHelper;
use Illuminate\Http\Request;
use App\Models\OrderReseller;
use Illuminate\Support\Carbon;
use App\Models\UserNotification;
use App\Models\OrderItemReseller;
use App\Services\ResellerService;
use App\Models\ResellerCommission;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\CommissionWithdrawal;
use App\Repositories\GetSocialsRepo;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cache;
use App\Models\ResellerTransferMethod;
use App\Models\{ResellerBank, MasterBank};
use App\Http\Requests\ListWithdrawalRequest;
use App\Models\ResellerCommissionWithdrawal;
use App\Models\{MasterParameter, TaxMatrix};
use Illuminate\Database\Eloquent\Collection;
use App\Http\Requests\CreateResellerWithdrawalRequest;
use App\Http\Requests\ResellerCommissionDetailRequest;
use App\Http\Resources\ListWithdrawalResellerResource;
use App\Http\Resources\ResellerIndexCommissionResource;
use App\Jobs\CreateCommissionWithdrawal;

class ResellerCommissionController extends Controller
{
  protected $resellerService;

  public function __construct(ResellerService $resellerService)
  {
    $this->resellerService = $resellerService;
  }

  public function getIndexCommission()
  {
    $currentMonth = Carbon::now()->month;
    $currentYear = Carbon::now()->year;
    $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();

    $commission = ResellerCommission::Select("potential_amount", "commission_amount")
      ->Where("reseller_id", $reseller->id)
      ->first();

    $salesThisMonth = OrderReseller::SalesSumTotalAmount($reseller->id, $currentMonth, $currentYear);

    $withdrawalHistory = ResellerCommissionWithdrawal::Select(
      "request_date",
      "status",
      "request_id",
      "amount",
      "transfer_fee",
      "tax_amount",
      "total"
    )
      ->Where("reseller_id", $reseller->id)
      ->orderBy("created_date", "desc")
      ->limit(5)
      ->get()
      ->toArray();

    $data = new Collection([
      'commission_amount' => $commission->commission_amount ?? 0,
      'potential_amount' => $commission->potential_amount ?? 0,
      'sales' => $salesThisMonth,
      'withdrawalHistory' => $withdrawalHistory,
    ]);

    return $this->sendSuccess('GET index commission success', new ResellerIndexCommissionResource($data));
  }

  public function getListCommissionWithdrawal(ListWithdrawalRequest $request)
  {
    $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();
    $validatedData = $request->validated();
    $startDate = $validatedData['start_date'];
    $endDate = $validatedData['end_date'];
    $perPage = $validatedData['per_page'] ?? 5;
    $status = $validatedData['status'];

    $withdrawals = ResellerCommissionWithdrawal::Select("request_id", "request_date", "status", "amount", "transfer_fee", "tax_amount", "total")
      ->when(!empty($startDate), function ($query) use ($startDate, $endDate) {
        $query->whereBetween('request_date', [$startDate, $endDate]);
      })
      ->when($status !== null, function ($query) use ($status) {
        $query->whereIn('status', $status);
      })
      ->where('reseller_id', $reseller->id)
      ->orderBy('created_date', 'desc')
      ->paginate($perPage);

    $datas = $this->pagedResponse(ListWithdrawalResellerResource::collection($withdrawals), $withdrawals->currentPage(), $perPage);
    $datas['data'] = collect(data_get($datas, 'data'))->values()->all();

    return $this->sendSuccess('get data list withdrawals success', $datas);
  }

  public function commissionWithdrawalDetail(ResellerCommissionDetailRequest $request)
  {
    $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();
    $validatedData = $request->validated();
    $requestId = $validatedData['request_id'];

    $withdrawalDetails = ResellerCommissionWithdrawal::With("rsl_bank.bank")
      ->Where("rsl_commission_withdrawal.request_id", $requestId)
      ->Where("rsl_commission_withdrawal.reseller_id", $reseller->id)
      ->first();

    $data = new Collection([
      'request_id' => $withdrawalDetails->request_id,
      'request_date' => $withdrawalDetails->request_date,
      'status' => $withdrawalDetails->status,
      'amount' => $withdrawalDetails->amount,
      'transfer_fee' => $withdrawalDetails->transfer_fee,
      'tax_amount' => $withdrawalDetails->tax_amount,
      'total' => $withdrawalDetails->total,
      'payment_date' => $withdrawalDetails->payment_date,
      'account_name' => $withdrawalDetails->account_name,
      'account_no' => $withdrawalDetails->account_no,
      'bank_name' => $withdrawalDetails->rsl_bank->bank->bank_name,
      'metadata' => json_decode($withdrawalDetails->tax_metadata)??[]
    ]);

    return $this->sendSuccess('get withdraw details success', $data);
  }

  public function getTransferMethod()
  {
    $transferMethod = ResellerTransferMethod::Select("id", "name", "description", "min_amount", "max_amount", "fees")
      ->Where("is_active", ResellerTransferMethod::ACTIVE_METHOD)
      ->OrderBy("sequence_no", "asc")
      ->get()
      ->toArray();

    return $this->sendSuccess("get transfer method success", $transferMethod);
  }

  public function calculateWithdrawalTax(Request $request)
  {
    $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();
    $withdrawAmount = $request->input('amount');
    $transferFee = $request->input('transfer_fee');

    // $taxParameter = MasterParameter::Select("value")->Where("group_key", "RESELLER_COMMISSION")->Where("key", "WITHDRAWAL_TAX")->first();
    $taxParameter = TaxMatrix::getCurrentMetadata();
    $currentUsage = CommissionWithdrawal::getLeverageTax($reseller);
    $progressiveTax = TaxMatrix::calculateTax($taxParameter, $withdrawAmount, $currentUsage);
    // $pph21 = $withdrawAmount * $taxParameter->value / 100;
    $pph21 = $progressiveTax['tax_total'];
    $commission = ResellerCommission::Select("potential_amount", "commission_amount")
      ->Where("reseller_id", $reseller->id)
      ->first();

    $currentSaldo = $commission->commission_amount;
    $checkRemainingSaldo = $currentSaldo - $withdrawAmount;
    $cashOut = $withdrawAmount - $pph21 - $transferFee; // total = amount - tax_amount - $transferFee

    $minimumDepositSaldo = MasterParameter::Select("value")->Where("group_key", "RESELLER_COMMISSION")->Where("key", "COMMISSION_DEPOSIT")->first();

    // compare remaining saldo tidak boleh kurang dari minimum saldo endapan
    if ($checkRemainingSaldo < $minimumDepositSaldo->value) {
      return $this->sendError("Jumlah penarikan komisi melewati saldo minimum anda.", 400);
    }

    return [
      "withdraw_amount" => (int) $withdrawAmount,
      "tax" => $pph21,
      "transfer_fee" => (int) $transferFee,
      "cash_out" => $cashOut,
      "remaining_saldo" => $checkRemainingSaldo,
      "metadata" => $progressiveTax
    ];
  }

  public function createWithdrawal(CreateResellerWithdrawalRequest $request)
  {
    usleep(1);
    $hashURL = RestHelper::hashURL($request);
    if (Cache::has($hashURL)) {
      Cache::forget($hashURL);
      return $this->sendError('Reseller Commission Withdrawal is on create',422);
    }
    Cache::put($hashURL,['ada']);
    $validatedData = $request->validated();
    $accountName = $validatedData['account_name'];
    $accountNo = $validatedData['account_no'];
    $amount = $validatedData['amount'];
    // $transfer_fee = $validatedData['transfer_fee'];
    $taxAmount = $validatedData['tax_amount'];
    $total = $validatedData['total'];
    $transferMethod = $validatedData['transfer_method'] ?? "Overbooking";
    $mp = MasterParameter::where('group_key','COMMISSION_WITHDRAWAL_FEE')
                                    ->where('key',$transferMethod)->first();
    $transfer_fee = (int)$mp->value;

    $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();

    #Current Saldo
    $checkAmount = ResellerCommission::Where("reseller_id", $reseller->id)->first();

    $resellerBank = ResellerBank::where('reseller_id', $reseller->id)->first();
        $bank = MasterBank::where('id', $resellerBank->bank_id)->first();
    #Current Pending Request
    $checkPendingRequest = ResellerCommissionWithdrawal::Where("reseller_id", $reseller->id)
      ->Where("status", ResellerCommissionWithdrawal::WAITING_FOR_APPROVAL)->first();

    if ($checkPendingRequest) {
      return $this->sendError("There is a pending request of your commission waiting for approval. RequestID: " . $checkPendingRequest->request_id, 200);
    }

    if ($total < 10000 && $transferMethod == CommissionWithdrawal::RTOL) {
      return $this->sendError("Minimal Pengajuan Rp 10.000 setelah dipotong Biaya transfer & Pajak", 200);
    }

    $requestId = "CR" . date("Ymd") . mt_rand(1000, 9999);
    $extRequestId = "EXT" . $requestId;
    try {
      $taxCalculation = TaxMatrix::calculateTax(TaxMatrix::getCurrentMetadata(), $amount, CommissionWithdrawal::getLeverageTax($reseller));
      ResellerCommissionWithdrawal::create(
      [
        'request_date' => date("YmdHis"),
        'request_id' => $requestId,
        'ext_request_id' => $extRequestId,
        'reseller_id' => $reseller->id,
        'bank_name' => $bank->bank_name??'',
        'account_name' => $accountName,
        'tax_reference' => TaxMatrix::getCurrentMetadata()->id??null,
        'tax_metadata'=> json_encode($taxCalculation),
        'account_no' => $accountNo,
        'status' => ResellerCommissionWithdrawal::WAITING_FOR_APPROVAL,
        'amount' => $amount,
        'transfer_fee' => $transfer_fee,
        'tax_amount' => $taxCalculation['tax_total']??0,
        'payout_amount' =>  $taxCalculation['nett_total'],
        'total' =>  $taxCalculation['nett_total'] - $transfer_fee,
        'transfer_method' => $transferMethod
      ]);
    } catch (\Exception $e) {
      return $this->sendError("Sorry system can't process your withdrawal request, " . $e->getMessage(), 500);
    }

    $notificationMesssage = 'Selamat pengajuan komisi Anda dengan nomor request ' . $requestId . '. telah dikirimkan ke nomor rekening terpilih!';
    $this->notifStore(auth()->user()->reference_id, 'Pengajuan Penarikan Komisi Menunggu Persetujuan', 'reseller-commission-withdrawal', $notificationMesssage, $requestId, UserNotification::RESELLER_COMMISSION);

    $notificationMesssageInternal = 'Reseller atas nama ' . $reseller->name . ' mengajukan penarikan komisi dengan nomor ' . $requestId . ' memerlukan persetujuan Anda. Klik pesan ini untuk melihat detail.';
    $salesAll = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'RESELLER')->first();
    $this->notifStore($salesAll->value, 'Pengajuan Penarikan Komisi Baru', 'reseller-commission-withdrawal-internal', $notificationMesssageInternal, $requestId, UserNotification::RESELLER_COMMISSION);

    $socmed = new GetSocialsRepo();
    $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
    $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
    $twitter = $socmed->getSocialMediaParameters('TWITTER');
    $linkedin = $socmed->getSocialMediaParameters('LINKEDIN');

    $bank = ResellerBank::With("bank")
      ->Where("reseller_id", "=", $reseller->id)->first();

    $param = [
      'data' => [
        'type' => 'Reseller',
        'reseller_id' => $reseller->reseller_id,
        'name' => $reseller->name,
        'phone_no' => $reseller->phone_number,
        'email' => $reseller->email,
        'ktp' => $reseller->national_id,
        'npwp' => $reseller->npwp,
        'address' => $reseller->address,

        'request_id' => $requestId,
        'request_date' => date("d-m-Y"),
        'status' => ResellerCommissionWithdrawal::WAITING_FOR_APPROVAL,
        'bank_name' => $bank->bank->bank_name,
        'account_no' => $accountNo,
        'account_name' => $accountName,
        'transfer_method' => $transferMethod,
        'metadata' => $taxCalculation,
        'amount' => $amount,
        'transfer_fee' => $transfer_fee,
        'tax_amount' => $taxCalculation['tax_total']??0,
        'payout_amount' =>  $taxCalculation['nett_total'],
        'total' =>  $taxCalculation['nett_total'] - $transfer_fee,

        'facebook' => $facebook,
        'twitter' => $twitter,
        'instagram' => $instagram,
        'linkedin' => $linkedin,
      ]
    ];
    // Send Mail to reseller
    MailSender::dispatch($reseller->email, json_encode($param), 'mail_reseller_withdrawal');

    $internal = DB::table('user as u')
      ->leftJoin('sales as s', 's.sales_id', '=', 'u.reference_id')
      ->leftJoin('user_matrix as um', 'um.user_id', '=', 'u.user_id')
      ->leftJoin('business_unit as bu', 'bu.id', '=', 'um.business_unit_id')
      ->Select('u.email')
      ->Where('bu.name', 'Reseller')
      ->whereIn('um.tier_level',[4,5])
      ->get()
      ->toArray();

    $param['data']['type'] = 'Internal';
    #Send Mail to internal
    foreach ($internal as $admin) {
      MailSender::dispatch($admin->email, json_encode($param), 'mail_reseller_withdrawal');
    }
    Cache::forget($hashURL);
    return $this->sendSuccess("Commission withdrawal created. Request ID: " . $requestId);
  }

  public function createWithdrawalNew(CreateResellerWithdrawalRequest $request)
  {
    $validatedData = $request->validated();
    CreateCommissionWithdrawal::dispatch($request->input(),auth()->user()->user_id??"");
  }

}
