<?php

namespace App\Http\Controllers\Reseller;

use App\Helpers\DanamonHelper;
use App\Helpers\Promotion\PromotionHelper;
use App\Helpers\Coupon\CouponHelper;
use App\Helpers\Voucher\VoucherHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\EditResellerRequest;
use App\Http\Requests\EditResellerStatusRequest;
use App\Http\Requests\InternalAdjustmentRequest;
use App\Http\Requests\ListOrdersInternalRequest;
use App\Http\Requests\ListResellersRequest;
use App\Http\Requests\ListTransactionsRequest;
use App\Http\Requests\ListWithdrawalInternalRequest;
use App\Http\Requests\ResellerChartAllDownloadRequest;
use App\Http\Requests\ResellerChartAllRequest;
use App\Http\Requests\ResellerChartRequest;
use App\Http\Requests\ResellerOrderListInternalRequest;
use App\Http\Requests\ResellerProfileRequest;
use App\Http\Requests\ResellerSummaryDashboardRequest;
use App\Http\Requests\TopSellingItemsRequest;
use App\Http\Requests\ListTransactionResellerRequest;
use App\Http\Requests\DetailTransactionResellerRequest;
use App\Http\Requests\VerifyWithdrawalRequest;
use App\Http\Requests\WithdrawalDetailInternalRequest;
use App\Http\Requests\ListOrderDetailsInternalRequest;
use App\Http\Resources\DownloadResellerResource;
use App\Http\Resources\ListFilteredTransactionsResource;
use App\Http\Resources\ListOrdersInternalResource;
use App\Http\Resources\ListResellersResource;
use App\Http\Resources\ListTransactionResellerResource;
use App\Http\Resources\ListTransactionsResource;
use App\Http\Resources\ListWithdrawalsResource;
use App\Http\Resources\OngoingOrdersResource;
use App\Http\Resources\ResellerCommissionDashboardResource;
use App\Http\Resources\ResellerInternalChartResource;
use App\Http\Resources\ResellerInternalDashboardResource;
use App\Http\Resources\ResellerOrderListInternal;
use Illuminate\Support\Arr;
use App\Http\Resources\ResellerOrderListInternalResource;
use App\Http\Resources\ResellerProfileResource;
use App\Http\Resources\ResellerSalesReportInternalResource;
use App\Http\Resources\ResellerSalesReportResource;
use App\Http\Resources\ResellerSummaryDashboardResource;
use App\Http\Resources\TopCommissionsResource;
use App\Http\Resources\TopResellersResource;
use App\Http\Resources\TopSellingResource;
use App\Http\Resources\ResellerIndexTransactionResource;
use App\Http\Resources\ResellerDbPerfomanceResource;
use App\Http\Resources\ResellerMonthlyPerformanceResource;
use App\Http\Resources\ResellerDetailTrxResource;
use App\Http\Resources\ResellerIndexCommissionResource;
use App\Http\Resources\ResellerRegisteredBankResource;
use App\Http\Resources\ListOrdersInternalDownloadResource;
use App\Http\Resources\ListWithdrawalsDownloadResource;
use App\Http\Resources\ListOrderDetailsInternalResource;
use App\Jobs\MailSender;
use App\Models\Commission;
use App\Models\CommissionLedgers;
use App\Models\MasterBank;
use App\Http\Resources\WithdrawalDetailInternalResource;
use App\Models\CommissionWithdrawal;
use App\Models\MasterParameter;
use App\Models\OrderReseller;
use App\Models\OrderItemReseller;
use App\Models\Reseller;
use App\Models\ResellerBank;
use App\Models\ResellerLink;
use App\Models\ResellerLinkHistory;
use App\Models\ResellerCommission;
use App\Models\ResellerOrderPromotion;
use App\Models\ResellerTransaction;
use App\Models\TaxMatrix;
use App\Models\User;
use App\Models\ResellerOrderShipment;
use App\Models\ResellerOrderHeaders;
use App\Models\ResellerCommissionWithdrawal;
use App\Models\ImageVariant;
use App\Models\UserNotification;
use App\Repositories\GetSocialsRepo;
use App\Services\ResellerService;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class ResellerController extends Controller
{
  protected $resellerService;
  private $dn_helper;

  use  PromotionHelper, CouponHelper, VoucherHelper;
  public function __construct(ResellerService $resellerService) 
  {
      $this->resellerService = $resellerService;
      $this->dn_helper = new DanamonHelper;
  }

  public function listResellers(ListResellersRequest $request)
  {
    try{

    $validatedData = $request->validated();
    // $orderBy = $validatedData['order_by'];
    // $sortValue = $validatedData['sort_value'];
    $search = $validatedData['search'];
    $perPage = $validatedData['per_page'];
    $status = $validatedData['reseller_status'];

    $resellers = Reseller::leftJoin('rsl_order_headers', 'rsl_reseller.id', '=', 'rsl_order_headers.reseller_id')
    ->leftJoin('rsl_commission', 'rsl_reseller.id', '=', 'rsl_commission.reseller_id')
    ->select('rsl_reseller.*', DB::raw('MAX(rsl_order_headers.created_date) as newest_order'), DB::raw('MAX(rsl_commission.commission_amount) as commission_amount'))
    ->groupBy('rsl_reseller.id')
    ->orderByDesc('newest_order')
    ->when(!empty($search), function ($query) use ($search) {
        $query->where(function ($subQuery) use ($search) {
            $subQuery->where('rsl_reseller.name', 'LIKE', '%' . $search . '%')
                      ->orWhere('rsl_reseller.phone_number', 'LIKE', '%' . $search . '%')
                      ->orWhere('rsl_reseller.reseller_id', 'LIKE', '%' . $search . '%');
        });
      })
      ->when($status !== null, function ($query) use ($status) {
          $query->where('is_active', $status);
      })
    ->paginate($perPage);

    $datas = $this->pagedResponse(ListResellersResource::collection($resellers), $resellers->currentPage(), $perPage);
    return $this->sendSuccess('get data resellers success',$datas);
    } catch (Exception $e){
        dd($e->getMessage());
    }
  }

  public function listTransactions(ListTransactionsRequest $request, $id)
  {
    $validatedData = $request->validated();
    $search = $validatedData['search'];
    $perPage = $validatedData['per_page'];
    $orderStatus = $validatedData['transaction_status'];
    $resellerId = $validatedData['id'];
    $start = $validatedData['date_from'];
    $to = $validatedData['date_to'];
    $commissionStatus = $validatedData['commission_status'];


    $transactions = ResellerTransaction::
    leftJoin('rsl_order_headers', 'rsl_order_headers.order_no', '=', 'rsl_transactions.reference_id')
    ->leftJoin('rsl_commission_ledgers', 'rsl_commission_ledgers.transaction_id', '=', 'rsl_transactions.id')
    ->select(
    'rsl_order_headers.customer_name', 
    'rsl_transactions.reference_id',
    'rsl_transactions.created_date',
    'rsl_transactions.amount',
    'rsl_transactions.order_status',
    'rsl_transactions.type',
    //'rsl_commission_ledgers.amount as commission_amount',
    'rsl_order_headers.commission_amount as order_commission', 
    'rsl_transactions.request_no',
    'rsl_transactions.remarks'
     )->distinct()
    ->whereNotIn('rsl_transactions.order_status', ['Withdrawal'])
    ->where(function($query) use ($resellerId, $id) {
        $query->where('rsl_order_headers.reseller_id', $resellerId)
        ->orWhere('rsl_commission_ledgers.reseller_id', $id);
     })
    ->when(!empty($search), function ($query) use ($search) {
        $query->where(function ($subQuery) use ($search) {
            $subQuery->where('reference_id', 'LIKE', '%' . $search . '%')
                      ->orWhere('customer_name', 'LIKE', '%' . $search . '%');
        });
      })

    ->when($start !== null && $to !== null, function ($query) use ($start, $to) {
        $query->whereDate('rsl_transactions.created_date', '>=', $start)
        ->whereDate('rsl_transactions.created_date', '<=', $to);
    })
    ->when(!empty($orderStatus), function ($query) use ($orderStatus) {
        $query->where('rsl_transactions.order_status', $orderStatus);
    })
    ->when(!empty($commissionStatus) && $commissionStatus != 'potential_adjustment'&& $commissionStatus != 'commission_adjustment', function ($query) use ($commissionStatus) {
        $query->where('rsl_transactions.type',$commissionStatus)
        ->whereNotIn('rsl_transactions.order_status',['Adjustment']);
        if ($commissionStatus === ResellerTransaction::TX_TYPE_POTENTIAL_COMMISSION) {
            $query->orWhere(function ($subQuery) {
                $subQuery->where('rsl_transactions.type',ResellerTransaction::TX_TYPE_INVOICE_PAYMENT)
                    ->where('rsl_transactions.order_status', 'Baru');
            });
        }

    })
    ->when($commissionStatus === 'potential_adjustment', function ($query) {
        $query->where(function ($subQuery) {
            $subQuery->where('rsl_transactions.type', ResellerTransaction::TX_TYPE_POTENTIAL_COMMISSION)
                      ->where('rsl_transactions.order_status', 'Adjustment');
        });
    })
    ->when($commissionStatus === 'commission_adjustment', function ($query) {
        $query->where(function ($subQuery) {
            $subQuery->where('rsl_transactions.type', ResellerTransaction::TX_TYPE_COMMISSION)
                      ->where('rsl_transactions.order_status', 'Adjustment');
        });
    })
    ->orderBy('rsl_transactions.created_date','desc')
    ->get()->paginate($perPage);

    $datas = $this->pagedResponse(ListTransactionsResource::collection($transactions), $transactions->currentPage(), $perPage);


    return $this->sendSuccess('get data resellers success',$datas);
  }

  public function topResellers()
  {
    $result = $this->resellerService->getTopResellers();
    return $this->sendSuccess('get top resellers data success',TopResellersResource::collection($result));
  }

  public function topCommissions()
  {
    $result = $this->resellerService->getTopCommissions();
    return $this->sendSuccess('get top resellers commissions data success',TopCommissionsResource::collection($result));
  }

  public function listCommissionsWithdrawalsInternal(ListWithdrawalInternalRequest $listWithdrawalInternalRequest)
  {
    $request = $listWithdrawalInternalRequest->validated();
    $search = $request['search'];
    $perPage = $request['per_page'];
    $status = $request['withdrawal_status'];
    $start = $request['date_from'];
    $to = $request['date_to'];
    $isDownload = $request['is_download'];

    $withdrawals = CommissionWithdrawal::leftJoin('rsl_reseller', 'rsl_reseller.id', '=', 'rsl_commission_withdrawal.reseller_id')
    ->select('rsl_commission_withdrawal.request_date',
    'rsl_commission_withdrawal.request_id',
    'rsl_reseller.reseller_id',
    'rsl_reseller.name',
    'rsl_reseller.phone_number',
    'rsl_reseller.email',
    'rsl_reseller.national_id',
    'rsl_reseller.npwp',
    'rsl_commission_withdrawal.action_by',
    'rsl_commission_withdrawal.action_date',
    'rsl_commission_withdrawal.action_notes',
    'rsl_commission_withdrawal.amount',
    'rsl_commission_withdrawal.tax_amount',
    'rsl_commission_withdrawal.transfer_fee',
    'rsl_commission_withdrawal.total',
    'rsl_commission_withdrawal.tax_metadata',
    'rsl_commission_withdrawal.payout_amount',
    'rsl_commission_withdrawal.status',
    'rsl_commission_withdrawal.bank_name',
    'rsl_commission_withdrawal.account_no',
    'rsl_commission_withdrawal.account_name',
    'rsl_reseller.is_active as reseller_status')
    ->when(!empty($search), function ($query) use ($search) {
        $query->where(function ($subQuery) use ($search) {
            $subQuery->where('rsl_reseller.name', 'LIKE', '%' . $search . '%')
                      ->orWhere('rsl_commission_withdrawal.request_id', 'LIKE', '%' . $search . '%');
        });
      })
      ->when($status !== null, function ($query) use ($status) {
          $query->where('rsl_commission_withdrawal.status', $status);
      })
      ->when($start !== null && $to !== null, function ($query) use ($start, $to) {
        $query->whereDate('rsl_commission_withdrawal.request_date', '>=', $start)
        ->whereDate('rsl_commission_withdrawal.request_date', '<=', $to);
    })
    ->orderBy('request_date','desc')
    ->get();

    #export CSV
    if ($isDownload) {
        $datas = ListWithdrawalsDownloadResource::collection($withdrawals);
        try {
            $dataJson = json_encode($datas);
            $dataArray = json_decode($dataJson, true);
        }
        catch (Exception $e) {
            return $this->sendError($e->getMessage(), 404);
        }

        $tempFile = tempnam(sys_get_temp_dir(), 'csv');
        $handle = fopen($tempFile, 'w');
        
        $filename = 'output.csv';

        $currentTaxSetting = Arr::flatten(array_map(fn($i) => ["L$i Tax Withdraw Base", "L$i Tax Percentage", "L$i Tax Amount"] ,collect(json_decode(TaxMatrix::getCurrentMetadata()->metadata)->ruleset)->sortBy('sequence')->pluck('sequence')->toArray()));
        $col = [
        'Withdraw ID', 
        'Reseller ID', 
        'Reseller Name',
        'KTP',
        'NPWP',
        'Phone Number',
        'Email',
        'Request Withdraw Date',
        'Approved Date',
        'Action By',
        'Request Withdraw Amount',
        ...$currentTaxSetting,
        'Transfer Fee',
        'Total Amount',
        'Payout Amount',
        'Withdraw Status',
        'Bank Name',
        'Account Number',
        'Account Name',
        'Rejection Reason'
        ];

        fputcsv($handle, $col,';');
        foreach ($dataArray as $row) {
            fputcsv($handle, $row,';');
        }

        fclose($handle);
        return response()->download($tempFile, $filename)->deleteFileAfterSend(true);
    }
    
    $withdrawals = $withdrawals->paginate($perPage);

    return $this->pagedResponse(ListWithdrawalsResource::collection($withdrawals), $withdrawals->currentPage(), $perPage);

  }

  public function withdrawalDetailInternal(WithdrawalDetailInternalRequest $withdrawalDetailInternalRequest)
  {
    $request = $withdrawalDetailInternalRequest->validated();
    $id = $request['id'];

    $withdrawal = CommissionWithdrawal::where('request_id',$id)
    ->first();

    if(!$withdrawal || $withdrawal->reseller_id == null){
        return $this->sendError('data not found', 404);
    }

    return $this->sendSuccess('get data withdrawal detail success',WithdrawalDetailInternalResource::make($withdrawal));

  }

//todo CARERS-81
  public function verifyWithdrawalInternal(VerifyWithdrawalRequest $verifyWithdrawalRequest)
  {
    $request = $verifyWithdrawalRequest->validated();
    $type = $request['type'];
    $requestId = $request['request_id'];
    $password = $request['password'];
    $actionNotes = $request['action_notes'];

    if($type == 'approved'){
        $user = User::where('reference_id',auth()->user()->reference_id)->first();
        $hashedPassword = $user->password;
        if (!Hash::check($password, $hashedPassword)) {
            return $this->sendError('Password tidak sesuai', 400);
        }
    }


    $withdrawal = ResellerCommissionWithdrawal::where('request_id',$requestId)
    ->first();
    
    if(!$withdrawal || $withdrawal->status == ResellerCommissionWithdrawal::APPROVED){
       return $this->sendError('data not found', 404);
    }
    $resellerData = Reseller::with('bankAccounts')->where('id', $withdrawal->reseller_id)->first();
    foreach($resellerData->bankAccounts as $account) {
        $bankAccount = $account->account_no;
        $accountName = $account->account_name;
        $bankName = MasterBank::where('id',$account->bank_id)->first()->bank_name;
    }
    $trfmethod = $withdrawal->transfer_method;
    //switch transfer method
    if($bankName == 'BANK DANAMON'){
        $trfmethod = CommissionWithdrawal::OVERBOOKING;
    }



    $socialMedia = new GetSocialsRepo();

    if($type == 'approved'){
        try{
            ////hit danamon
            if($withdrawal->total < 10000 && $trfmethod == CommissionWithdrawal::RTOL){
                return $this->sendError('Penarikan untuk metode Transfer Online minimal Rp.10.000', 400);
        
            }
            //va balance inquiry danamon (mocked)
            $rqBalanceInquiry = $verifyWithdrawalRequest->transformBalanceInquiry($withdrawal, $resellerData->reseller_id);
            $hitInquiry = $this->dn_helper->vaInquiry($rqBalanceInquiry);
            $jsonContent = $hitInquiry->getContent();
            $data = json_decode($jsonContent, true);
            if($data['message'] == 'success'){
                $dataArray = $data['data'];
                $vaBalance = $dataArray['AvailableBalance'];
                if($vaBalance - $withdrawal->amount < MasterParameter::where('group_key','RESELLER_COMMISSION')->where('key','COMMISSION_DEPOSIT')->first()->value){
                    return $this->sendError('Withdraw amount exceed deposit amount', 400);
                }
            } else {
                return $this->sendError('Terjadi Kendala pada proses, Hubungi Admin - ID Laporan : ' . $data['data']['log_id']??'-' , 400);
            }
            if($trfmethod == CommissionWithdrawal::SKN && !Carbon::now()->between(Carbon::createFromTimeString('09:00'), Carbon::createFromTimeString('13:00'))){
                return $this->sendError('Waktu Withdraw SKN Melebihi Jam Cutoff (09:00 ~ 13:00)' , 400);
            }
            // end va balance inquiry danamon danamon (mocked)
            if(env('DANAMON_TAX_TRANSFER', '1') == '1'){
                $hit = null;
                $hitTax = $this->dn_helper->transferOverbooking($verifyWithdrawalRequest->transformOverbookingTax($withdrawal, $resellerData->reseller_id));
                $jsonContentTax = $hitTax->getContent();
                $dataTax = json_decode($jsonContentTax, true);
                if($dataTax['message'] != 'success'){
                    return $this->sendError('Terjadi Kendala pada proses, Hubungi Admin - ID Laporan : ' . $dataTax['data']['log_id']??'-', 400);
                }    
            }
           

          

            switch($trfmethod){
                case CommissionWithdrawal::OVERBOOKING:
                    $hit = $this->dn_helper->transferOverbooking($verifyWithdrawalRequest->transformOverbooking($withdrawal, $resellerData->reseller_id));
                    break;
                case CommissionWithdrawal::RTGS:
                    $hit = $this->dn_helper->transferRTGS($verifyWithdrawalRequest->transformRTGS($withdrawal, $resellerData->reseller_id));
                    break;
                case CommissionWithdrawal::SKN:
                    $hit = $this->dn_helper->transferSKN($verifyWithdrawalRequest->transformSKN($withdrawal, $resellerData->reseller_id));
                    break;
                case CommissionWithdrawal::RTOL:
                    $hit = $this->dn_helper->transferOnline($verifyWithdrawalRequest->transformRTOL($withdrawal, $resellerData->reseller_id));
                    break;
                default:
                return $this->sendError('trf method unknown',404);
            }
            $jsonContentTf = $hit->getContent();
            $data = json_decode($jsonContentTf, true);
            if($data['message'] != 'success'){
                if(env('DANAMON_TAX_TRANSFER', '1') == '1'){
                    $this->dn_helper->transferOverbooking($verifyWithdrawalRequest->transformOverbookingTaxReversal($withdrawal, $resellerData->reseller_id));
                }
                return $this->sendError('Terjadi Kendala pada proses, Hubungi Admin - ID Laporan : ' . $data['data']['log_id']??'-', 400);
            }
            // END hit danamon

            //deduct saldo
            $commission = Commission::where('reseller_id', $withdrawal->reseller_id)->first();
            $updateBalance = $verifyWithdrawalRequest->updateBalance($commission, $withdrawal);
            $commission->fill($updateBalance);
            $commission->update();
                //change status to approve
            $transformApprove = $verifyWithdrawalRequest->transformApprove();
            $withdrawal->fill($transformApprove);
            $withdrawal->save();

                //create tx record
            $transaction = new ResellerTransaction;
            $txFill = $verifyWithdrawalRequest->createTxRecord($withdrawal);
            $transaction->fill($txFill);
            $transaction->save();

                //create ledger record
            $ledger = new CommissionLedgers;
            $ledgerFill = $verifyWithdrawalRequest->createLedgerRecord($withdrawal);
            $ledger->fill($ledgerFill);
            $ledger->save();
            

        } catch (Exception $e){
            return $this->sendException('Exception Occured', 500, 'Exception Occured', $e);
        }
        //email sender approved
        $param['data'] = [
            'reseller_id' => $resellerData->reseller_id,
            'name' => $resellerData->name,
            'phone_number' => $resellerData->phone_number,
            'email' => $resellerData->email,
            'address' => Reseller::resellerAddress($resellerData),
            'national_id' => $resellerData->national_id,
            'npwp' => $resellerData->npwp,
            // data komisi
            'ref_no' => $withdrawal->request_id,
            'request_date' => Carbon::parse($withdrawal->request_date)->format('d-m-Y'),
            'request_status' => $withdrawal->status,
            'payment_date' => Carbon::parse($withdrawal->payment_date)->format('d-m-Y'),
            'bank_name' => $bankName,
            'bank_account_no' => $bankAccount,
            'bank_account_name' => $accountName,
            'transfer_method' => $withdrawal->transfer_method,
            'amount' => $withdrawal->amount,
            'metadata' => $withdrawal->metadata,
            'tax_amount' => $withdrawal->tax_amount,
            'transfer_fee' => $withdrawal->transfer_fee,
            'total' => $withdrawal->total,
            'action_note' => $withdrawal->action_notes,
            //GET SOCIAL MEDIA ACCOUNTS
            'facebook' => $socialMedia->getSocialMediaParameters('FACEBOOK'),
            'twitter' => $socialMedia->getSocialMediaParameters('TWITTER'),
            'instagram' => $socialMedia->getSocialMediaParameters('INSTAGRAM'),
            'support' => $socialMedia->getSocialMediaParameters('SUPPORT'),
            'tiktok' => $socialMedia->getSocialMediaParameters('TIKTOK'),
            'linkedin' => $socialMedia->getSocialMediaParameters('LINKEDIN'),
        ];
        MailSender::dispatch($resellerData->email, json_encode($param), 'mail_commission_approve');
        $notificationMesssage = 'Pengajuan penarikan komisi Anda dengan nomor ' .$requestId. ' telah disetujui. Klik di sini untuk melihat lebih detail!';
        $this->notifStore($resellerData->reseller_id, 'Pengajuan Komisi Disetujui', 'reseller-commission-withdrawal', $notificationMesssage, $withdrawal->request_id, UserNotification::RESELLER_COMMISSION);
        //end email sender approved
        return $this->sendSuccess('successfully approved');

    } else if($type == 'rejected'){
        $transformReject = $verifyWithdrawalRequest->transformReject($actionNotes);
        $withdrawal->fill($transformReject)->save();   
        if ($withdrawal->save()) {
            //email sender rejected
            $param['data'] = [
                'reseller_id' => $resellerData->reseller_id,
                'name' => $resellerData->name,
                'phone_number' => $resellerData->phone_number,
                'email' => $resellerData->email,
                'address' => Reseller::resellerAddress($resellerData),
                'national_id' => $resellerData->national_id,
                'npwp' => $resellerData->npwp,
                'metadata' => $withdrawal->metadata,
                // data komisi
                'ref_no' => $withdrawal->request_id,
                'request_date' => Carbon::parse($withdrawal->request_date)->format('d-m-Y'),
                'request_status' => $withdrawal->status,
                'payment_date' => Carbon::parse($withdrawal->payment_date)->format('d-m-Y'),
                'bank_name' => $bankName,
                'bank_account_no' => $bankAccount,
                'bank_account_name' => $accountName,
                'transfer_method' => $withdrawal->transfer_method,
                'amount' => $withdrawal->amount,
                'tax_amount' => $withdrawal->tax_amount,
                'transfer_fee' => $withdrawal->transfer_fee,
                'total' => $withdrawal->total,
                'action_note' => $withdrawal->action_notes,
                //GET SOCIAL MEDIA ACCOUNTS
                'facebook' => $socialMedia->getSocialMediaParameters('FACEBOOK'),
                'twitter' => $socialMedia->getSocialMediaParameters('TWITTER'),
                'instagram' => $socialMedia->getSocialMediaParameters('INSTAGRAM'),
                'support' => $socialMedia->getSocialMediaParameters('SUPPORT'),
                'tiktok' => $socialMedia->getSocialMediaParameters('TIKTOK'),
                'linkedin' => $socialMedia->getSocialMediaParameters('LINKEDIN'),
            ];
            MailSender::dispatch($resellerData->email, json_encode($param), 'mail_commission_reject');
            //end email sender rejected
            $notificationMesssage = 'Pengajuan penarikan komisi Anda dengan nomor ' .$requestId. ' telah ditolak. Klik di sini untuk melihat lebih detail!';
            $this->notifStore($resellerData->reseller_id, 'Pengajuan Komisi Ditolak', 'reseller-commission-withdrawal', $notificationMesssage, $withdrawal->request_id, UserNotification::RESELLER_COMMISSION);
            return $this->sendSuccess('rejected Succesfully');
          } else {
            return $this->sendError("update failed", 400);
          }
    } else {
        return $this->sendError('type not recognized');
    }

  }

  public function internalAdjustment(InternalAdjustmentRequest $internalAdjustmentRequest, $id)
  {
    $request = $internalAdjustmentRequest->validated();
    $adjustmentType = $request['adjustment_type'];
    $commissionType = $request['commission_type'];
    $orderNo = $request['order_no'];
    $nominal = $request['nominal'];
    $commissionInput = $request['commission'];

    if($adjustmentType == 'Adjustment' || $adjustmentType == 'Refund'){
        $nominal = - $nominal;
        $commissionInput = - $commissionInput;
    }

    $reason = $request['reason'];
    $password = $request['password'];
    $resellerId = $request['reseller_id'];

    //cek password user
    $user = User::where('reference_id',auth()->user()->reference_id)->first();
    $hashedPassword = $user->password;
    if (!Hash::check($password, $hashedPassword)) {
        return $this->sendError('Password tidak sesuai', 400);
    }
    //cek saldo komisi
    $commission = Commission::where('reseller_id', $resellerId)->first();
    if(!$commission){
        Commission::create([
            'reseller_id' => $resellerId,
            'potential_amount' => 0,
            'commission_amount' => 0
        ]);
        return $this->sendError('commission data initalize, try again', 404);
    }
    // DB::beginTransaction();
    try{
        switch($adjustmentType){
            case 'Adjustment':
            case 'Bonus':
                if($commissionType == ResellerTransaction::TX_TYPE_COMMISSION){
                    // call API DANAMON OVERBOOKING
                    $payload = $internalAdjustmentRequest->transformOverbooking($resellerId, $nominal, $adjustmentType, $id, $reason);
                    $hit = $adjustmentType == 'Adjustment' ? $this->dn_helper->transferOverbooking($payload) : $this->dn_helper->topupTransfer($payload);
                    $jsonContent = $hit->getContent();
                    $data = json_decode($jsonContent, true);
                    if($data['message'] == 'failed'){
                        return $this->sendError('Terjadi Kendala pada proses, Hubungi Admin - ID Laporan : ' . $data['data']['log_id']??'-', 400);
                    }
                }
                //record tx
                $tx = new ResellerTransaction;
                $tx->fill($internalAdjustmentRequest->createTxRecord($orderNo, $commissionType,$nominal, $reason, $adjustmentType));
                $tx->save();
                //record ledger
                $newTx = ResellerTransaction::where('reference_id', $orderNo)->where('order_status','Bonus')->orWhere('order_status','Adjustment') 
                ->orderBy('created_date','desc')->first();
                $ledger = new CommissionLedgers;
                $ledger->fill($internalAdjustmentRequest->createLedgerRecord($commission, $newTx, $commissionType, $nominal,$id, $adjustmentType, $reason));
                $ledger->save();
                //deduct saldo
                $commission->fill($internalAdjustmentRequest->updateBalance($commissionType, $commission, $nominal, $adjustmentType));
                $commission->update();
                
                //check commission below 0
                $check = ResellerCommission::where('reseller_id', $resellerId)->first();
                if($commissionType == ResellerTransaction::TX_TYPE_COMMISSION){
                    if($check->commission_amount < 0){
                        return $this->sendError('commission below zero',400);
                    }
                } else {
                    if($check->potential_amount < 0){
                        return $this->sendError('potential commission below zero',400);
                    }
                }

                // dd('sampe');
                // if($check->commission_amount <= 0|| $check->potential_amount <= 0){
                //     return $this->sendError('commission below zero',400);
                // }
                //sendnotif
                if($adjustmentType == 'Adjustment'){
                    $msg = 'Telah terjadi adjustment komisi Anda oleh admin kami pada tanggal '.Carbon::now()->format('d-m-Y H:i:s').'. Klik di sini untuk melihat lebih detail!';
                    $this->notifStore($id, 'ADJUSTMENT KOMISI', null, $msg, null, UserNotification::RESELLER_COMMISSION);
                } else {
                    $msg = 'Anda menerima bonus komisi dari admin kami pada tanggal '.Carbon::now()->format('d-m-Y H:i:s').'. Klik di sini untuk melihat lebih detail!';
                    $this->notifStore($id, 'BONUS KOMISI', null, $msg, null, UserNotification::RESELLER_COMMISSION); 
                }
                break;
            case 'Refund':
                //record tx
                $orderHeader = OrderReseller::where('order_no', $orderNo)->first();
                if(!$orderHeader){
                    return $this->sendError('order not found',404);
                }
                switch($orderHeader->order_status){
                    case OrderReseller::ORDER_RESELLER_PENDING:
                    case OrderReseller::ORDER_RESELLER_BARU:
                    case OrderReseller::ORDER_RESELLER_DIKEMAS:
                    case OrderReseller::ORDER_RESELLER_DIKIRIM:
                    case OrderReseller::ORDER_RESELLER_DITERIMA:
                    case OrderReseller::ORDER_RESELLER_DIPROSES:
                        $commissionType = ResellerTransaction::TX_TYPE_POTENTIAL_COMMISSION;
                        break;
                    case OrderReseller::ORDER_RESELLER_SELESAI;
                        $commissionType = ResellerTransaction::TX_TYPE_COMMISSION;
                        break;
                }
                if($commissionType == ResellerTransaction::TX_TYPE_COMMISSION){
                    // call API DANAMON OVERBOOKING
                    $hit = $this->dn_helper->transferOverbooking($internalAdjustmentRequest->transformOverbooking($resellerId, $nominal, $adjustmentType, $id, $reason));
                    $jsonContent = $hit->getContent();
                    $data = json_decode($jsonContent, true);
                    if($data['message'] == 'failed'){
                        return $this->sendError('Terjadi Kendala pada proses, Hubungi Admin - ID Laporan : ' . $data['data']['log_id']??'-' , 400);
                    }
                }
                $existingTx = ResellerTransaction::where('reference_id',$orderNo)->whereIn('order_status',[OrderReseller::ORDER_RESELLER_BARU, 
                OrderReseller::ORDER_RESELLER_SELESAI])->get();
                foreach ($existingTx as $tx){
                    $newTxRefund = new ResellerTransaction;
                    $newTxRefund->amount = -$tx->amount;
                    $newTxRefund->order_status = 'Refund';
                    $newTxRefund->reference_name = $tx->reference_name;
                    $newTxRefund->reference_id = $tx->reference_id;
                    $newTxRefund->remarks = $reason;
                    $newTxRefund->type = $tx->type;
                    $newTxRefund->status = $tx->status;
                    $newTxRefund->discount_type = $tx->discount_type;
                    $newTxRefund->discount_id = $tx->discount_id;
                    $newTxRefund->request_no = strtoupper('RF'.substr(uniqid(), -8));
                    $newTxRefund->save();
                }
                //record ledger
                $newTx = ResellerTransaction::where('reference_id', $orderNo)->where('order_status','Refund')->whereIn('type',[ResellerTransaction::TX_TYPE_POTENTIAL_COMMISSION,
                 ResellerTransaction::TX_TYPE_COMMISSION]) 
                ->orderBy('created_date','desc')->first();
                $ledger = new CommissionLedgers;
                $ledger->fill($internalAdjustmentRequest->createLedgerRecord($commission, $newTx, $commissionType, $commissionInput, $id, $adjustmentType, $reason));
                $ledger->save();
                //deduct saldo
                $commission->fill($internalAdjustmentRequest->updateBalance($commissionType, $commission, $commissionInput, $adjustmentType));
                $commission->update();

                //set order to refund
                $order = OrderReseller::where('order_no', $orderNo)->first();
                $order->update(['order_status'=> OrderReseller::ORDER_RESELLER_REFUND]);

                //return quota voucher
                $orderPromo = OrderReseller::where('order_no',$orderNo)->with('items')->first();
                $ids = $orderPromo->items->pluck('id')->toArray();
                $data = ResellerOrderPromotion::whereIn('reference_id',$ids)->whereIn('discount_type',['coupon','voucher'])->get();
                foreach($data as $d){
                    if (strtolower($d->discount_type) == 'voucher') {
                        $voucher = $d->voucher;
                        if (strtolower($voucher->discount_type) == 'percentage') {
                            $voucher->remaining_amount = $voucher->amount;
                            $voucher->used_amount = 0;
                            $voucher->save();
                        }
                        if (strtolower($voucher->discount_type) == 'absolute') {
                            $voucher->remaining_amount = $voucher->remaining_amount + $d->amount;
                            $voucher->used_amount = $voucher->used_amount - $d->amount;
                            $voucher->save();
                        }
                    }
                    if (strtolower($d->discount_type) == 'coupon') {
                        $coupon = $d->coupon;
                        $coupon->used_count -= 1;
                        $coupon->save();
                    }
                }
                //check commission below 0
                $check = ResellerCommission::where('reseller_id', $resellerId)->first();
                if($check->commission_amount <= 0|| $check->potential_amount <= 0){
                    return $this->sendError('commission below zero',400);
                }
                break;
            default:
                return $this->sendError('unknown type', 400);
        }
    } catch (Exception $e) {    
        // DB::rollBack();
        return $this->sendException('Exception Occured', 500, 'Exception Occured', $e);
    }
    // DB::commit();
    return $this->sendSuccess('commission adjusted successfully');
            
  }

  public function ongoingOrdersDashboardInternal()
  {
    $ongoingOrders = OrderReseller::ongoing()->orderBy('order_date','desc')->get()->take(5);
    return $this->sendSuccess('get ongoing orders data success',OngoingOrdersResource::collection($ongoingOrders));
  }

  public function resellerOrderListInternal(ResellerOrderListInternalRequest $resellerOrderListInternalRequest)
  {
    $request = $resellerOrderListInternalRequest->validated();
    $resellerId = $request['reseller_id'];
    $search = $request['search'];
    $type = $request['type'];
    $orders = OrderReseller ::where('reseller_id', $resellerId)->ongoing()
    ->when($search !== null, function ($query) use ($search) {
        $query->where('order_no','LIKE', '%'.$search.'%');
    })
    ->when($type == 'potential', function ($query){
        $query->eligible();
    })
    ->when($type == 'commission', function ($query) {
        $query->where('order_status',OrderReseller::ORDER_RESELLER_SELESAI);
    })
    ->when($type == 'refund', function ($query) {
        $query->whereIn('order_status',[OrderReseller::ORDER_RESELLER_SELESAI, OrderReseller::ORDER_RESELLER_DITERIMA]);
    })
    ->get();

    return $this->sendSuccess('get reseller ordernumber data success',ResellerOrderListInternalResource::collection($orders));
  }

  public function ordersDashboardInternal(ListOrdersInternalRequest $listOrdersInternalRequest)
  {
    $request = $listOrdersInternalRequest->validated();
    $search = $request['search'];
    $perPage = $request['per_page'];
    $status = $request['order_status'];
    $start = $request['date_from'];
    $to = $request['date_to'];
    $isDownload = $request['is_download'];
 
    $orders = OrderReseller::with('reseller', 'order_shipment', 'items')
    ->when(!empty($search), function ($query) use ($search) {
        $query->where(function ($subQuery) use ($search) {
            $subQuery->where('order_no', 'LIKE', '%' . $search . '%')
            ->orWhere('customer_name', 'LIKE', '%' . $search . '%')
                ->orWhereHas('reseller', function ($resellerQuery) use ($search) {
                    $resellerQuery->where('name', 'LIKE', '%' . $search . '%');
                });
        });
    })
    ->when($status !== null, function ($query) use ($status) {
        $query->whereIn('order_status', $status);
    })
    ->when($start !== null && $to !== null, function ($query) use ($start, $to) {
        $query->whereDate('order_date', '>=', $start)
        ->whereDate('order_date', '<=', $to);
    })
    ->orderBy('order_date', 'desc');

    // export csv
    if ($isDownload) {
        $orders = $orders->get();
        $datas = ListOrdersInternalDownloadResource::collection($orders);

        $dataJson = json_encode($datas);
        $dataArray = json_decode($dataJson, true);
        $tempFile = tempnam(sys_get_temp_dir(), 'csv');
        $handle = fopen($tempFile, 'w');
        
        $filename = 'output.csv';

        $col = [
        'Transaction Date', 
        'Order ID', 
        'Payment reference ID',
        'Order reference ID',
        'Sold By', 
        'Phone No', 
        'Reseller ID', 
        'Purchased By',
        'Recipient',
        'Recipient Phone',
        'Recipient Email',
        'Address',
        'City',
        'Zip Code',
        'Sub Total',
        'Shipping Fee',
        'Handling Charge',
        'Discount',
        'Total',
        'Payment Amount',
        'Commision Amount',
        'Promotion Name',
        'Voucher Code',
        'Coupon Code',
        'Payment Method',
        'Courier',
        'AWB No',
        'Delivery Number',
        'Order Status',
        ];

        fputcsv($handle, $col,';');
        foreach ($dataArray as $row) {
            try {

                fputcsv($handle, $row,';');
            } catch (\Exception $e) {
                return ($e->getMessage());
            }
        }

        fclose($handle);
        return response()->download($tempFile, $filename)->deleteFileAfterSend(true);
    }

    $orders = $orders->paginate($perPage);

    return $this->pagedResponse(ListOrdersInternalResource::collection($orders), $orders->currentPage(), $perPage);

  }

  public function topSellingItems(TopSellingItemsRequest $id)
  {
    $validatedData = $id->transform();
    $resellerId = $validatedData['id'];
    $result = $this->resellerService->getTopSellingItems($resellerId);
    return $this->sendSuccess('get top resellers data success',TopSellingResource::collection($result));
  }
  public function topSellingItemsAll()
  {
    $result = $this->resellerService->getTopSellingItemsAll();
    return $this->sendSuccess('get top resellers data success',TopSellingResource::collection($result));
  }

  public function ResellerInternalDashboard()
  {
    $result = $this->resellerService->getResellerDashboardData();
    return $this->sendSuccess('get data applicants success',new ResellerInternalDashboardResource($result));
  }

  public function ResellerInternalSummaryDashboard(ResellerSummaryDashboardRequest $resellerSummaryDashboardRequest)
  {
    $request = $resellerSummaryDashboardRequest->validated();
    $start = $request['date_from'];
    $to = $request['date_to'];
    $result = $this->resellerService->getResellerSummaryDashboardData($start, $to);
    return $this->sendSuccess('get data summary success',new ResellerSummaryDashboardResource($result));
  }

  public function ResellerInternalCommissionsDashboard(ResellerSummaryDashboardRequest $resellerSummaryDashboardRequest)
  {
    $request = $resellerSummaryDashboardRequest->validated();
    $start = $request['date_from'];
    $to = $request['date_to'];
    $result = $this->resellerService->getResellerCommissionsDashboardData($start, $to);
    return $this->sendSuccess('get data summary success',new ResellerCommissionDashboardResource($result));
  }

    public function updateProfile(ResellerProfileRequest $request)
    {
        $request->validated();
        $transformedRequest = $request->transform();
        $reseller = Reseller::Where('reseller_id', "=", auth()->user()->reference_id)->first();
        
        try {
            switch($transformedRequest["type"]) {
                case "profile":
                    $profile = [
                        'name' => $transformedRequest['name'],
                        'gender' => $transformedRequest['gender'],
                        'date_of_birth' => $transformedRequest['date_of_birth']
                    ];

                    Reseller::Where('reseller_id', '=', auth()->user()->reference_id)->Update([
                        'name' => $transformedRequest['name'],
                        'gender' => $transformedRequest['gender'],
                        'date_of_birth' => $transformedRequest['date_of_birth']
                    ]);
                    
                    User::Where('reference_id', '=', auth()->user()->reference_id)->Update([
                        'name' => $transformedRequest['name']
                    ]);
                    break;
                
                case "password":
                    $checkUser = User::where('reference_id', '=', auth()->user()->reference_id)->first();
                    
                    if (!Hash::check($transformedRequest["old_password"], $checkUser->password)) {
                       return $this->sendError("Invalid Credential", 500);
                    }
                    
                    User::where('reference_id', '=', auth()->user()->reference_id)->update([
                        'password' => $transformedRequest['password']
                    ]);
                    break;

                case "phone":
                    return $this->sendError('User Cannot Update Phone Number',422);
                    
                    // disable berdasarkan pentest bug 2
                    // Reseller::where('reseller_id', '=', auth()->user()->reference_id)->update([
                    //     'phone_number' => $transformedRequest['phone_number']
                    // ]);
                    break;

                case "address":
                    Reseller::where('reseller_id', '=', auth()->user()->reference_id)
                    ->update([
                        'address' => $transformedRequest['address'],
                        'province_code' => $transformedRequest['province_code'],
                        'city_code' => $transformedRequest['city_code'],
                        'district_code' => $transformedRequest['district_code'],
                        'zip_code' => $transformedRequest['zip_code']
                    ]);
                    break;

                case "bank":
                    ResellerBank::updateOrCreate([
                        "reseller_id" => $reseller->id
                    ],
                    [
                        'bank_id' => $transformedRequest['bank_id'],
                        'account_no' => $transformedRequest['account_no'],
                        'account_name' => $transformedRequest['account_name']
                    ]);
                    break;
            }
        } catch (\Exception $e) {
            return $this->sendError("Sorry system can't update your profile, ".$e->getMessage(), 500);
        }

        return $this->sendSuccess("Update ".$transformedRequest["type"]." Successful", $transformedRequest);
    }

    public function getProfile()
    {   
        $user = [];

        $getProfile = Reseller::Where('reseller_id', "=", auth()->user()->reference_id)->first();

        if (!$getProfile) {
            return $this->sendError("Profile data not found", 200);
        }

        $getStore =  ResellerLink::Select("identifier")->Where('reseller_id', '=', $getProfile->id)->Where('type', '=', 'STORE')->first();
        $user["reseller_store_name"] = $getStore ? $getStore->identifier : null;
        $user["name"] = $getProfile->name;
        $user["email"] = $getProfile->email;
        $user["phone"] = $getProfile->phone_number;
        $user["gender"] = $getProfile->gender;
        $user["date_of_birth"] = $getProfile->date_of_birth;
        $user["nationalId"] = $getProfile->national_id;
        $user["npwp"] = $getProfile->npwp;
        $user["address"] = $getProfile->address;
        $user["province_code"] = $getProfile->province_code;
        $user["city_code"] = $getProfile->city_code;
        $user["district_code"] = $getProfile->district_code;
        $user["zip_code"] = $getProfile->zip_code;
        $user["is_freeze"] = $getProfile->is_active == 9 ? true : false;

        return $this->sendSuccess("Get Profile Success", $user);
    }
    
    public function resellerProfile($id){
      $reseller = Reseller::with(['orders' => function ($query) {
        $query->whereIn('order_status', [
          OrderReseller::ORDER_RESELLER_BARU,
          OrderReseller::ORDER_RESELLER_DIPROSES,
          OrderReseller::ORDER_RESELLER_DIKEMAS,
          OrderReseller::ORDER_RESELLER_DIKIRIM,
          OrderReseller::ORDER_RESELLER_DITERIMA,
          OrderReseller::ORDER_RESELLER_SELESAI]);
      }])->with('link')->where('reseller_id',$id)->first();
      if(!$reseller){
        return $this->sendError('reseller not found', 404);
      }
      return $this->sendSuccess('get reseller profile success',ResellerProfileResource::make($reseller));
    }

    public function getPerformaDashboard()
    {   
        $currentMonth = Carbon::now()->month;
        $lastMonth = Carbon::now()->subMonth();
        $currentYear = Carbon::now()->year;

        $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();
        $getLink = ResellerLink::Select("id")->Where("reseller_id", "=", $reseller->id)->get()->toArray();
        $links = [];

        foreach($getLink as $eachId) {
            $links[] = $eachId["id"];
        }
        #Clicks
        $clickLastMonth = ResellerLinkHistory::linkClickCount($links, $lastMonth, $currentYear);
        $clickCurrentMonth = ResellerLinkHistory::linkClickCount($links, $currentMonth, $currentYear);

        $clickDiff = $this->getAverageIndexTransaction($clickCurrentMonth, $clickLastMonth);
        
        #Transactions
        $transactionLastMonth = OrderReseller::transactionCount($reseller->id, $lastMonth, $currentYear);
        $transactionCurrentMonth = OrderReseller::transactionCount($reseller->id, $currentMonth, $currentYear);

        $transactionDiff = $this->getAverageIndexTransaction($transactionCurrentMonth, $transactionLastMonth);

        #Sales
        $salesLastMonth = OrderReseller::salesSumTotalAmount($reseller->id, $lastMonth, $currentYear);
        $salesCurrentMonth = OrderReseller::salesSumTotalAmount($reseller->id, $currentMonth, $currentYear);
        
        $salesDiff =  $this->getAverageIndexTransaction($salesCurrentMonth, $salesLastMonth);

        #Commissions
        $commissionCurrentMonth = OrderReseller::commissionSumAmount($reseller->id, $currentMonth, $currentYear);
        $commissionLastMonth = OrderReseller::commissionSumAmount($reseller->id, $lastMonth, $currentYear);

        $commissionDiff = $this->getAverageIndexTransaction($commissionCurrentMonth, $commissionLastMonth);

        $data = new Collection([
            'clickCurrent' => $clickCurrentMonth,
            'clickPrev' => $clickLastMonth,
            'clickIncrease' => $clickDiff["increase"],
            'clickPercentage' => $clickDiff["percentage"],
            'transactionCurrent' => $transactionCurrentMonth,
            'transactionPrev' => $transactionLastMonth,
            'transactionIncrease' => $transactionDiff["increase"],
            'transactionPercentage' => $transactionDiff["percentage"],
            'salesCurrent' => $salesCurrentMonth,
            'salesPrev' => $salesLastMonth,
            'salesIncrease' => $salesDiff["increase"],
            'salesPercentage' => $salesDiff["percentage"],
            'commissionCurrent' => $commissionCurrentMonth,
            'commissionPrev' => $commissionLastMonth,
            'commissionIncrease' => $commissionDiff["increase"],
            'commissionPercentage' => $commissionDiff["percentage"]
        ]);

        return $this->sendSuccess("Dashboard Performance", new ResellerDbPerfomanceResource($data));
    }

    public function getLatestTransactions()
    {
        $response = [];
        $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();

        $latestTransaction = OrderReseller::
        Select('id', 'order_no', 'total_amount', 'commission_amount',
        'order_status', 'created_date'
        )
        ->where('rsl_order_headers.reseller_id', $reseller->id)
        ->where('rsl_order_headers.order_status', OrderReseller::ORDER_RESELLER_SELESAI)
        ->orderBy('rsl_order_headers.completed_date', 'DESC')
        ->limit(5)
        ->get()
        ->toArray();

        if (!$latestTransaction) {
            return $this->sendSuccess("5 Latest Transactions Not Found", []);
        }

        foreach ($latestTransaction as $transaction) {
            $details = OrderItemReseller::Select('rsl_order_details.product_name', 'article_id')
            ->where('order_header_id', $transaction['id'])
            ->get();

            $otherArticles = $details->count("id") > 1 ? $details->count("article_id") -1 : 0;
            $detail = $details->first();
            if($detail){
                $image = ImageVariant::Select('file_path')
                ->Where('article', $detail->article_id)
                ->Where('is_main_image', 1)->first();
    
                $response[] = [
                    "order_no" => $transaction["order_no"],
                    "total_amount" => $transaction["total_amount"],
                    "commission_amount" => $transaction["commission_amount"],
                    "order_status" => $transaction["order_status"],
                    "product_name" => $detail["product_name"],
                    "product_image" => $image ? env('S3_STREAM_URL') . $image->file_path : 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
                    "other_articles" => $otherArticles,
                    "order_date" => Carbon::parse($transaction["created_date"])->timestamp,
                ];
            }
       
        }

        return $this->sendSuccess("Get 5 Latest Transactions", $response);
    }

    public function getMonthlyPerformance()
    {
        $currentMonth = Carbon::now()->month;
        $lastMonth = Carbon::now()->subMonth();
        $currentYear = Carbon::now()->year;
        $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();

        #Sales
        $salesLastMonth = OrderReseller::salesMonthlyPerformance($reseller->id, $lastMonth, $currentYear);
        $sumSalesLastMonth = $salesLastMonth->sum("total_amount");
        
        $salesCurrentMonth = OrderReseller::salesMonthlyPerformance($reseller->id, $currentMonth, $currentYear);
        $sumSalesCurrentMonth = $salesCurrentMonth->sum("total_amount");
        
        $salesDiff = $this->getAverageIndexTransaction($sumSalesCurrentMonth, $sumSalesLastMonth);

        #Products Sold
        $soldLastMonth = OrderReseller::productSold($reseller->id, $lastMonth, $currentYear);
        $sumSoldLastMonth = $soldLastMonth->sum("qty");
        
        $soldCurrentMonth = OrderReseller::productSold($reseller->id, $currentMonth, $currentYear);
        $sumSoldCurrentMonth = $soldCurrentMonth->sum("qty");
        
        $soldDiff = $this->getAverageIndexTransaction($sumSoldCurrentMonth, $sumSoldLastMonth);

        #Commission
        $sumCommissionCurrentMonth = OrderReseller::commissionSumAmount($reseller->id, $currentMonth, $currentYear);
        $sumCommissionLastMonth = OrderReseller::commissionSumAmount($reseller->id, $lastMonth, $currentYear);

        $commissionDiff = $this->getAverageIndexTransaction($sumCommissionCurrentMonth, $sumCommissionLastMonth);

        #Average Transactions
        $countSalesLastMonth = $salesLastMonth->count("order_status");
        $countSalesCurrentMonth = $salesCurrentMonth->count("order_status");
        $averageSalesLastMonth = $sumSalesLastMonth / ($countSalesLastMonth != 0 ? $countSalesLastMonth : 1); 
        $averageSalesCurrentMonth = $sumSalesCurrentMonth / ($countSalesCurrentMonth != 0 ? $countSalesCurrentMonth : 1); 
        
        $averageSalesDiff = $this->getAverageIndexTransaction($averageSalesCurrentMonth, $averageSalesLastMonth);

        #Average Products Sold
        $countSoldLastMonth = $soldLastMonth->count("order_status");
        $countSoldCurrentMonth = $soldCurrentMonth->count("order_status");
        $averageSoldLastMonth = round($sumSoldLastMonth / ($countSoldLastMonth != 0 ? $countSoldLastMonth : 1), 1);
        $averageSoldCurrentMonth = round($sumSoldCurrentMonth / ($countSoldCurrentMonth != 0 ? $countSoldCurrentMonth : 1), 1);

        $averageSoldDiff = $this->getAverageIndexTransaction($averageSoldCurrentMonth, $averageSoldLastMonth);

        $data = new Collection([
            'salesCurrent' => $sumSalesCurrentMonth,
            'salesPrev' => $sumSalesLastMonth,
            'salesIncrease' => $salesDiff["increase"],
            'salesPercentage' => $salesDiff["percentage"],
            'soldCurrent' => $sumSoldCurrentMonth,
            'soldPrev' => $sumSoldLastMonth,
            'soldIncrease' => $soldDiff["increase"],
            'soldPercentage' => $soldDiff["percentage"],
            'avgSalesCurrent' => $averageSalesCurrentMonth,
            'avgSalesPrev' => $averageSalesLastMonth,
            'avgSalesIncrease' => $averageSalesDiff["increase"],
            'avgSalesPercentage' => $averageSalesDiff["percentage"],
            'avgSoldCurrent' => $averageSoldCurrentMonth,
            'avgSoldPrev' => $averageSoldLastMonth,
            'avgSoldIncrease' => $averageSoldDiff["increase"],
            'avgSoldPercentage' => $averageSoldDiff["percentage"],
            'commissionCurrent' => $sumCommissionCurrentMonth,
            'commissionPrev' => $sumCommissionLastMonth,
            'commissionIncrease' => $commissionDiff["increase"],
            'commissionPercentage' => $commissionDiff["percentage"]
        ]);
        
        return $this->sendSuccess("Dashboard Performance",  new ResellerMonthlyPerformanceResource($data));
    }

    public function getPopularProducts(Request $request)
    {
        $isYearly = $request->input("isYearly") ?? false;
        $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;

        $popularProducts = OrderReseller::
        Select(DB::raw("CONCAT(product_name, ',', product_variant,',', product_size) AS product_name, SUM(qty) AS qty, unit_price"))
        ->leftJoin('rsl_order_details', 'rsl_order_details.order_header_id', '=', 'rsl_order_headers.id');

        if ($isYearly === false) {
            $popularProducts = $popularProducts->whereMonth("rsl_order_headers.created_date", $currentMonth);
        } else {
            $popularProducts = $popularProducts->whereYear("rsl_order_headers.created_date", $currentYear);
        }
        $popularProducts = $popularProducts->whereIn('order_status', [
            OrderReseller::ORDER_RESELLER_BARU, 
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA,
            OrderReseller::ORDER_RESELLER_SELESAI
         ])
         ->Where("reseller_id", $reseller->id)
         ->groupBy('article_id')
         ->orderBy('qty', 'DESC')
         ->limit(5)
         ->get()
         ->toArray();

         return $this->sendSuccess("Get 5 Latest Popular Products", $popularProducts);
    }

    public function editReseller(EditResellerRequest $request, $id){

        $request->validated();
        $transformed = $request->transform($id);
        $reseller = Reseller::Where("reseller_id", $id)->first();
        if(!$reseller){
            return $this->sendError('reseller not found', 404);
        }
        $reseller->fill($transformed);   
            if($request->status == 'non-active'){
                //get va balance danamon
                $rqBalanceInquiry = $request->transformBalanceInquiry($reseller, $reseller->reseller_id);
                $hitInquiry = $this->dn_helper->vaInquiry($rqBalanceInquiry);
                $jsonContent = $hitInquiry->getContent();
                $data = json_decode($jsonContent, true);
                if($data['message'] == 'success'){
                    $dataArray = $data['data'];
                    $vaBalance = $dataArray['AvailableBalance'];
                    if($vaBalance > 0){
                        return $this->sendError("Maaf, Proses non aktif Reseller gagal. dikarenakan masih ada saldo sebesar Rp. $vaBalance pada VA Reseller terkait, Silakan kosongkan saldo komisi reseller melalui proses adjustment ke rekening operational Eiger.", 400);
                    }
                    $this->dn_helper->queueDispatch($this->MappingDeleteVA($reseller), 'delete_va_debit');
                }
                else{
                    return $this->sendError($data['data']['response']['ErrorMessage']['Indonesian']??"Update failed", 400);
                }

             
            }
            $reseller->update();

            return $this->sendSuccess('updated Succesfully');
        

    }

    public function editResellerStatus(EditResellerStatusRequest $request, $id){

        $request->validated();
        $transformed = $request->transform();
        $reseller = Reseller::Where("reseller_id", $id)->first();
        $user = User::where('reference_id',$id)->first();
        if(!$reseller){
            return $this->sendError('reseller not found', 404);
        }
        $reseller->fill($transformed);
        $user->fill($transformed);
        if($request->status == 'non-active'){
            //get va balance danamon
            $rqBalanceInquiry = $request->transformBalanceInquiry($reseller, $reseller->reseller_id);
            $hitInquiry = $this->dn_helper->vaInquiry($rqBalanceInquiry);
            $jsonContent = $hitInquiry->getContent();
            $data = json_decode($jsonContent, true);
            if($data['message'] == 'success'){
                $dataArray = $data['data'];
                $vaBalance = $dataArray['AvailableBalance'];
                if($vaBalance > 0){
                    return $this->sendError("Maaf, Proses non aktif Reseller gagal. dikarenakan masih ada saldo sebesar Rp. $vaBalance pada VA Reseller terkait, Silakan kosongkan saldo komisi reseller melalui proses adjustment ke rekening operational Eiger.", 400);
                }
                $this->dn_helper->queueDispatch($this->MappingDeleteVA($reseller), 'delete_va_debit');
            }
            else{
                return $this->sendError($data['data']['response']['ErrorMessage']['Indonesian']??"Update failed", 400);
            }

         
        }
        $reseller->update();
        $user->update();

    }

    public function downloadResellerData(ListResellersRequest $request){

        $validatedData = $request->validated();
        $search = $validatedData['search'];
        $status = $validatedData['reseller_status'];
        $resellers = Reseller::with(['orders' => function ($query) {
            $query->whereIn('order_status', [
              OrderReseller::ORDER_RESELLER_BARU,
              OrderReseller::ORDER_RESELLER_DIPROSES,
              OrderReseller::ORDER_RESELLER_DIKEMAS,
              OrderReseller::ORDER_RESELLER_DIKIRIM,
              OrderReseller::ORDER_RESELLER_DITERIMA,
              OrderReseller::ORDER_RESELLER_SELESAI]);
          }])
            ->with('link')->with('commissions')->with('withdrawals')
            ->when($search !== null, function ($query) use ($search) {
            $query->where(function ($subQuery) use ($search) {
                $subQuery->where('name', 'LIKE', '%' . $search . '%')
                          ->orWhere('phone_number', 'LIKE', '%' . $search . '%')
                          ->orWhere('reseller_id', 'LIKE', '%' . $search . '%');
            });
          })
            ->when($status !== null, function ($query) use ($status) {
              $query->where('is_active', $status);
        })->get();

        $datas = DownloadResellerResource::collection($resellers);

        $dataJson = json_encode($datas);
        $dataArray = json_decode($dataJson, true);

        $tempFile = tempnam(sys_get_temp_dir(), 'csv');
        $handle = fopen($tempFile, 'w');
        
        $filename = 'output.csv';

        $col = ['Reseller ID', 
        'Name', 
        'Phone Number', 
        'Email', 
        'Gender', 
        'Birthday',
        'Age', 
        'City',
        'Address',
        'Komisi Saat Ini', 
        'Activation Date', 
        'Last Transaction', 
        'Total Transaction',
        'Total Komisi', 
        'Total Potensi Komisi', 
        'Total Komisi yang pernah ditarik',
        'Registration Date',
        'Status Member',
        'Total Shares', 
        'Total Klik', 
        'Churn Risk'];

        fputcsv($handle, $col,';');
        foreach ($dataArray as $row) {
            fputcsv($handle, $row,';');
        }

        fclose($handle);
        return response()->download($tempFile, $filename)->deleteFileAfterSend(true);

    }

    public function getIndexTransaction()
    {
        Carbon::setWeekStartsAt(Carbon::SUNDAY);
        Carbon::setWeekEndsAt(Carbon::SATURDAY);
        $currentYear = Carbon::now()->year;
        $startCurrentWeek = Carbon::now()->startOfWeek()->toDateTimeString();
        $endCurrentWeek = Carbon::now()->endOfWeek()->toDateTimeString();

        $startLastWeek = Carbon::now()->startOfWeek()->subDays(7)->toDateTimeString();
        $endLastWeek = Carbon::now()->endOfWeek()->subDays(7)->toDateTimeString();

        $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();
        $resellerCommission = ResellerCommission::Where("reseller_id", "=", $reseller->id)->first();

        if (!$resellerCommission) {
            return $this->sendError('reseller not found', 200);
        }

        #New Order
        $newOrderCurrentWeek = OrderReseller::newOrder($reseller->id, $startCurrentWeek, $endCurrentWeek);
        $newOrderLastWeek = OrderReseller::newOrder($reseller->id, $startLastWeek, $endLastWeek);
        $newOrderCalculation = $this->getAverageIndexTransaction($newOrderCurrentWeek, $newOrderLastWeek);

        #Completed Order
        $completedOrderCurrentWeek = OrderReseller::completedOrder($reseller->id, $startCurrentWeek, $endCurrentWeek);
        $completedOrderLastWeek = OrderReseller::completedOrder($reseller->id, $startLastWeek, $endLastWeek);
        $completedOrderCalculation = $this->getAverageIndexTransaction($completedOrderCurrentWeek, $completedOrderLastWeek);

        $data = new Collection([
            'commission_amount' => $resellerCommission->commission_amount,
            'potential_amount' => $resellerCommission->potential_amount,
            'newOrderCurrent' => $newOrderCurrentWeek,
            'newOrderPrevious' => $newOrderLastWeek,
            'newOrderPercentage' => $newOrderCalculation["percentage"],
            'newOrderIncrease' => $newOrderCalculation["increase"],
            'completedOrderCurrent' => $completedOrderCurrentWeek,
            'completedOrderPrevious' => $completedOrderLastWeek,
            'completedOrderPercentage' => $completedOrderCalculation["percentage"],
            'completedOrderIncrease' => $completedOrderCalculation["increase"]
        ]);

        return $this->sendSuccess('GET index transaction success', new ResellerIndexTransactionResource($data));
    }

    private function getAverageIndexTransaction($current, $prev) 
    {
        $current = $current > 0 ? $current : 1;
        // $prevDivision = $prev > 0 ? $prev : 1;

        $diff = ($prev - $current) < 0; //boolean

        $calculate = $prev == 0 ? 100 : (abs($current - $prev)) / $prev * 100;

        return $res = [
            "increase" => $diff,
            "percentage" => $calculate
        ];
    }

    public function getListTransaction(ListTransactionResellerRequest $request)
    {
        $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();

        $validatedData = $request->validated();
        $startDate = $validatedData['start_date'];
        $endDate = $validatedData['end_date'];
        $perPage = $validatedData['per_page'];
        $orderStatus = $validatedData['order_status'];

        $orders = OrderReseller::
        with(['items' => fn($q) => $q->with('mainImageVariant')])
        ->when(!empty($startDate), function ($query) use ($startDate, $endDate) {
            $query->whereBetween('order_date', ["$startDate 00:00:00", "$endDate 23:59:59"]);
        })
        ->when($orderStatus !== null, function ($query) use ($orderStatus) {
            $query->whereIn('order_status', $orderStatus);
        })
        ->where('reseller_id', $reseller->id)
        ->orderBy('created_date', 'DESC')
        ->paginate($perPage);
        
        try {
            $datas = $this->pagedResponse(ListTransactionResellerResource::collection($orders), $orders->currentPage(), $perPage);
            $datas['data'] = collect(data_get($datas, 'data'))->values()->all();
            
        } catch (Exception $e) {
            return $this->sendSuccess('',$e->getMessage());
        }
        
        return $this->sendSuccess('get data list transactions success', $datas);
    }

    public function getDetailTransaction(DetailTransactionResellerRequest $request)
    {
        $order_no = $request->order_no;
        $order = OrderReseller::where('order_no', $order_no)
                ->with(['items' => fn($q) => $q->with('mainImageVariant'),
                        'reseller','customer_shipment','link',
                        'item_bundlings' => fn($q) => $q->with('mainImageVariant')])
                ->first();

        if (!isset($order)) {
            return $this->sendSuccess("Data transaction not found", []);
        }

        if($order && !Cache::tags('order_promotion')->has($order->id)) $this->applyBestPromotion(ResellerOrderHeaders::where('id', $order->id)->first());
        Cache::tags('order_promotion')->put($order->id, 1);

        return $this->sendSuccess('get order detail success', ResellerDetailTrxResource::make($order->refresh()));
    }

    public function resellerChart(ResellerChartRequest $resellerChartRequest)
    {
        $request = $resellerChartRequest->validated();
        $id = $request['id'];
        $data = $request['displayed_data'];
        $result = $this->resellerService->getResellerChartData($data,$id);
        return $this->sendSuccess('get data chart success',new ResellerInternalChartResource($result));
    }

    public function resellerChartAll(ResellerChartAllRequest $resellerChartAllRequest)
    {
        try{
        $request = $resellerChartAllRequest->validated();
        $data = $request['displayed_data'];
        $start = $request['date_from'];
        $to = $request['date_to'];
        $result = $this->resellerService->getResellerChartDataAll($data, $start, $to);

    } catch (\Exception $e) {
        // Add detailed error logging
        Log::error('Exception Occurred: ' . $e->getMessage());
        return $this->sendException('Exception Occurred', 500, 'Exception Occurred', $e);
    }
        return $this->sendSuccess('get data chart success',new ResellerInternalChartResource($result));
    }

    public function resellerChartAllDownload(ResellerChartAllDownloadRequest $resellerChartAllDownloadRequest)
    {
        try{
            $request = $resellerChartAllDownloadRequest->validated();
            $start = $request['date_from'];
            $to = $request['date_to'];
            $result = $this->resellerService->getResellerChartDataAllDownload( $start, $to);
    
            return $this->sendSuccess('get sales report data success', new ResellerSalesReportInternalResource($result));
        }
        catch(\Exception $e){
            return $this->sendException('Exception Occured', 500, 'Exception Occured', $e);
        }

    }

    public function getSalesReport()
    {
        $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();

        $result = $this->resellerService->getSalesReport($reseller->id);
        
        return $this->sendSuccess('get sales report data success', new ResellerSalesReportResource($result));
    }
    
    public function getRegisteredBank()
    {
        $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();

        $bank = ResellerBank::With("bank")
        ->Where("reseller_id", "=", $reseller->id)->first();
       
        
        return $this->sendSuccess('get reseller bank data success', new ResellerRegisteredBankResource($bank));
    }
    
    public function getListBank()
    {
        $banks = MasterBank::get()->toArray();
        
        return $this->sendSuccess('get master bank data success', $banks);
    }

    public function salesDashboardInternal(ListOrderDetailsInternalRequest $salesInternalRequest)
  {
    $request = $salesInternalRequest->validated();
    $search = $request['search'];
    $status = $request['order_status'];
    $start = $request['date_from'];
    $to = $request['date_to'];
 
    $orders = OrderReseller::with('reseller', 'order_shipment', 'items')
    ->leftJoin('rsl_order_details', 'rsl_order_details.order_header_id', '=', 'rsl_order_headers.id')
    ->when(!empty($search), function ($query) use ($search) {
        $query->where(function ($subQuery) use ($search) {
            $subQuery->where('order_no', 'LIKE', '%' . $search . '%')
            ->orWhere('customer_name', 'LIKE', '%' . $search . '%')
                ->orWhereHas('reseller', function ($resellerQuery) use ($search) {
                    $resellerQuery->where('name', 'LIKE', '%' . $search . '%');
                });
        });
    })
    ->when($status !== null, function ($query) use ($status) {
        $query->whereIn('order_status', $status);
    })
    ->when($start !== null && $to !== null, function ($query) use ($start, $to) {
        $query->whereDate('order_date', '>=', $start)
        ->whereDate('order_date', '<=', $to);
    })
    ->orderBy('order_date', 'desc')
    ->get();
    // export csv
    $datas = ListOrderDetailsInternalResource::collection($orders);
    $dataJson = json_encode($datas);
    $dataArray = json_decode($dataJson, true);
    $tempFile = tempnam(sys_get_temp_dir(), 'csv');
    $handle = fopen($tempFile, 'w');
    
    $filename = 'output.csv';

    $col = [
    'Transaction Date', 
    'Order ID', 
    'Payment reference ID',
    'Order reference ID',
    'Sold By', 
    'Phone No', 
    'Reseller ID', 
    'Purchased By',
    'Recipient',
    'Recipient Phone',
    'Recipient Email',
    'Address',
    'City',
    'Zip Code',
    'Item Name',
    'Qty',
    'Variant',
    'Article ID',
    'Unit Price',
    'Sub Total',
    'Discount',
    'Total',
    'Promo Name',
    'Voucher Code',
    'Coupon Code',
    'Payment Method',
    'Courier',
    'AWB No',
    'Delivery Number',
    'Order Status',
    ];

    fputcsv($handle, $col,';');
    foreach ($dataArray as $row) {
        fputcsv($handle, $row,';');
    }

    fclose($handle);
    return response()->download($tempFile, $filename)->deleteFileAfterSend(true);
  }
}
