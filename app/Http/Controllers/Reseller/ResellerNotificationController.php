<?php
namespace App\Http\Controllers\Reseller;

use App\Http\Controllers\Controller;
use App\Models\MasterParameter;
use Illuminate\Http\Request;
use App\Models\UserNotification;
use App\Http\Resources\User\NotificationResource;
use App\Helpers\RestHelper;
use Illuminate\Support\Facades\Cache;


class ResellerNotificationController extends Controller
{
    public function getNotification(Request $request)
    {
        $acceptableFilter = [
            UserNotification::RESELLER_COMMISSION,
            UserNotification::RESELLER_INFORMATION,
            UserNotification::RESELLER_TRANSACTION
        ];
        $filter = request()->filter ?? null;
        
        
            
        if (!in_array($filter, $acceptableFilter)) {
            $filter = null;
        }
        $data = UserNotification::where('user_id', auth()->user()->reference_id)
        ->groupBy('message');

        if ($filter) {
            $data = $data->Where('category', $filter);
        }
        
        $data = $data->desc('created_date')
        ->paginate(10);
        
        $datas = $this->pagedResponse(NotificationResource::collection($data), $data->currentPage(), 10);
        $datas['data'] = collect(data_get($datas, 'data'))->values()->all();
        
        return $this->sendSuccess("Get reseller notifications success", $datas);
    }

    public function readAll(Request $request)
    {
        UserNotification::where('user_id', auth()->user()->reference_id)
        ->update(['is_read' => 1]);
        return $this->sendSuccess('read all notifications success');
    }

    public function readOne(Request $request)
    {
        $notifId = $request->input('id');
        UserNotification::where('user_id', auth()->user()->reference_id)
        ->where('id', $notifId)
        ->update(['is_read' => 1]);

        return $this->sendSuccess('read one notification success');
    }

    public function newNotifs(Request $request) {
        return $this->doStore(UserNotification::class, $request, [], false, true);
    }
}
