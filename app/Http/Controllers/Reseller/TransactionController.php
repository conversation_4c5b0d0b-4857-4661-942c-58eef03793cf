<?php

namespace App\Http\Controllers\reseller;

use Carbon\Carbon;
use App\Models\City;
use App\Models\Coupon;
use App\Models\Product;
use App\Models\Voucher;
use App\Models\District;
use App\Services\Xendit;
use App\Services\CareOmni;
use App\Helpers\RestHelper;
use Illuminate\Http\Request;
use App\Models\OrderReseller;
use App\Models\PublicProduct;
use App\Models\ResellerToken;
use App\Models\MasterParameter;
use App\Models\OrderItemReseller;
use App\Models\ResellerCartDetail;
use App\Models\ResellerMasterSite;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Models\ResellerTransporter;
use App\Services\LoggerIntegration;
use Illuminate\Support\Facades\Log;
use VXM\Async\AsyncFacade as Async;
use App\Helpers\Coupon\CouponHelper;
use App\Helpers\OrderResellerHelper;
use App\Http\Controllers\Controller;
use App\Models\ResellerArticleStock;
use App\Models\ResellerOrderHeaders;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Helpers\Voucher\VoucherHelper;
use App\Models\ResellerOrderPromotion;
use App\Models\ResellerCustomerShipment;
use App\Helpers\Shipments\ShipmentEngine;
use App\Helpers\Shipments\ShipmentHelper;
use App\Jobs\RankDiscountArticleReseller;
use App\Helpers\Promotion\PromotionEngine;
use App\Helpers\Promotion\PromotionHelper;
use App\Http\Requests\CancelOrderReseller;
use App\Http\Requests\GetTransportRequest;
use App\Http\Requests\ApplyBundlingRequest;
use App\Http\Requests\CheckoutResellerRequest;
use App\Http\Requests\ProceedToPaymentRequest;
use App\Http\Resources\ProductVariantResource;
use App\Http\Requests\CancelOrderResellerRequest;
use App\Http\Requests\StoreOrderPromotionRequest;
use App\Http\Requests\DeleteOrderPromotionsRequest;
use App\Http\Requests\StockCustomerResellerRequest;
use App\Http\Requests\StoreOrderTransporterRequest;
use App\Http\Requests\OrderDetailInternalResellerRequest;
use App\Http\Resources\CustomerResellerOrderDetailResource;
use App\Http\Resources\InternalResellerOrderDetailResource;
use App\Http\Requests\getTransactionDetailCustResellerRequest;
use App\Http\Resources\ListTransactionCustomerResellerResource;

class TransactionController extends Controller
{
    use OrderResellerHelper, PromotionHelper, CouponHelper, VoucherHelper;

    public $test = 0;

    public function testAsync(Request $request){
        // dd('test');
        // if (!$customer_type) {
        //     $customer_type = @Auth::user()->customer->customer_type;
        // }
        $customer_type = 'Z1';
        $sku = '910000005';
        // dd('test');
        try{
            $datas = Product::exclude($customer_type)
                ->where('sku_code_c', $sku)
                ->whereIn('product_variant_c', ['BLK', 'GRE'])->with('price')->get();
            // dd($datas);
            // $stock_cache = RestHelper::stockCache($datas->pluck('article'));
            $stock_cache = [
                [
                    'article' => '910000005001',
                    'qty' => 2,
                    'moq' => 1
                ],
                [
                    'article' => '910000005002',
                    'qty' => 3,
                    'moq' => 1
                ]
            ];

            // dd($datas->toArray());

            // $datas = array_map(function ($item) use ($stock_cache){
            //     $item['stock'] = RestHelper::searchStock($stock_cache,$item['article'])['qty'];
            //     $item['moq'] = RestHelper::searchStock($stock_cache,$item['article'])['moq'];
            //     return (object)$item;
            // },$datas->toArray());

            $datas = RestHelper::addFieldStockMoq($stock_cache,$datas->toArray());
            $datas = RestHelper::vArrayToObject($datas);

            // dd($datas);

            // dd($datas);
            // $stock_cache = 'test';
            return $this->sendSuccess("Products retrieved successfully.", ProductVariantResource::collection($datas));
            // return $this->sendSuccess("Products retrieved successfully.", ProductVariantResource::collection(
            //     $datas->map(function ($resource) use ($stock_cache) {return new ProductVariantResource($resource,$stock_cache);})
            // ));
        }catch(\Exception $e){
            return $this->sendError($e->getMessage(), $e->getCode()?: 500);
        }
        
        // $items = OrderItemReseller::where('order_header_id','e0eec50a-e5a4-11ee-97c4-06c39b9216e2')->with('product')->get()->toArray();
        // $weight = $this->calculateWeight($items);
        // Async::run(new ShipmentEngine(['zip_code' => '16417', 'city_name' =>strtolower('sukmajaya')],[
        //     'zip_code' => '16830',
        //     'city'     => strtolower('bogor'),
        //     'subdistrict'=> strtolower('singajaya')
        // ],ceil($weight), 'JNE'));

        // $datas = array_merge(...Async::wait());

        // return $this->sendSuccess('Get transporter success',$datas);
           // Async::run(new PromotionEngine(ResellerOrderHeaders::find('00fec3f1-680a-11ee-8e87-0628bea0944a')));
        //    return $this->applyArticlePromotion('910001241002')['disc'] * 2;
        // return $this->applyBundling(ResellerOrderHeaders::where('order_no','INVRS742055C9')->first());
           // return Async::wait();

        // $batches = [];
        // PublicProduct::where('is_reseller',1)
        //             ->chunk(100,function($d)use(&$batches){
        //                 $batches[] = new RankDiscountArticleReseller($d->toArray());
        //                 // $batches[] = $d->toArray();
        //             });
        // dd($batches);
        //    return $request->ip();
        //    sleep(10);
        //    Cache::forget('transaction-lock');
        //    return $this->sendSuccess('waw');

        //    $order = OrderReseller::where('order_no','INVRS742055C9')
        //    ->with(['items' => fn($q) => $q->with('mainImageVariant'),
        //            'reseller','customer_shipment','link',
        //            'item_bundlings' => fn($q) => $q->with('mainImageVariant')])->first();
        //     return($order);

        // if($order && !Cache::tags('order_promotion')->has($order->id)) $this->applyBestPromotion(ResellerOrderHeaders::where('id', $order->id)->first());
        // Cache::tags('order_promotion')->put($order->id, 1);

        // untuk sprint sekarang ini dlu
        // return $this->sendSuccess('get order detail success',CustomerResellerOrderDetailResource::make($order->refresh()));
        
        // $this->testIncrement(5);
        // $this->testIncrement(2);
        // return $this->test;

        // dd($request->input('customs.*.customLogos.*.data'));
        // dd(array_values(array_unique($request->input('customs.*.customLogos.*.data'))));
        // return array_unique($request->input('*.test'));
    
    }

    public function testIncrement($n = 1)
    {
        $this->test += $n;
    }

    public function checkStock(StockCustomerResellerRequest $request)
    {
        $stock = ResellerArticleStock::where('article',$request->article)->orderBy('available_stock','desc')
                                        ->first();
        
        if ($stock->available_stock < $request->qty) {
            $message = 'The selected quantity exceeds the available stock.';
        } else {
            $message = 'The selected quantity is safe to use.';
        }

        return $this->sendSuccess($message,$stock->toArray());
    }

    public function applyBundlingOrder(ApplyBundlingRequest $request){
        try{
            $request->validated();
            $order = ResellerOrderHeaders::where('order_no', $request->order_no)->first();

            $this->releaseStock($order->detail_bundling, $order->location_code);
            $this->deleteBundling($order);
            if(empty($request->items)) return $this->sendSuccess('Bundling cleared',[], 'success');
            $bundling = collect($this->applyBundling($order));
            if(empty($bundling)) return $this->sendSuccess('Bundling Invalid', []);
            $applied = [];
            $list_unavailable = [];
            foreach($request->items as $i){
                $valid = $bundling->where('promotion_id', $i['promotion_id'])->where('sequence', $i['sequence'])->first();
                if ($valid) {
                    $valid_article = collect($valid['choose'])->firstWhere('item_id', $i['article'])??[];
                        if(array_key_exists('article',$valid_article) && $valid_article['stock']['available_stock']??0 > 0){
                        array_push($applied, $i);
                        $b_code = $this->formatBundlingCode($order->order_no, $i);
                        $b_item = $order->bundlings()->where('bundling_code', $b_code)->first();
                        $details = [[
                            'article_id' => $i['article'],
                            'sku_code' => $valid_article['article']['sku_code_c']??'',
                            'product_name' => $valid_article['article']['product_name_c']??'',
                            'product_size' => $valid_article['article']['product_size_c']??'',
                            'product_variant' => $valid_article['article']['product_variant_c']??'',
                            'qty' => 1,
                            'unit_price' => $valid_article['article']['price']['amount']??0,
                            'line_amount' =>  $valid_article['article']['price']['amount']??0,
                            'discount_amount' =>  $valid_article['article']['price']['amount']??0,
                            'total_amount' => 0,
                            'remarks'   => $b_code
                        ]];
                        // dd($details);
                        if(!$b_item){
                            $order->detail_bundling()->create($details[0]);
                            $this->bookStock($details,$order->location_code);
                            $order->bundlings()->create([
                                'reference_name' => 'headers',
                                'reference_id'   => $order->id,
                                'discount_type'  => 'bundlings',
                                'discount_id'    => $i['promotion_id'],
                                'amount'         => $valid_article['article']['price']['amount']??0,
                                'bundling_code'  => $b_code,
                                'created_by'     => 'PROMOTION_ENGINE_BUNDLING',
                                'modified_by'    => 'PROMOTION_ENGINE_BUNDLING',
                            ]);
                        }
                        else{
                            $order->detail_bundling()->where('remarks', $b_code)->update($details[0]);
                            $this->bookStock($details,$order->location_code);
                            $b_item->update([
                                            'reference_name' => 'headers',
                                            'reference_id'   => $order->id,
                                            'discount_type'  => 'bundlings',
                                            'discount_id'    => $i['promotion_id'],
                                            'amount'         => $valid_article['article']['price']['amount']??0,
                                            'bundling_code'  => $b_code,
                                            'created_by'     => 'PROMOTION_ENGINE_BUNDLING',
                                            'modified_by'    => 'PROMOTION_ENGINE_BUNDLING',                                    ]);
                        }
                    }
                    else{
                        $list_unavailable[] = $valid_article['article']['product_name_c']??'';
                    }
                }
        }
        $msg_article = join(",", $list_unavailable);
        return $this->sendSuccess(!empty($list_unavailable)?"Terdapat perubahan stok $msg_article silahkan coba lagi":(empty($applied) ? 'Bundling not applied' : 'Bundling Applied'),$applied, !empty($list_unavailable) ? 'failed' : (empty($applied) ? 'failed' : 'success'));

       
        }
        catch(\Exception $e){
            $this->sendException("Exception Occured", 500, "Exception Occured", $e);
        }


    }

    public function checkout(CheckoutResellerRequest $request)
    {   
        DB::beginTransaction();
        try {
            $header = $request->transformHeader();
            $detail = $request->transformDetail();

            OrderReseller::create($header);
            $header = OrderReseller::where('order_no',$header['order_no'])->first();
            $header->items()->createMany($detail['detail']);
            $this->bookStock($detail['detail'],$header['location_code']);
            ResellerCartDetail::whereIn('id',$detail['cart_detail_id'])->delete();
            
            $order = ResellerOrderHeaders::where('order_no',$header['order_no'])->first();
            $this->applyBestPromotion($order);
            $oh = OrderReseller::where('order_no',$header['order_no'])->with(['items','promotions','items.promotions'])->first();
            $this->adjustCommissionAmount($oh);
            $oh['minutes_pending'] = MasterParameter::where('group_key','RESELLER_TIMER')->where('key','MINUTES_PENDING')->first()->value;
            DB::commit();

            return $this->sendSuccess('Checkout success',$oh);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendException('Checkout failed', 500, 'exception', $e);
            // return $this->sendError('Checkout failed : '.$e->getMessage().'. Line :'.$e->getLine());
        }
    }

    public function cancel(CancelOrderResellerRequest $request)
    {
        DB::beginTransaction();
        try {
            $order = OrderReseller::where('order_no',$request->order_no)->with('items')->first();
            $this->releaseStock($order->all_items->toArray(),$order->location_code);
            $order->order_status = OrderReseller::ORDER_RESELLER_BATAL;
            $order->modified_date = Carbon::now();
            $order->save();

            $ids = $order->items->pluck('id')->toArray();
            $ids[] = $order->id;
            $data = ResellerOrderPromotion::whereIn('reference_id',$ids)->whereIn('discount_type',['coupon','voucher'])->get();
            foreach($data as $d){
                if (strtolower($d->discount_type) == 'voucher') {
                    $voucher = $d->voucher;
        
                    if (strtolower($voucher->discount_type) == 'percentage') {
                        $voucher->remaining_amount = $voucher->amount;
                        $voucher->used_amount = 0;
                        $voucher->save();
                    }
        
                    if (strtolower($voucher->discount_type) == 'absolute') {
                        $voucher->remaining_amount = $voucher->remaining_amount + $d->amount;
                        $voucher->used_amount = $voucher->used_amount - $d->amount;
                        $voucher->save();
                    }
                }

                if (strtolower($d->discount_type) == 'coupon') {
                    $coupon = $d->coupon;
                    $coupon->used_count -= 1;
                    $coupon->save();
                }
            }

            $this->orderToCart($order->toArray());
            DB::commit();

            return $this->sendSuccess('Cancel order success',$order);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendException('Cancel order', 500, 'exception', $e);
            // return $this->sendError('Cancel order failed : '.$e->getMessage().'. Line : '.$e->getLine());
        }
    }

    public function getTransactionDetail(getTransactionDetailCustResellerRequest $request)
    {
        $order_no = $request->order_no;
        $order = OrderReseller::where('order_no',$order_no)
                            ->with(['items' => fn($q) => $q->with('mainImageVariant'),
                                    'reseller','customer_shipment','link',
                                    'item_bundlings' => fn($q) => $q->with('mainImageVariant')])->first();
        
        if($order && !Cache::tags('order_promotion')->has($order->id)) $this->applyBestPromotion(ResellerOrderHeaders::where('id', $order->id)->first());
        Cache::tags('order_promotion')->put($order->id, 1);

        // untuk sprint sekarang ini dlu
        return $this->sendSuccess('get order detail success',CustomerResellerOrderDetailResource::make($order->refresh()));
    }

    public function orderDetailInternal(OrderDetailInternalResellerRequest $orderDetailInternalResellerRequest, $id)
    {
        $request = $orderDetailInternalResellerRequest->validated();
        $page = $request['page'];
        $perPage = $request['per_page'];
        $order = OrderReseller::where('order_no',$id)
                            ->with(['reseller' => fn($q) => $q->with('bankAccounts'),'customer_shipment','link'])->first();

        return $this->sendSuccess('get order detail success',InternalResellerOrderDetailResource::make($order, $perPage, $page));
    }

    public function getTransport(GetTransportRequest $request)
    {
        $user = Auth::guard('sanctum')->user();
        $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;
        $cs = ResellerCustomerShipment::where('customer_id',$customer_id)->where('is_active',1)->first();
        $order = OrderReseller::where('order_no',$request->order_no)->first();
        $items = OrderItemReseller::where('order_header_id',$order->id)->with('product')->get()->toArray();
        $weight = $this->calculateWeight($items);
        $site = ResellerMasterSite::where('code',$order->location_code)->first();

        Async::run(new ShipmentEngine(['zip_code' => $site->zip_code, 'city_name' =>strtolower($site->ma->city_name)],[
            'zip_code' => $cs->zip_code,
            'city'     => strtolower($cs->city_name),
            'subdistrict'=> strtolower($cs->subdistrict_name)
        ],ceil($weight), 'JNE'));
        Async::run(new ShipmentEngine(['zip_code' => $site->zip_code, 'city_name' =>strtolower($site->ma->city_name)],[
            'province' => strtolower($cs->region_name),
            'zip_code' => $cs->zip_code,
            'city'     => strtolower($cs->city_name),
            'subdistrict'=> strtolower($cs->subdistrict_name)
        ],ceil($weight), 'SICEPAT'));

        $datas = array_merge(...Async::wait());

        return $this->sendSuccess('Get transporter success',$datas);
    }

    public function storeOrderTransporter(StoreOrderTransporterRequest $request)
    {
        DB::beginTransaction();
        try {
            $transporter_id = $request->transporter_id;
            $user = Auth::guard('sanctum')->user();
            $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;
            $cs = ResellerCustomerShipment::where('customer_id',$customer_id)->where('is_active',1)->first();
            $order = OrderReseller::where('order_no',$request->order_no)->first();
            $items = OrderItemReseller::where('order_header_id',$order->id)->with('product')->get()->toArray();
            $weight = $this->calculateWeight($items);
            $site = ResellerMasterSite::where('code',$order->location_code)->first();

            Async::run(new ShipmentEngine(['zip_code' => $site->zip_code, 'city_name' =>strtolower($site->ma->city_name)],[
                'zip_code' => $cs->zip_code,
                'city'     => strtolower($cs->city_name),
                'subdistrict'=> strtolower($cs->subdistrict_name)
            ],$weight, 'JNE'));
            Async::run(new ShipmentEngine(['zip_code' => $site->zip_code, 'city_name' =>strtolower($site->ma->city_name)],[
                'zip_code' => $cs->zip_code,
                'province' => strtolower($cs->region_name),
                'city'     => strtolower($cs->city_name),
                'subdistrict'=> strtolower($cs->subdistrict_name)
            ],$weight, 'SICEPAT'));

            $datas = array_merge(...Async::wait());

            $f_transporter = array_filter($datas, function ($item) use ($transporter_id) {
                return isset($item['transporter_id']) && $item['transporter_id'] == $transporter_id;
            });
    
            $t_data = array_values($f_transporter)[0];

            $order = OrderReseller::where('order_no',$request->order_no)->first();
            OrderReseller::where('order_no',$request->order_no)->update([
                'transporter_id' => $t_data['transporter_id'],
                'shipment_method' => $t_data['fullname'],
                'shipment_charges' => $t_data['price'],
                'pay_amount' => $order->total_amount + $t_data['price']
            ]);
            DB::commit();

            return $this->sendSuccess('update order transporter success',$order->fresh());
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendException('update order transporter failed', 500, 'exception', $e);
            // return $this->sendError('update order transporter failed : '.$e->getMessage().'. Line : '.$e->getLine());
        }
    }

    public function proceedToPayment(ProceedToPaymentRequest $request, Xendit $xendit, CareOmni $careOmni)
    {
        $updateOrder = $request->transformUpdateHeader();
        
        try {
            $order = OrderReseller::where('order_no',$request->order_no)->first();

            // $this->buyStock($order->all_items->toArray(),$order->location_code);

            // $oh = ResellerOrderHeaders::where('order_no',$request->order_no)->first();
            // $this->transformProrateCV($order);

            $order->update($updateOrder);

            // care omni create order integration
            if (empty($order->ext_order_id)) {
                $payloadCareOmni = $this->payloadCreateOrderCareOmni($request->order_no);
                $create_order = $careOmni->create_order($payloadCareOmni);
                if ($create_order->failed()) {
                    (new LoggerIntegration())->InsertLogger([
                        'reference_no' => $request->order_no,
                        'module' => 'Care Omni',
                        'name' => 'Create Order to Care Omni',
                        'type' => 'Outbound',
                        'status' => 'failed',
                        'description' => [
                            'payload' => $payloadCareOmni,
                            'response' => $create_order->json()
                        ]
                    ]);
                    return $this->sendError('Please retry again later, Care omni is temporary unavailable.',503);
                }

                $data_omni = $create_order->json();

                (new LoggerIntegration())->InsertLogger([
                    'reference_no' => $request->order_no,
                    'module' => 'Care Omni',
                    'name' => 'Create Order to Care Omni',
                    'type' => 'Outbound',
                    'status' => 'success',
                    'description' => [
                        'payload' => $payloadCareOmni,
                        'response' => $data_omni
                    ]
                ]);

                OrderReseller::where('order_no',$request->order_no)->update([
                    'ext_order_id' => $data_omni['data']['id'],
                    'ext_order_no' => $data_omni['data']['order_number'],
                    'ext_order_status' => $data_omni['data']['status'],
                    'sla_date' => $data_omni['data']['sla_date'],
                    'sla_status' => $data_omni['data']['sla_status']
                ]);
            }

            // xendit integration
            if (empty($order->payment_link)) {
                $payloadXendit = $this->payloadXenditInvoices($request->order_no);
                $xendit_inv = $xendit->invoices($payloadXendit);
                
                if ($xendit_inv->failed()) {
                    (new LoggerIntegration())->InsertLogger([
                        'reference_no' => $request->order_no,
                        'module' => 'Xendit',
                        'name' => 'Create Invoice',
                        'type' => 'Outbound',
                        'status' => 'failed',
                        'description' => [
                            'payload' => $payloadXendit,
                            'response' => $xendit_inv->json()
                        ]
                    ]);
                    
                    return $this->sendError('Please retry again later, Xendit is temporary unavailable.',503);
                }

                $data_xendit_inv = $xendit_inv->json();

                (new LoggerIntegration())->InsertLogger([
                    'reference_no' => $request->order_no,
                    'module' => 'Xendit',
                    'name' => 'Create Invoice',
                    'type' => 'Outbound',
                    'status' => 'success',
                    'description' => [
                        'payload' => $payloadXendit,
                        'response' => $data_xendit_inv
                    ]
                ]);

                OrderReseller::where('order_no',$request->order_no)->update([
                    'invoice_no' => $data_xendit_inv['id'],
                    'payment_link' => $data_xendit_inv['invoice_url'],
                    'due_payment_date' => Carbon::parse($data_xendit_inv['expiry_date'])->timezone('Asia/Jakarta')->format('Y-m-d H:i:s')
                ]);
            }

            

            $oh = OrderReseller::where('order_no',$order->order_no)->first();
            $oh->order_status = OrderReseller::ORDER_RESELLER_MENUNGGU_PEMBAYARAN;
            $oh->save();

            $ids = $order->items()->pluck('id')->toArray();
            $ids[] = $order->id;
            $ops = ResellerOrderPromotion::whereIn('reference_id',$ids)->where('discount_type','voucher')->get()->unique('discount_id');
            if (!$ops->isEmpty()) {
                $v_error = false;
                foreach ($ops as $op) {
                    $voucher = Voucher::where('id',$op->discount_id)->first();
                    $payload = [
                        'order_number' => $order->order_no,
                        'amount' => $voucher->amount,
                        'used_amount' => $op->amount,
                        'code' => $voucher->code
                    ];
                    $req = $careOmni->use_voucher($payload);
                    if ($req->failed()) {
                        $v_error = true;
                        $v_error_item[] = $voucher->code;
                    }
                }
                if ($v_error == true) {
                    return $this->sendError('Voucher ini tidak bisa digunakan : '.implode(', ',$v_error_item));
                }
            }

            $order = OrderReseller::where('order_no',$request->order_no)->first();
            $this->adjustCouponVoucher($order);

            $log_tr = $this->logTransactions($request->order_no,['ip','d','sc'],'Completed');

            return $this->sendSuccess('Proceed to payment success',OrderReseller::where('order_no',$request->order_no)->first()->toArray());
        } catch (\Exception $e) {

            return $this->sendException('Proceed to payment failed', 500, 'exception', $e);
            // return $this->sendError('Proceed to payment failed : '.$e->getMessage().'. Line : '.$e->getLine());
        }
    }

    public function getDiscounts(Request $request)
    {
        $search = $request->query('search','');
        
        $vouchers = Voucher::active()->activeDate()->usable()->where('code',$search)->get()
        ->map(function($m){
            return [
                'id' => $m->id,
                'name' => $m->master->name,
                'category' => $m->category,
                'code' => $m->code,
                'amount' => $m->remaining_amount,
                'discount_type' => $m->discount_type,
                'start_date' => $m->start_date,
                'end_date' => $m->end_date,
                'type' => 'voucher',
                'terms_and_conditions' => '-' 
            ];
        });


        $coupons = $this->getCurrentAvailableCoupons()
        ->when($search,function($q) use ($search){
            $q->where('coupon_code',$search);
        })
        ->orderBy('created_date','desc')
        ->get()
        ->map(function($m){
            return [
                'id' => $m->id,
                'name' => $m->name,
                'category' => $m->category,
                'code' => $m->coupon_code,
                'amount' => $m->amount,
                'discount_type' => $m->discount_type,
                'start_date' => $m->start_date,
                'end_date' => $m->end_date,
                'type' => 'coupon',
                'terms_and_conditions' => $m->description??'-'
            ];
        });

        $merge = new Collection();
        $merge = $merge->concat($vouchers)->concat($coupons);

        if ($merge->isEmpty()) {
            return $this->sendError('Voucher or coupon not found!');
        }
        return $this->sendSuccess('Get vouchers & coupon success',$merge);
        // return $this->sendSuccess('Get vouchers success',VouchersResource::collection($merge));
    }

    public function deleteOrderPromotions(DeleteOrderPromotionsRequest $request)
    {
        DB::beginTransaction();
        try {
            $header = OrderReseller::where('order_no',$request->order_no)->first();
            $ids = $header->items->pluck('id')->toArray();
            $ids[] = $header->id;
            $data = ResellerOrderPromotion::whereIn('reference_id',$ids)->whereIn('discount_type',['coupon','voucher'])->get();
            foreach($data as $d){
                if ($d->header != null) {
                    $order = $d->header;
        
                    $order->total_amount = $order->total_amount + $d->amount;
                    $order->pay_amount = $order->pay_amount + $d->amount;
                    $order->save();
                }
        
                if ($d->detail != null) {
                    $item = $d->detail;
        
                    $item->discount_amount = $item->discount_amount - $d->amount;
                    $item->total_amount = $item->total_amount + $d->amount;
                    $item->save();
                }
        
                if (strtolower($d->discount_type) == 'voucher') {
                    $voucher = $d->voucher;
        
                    if (strtolower($voucher->discount_type) == 'percentage') {
                        $voucher->remaining_amount = $voucher->amount;
                        $voucher->used_amount = 0;
                        $voucher->save();
                    }
        
                    if (strtolower($voucher->discount_type) == 'absolute') {
                        $voucher->remaining_amount = $voucher->remaining_amount + $d->amount;
                        $voucher->used_amount = $voucher->used_amount - $d->amount;
                        $voucher->save();
                    }
                }
                $d->delete();
            }
            $this->applyBestPromotion(ResellerOrderHeaders::where('order_no',$request->order_no)->first());
            DB::commit();

            $data = OrderReseller::where('order_no',$request->order_no)->with(['items','promotions','items.promotions'])->first();
            return $this->sendSuccess('Delete order promotions success',$data);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendException('Delete order promotions failed', 500, 'exception', $e);
            // return $this->sendError('Delete order promotions failed : '.$e->getMessage().'. Line : '.$e->getLine());
        }
    }

    public function storeOrderPromotions(StoreOrderPromotionRequest $request)
    {
        DB::beginTransaction();
        try {
            $order = OrderReseller::where('order_no',$request->order_no)->first();
            if (strtolower($request->discount_type) == 'coupon') {
                $coupon = Coupon::where('id',$request->discount_id)->first();
                $apply_coupon = $this->addCoupon($order,$coupon);
                if (isset($apply_coupon['valid']) && $apply_coupon['valid'] == false) {
                    foreach($apply_coupon['validation'] as $validation){
                        $errorMsg[] = $validation['message'];
                    }
                    $error[] = [
                        'coupon' => $coupon->name,
                        'message' => $errorMsg??[]
                    ];
                }
            }

            if (strtolower($request->discount_type) == 'voucher') {
                $voucher = Voucher::where('id',$request->discount_id)->first();
                $apply_voucher = $this->addPaymentVoucher($order,$voucher);
            }

            $oh = OrderReseller::where('id',$order->id)->first();
            $min_order = MasterParameter::where('group_key', 'RESELLER_VALIDATION')->where('key', 'APPLY_COUPON_VOUCHER')->first();
            $min_order =  $min_order ? $min_order->value : 10000;
            if($oh->total_amount < $min_order){
                DB::rollBack();
                return $this->sendError(['order_no' => ['Voucher/Kupon tidak dapat digunakan karena total transaksi kurang dari RP'.number_format($min_order,0,'','.')]], 422);
            }
            $this->adjustCommissionAmount($oh);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $error[] = [
                'message' => $e->getMessage(),
                'line' => $e->getLine()
            ];
        }

        $order = OrderReseller::where('order_no',$request->order_no)->with(['items','items.promotions','promotions'])->first();
        return $this->sendSuccess('Store order promotions success', [
            'error' => $error??[],
            'order' => $order
        ]);
        
    }

    public function getTransactions(Request $request)
    {
        $search = $request->query('search');
        $status = $request->query('status');
        $date_from = $request->query('date_from');
        $date_to = $request->query('date_to');

        $user = $request->user();
        $customer_id = $user->reference_id;

        $datas = OrderReseller::where('customer_id',$customer_id)
                                ->when($search,function ($q) use ($search){
                                    $q->where(function ($q) use ($search){
                                        $q->where('order_no','LIKE','%'.$search.'%')
                                        ->orWhereHas('items',function($q) use ($search){
                                            $q->where('article_id','LIKE','%'.$search.'%')
                                            ->orWhere('product_name','LIKE','%'.$search.'%');
                                        });
                                    });
                                })
                                ->when($status,function ($q) use ($status){
                                    $q_status = explode(',',$status);
                                    if (!in_array('Semua',$q_status)) {
                                        $q->whereIn('order_status',$q_status);
                                    }
                                })
                                ->when($date_to, function ($q) use ($date_to){
                                    $date_to = Carbon::parse($date_to)->format('Y-m-d H:i:s');
                                    $q->whereDate('created_date','<=',$date_to);
                                })
                                ->when($date_from, function ($q) use ($date_from){
                                    $date_from = Carbon::parse($date_from)->format('Y-m-d H:i:s');
                                    $q->whereDate('created_date','>=',$date_from);
                                })
                                ->withSum('items','qty')
                                ->orderBy('created_date','desc')
                                ->pager($request);

        return $this->pagedResponse(ListTransactionCustomerResellerResource::collection($datas),$datas->currentPage(), $request->query('per_page')??12);
    }
}
