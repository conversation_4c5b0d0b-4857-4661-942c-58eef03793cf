<?php

namespace App\Http\Controllers\Reseller;

use Carbon\Carbon;
use App\Models\User;
use App\Jobs\MailSender;
use App\Models\Reseller;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Models\OrderReseller;
use App\Models\ResellerToken;
use App\Models\MasterParameter;
use App\Models\ResellerCustomer;
use Illuminate\Support\Facades\DB;
use App\Helpers\UserResellerHelper;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Repositories\GetSocialsRepo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\ResellerCustomerShipment;
use Illuminate\Support\Facades\Password;
use Illuminate\Auth\Events\PasswordReset;
use App\Models\ResellerCustomerActivation;
use App\Http\Requests\LoginResellerRequest;
use App\Http\Requests\ForgotPasswordRequest;
use App\Http\Requests\GenerateGuestTokenRequest;
use App\Http\Requests\GetProfileResellerRequest;
use App\Http\Resources\CustomerResellerResource;
use App\Http\Requests\ActivateUserResellerRequest;
use App\Http\Requests\ChangeForgotPasswordRequest;
use App\Http\Resources\ProfileResellerForCustomer;
use App\Http\Requests\ForgotPasswordResellerRequest;
use App\Http\Resources\UserCustomerResellerResource;
use App\Http\Requests\GetShipmentCustResellerRequest;
use App\Http\Requests\RegisterCustomerResellerRequest;
use App\Http\Requests\DeleteShipmentCustResellerRequest;
use App\Http\Requests\GetProfileCustomerResellerRequest;
use App\Http\Requests\UpsertShipmentCustResellerRequest;
use App\Http\Requests\ResendActivationLinkResellerRequest;
use App\Http\Resources\GetProfileCustomerResellerResource;
use App\Http\Requests\Step1RegisterCustomerResellerRequest;
use App\Http\Requests\Step2RegisterCustomerResellerRequest;
use App\Http\Requests\ChangePasswordCustomerResellerRequest;
use App\Models\City;

class UserController extends Controller
{
    use UserResellerHelper;

    public function login(LoginResellerRequest $req)
    {
        $credentials = $req->credentials();

        try {
            // $user = User::where('username',$credentials['email'])->where('reference_object',$credentials['reference_object'])->first();
            if (!Auth::attempt($credentials)) {
                return $this->sendError('Email atau kata sandi yang anda masukkan salah, silahkan coba lagi.', 400);
            }
            $user = $req->user();

            $validate = $this->validateLogin($user,$req);
            if ($validate['error'] == true) {
                return $this->sendError($validate['message'],400);
            }

            $data = $this->authResponse($user);
                    
            return $this->sendSuccessWithCookies('Login successfully.', $data, cookie('jwt',$data['auth']['access_token'],60*24,null,null,null,true,false,'lax'));
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage().' : '.$e->getLine());
        }
    }

    private function authResponse($user)
    {
        $data_user = new UserCustomerResellerResource($user);
        $profile = $user->rsl_customer;
        $token = $user->createToken($user->reference_object.'-'.@$user->rsl_customer->id);
        $data = new CustomerResellerResource($profile);

        return [
            'auth' => [
                'access_token' => $token->plainTextToken,
                'token' => 'bearer'
            ],
            'user' => $data_user,
            'profile' => $data,
        ];
    }

    public function forgotPassword(ForgotPasswordResellerRequest $request)
    {
        // $request->validated();

        try {
            // We will send the password reset link to this user. Once we have attempted
            // to send the link, we will examine the response then see the message we
            // need to show to the user. Finally, we'll send out a proper response.
            $status = Password::sendResetLink(
                $request->only('email')
            );

            Log::info('Forgot Password Reseller Log',[
                'email' => $request->input('email'),
                'status' => $status == Password::RESET_LINK_SENT ? 'success' : 'failed'
            ]);

            // return $status == Password::RESET_LINK_SENT
            //             ? $this->sendSuccess('Please check your email for the URL to reset your password.')
            //             : $this->sendError("There are some issues requesting the password reset link.");
        } catch (\Exception $e) {
            Log::info('Forgot Password Reseller Log Exception',[
                'email' => $request->input('email'),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ]);
            // return $this->sendError($e->getMessage());
        }
        return $this->sendSuccess('email reset password will be sent if your email is registered');
    }

    public function changeForgotPassword(ChangeForgotPasswordRequest $request)
    {
        $request->validated();

        // Here we will attempt to reset the user's password. If it is successful we
        // will update the password on an actual user model and persist it to the
        // database. Otherwise we will parse the error and return the response.
        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function ($user) use ($request) {
                $user->forceFill([
                    'password' => Hash::make($request->password),
                    'is_change_password' => 1,
                    'remember_token' => Str::random(60),
                ])->save();

                event(new PasswordReset($user));
            }
        );

        // If the password was successfully reset, we will redirect the user back to
        // the application's home authenticated view. If there is an error we can
        // redirect them back to where they came from with their error message.
        if ($status == Password::PASSWORD_RESET) {
            return $this->sendSuccess('Your password has been change successfully.');
        } else {
            switch ($status) {
                case 'passwords.user':
                    return $this->sendError("The user not found.");
                    break;
                case 'passwords.token':
                    return $this->sendError("Link untuk reset password sudah kadaluarsa silahkan lakukan reset password kembali");
                    break;
                default:
                    return $this->sendError("There are some issues when change new password.");
                    break;
            }
        }
    }

    public function generateGuestToken(GenerateGuestTokenRequest $request)
    {
        $data = $request->transform();
        
        DB::beginTransaction();
        try {
            ResellerToken::updateOrCreate(['customer_id' => $data['customer_id']],['auth_device_token' => $data['auth_device_token']]);
            DB::commit();

            return $this->sendSuccess('Generate guest token success',$data);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError('Generate guest token failed : '.$e->getMessage().', at line : '.$e->getLine());
        }
    }

    public function registerCustomerReseller(RegisterCustomerResellerRequest $request)
    {
        $user = $request->transformUser();
        $customer = $request->transformCustomer();
        $customer_shipments = $request->transformCustomerShipment();
        $activation_link = $request->getActivationData();

        DB::beginTransaction();
        try {
            ResellerCustomer::create($customer);
            $user = User::create($user);
            ResellerCustomerShipment::insert($customer_shipments);
            ResellerCustomerActivation::insert($activation_link);

            $socmed = new GetSocialsRepo();
            $param['data'] = [
                'token' => $activation_link['token'],
                'email' => $customer['email'],
                
                'facebook' => $socmed->getSocialMediaParameters('FACEBOOK'),
                'linkedin' => $socmed->getSocialMediaParameters('LINKEDIN'),
                'twitter' => $socmed->getSocialMediaParameters('TWITTER'),
                'instagram' => $socmed->getSocialMediaParameters('INSTAGRAM'),
                'support' => $socmed->getSocialMediaParameters('SUPPORT')
            ];

            MailSender::dispatch($customer['email'], json_encode($param),'mail_activate_customer_reseller');

            DB::commit();
            
            return $this->sendSuccess('Register customer reseller success',[
                'user' => $user, 
                'customer' => $customer, 
                'customer_shipment' => $customer_shipments,
                'expired_activation_at' => $activation_link['expired_at']
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError('Register customer reseller failed : '.$e->getMessage().'. Line : '.$e->getLine());
        }
        
    }

    public function validate1RegCustomer(Step1RegisterCustomerResellerRequest $request)
    {
        $request->validated();
        return $this->sendSuccess('Pass');
    }

    public function validate2RegCustomer(Step2RegisterCustomerResellerRequest $request)
    {
        $request->validated();
        return $this->sendSuccess('Pass');
    }

    public function activateUserReseller(ActivateUserResellerRequest $request)
    {
        $token = $request->input('token');
        $email = $request->input('email');
        DB::beginTransaction();
        try {
            $rsl_cus = ResellerCustomer::where('email',$email)->registered()->first();
            $rsl_act = ResellerCustomerActivation::where('token',$token)->where('customer_id',$rsl_cus->id)->first();
            $activation_at = Carbon::now();
            if (empty($rsl_act)) {
                return $this->sendError('Link is not found');
            }
            if (isset($rsl_act->activation_at)) {
                return $this->sendSuccess('Your link has been activated');
            }
            if ($activation_at->gt($rsl_act->expired_at)) {
                return $this->sendError('Your link is expired');
            }
            $rsl_act->activation_at = Carbon::parse()->format('Y-m-d H:i:s');
            $rsl_act->save();
            
            ResellerCustomer::where('id',$rsl_act->customer_id)->update(['is_active' => 1]);
            $user = User::where('reference_id',$rsl_act->customer_id)->first();
            $user->is_active = 1;
            $user->save();

            $token = $user->createToken($user->reference_object.'-'.@$user->rsl_customer->id);
            DB::commit();

            return $this->sendSuccess('Account has been activated',[
                'user' => User::where('reference_id',$rsl_act->customer_id)->first()->toArray(),
                'access_token' => $token->plainTextToken
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError('User activation failed : '.$e->getMessage().'. Line : '.$e->getLine());
        }
    }

    public function resendActivateLink(ResendActivationLinkResellerRequest $request)
    {
        DB::beginTransaction();
        try {
            $data = [
                'token' => bin2hex(random_bytes(16)),
                'expired_at' => Carbon::now()->addMinutes(5)->format('Y-m-d H:i:s')
            ];
            $rsl_customer = ResellerCustomer::where('email',$request->input('email'))->registered()->first();
            ResellerCustomerActivation::where('customer_id',$rsl_customer->id)
                                        ->update($data);

            $socmed = new GetSocialsRepo();
            $param['data'] = [
                'token' => $data['token'],
                'email' => $rsl_customer['email'],
                
                'facebook' => $socmed->getSocialMediaParameters('FACEBOOK'),
                'linkedin' => $socmed->getSocialMediaParameters('LINKEDIN'),
                'twitter' => $socmed->getSocialMediaParameters('TWITTER'),
                'instagram' => $socmed->getSocialMediaParameters('INSTAGRAM'),
                'support' => $socmed->getSocialMediaParameters('SUPPORT')
            ];

            MailSender::dispatch($rsl_customer['email'], json_encode($param),'mail_activate_customer_reseller');

            DB::commit();
            
            return $this->sendSuccess('Resend activation link success',['expired_at' => $data['expired_at']]);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError('Resend activation link failed : '.$e->getMessage().' Line : '.$e->getLine());
        }
    }

    public function upsertShipment(UpsertShipmentCustResellerRequest $request)
    {
        $user = Auth::guard('sanctum')->user();
        $customer = $request->transformCustomer();
        $cs = $request->transformCustomerShipment();
        $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;

        DB::beginTransaction();
        try {
            if (!$user) {
                if (!empty($customer)) {
                    $customer = ResellerCustomer::updateOrCreate(
                        ['id' => $customer['id']],
                        ['email' => $customer['email']]
                    );
                }

                foreach ($cs as $d) {
                    ResellerCustomerShipment::updateOrCreate(
                        [
                            'customer_id' => $d['customer_id'],
                        ],
                        [
                            'name' => $d['name'],
                            'phone_number' => $d['phone_number'],
                            'address' => $d['address'],
                            'region_code' => $d['region_code'],
                            'region_name' => $d['region_name'],
                            'city_code' => $d['city_code'],
                            'city_name' => $d['city_name'],
                            'district_code' => $d['district_code'],
                            'district_name' => $d['district_name'],
                            'subdistrict_code' => $d['subdistrict_code'],
                            'subdistrict_name' => $d['subdistrict_name'],
                            'zip_code' => $d['zip_code'],
                            'is_active' => $d['is_active']
                        ]
                    );
                }
            } else {
                foreach($cs as $d){
                    if ($d['is_active'] == true) {
                        ResellerCustomerShipment::where('customer_id',$customer_id)->update(['is_active' => 0]);
                    }
                    if ($d['id'] != null) { 
                        $cs_m = ResellerCustomerShipment::where('id',$d['id'])->first();
                        $cs_m->customer_id = $d['customer_id'];
                        $cs_m->name = $d['name'];
                        $cs_m->phone_number = $d['phone_number'];
                        $cs_m->address = $d['address'];
                        $cs_m->region_code = $d['region_code'];
                        $cs_m->region_name = $d['region_name'];
                        $cs_m->city_code = $d['city_code'];
                        $cs_m->city_name = $d['city_name'];
                        $cs_m->district_code = $d['district_code'];
                        $cs_m->district_name = $d['district_name'];
                        $cs_m->subdistrict_code = $d['subdistrict_code'];
                        $cs_m->subdistrict_name = $d['subdistrict_name'];
                        $cs_m->zip_code = $d['zip_code'];
                        $cs_m->is_active = $d['is_active'];
                        $cs_m->modified_by = $d['modified_by'];
                        $cs_m->save();
                    } else {
                        ResellerCustomerShipment::create([
                            'customer_id' => $d['customer_id'],
                            'name' => $d['name'],
                            'phone_number' => $d['phone_number'],
                            'address' => $d['address'],
                            'region_code' => $d['region_code'],
                            'region_name' => $d['region_name'],
                            'city_code' => $d['city_code'],
                            'city_name' => $d['city_name'],
                            'district_code' => $d['district_code'],
                            'district_name' => $d['district_name'],
                            'subdistrict_code' => $d['subdistrict_code'],
                            'subdistrict_name' => $d['subdistrict_name'],
                            'zip_code' => $d['zip_code'],
                            'is_active' => $d['is_active'],
                        ]);
                    }
                }
            }
            
            $check = ResellerCustomerShipment::where('customer_id',$customer_id)->groupBy('is_active')->get()->pluck('is_active')->toArray();
            if (!in_array(1,$check)) {
                ResellerCustomerShipment::where('customer_id',$customer_id)->latest()->first()->update(['is_active' => 1]);
            }
            DB::commit();

            $res_cs = ResellerCustomerShipment::where('customer_id',$customer_id)->get();

            foreach ($res_cs as $d) {
                $d['city_id'] = City::where('code',$d['city_code'])->first()->id??null;
            }

            return $this->sendSuccess('Upsert shipment success',[
                'customer' => $customer??null,
                'customer_shipment' => $res_cs->toArray()
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError('Upsert shipment failed : '.$e->getMessage().', Line : '.$e->getLine());
        }
    }

    public function getShipment(GetShipmentCustResellerRequest $request)
    {
        $cs = ResellerCustomerShipment::where('customer_id',$request->query('customer_id'))
                                        ->orderBy('modified_date','desc')->get();

        foreach ($cs as $d) {
            $d['city_id'] = City::where('code',$d['city_code'])->first()->id??null;
        }
        
        return $this->sendSuccess('Get customer shipment success',$cs->toArray());
    }

    public function deleteShipment(DeleteShipmentCustResellerRequest $request)
    {
        DB::beginTransaction();
        try {
            $cs = ResellerCustomerShipment::where('id',$request->customer_shipment_id)->first();
            $cs->delete();
            DB::commit();

            return $this->sendSuccess('Delete customer shipment success',$cs);
        } catch (\Exception $e) {
            DB::rollback();
            return $this->sendError('Delete customer shipment failed : '.$e->getMessage().'. Line : '.$e->getLine());
        }
    }

    public function getProfileResellerForCustomer(GetProfileResellerRequest $request)
    {
        $getProfile = Reseller::where('id', "=", $request->reseller_id)
                                ->withCount(['orders' => fn($q) 
                                    => $q->whereNotIn('order_status',[OrderReseller::ORDER_RESELLER_BATAL,OrderReseller::ORDER_RESELLER_PENDING,OrderReseller::ORDER_RESELLER_MENUNGGU_PEMBAYARAN,OrderReseller::ORDER_RESELLER_PENGEMBALIAN])])
                                ->with(['orders' => fn($q) 
                                    => $q->whereNotIn('order_status',[OrderReseller::ORDER_RESELLER_BATAL,OrderReseller::ORDER_RESELLER_PENDING,OrderReseller::ORDER_RESELLER_MENUNGGU_PEMBAYARAN,OrderReseller::ORDER_RESELLER_PENGEMBALIAN])->withSum('items','qty')])
                                ->first();
        // dd($getProfile->toArray());
        return $this->sendSuccess('Get profile reseller success',ProfileResellerForCustomer::make($getProfile));
    }

    public function getProfileCustomer(Request $request)
    {
        $customer = ResellerCustomer::where('id',$request->user()->reference_id)->first();

        return $this->sendSuccess('Get profile customer success',GetProfileCustomerResellerResource::make($customer));
    }

    public function changePasswordCustomer(ChangePasswordCustomerResellerRequest $request)
    {   
        $user = $request->updateUserData();
        DB::beginTransaction();
        try {
            $request->user()->update($user);
            DB::commit();

            return $this->sendSuccess('Change user password success',$request->user());
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->sendError('Change user password failed : '.$e->getMessage().'. Line : '.$e->getLine());
        }
    }
}
