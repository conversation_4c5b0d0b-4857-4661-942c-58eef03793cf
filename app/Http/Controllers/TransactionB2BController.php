<?php

namespace App\Http\Controllers;

use App\Events\GenericEvent;
use App\Helpers\FileHelper;
use App\Models\Customer;
use Carbon\Carbon;
use App\Models\Order;
use App\Models\Product;
use App\Models\OrderItem;
use App\Models\Transaction;
use App\Models\ImageGeneric;
use App\Models\ImageVariant;
use Illuminate\Http\Request;
use App\Models\DeliveryOrder;
use App\Models\TransactionItem;
use App\Models\CustomerShipment;
use App\Repositories\GetLimitRepo;
use App\Models\DeliveryOrderDetail;
use App\Models\Invoice;
use App\Models\OrderApproval;
use App\Models\OrderCustom;
use App\Models\OrderCustomAttachment;
use App\Models\Proforma;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class TransactionB2BController extends Controller
{
    use FileHelper;

    const OrderStatusWait = 'Menunggu Konfirmasi';
    const OrderStatusPending = 'Pending';
    const OrderStatusOnHold = 'On Hold';
    const OrderStatusOnProcess = 'Diproses';
    const OrderStatusBaru = 'Baru';
    const OrderStatusGI = 'Siap Dikirim';
    const OrderStatusOnShipping = 'Dikirim';
    const OrderStatusDelivered = 'Diterima';
    const OrderStatusFinish = 'Selesai';
    const OrderStatusCancel = 'Batal';
    const OrderStatusPembayaran = 'Pembayaran';
    const OrderStatusSemua = 'Semua';
    const OrderStatusVerif = 'Menunggu Verifikasi';

    public function getTransactions(Request $request){
        $cari = $request->input('text');
        $date_from = $request->input('date_from');
        $date_to = $request->input('date_to');
        $status = $request->input('status');
        $type = $request->input('type');
        $page = $request->input('page');
        $per_page = $request->input('per_page');
        $customer_id = auth()->user()->customer->customer_id;
        
        if($status == 'Semua' || $status == null || $status == 'Dikirim' || $status == 'Diproses' || $status == 'Pembayaran'){
            $orderParams = Order::whereIn('distribution_channel', ['B2B', 'W3', 'RE', 'RD'])->where('customer_id',$customer_id)->whereIn('order_status', ['Dikirim', "Diproses", "Pembayaran"])->get();
            $invoice = Invoice::whereIn('sales_order_no', $orderParams->pluck('sales_order_no')->toArray())->where('invoice_type', 'BILLING')->get();
            $do = DeliveryOrder::whereIn('sales_order_no', $orderParams->pluck('sales_order_no')->toArray())->get();

            $statusMap = [];

            foreach ($orderParams as $order) {
                $salesOrderNo = $order->sales_order_no;
                $isPaid = $invoice->firstWhere('sales_order_no', $salesOrderNo)?->status === 'LUNAS';
                $doForOrder = $do->where('sales_order_no', $salesOrderNo);
            
                if ($order->order_status === 'Pembayaran' && $isPaid) {
                    $gi = $doForOrder->firstWhere('good_issue_date', '!=', null);
                    if ($gi) {
                        $statusMap[$order->order_no] = 'Dikirim';
                    } elseif ($doForOrder->isNotEmpty()) {
                        $statusMap[$order->order_no] = 'Diproses';
                    }
                }
            
                if (
                    in_array($order->order_status, ['Dikirim', 'Diproses']) &&
                    !$isPaid
                ) {
                    $statusMap[$order->order_no] = 'Pembayaran';
                }
            }
            
            if (!empty($statusMap)) {
                $cases = '';
                $quotedOrderNos = [];
            
                foreach ($statusMap as $orderNo => $status) {
                    $escapedOrderNo = addslashes($orderNo);
                    $escapedStatus = addslashes($status);
                    $cases .= "WHEN '{$escapedOrderNo}' THEN '{$escapedStatus}' ";
                    $quotedOrderNos[] = "'{$escapedOrderNo}'";
                }
            
                $orderNoList = implode(',', $quotedOrderNos);
            
                DB::statement("
                    UPDATE order_header
                    SET order_status = CASE order_no
                        {$cases}
                    END
                    WHERE order_no IN ({$orderNoList})
                ");
            }
        }

        $orders = Order::whereIn('distribution_channel', ['B2B', 'W3', 'RE', 'RD'])
            ->where('customer_id',$customer_id)
            ->when($cari, function ($q) use ($cari){
                $q->where('order_no','LIKE','%'.$cari.'%');
            })
            ->when($date_to, function ($q) use ($date_to){
                $date_to = Carbon::parse($date_to)->format('Y-m-d H:i:s');
                $q->whereDate('created_date','<=',$date_to);
            })
            ->when($date_from, function ($q) use ($date_from){
                $date_from = Carbon::parse($date_from)->format('Y-m-d H:i:s');
                $q->whereDate('created_date','>=',$date_from);
            })
            ->when(in_array($type, ['custom', 'regular']), function ($q) use ($type) {
                $orderNos = $type === 'custom'
                    ? OrderCustom::pluck('reference_id')
                    : OrderItem::pluck('order_no');

                $q->whereIn('order_header.order_no', $orderNos);
            })
            ->when($status, function ($q) use ($status){
                if ($status == 'Semua') {
                    $q->whereIn('order_status',['Pending', 'On Hold', 'Menunggu Verifikasi', 'Menunggu Konfirmasi', 'Pembayaran', 'Diproses', 'Siap Dikirim', 'Dikirim', 'Selesai', 'Batal']);
                } else {
                    $q->where('order_status',$status);
                }
            })
            ->orderBy('created_date','desc')
            ->paginate($per_page ?? 12, ['*'], 'page', $page);
        
        $data_order = [];
        $i = 0;

        $total_product = fn(array $arr) => array_reduce($arr, function($total, $order_items)
            {
                return $total += $order_items['qty'] ?? 0;
            }, 0);
        
       
        $invoice = Invoice::whereIn('sales_order_no', $orders->pluck('sales_order_no'))->where('invoice_type', 'BILLING')->get();
            
        foreach ($orders as $order) {
            if ($type == 'regular') {
                $orderItems = $order->items;
                $image = ImageGeneric::where('sku_code_c',@$orderItems[0]->product->sku_code_c)
                    ->where('is_main_image',1)->where('sequence_no',0)->first();
            } else {
                $orderItems = $order->custom;
                $image = ImageGeneric::where('sku_code_c',@$orderItems[0]->article->sku_code_c)
                    ->where('is_main_image',1)->where('sequence_no',0)->first();
            }

            // if(isset($orderItems[0]->attachment) && count($orderItems[0]->attachment) < 1){
            //     $orderItems[0]->attachment = $orderItems[0]->attachmentSku;
            // }
        
            $isPaid = $invoice->where('sales_order_no', $order->sales_order_no)->where('status', 'LUNAS')->first();
            
            // if($order->order_status == 'Pembayaran' && $isPaid){
            //     $gi = $do->where('sales_order_no', $order->sales_order_no)->where('good_issue_date', '!=', null)->first();
            //     if($gi){
            //         $order->update(['order_status' =>'Dikirim']);
            //     }

            //     if(empty($gi) && $do->where('sales_order_no', $order->sales_order_no)->first()){
            //         $order->update(['order_status' =>'Diproses']);
            //     }
            // }
            
            // if(($order->order_status == 'Dikirim' || $order->order_status == 'Diproses') && empty($isPaid)){
            //     $order->update(['order_status' =>'Pembayaran']);
            // }

            $data_order[$i] = [
                'order_no' => $order->order_no,
                'order_group_id' => $order->order_group_id,
                'order_status' => ($order->order_status == 'Dikirim' || $order->order_status == 'Diproses') && empty($isPaid) ? 'Pembayaran' : $order->order_status,
                'order_date' => Carbon::parse($order->created_date)->format('Y-m-d H:i:s'),
                'total_produk' => $total_product($orderItems->all()) ?? 0,
                'nama_produk' => ($type == "regular" ? @$orderItems[0]->product_name : @$orderItems[0]->article->product_name_c) ?? '-',
                'image' => @$image->file_path == null ? null : env('S3_STREAM_URL').$image->file_path,
                'total_pesanan' => $order->total_nett,
                'custom_type' =>  $type == "custom" ? ($orderItems[0]->attachment[0]->custom_type ?? '-') : '-',
            ];

            if ($type == 'custom') {
                $cartAttachments =  isset($orderItems[0]->attachment[0]) ? $orderItems[0]->attachment : $orderItems[0]->attachmentSku;
                $mappedAttachments = collect($cartAttachments)->map(function ($item) {
                    return [
                        'id' => (string) $item->id,
                        'attachments_group_id' => $item->order_custom_id,
                        'file_path' => $item->file_path ?? null,
                        'text' => $item->custom_text ?? null,
                        'estimate_price' => $item->custom_price ?? 0,
                    ];
                })->toArray();;
                $data_order[$i]['cart_attachments'] = $mappedAttachments;
            }
            $i++;
        }

        // foreach ($orders as $order) {
        //     // dd($order->items[0]->article_id);

        //     if ($order->items->isEmpty()) {
        //         $image = null;
        //     } else {
        //         $image = ProductImage::where('article',$order->items[0]->article_id)
        //                         ->where('is_thumbnail',1)->where('sequence_no',1 )->first();
        //     }

        //     if ($order->order_status == 'Pending' || $order->order_status == 'Baru' || $order->order_status == 'On Hold') {
        //         $data_order[$i] = [
        //             'order_no' => $order->order_no,
        //             'order_status' => $order->order_status,
        //             'order_date' => $order->order_date,
        //             'total_produk' => count($order->items),
        //             'image' => !is_null($image) ? $image->url : null,
        //             'total_pesanan' => $order->total_nett,
        //         ];
        //         $i++;
        //     }  else {
        //         $delivery_orders = DeliveryOrder::where('sales_order_no',$order->sales_order_no)->get();

        //         if ($delivery_orders->isEmpty()) {
        //             $data_order[$i] = [
        //                 'order_no' => $order->order_no,
        //                 'order_status' => $order->order_status,
        //                 'order_date' => $order->order_date,
        //                 'total_produk' => count($order->items),
        //                 'image' => !is_null($image) ? $image->url : null,
        //                 'total_pesanan' => $order->total_nett,
        //             ];
        //             $i++;
        //         }

        //         foreach ($delivery_orders as $delivery_order) {
        //             $do_details = DeliveryOrderDetail::where('delivery_order_no',$delivery_order->delivery_order_no)->get();
        //             $data_order[$i] = [
        //                 'order_no' => $order->order_no,
        //                 'order_status' => $order->order_status,
        //                 'order_date' => $order->order_date,
        //                 'total_produk' => count($do_details),
        //                 'image' => !is_null($image) ? $image->url : null,
        //                 'total_pesanan' => $delivery_order->total,
        //             ];
        //             $i++;
        //         }
        //     }
            
        // }

        $res_order = [
            'total_data' => $orders->total(),
            'size' => intval($orders->perPage()),
            'active_page' => $orders->currentPage(),
            'total_page' => $orders->lastPage(),
            'data' => $data_order
        ];

        return $this->sendSuccess('Data successfully retrieved', $res_order);
    }
    
    public function getTransactionDetails(Request $request, $order_no){
        $page = !empty($request->input('page')) ? (int) $request->input('page') : 1;
        $per_page = $request->input('limit') ?? 12;

        $order = Order::find($order_no);
        $nomorresi = [];

        if ($order == null) {
            return $this->sendError("Data transaction not found", 404);
        }

        $orderNos = OrderItem::pluck('order_no')->toArray();
        if (in_array($order_no, $orderNos)) {
            // $order_detail = OrderItem::where('order_no', $order_no)->get();
            $order_detail = OrderItem::leftJoin('article', 'order_detail.article_id', '=', 'article.article')
                    ->select('order_detail.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_variant_c')
                    ->where('order_no', $order->order_no)->orderBy('order_detail.order_detail_id', 'desc')
                    ->get();
            $customCheck = false;
        } else {
            // $order_detail = OrderCustom::leftJoin('article', 'order_custom.article_id', '=', 'article.article')
            //     ->leftJoin('article_price', 'order_custom.sku', '=', 'article_price.sku_code_c')
            //     ->select('order_custom.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_size_c', 'article_price.amount')
            //     ->where('reference_id', $order_no)
            //     ->get();
            $order_detail = OrderCustom::leftJoin('article', 'order_custom.article_id', '=', 'article.article')
                ->select('order_custom.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_variant_c', 'article.product_size_c')
                ->where('reference_id', $order->order_no)->orderBy('order_custom.id', 'desc')
                ->get();

            $customCheck = true;
        }

        $customer = DB::table('customers')->where('customer_id', $order->customer_id)->first();

        //checking order custom
        // $orderCustom = Order::join('order_custom', 'order_custom.reference_id', '=', 'order_header.order_no')
        //     ->select('order_header.order_no')
        //     ->get()->toArray();
        // $customCheck = false;
        // foreach ($orderCustom as $orderitems) {
        //     if ($orderitems['order_no'] === $order->order_no) {
        //         $customCheck = true;
        //         break;
        //     }
        // }

        $do = DeliveryOrder::where('sales_order_no', $order->sales_order_no)->first();

        $product_items = [];
        $items = [];
        $total_qty = 0;
        $sub_total = 0;
        $total_kustomisasi = 0;
        if ($do != null) {
            $do_items = $do->items;
            $do_discount = $do->good_issue_date != null ? $do->issued_discount : $do->discount;
            // $stock_collection = RestHelper::stockCache($item->pluck('article')->toArray(), $request->user()->sales->sales_id);
            foreach ($do_items as $item) {
                DB::connection()->enableQueryLog();
                // $stock = RestHelper::searchStock($stock_collection, $item[$i]->article);
                if ($customCheck) {
                    $image = DB::table('article as art')
                        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                        ->where('art.article', $item->article)
                        ->where('ig.is_main_image', 1)
                        ->select('ig.file_path')
                        ->first();
                
                    $warna = DB::table('master_color')
                        ->where('key', $item->product_variant)
                        ->where('is_active', 1)
                        ->pluck('value')->first();
                    
                    $article = Product::where('article', $item->article)->first();

                    $ocs = OrderCustom::where('reference_id', $order->order_no)
                        ->where('sku', $article->sku_code_c)
                        ->first();

                    $custom_price = OrderCustomAttachment::where('order_custom_id', $ocs->attachment_group_id)->sum('custom_price');
                    
                    $customs = [];
                    $cartAttachments = [];
                    $ocas = OrderCustomAttachment::where('order_custom_id', $ocs->attachment_group_id)->get();
                    $logoQty = 0;
                    $textQty = 0;
                    foreach ($ocas as $oca) {
                        $customs[] = [
                            'custom_type' => $oca->custom_type,
                            'harga_satuan' => (int)$oca->custom_price
                        ];

                        $logoQty += $oca->file_path != null ? 1 : 0;
                        $textQty += $oca->custom_text != null ? 1 : 0;


                        $cartAttachments[] = [
                            'id' => $oca->id,
                            'attachments_group_id' => $oca->order_custom_id,
                            'file_path' => $oca->file_path,
                            'text' => $oca->custom_text,
                            'color' => $oca->color,
                            'estimate_price' => (int)$oca->custom_price
                        ];
                    }
                    
                    $attachment_qty = 0;
                    $sub_total = 0;
                    $product_items = [];

                    $itemData = new TransactionItem();
                    $itemData->article = $item->article;
                    $itemData->product_name = $item->product_name;
                    $itemData->product_variant = $warna ?? null;
                    $itemData->product_size = $item->product_size;
                    $itemData->qty = $item->qty;
                    $itemData->issued_qty = $item->issued_qty;
                    $itemData->sub_total = $do->good_issue_date == null ? (int) ($item->qty * $article->price->amount) : (int) (($item->issued_qty ?? 0) * $article->price->amount);
                    // $itemData->sub_total = $item->sub_total;
                    $itemData->is_available = $item->is_available ?? 1;
                    array_push($product_items, $itemData);

                    $sub_total += (int)((int)$item->qty * (int)$article->price->amount);
                    $total_qty += (int)$item->qty;
                    $attachment_qty += (int)$item->qty;
                    
                    $cdate = now()->format('Y-m-d');

                    $flag = [];
                    if (!empty($article)) {
                        if ($article->transfer_date <= $cdate && $article->expired_date >= $cdate) {
                            array_push($flag, 'NEW');
                        }
                        if ($article->is_custom_size || $article->is_custom_logo) {
                            array_push($flag, 'CUSTOM');
                        }
                        array_push($flag, $article->lvl4_description);
                    }

                    $price = DB::table('article_price')
                        ->where('sku_code_c', $article->sku_code_c)
                        ->where('valid_from', '<=', $cdate)
                        ->where('valid_to', '>=', $cdate)
                        ->orderBy('valid_from', 'desc')
                        ->select('amount')
                        ->first();
                    
                    $customs = collect($customs);
                    
                    $productItem = [
                        'sku_code_c' => $article->sku_code_c,
                        'attachment_group_id' => $ocs->attachment_group_id,
                        'product_name_c' => $article->product_name_c,
                        'image_url' => @$image->file_path ? env('S3_STREAM_URL') . $image->file_path : null,
                        'flag' => $flag,
                        'base_price' => $article->price->amount,
                        'total_custom_logo' => $logoQty * ($customs->firstWhere('custom_type', 'Logo')['harga_satuan'] ?? 0),
                        'total_custom_text' => $textQty * ($customs->firstWhere('custom_type', 'Text')['harga_satuan'] ?? 0),
                        'sub_total' => $sub_total,
                        'custom_type' => $ocs->attachment->pluck('custom_type')->filter()->implode(' + ') ?: null,
                        'customs' => $customs,
                        'cart_attachments' => $cartAttachments,
                        'product_items' => $product_items
                    ];
                
                    if (in_array($order->order_status, ['Pending', 'On Hold', 'Menunggu Konfirmasi', 'Baru'])) {
                        $productItem['base_price'] = $price->amount;
                    }
                    
                    array_push($items, $productItem);
                    $total_kustomisasi += (int)$custom_price * (int)$attachment_qty;
                } else {
                    $image = DB::table('article as art')
                        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                        ->where('art.article', $item->article)
                        ->where('ig.is_main_image', 1)
                        ->select('ig.file_path')
                        ->first();
        
                    $article = Product::where('article', $item->article)->first();
                    $warna = DB::table('master_color')
                        ->where('key', $item->product_variant)
                        ->where('is_active', 1)
                        ->pluck('value')->first();

                    $sub_total = 0;
                    $product_items = [];
                    $itemData = new TransactionItem();
                    $itemData->article = $item->article;
                    $itemData->product_name = $item->product_name;
                    $itemData->product_variant = $warna ?? null;
                    $itemData->product_size = $item->product_size;
                    $itemData->qty = $item->qty;
                    $itemData->issued_qty = $item->issued_qty;
                    $itemData->sub_total = $do->good_issue_date == null ? (int) ($item->qty * $article->price->amount) : (int) (($item->issued_qty ?? 0) * $article->price->amount);
                    // $itemData->sub_total = $item->sub_total;
                    $itemData->is_available = $item->is_available ?? 1;
                    array_push($product_items, $itemData);
                    
                    $sub_total += (int)((int)$item->qty * (int)$article->price->amount);
                    $total_qty += (int)$item->qty;

                    $cdate = now()->format('Y-m-d');
                        
                    $flag = [];
                    if (!empty($article)) {
                        if ($article->transfer_date <= $cdate && $article->expired_date >= $cdate) {
                            array_push($flag, 'NEW');
                        }
                        if ($article->is_custom_size || $article->is_custom_logo) {
                            array_push($flag, 'CUSTOM');
                        }
                        array_push($flag, $article->lvl4_description);
                    }
                    
                    $price = DB::table('article_price')
                        ->where('article_price.sku_code_c', $article->sku_code_c)
                        ->where('valid_from', '<=', $cdate)
                        ->where('valid_to', '>=', $cdate)
                        ->orderBy('valid_from', 'desc')
                        ->select('article_price.amount')
                        ->first();
                    
                    $productItem = [
                        'sku_code_c' => $article->sku_code_c,
                        'product_name_c' => $article->product_name_c,
                        "image_url" => @$image->file_path == null ? null : env('S3_STREAM_URL') . $image->file_path,
                        'flag' => $flag,
                        'base_price' => $article->price->amount,
                        'sub_total' => $sub_total,
                        // 'customs' => [],
                        'product_items' => $product_items
                    ];

                    if (in_array($order->order_status, ['Pending', 'On Hold', 'Menunggu Konfirmasi', 'Baru'])) {
                        $productItem['base_price'] = $price->amount ?? 0;
                    }

                    array_push($items, $productItem);
                }
            }

            $counts = array_reduce($items, function ($carry, $item) {
                foreach ($item['product_items'] as $product) {
                    $carry[$product['is_available'] ? 'available' : 'unavailable']++;
                }
                return $carry;
            }, ['available' => 0, 'unavailable' => 0]);

            $total_available = $counts['available'];
            $total_unavailable = $counts['unavailable'];

            // $isAvailable = filter_var($request->query('is_available', true), FILTER_VALIDATE_BOOLEAN);
            // $isAvailableInt = $isAvailable ? 1 : 0;
            
            // $filteredItems = array_map(function ($item) use ($isAvailableInt) {
            //     $filteredProductItems = array_filter($item['product_items'], fn($product) => 
            //         $product['is_available'] === $isAvailableInt
            //     );

            //     if (empty($filteredProductItems)) {
            //         return null;
            //     }

            //     $item['product_items'] = array_values($filteredProductItems);
            //     return $item;
            // }, $items);

            // $filteredItems = array_values(array_filter($filteredItems));
            $allItems[] = $items;
        } else {
            // $stock_collection = RestHelper::stockCache($order_detail->pluck('article_id')->toArray(), $request->user()->sales->sales_id);
            if (!$customCheck) {
                foreach ($order_detail as $item) {
                    DB::connection()->enableQueryLog();
                    // $stock = RestHelper::searchStock($stock_collection, $order_detail[$i]->article_id);
                    $image = DB::table('article as art')
                    ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                    ->where('art.article', $item->article_id)
                    ->where('ig.is_main_image', 1)
                    ->select('ig.file_path')
                    ->first();
    
                    $article = Product::where('article', $item->article_id)->first();
                    $warna = DB::table('master_color')
                        ->where('key', $item->product_variant_c)
                        ->where('is_active', 1)
                        ->pluck('value')->first();

                    $sub_total = 0;
                    $product_items = [];
                    $itemData = new TransactionItem();
                    $itemData->article = $item->article_id;
                    $itemData->product_name = $item->product_name;
                    $itemData->product_variant = $warna ?? null;
                    $itemData->product_size = $item->product_size;
                    $itemData->qty = $item->qty;
                    $itemData->sub_total = (int) ($item->qty * $article->price->amount);
                    $itemData->is_available = $item->is_available;
                    array_push($product_items, $itemData);
                    
                    $sub_total += (int)((int)$item->qty * (int)$article->price->amount);
                    $total_qty += (int)$item->qty;
                
                    $cdate = now()->format('Y-m-d');
                    
                    $flag = [];
                    if (!empty($article)) {
                        if ($article->transfer_date <= $cdate && $article->expired_date >= $cdate) {
                            array_push($flag, 'NEW');
                        }
                        if ($article->is_custom_size || $article->is_custom_logo) {
                            array_push($flag, 'CUSTOM');
                        }
                        array_push($flag, $article->lvl4_description);
                    }
                    
                    $price = DB::table('article_price')
                        ->where('article_price.sku_code_c', $item->sku_code_c)
                        ->where('valid_from', '<=', $cdate)
                        ->where('valid_to', '>=', $cdate)
                        ->orderBy('valid_from', 'desc')
                        ->select('article_price.amount')
                        ->first();
                        
                    $productItem = [
                        'sku_code_c' => $item->sku_code_c,
                        'product_name_c' => $item->product_name_c,
                        "image_url" => @$image->file_path == null ? null : env('S3_STREAM_URL') . $image->file_path,
                        'flag' => $flag,
                        'base_price' => $article->price->amount,
                        'sub_total' => $sub_total,
                        // 'customs' => [],
                        'product_items' => $product_items
                    ];
                    if (in_array($order->order_status, ['Pending', 'On Hold', 'Menunggu Konfirmasi', 'Baru'])) {
                        $productItem['base_price'] = $price->amount;
                    }
                    array_push($items, $productItem);
                }

                $counts = array_reduce($items, function ($carry, $item) {
                    foreach ($item['product_items'] as $product) {
                        $carry[$product['is_available'] ? 'available' : 'unavailable']++;
                    }
                    return $carry;
                }, ['available' => 0, 'unavailable' => 0]);

                $total_available = $counts['available'];
                $total_unavailable = $counts['unavailable'];

                $isAvailable = filter_var($request->query('is_available', true), FILTER_VALIDATE_BOOLEAN);
                $isAvailableInt = $isAvailable ? 1 : 0;
                
                $filteredItems = array_map(function ($item) use ($isAvailableInt) {
                    $filteredProductItems = array_filter($item['product_items'], fn($product) => 
                        $product['is_available'] === $isAvailableInt
                    );

                    if (empty($filteredProductItems)) {
                        return null;
                    }

                    $item['product_items'] = array_values($filteredProductItems);
                    return $item;
                }, $items);

                $filteredItems = array_values(array_filter($filteredItems));
                $allItems[] = $filteredItems;
            } else {
                foreach ($order_detail->groupBy('attachment_group_id') as $groupId => $groupItems) {
                    DB::connection()->enableQueryLog();
                    // $stock = RestHelper::searchStock($stock_collection, $order_detail[$i]->article_id);
                    $firstItem = $groupItems->first();
                    
                    $image = DB::table('article as art')
                        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                        ->where('art.article', $firstItem->article_id)
                        ->where('ig.is_main_image', 1)
                        ->select('ig.file_path')
                        ->first();
                
                    $warna = DB::table('master_color')
                        ->where('key', $firstItem->product_variant_c)
                        ->where('is_active', 1)
                        ->pluck('value')->first();
                    
                    $article = Product::where('article', $firstItem->article_id)->first();
                
                    $ocs = OrderCustom::where('reference_id', $order->order_no)
                        ->where('sku', $article->sku_code_c)
                        ->where('attachment_group_id', $groupId)
                        ->pluck('attachment_group_id')->first();

                    $custom_price = OrderCustomAttachment::where('order_custom_id', $ocs)->sum('custom_price');
                    
                    $customs = [];
                    $cartAttachments = [];
                    $ocas = OrderCustomAttachment::where('order_custom_id', $ocs)->get();
                    $logoQty = 0;
                    $textQty = 0;
                    foreach ($ocas as $oca) {
                        $customs[] = [
                            'custom_type' => $oca->custom_type,
                            'harga_satuan' => (int)$oca->custom_price
                        ];

                        $logoQty += $oca->file_path != null ? 1 : 0;
                        $textQty += $oca->custom_text != null ? 1 : 0;
                        
                        $cartAttachments[] = [
                            'id' => $oca->id,
                            'attachments_group_id' => $oca->order_custom_id,
                            'file_path' => $oca->file_path,
                            'text' => $oca->custom_text,
                            'estimate_price' => (int)$oca->custom_price
                        ];
                    }
                    
                    $sub_total = 0;
                    $product_items = [];
                    foreach ($groupItems->groupBy('id') as $itemGroup) {
                        $item = $itemGroup->first();
                        $itemData = new TransactionItem();
                        $itemData->article = $item->article_id;
                        $itemData->product_name = $item->product_name_c;
                        $itemData->product_variant = $warna ?? null;
                        $itemData->product_size = $item->product_size_c;
                        $itemData->qty = $item->qty;
                        $itemData->sub_total += (int)((int)$item->qty * (int)$article->price->amount);
                        $itemData->is_available = $item->is_available ?? 1;
                        array_push($product_items, $itemData);

                        $sub_total += (int)((int)$item->qty * (int)$article->price->amount);
                        $total_qty += (int)$item->qty;
                    }
                    
                    $cdate = now()->format('Y-m-d');

                    $flag = [];
                    if (!empty($article)) {
                        if ($article->transfer_date <= $cdate && $article->expired_date >= $cdate) {
                            array_push($flag, 'NEW');
                        }
                        if ($article->is_custom_size || $article->is_custom_logo) {
                            array_push($flag, 'CUSTOM');
                        }
                        array_push($flag, $article->lvl4_description);
                    }

                    $price = DB::table('article_price')
                        ->where('sku_code_c', $firstItem->sku_code_c)
                        ->where('valid_from', '<=', $cdate)
                        ->where('valid_to', '>=', $cdate)
                        ->orderBy('valid_from', 'desc')
                        ->select('amount')
                        ->first();

                    $productItem = [
                        'sku_code_c' => $firstItem->sku_code_c,
                        'total_custom_logo' => $logoQty * (collect($customs)->firstWhere('custom_type', 'Logo')['harga_satuan'] ?? 0),
                        'total_custom_text' => $textQty * (collect($customs)->firstWhere('custom_type', 'Text')['harga_satuan'] ?? 0),
                        'attachment_group_id' => $groupId,
                        'product_name_c' => $firstItem->product_name_c,
                        'image_url' => @$image->file_path ? env('S3_STREAM_URL') . $image->file_path : null,
                        'flag' => $flag,
                        'base_price' => $article->price->amount,
                        'sub_total' => $sub_total,
                        'custom_type' => $item->attachmentSku->pluck('custom_type')->filter()->implode(' + ') ?: null,
                        'customs' => $customs,
                        'cart_attachments' => $cartAttachments,
                        'product_items' => $product_items
                    ];
                
                    if (in_array($order->order_status, ['Pending', 'On Hold', 'Menunggu Konfirmasi', 'Baru'])) {
                        $productItem['base_price'] = $price->amount;
                    }
                    
                    array_push($items, $productItem);
                    $total_kustomisasi += (int)$custom_price * (int)$groupItems->sum('qty');
                }

                $counts = array_reduce($items, function ($carry, $item) {
                    foreach ($item['product_items'] as $product) {
                        $carry[$product['is_available'] ? 'available' : 'unavailable']++;
                    }
                    return $carry;
                }, ['available' => 0, 'unavailable' => 0]);

                $total_available = $counts['available'];
                $total_unavailable = $counts['unavailable'];
                $allItems[] = $items;
            }
        }    
        
        //grouping array
        $groupedItems = [];
        if(!$customCheck) {
            foreach ($allItems as $item) {
                foreach ($item as $subitem) {
                    if (!isset($groupedItems[$subitem['sku_code_c']])) {
                        $groupedItems[$subitem['sku_code_c']] = [
                            'sku_code_c' => $subitem['sku_code_c'],
                            'product_name_c' => $subitem['product_name_c'],
                            "image_url" => $subitem['image_url'],
                            'flag' => $subitem['flag'],
                            'base_price' => $subitem['base_price'],
                            'sub_total' => 0,
                            // 'customs' => $subitem['customs'],
                            'product_items' => []
                        ];
                    }
                    $groupedItems[$subitem['sku_code_c']]['product_items'] = array_merge($groupedItems[$subitem['sku_code_c']]['product_items'], $subitem['product_items']);
                }
            }
        } else{
            foreach ($allItems as $itemGroup) {
                foreach ($itemGroup as $subitem) {
                    $groupKey = $subitem['sku_code_c'] . '|' . $subitem['attachment_group_id'];
            
                    if (!isset($groupedItems[$groupKey])) {
                        $groupedItems[$groupKey] = [
                            'sku_code_c' => $subitem['sku_code_c'],
                            'attachment_group_id' => $subitem['attachment_group_id'],
                            'product_name_c' => $subitem['product_name_c'],
                            'image_url' => $subitem['image_url'],
                            'flag' => $subitem['flag'],
                            'base_price' => $subitem['base_price'],
                            'sub_total' => 0,
                            'custom_type' => $subitem['custom_type'] ?? null,
                            'customs' => $subitem['customs'],
                            'cart_attachments' => $subitem['cart_attachments'],
                            'product_items' => [],
                        ];
                    }
            
                    $groupedItems[$groupKey]['product_items'] = array_merge(
                        $groupedItems[$groupKey]['product_items'],
                        $subitem['product_items']
                    );
                }
            }
        }
        
        foreach ($groupedItems as &$groupedItem) {
            foreach ($groupedItem['product_items'] as $productItem) {
                $groupedItem['sub_total'] += $productItem['sub_total'];
            }
        }

        $groupedItems = array_values($groupedItems);
        $total = count($groupedItems);
        $total_pages = ceil($total / $per_page);
        $page = max($page, 1);
        $page = min($page, $total_pages);
        $offset = ($page - 1) * $per_page;
        if ($offset < 0) {
            $offset = 0;
        }
        $groupedItems = array_slice($groupedItems, $offset, $per_page);

        $customer_shipment = $order->customer->shipments()
            ->select('customer_shipment_id', 'customer_id', 'name', 'address', 'city', 'province', 'district', 'zip_code', 'shipment_type', 'phone_number')
            ->get();

        $DeliveryOrder = DB::table('delivery_order')
            ->leftJoin('order_header', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
            ->leftJoin('invoice', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
            ->leftJoin('proforma_invoice', 'order_header.sales_order_no', '=', 'proforma_invoice.sales_order_no')
            ->where('delivery_order.sales_order_no', $order->sales_order_no)
            ->select('delivery_order.*', 'invoice.invoice_no', 'proforma_invoice.due_date as payment_date', 'invoice.status as payment_status')
            ->first();

        if ($DeliveryOrder != null and $DeliveryOrder->invoice_no != null) {
            $nomorresi = DB::table('delivery_number')
                ->leftJoin('invoice', 'invoice.invoice_no', '=', 'delivery_number.invoice_no')
                ->where('invoice.invoice_no', $DeliveryOrder->invoice_no)
                ->select('delivery_number.delivery_no as noresi', 'delivery_number.delivery_name as namaresi')
                ->get()
                ->toArray();
        }

        $data_dp = Invoice::where('sales_order_no', $order->sales_order_no)->where('invoice_type', 'DOWN PAYMENT')->first();
        $data_bill = Invoice::where('sales_order_no', $order->sales_order_no)->where('invoice_type', 'BILLING')->first();
        $isPaid = Invoice::where('sales_order_no', $order->sales_order_no)->where('invoice_type', 'BILLING')->where('status', 'LUNAS')->first();
        $discount = $do != null ? $do_discount : $order->total_discount;
        $discount_percentage = $discount != 0 && $sub_total != 0 ? round(($discount / ($sub_total + $total_kustomisasi)) * 100, 2) : 0;
        $location_code = $order->location_code ?? null;
        $location_name = $order->location_name ?? null;
        $shipping_charges = $order->shipping_charges ?? 0;
        $envValue = env('S3_STREAM_URL');
        
        $data_detail = [
            'verifikasi_date' => OrderApproval::where('order_no', $order->order_no)->where('status', 'Approved')->pluck('action_date')->first() ?? null,
            'pembayaran_date' =>  empty($isPaid) ? null : (Invoice::where('sales_order_no', $order->sales_order_no)->pluck('created_date')->first() ?? null),
            'pembayaran_dp_date' => Invoice::where('sales_order_no', $order->sales_order_no)->where('invoice_type', 'DOWN PAYMENT')->pluck('created_date')->first() ?? null,
            'pesanan_diproses_date' => empty($isPaid) ? null : ($DeliveryOrder->created_date ?? null),
            'gi_date' => empty($isPaid) ? null : ($DeliveryOrder->good_issue_date ?? null),
            'pesanan_dikirim_date' => empty($isPaid) ? null  : ($DeliveryOrder->good_issue_date ?? null),
            'pesanan_diterima_date' => $order->completed_date ?? null,
            'pesanan_dibatalkan_date' => $order->status == 'Batal' ? $order->modified_date : null,
            'order_no' => $order->order_no,
            'order_status' => ($order->order_status == 'Diproses' || $order->order_status == 'Dikirim') && empty($isPaid) ? 'Pembayaran' : $order->order_status,
            'sales_order_no' => $order->sales_order_no ?? "",
            'no_resi' => $nomorresi,
            'invoice_no' => $data_bill->invoice_no ?? null,
            'invoice_no_dp' => empty($isPaid) ? null : ($data_dp->invoice_no ?? null),
            'delivery_number' => empty($isPaid) ? null : ($DeliveryOrder->delivery_order_no ?? ""),
            'transaction_date' => $order->created_date ?? null,
            'payment_status' => $DeliveryOrder->payment_status ?? $order->payment_status ?? null,
            'sales_name' => $order->sales_name,
            'bill_to' => $order->bill_to,
            'bill_to_address' => $order->bill_to_address,
            'bill_to_phone_number' => $order->bill_to_phone_number,
            'bill_to_email' => $order->bill_to_email,
            'ship_to' => $order->ship_to,
            'ship_to_address' => $order->ship_to_address,
            'ship_to_phone_number' => $order->ship_to_phone_number,
            // 'sub_total' => (int) $sub_total + $total_kustomisasi,
            'sub_total' => (int) $order->total,
            // 'sub_total' => (int) $order->total_nett,
            'total_discount' => (int) $discount,
            'total_discount_percentage' => $discount_percentage,
            'total_dp' => (int) $order->dp_amount,
            'dp_percentage' => $order->dp_percentage,
            'total_kustomisasi' => (int) $total_kustomisasi,
            // 'total_nett' => (int) (($sub_total + $total_kustomisasi) + $shipping_charges - $discount + $total_tax),
            'total_nett' => (int) $order->total_nett,
            // 'total_nett' => (int) ($order->total_nett + $total_kustomisasi + $shipping_charges - $discount),
            'customer_status' => $customer->status,
            'transportation_zone' => $location_code . ' - ' . $location_name,
            'pks_file_path' => $order->pks_file_path,
            'pks_file_name' => $order->pks_file_name,
            'is_custom' => $customCheck,
            'can_confirm' => strtolower($customer->status) != 'pending' ? true : false,
            'dp_due_date' => $order->dp_due_date,
            'instance_name' => $customer->instance_name,
            'shipping_charges' => (int) $shipping_charges,
            'total_qty' => $total_qty,
            'total_available' => $total_available,
            'total_unavailable' => $total_unavailable,
            'payment_file' => $order->payment_file ? $envValue . $order->payment_file : null,
            'items' => $groupedItems,
            'customer_shipment' => $customer_shipment
        ];

        $response = [
            'total_data' => $total,
            'size' => $per_page,
            'active_page' => $page,
            'total_page' => (int) $total_pages,
            'order_detail' => $data_detail
        ];

        // if (in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru, self::OrderStatusVerif])) {
        //     // $category = $order_detail[0]->product->lvl3_description == 'BAGS' ? 'BAGS' : 'NON BAGS';
        //     $m_d = DB::table('matrix_discount')
        //         ->where('is_custom', $customCheck == true ? 1 : 0)
        //         ->whereRaw('? BETWEEN min_bruto_from AND min_bruto_to', [(int) $order->total])
        //         ->whereRaw('? BETWEEN qty_from AND qty_to', [(int) $total_qty])
        //         // ->where('category', $category)
        //         ->first();

        //     $websocket = event(new GenericEvent(auth()->user()->sales->sales_id, [
        //         'total_tax' => 0,
        //         'total' => $order->total,
        //         'total_discount' => $m_d->discount * ($order->total / 100),
        //         'total_nett' => 0,
        //         'nett_before_tax' => 0
        //     ], 'simulateSO.new'));

        //     Log::info($websocket);
        // }

        return $this->sendSuccess('Transaction details retrieved successfully', $response);
    }

    public function getFlag($article)
    {
        Log::info("[GETFLAG] ArticleID ". $article);
        
        $flag = [];
        $date = now()->format('Y-m-d');
        $articleData = Product::Where('article', $article)->first();
        if(!empty($articleData)){
            if ($articleData->transfer_date <= $date && $articleData->expired_date >= $date) {
                array_push($flag, 'NEW');
            }
            if ($articleData->is_custom_logo || $articleData->is_custom_size) {
                array_push($flag, 'CUSTOM');
            }
            array_push($flag, $articleData->lvl4_description);
        }
        return $flag;
    }
    
    public function getLimitCustomer()
    {
        $custId = auth()->user()->customer->customer_id;
        $client = new GetLimitRepo();

        $request = [
                "source" => "CAREOM",
                "destination" => "PLF",
                "detailPlafond" => [
                    [
                        "destination" => "PLF",
                        "customer" => $custId
                    ]
                ]
            ];
        $dataResult =[];
        try {

                $response = $client->getLimit($request, $custId);
                $dataResult = $client->getData($response);
                // Log::channel('stderr')->info($limit);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
        
        return $dataResult;
    
    }

    public function uploadPayment($order_no, Request $request) {
        try {
            $order = Order::where('order_no', $order_no)->first();
            if (!$order) {
                return $this->sendError('Order not found', 404);
            }

            $user = $request->user();
            
            if ($user->reference_object == 'customer') {
                $custId = auth()->user()->customer->customer_id;
            } else if ($user->reference_object == 'sales') {
                $custId = $order->customer_id;
            }

            if ($order->payment_file) {
                \Storage::disk('s3')->delete(ltrim($order->payment_file, '/'));
            }

            $payment = Storage::disk('s3-public')->files('staging/payment-dev/' . $custId);
            $latestFile = collect($payment)->sortDesc()->first();
            $tf_payment_file = $this->fileTransfer($latestFile, 'payment-dev', true);
            if ($tf_payment_file['error'] == true) {
                return $this->sendError($tf_payment_file['message']);
            }

            // $request->validate([
            //     'payment_file' => 'required|file|mimes:jpg,jpeg,png,pdf|max:2048'
            // ]);

            // $file = $request->file('payment_file');
            // if (!$file) {
            //     return response()->json(['message' => 'No file uploaded'], 400);
            // }

            // $s3 = \Storage::disk('s3');
            // // $s3 = \Storage::disk('s3-public');
            // $client = $s3->getDriver()->getAdapter()->getClient();
            // $expiry = "+10 minutes";

            // // $envPrefix = app()->environment('production') ? '' : 'staging/';
            // // $key = $envPrefix . 'npwp-dev/' . $cust_id . '/' . $file->getClientOriginalName();
    
            // $cmd = $client->getCommand('PutObject', [
            //     'Bucket' => env('AWS_BUCKET_STAGING', 'bucket-public-careorder'),
            //     'Key' => 'staging/' . 'payment-dev/' . $custId . '/' . $order_no . '/' . $file->getClientOriginalName(),
            // ]);

            // $presignedRequest = $client->createPresignedRequest($cmd, $expiry);
            // $presignedUrl = (string) $presignedRequest->getUri();
            // $filePath = $cmd["Key"];

            $order->payment_file = '/' . $tf_payment_file['filepath'] ?? null;
            $order->modified_by  = Auth::user()->name;
            $order->update();

            return $this->sendSuccess('Upload payment to s3 successfully');
            
        } catch (\Exception $e) {
            Log::error('Upload error: ' . $e->getMessage());
            return response()->json([
                'message' => 'Failed to upload file to S3',
            ], 500);
        }
    }

    public function deletePayment($order_no)
    {
        try {
            $order = Order::where('order_no', $order_no)->firstOrFail();

            if ($order->payment_file) {
                \Storage::disk('s3')->delete(ltrim($order->payment_file, '/'));
                $order->payment_file = null;
                $order->modified_by = auth()->user()->name;
                $order->save();
            }

            return $this->sendSuccess('Payment file deleted successfully');
        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to delete payment file'], 500);
        }
    }    
}