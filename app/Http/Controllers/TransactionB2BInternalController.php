<?php

namespace App\Http\Controllers;


use Exception;
use Carbon\Carbon;
use App\Models\Color;
use App\Models\Order;
use App\Services\SAP;
use App\Models\Remark;
use App\Models\Article;
use App\Models\Invoice;
use App\Models\Product;
use App\Jobs\MailSender;
use App\Models\Customer;
use App\Models\Proforma;
use App\Models\OrderItem;
use App\Models\CartDetail;
use App\Models\ProductSku;
use App\Helpers\FileHelper;
use App\Helpers\RestHelper;
use App\Models\CreditLimit;
use App\Models\OrderCustom;
use Illuminate\Support\Str;
use App\Events\GenericEvent;
use App\Models\ArticlePrice;
use Illuminate\Http\Request;
use App\Models\CustomerSales;
use App\Models\DeliveryOrder;
use App\Models\InvoiceDetail;
use App\Models\OrderApproval;
use App\Models\MasterParameter;
use App\Models\SalesAssignment;
use App\Models\TransactionItem;
use App\Models\CustomerShipment;
use App\Models\TransportationZone;
use Illuminate\Support\Facades\DB;
use App\Models\DeliveryOrderDetail;
use Illuminate\Support\Facades\Log;
use App\Repositories\GetSocialsRepo;
use Illuminate\Support\Facades\Auth;
use App\Models\OrderCustomAttachment;
use App\Http\Requests\EditOrderRequest;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\EditCustomRequest;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\CreateTransactionRequest;
use App\Http\Requests\UpdateTransactionRequest;
use Symfony\Component\HttpFoundation\StreamedResponse;

class TransactionB2BInternalController extends Controller
{
    const OrderStatusWait = 'Menunggu Konfirmasi';
    const OrderStatusPending = 'Pending';
    const OrderStatusOnHold = 'On Hold';
    const OrderStatusOnProcess = 'Diproses';
    const OrderStatusBaru = 'Baru';
    const OrderStatusGI = 'Siap Dikirim';
    const OrderStatusOnShipping = 'Dikirim';
    const OrderStatusDelivered = 'Diterima';
    const OrderStatusFinish = 'Selesai';
    const OrderStatusCancel = 'Batal';
    const OrderStatusPembayaran = 'Pembayaran';
    const OrderStatusSemua = 'Semua';
    const OrderStatusVerif = 'Menunggu Verifikasi';

    use FileHelper;

    public function getTransactions(Request $request)
    {
        $page = $request->input('page');

        $salesId = auth()->user()->reference_id;
        $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
        $salesIdd = !in_array("0", $roles) ?
            array_map(
                function ($i) {
                    return $i->sales_id ?? '0';
                },
                $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())
            )
            : [];
        array_push($salesIdd, $salesId);
        $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
            ->pluck('customer_id')->toArray();

        $order_no = $request->input('order_no');
        $ref_order = $request->input('ref_order');
        $nama_customer = $request->input('nama_customer');
        $nama_instansi = $request->input('nama_instansi');
        $status = $request->input('status_pesanan');
        $date_to = $request->input('date_to');
        $date_from = $request->input('date_from');
        $nama_sales = $request->input('nama_sales');
        $custom = $request->input('custom');
        $per_page = $request->input('limit') ?? 12;

        if ($status == null) {
            $orderParams = Order::whereIn('distribution_channel', ['B2B', 'RE', 'RD', 'W3'])->whereIn('order_status', ['Dikirim', "Diproses", "Pembayaran"])->get();
            $invoice = Invoice::whereIn('sales_order_no', $orderParams->pluck('sales_order_no')->toArray())->where('invoice_type', 'BILLING')->get();
            $do = DeliveryOrder::whereIn('sales_order_no', $orderParams->pluck('sales_order_no')->toArray())->get();
    
            $statusMap = [];
    
            foreach ($orderParams as $order) {
                $salesOrderNo = $order->sales_order_no;
                $isPaid = $invoice->firstWhere('sales_order_no', $salesOrderNo)?->status === 'LUNAS';
                $doForOrder = $do->where('sales_order_no', $salesOrderNo);
            
                if ($order->order_status === 'Pembayaran' && $isPaid) {
                    $gi = $doForOrder->firstWhere('good_issue_date', '!=', null);
                    if ($gi) {
                        $statusMap[$order->order_no] = 'Dikirim';
                    } elseif ($doForOrder->isNotEmpty()) {
                        $statusMap[$order->order_no] = 'Diproses';
                    }
                }
            
                if (
                    in_array($order->order_status, ['Dikirim', 'Diproses']) &&
                    !$isPaid
                ) {
                    $statusMap[$order->order_no] = 'Pembayaran';
                }
            }
            
            if (!empty($statusMap)) {
                $cases = '';
                $quotedOrderNos = [];
            
                foreach ($statusMap as $orderNo => $status) {
                    $escapedOrderNo = addslashes($orderNo);
                    $escapedStatus = addslashes($status);
                    $cases .= "WHEN '{$escapedOrderNo}' THEN '{$escapedStatus}' ";
                    $quotedOrderNos[] = "'{$escapedOrderNo}'";
                }
            
                $orderNoList = implode(',', $quotedOrderNos);
            
                DB::statement("
                    UPDATE order_header
                    SET order_status = CASE order_no
                        {$cases}
                    END
                    WHERE order_no IN ({$orderNoList})
                ");
            }
        }
        
        $orders = Order::with('invoice')->whereIn('distribution_channel', ['B2B', 'RE', 'RD', 'W3'])
            ->leftJoin('delivery_order', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
            ->when($order_no, function ($q) use ($order_no) {
                $q->where('order_no', 'LIKE', '%' . $order_no . '%');
            })
            ->when($ref_order, function ($q) use ($ref_order) {
                $q->where('sales_order_no', 'LIKE', '%' . $ref_order . '%');
            })
            ->when($nama_customer, function ($q) use ($nama_customer) {
                $q->where('bill_to', 'LIKE', '%' . $nama_customer . '%');
            })
            ->when($nama_instansi, function ($q) use ($nama_instansi) {
                $q->whereHas('customer', function ($q) use ($nama_instansi) {
                    $q->where('instance_name', 'LIKE', '%' . $nama_instansi . '%');
                });
            })
            ->when($status, function ($q) use ($status) {
                $status = explode(',', $status);
                $q_status = [];
                $i = 0;
                foreach ($status as $data) {
                    $cek = str_contains($data, '_');
                    if ($cek == true) {
                        $q_status[$i] = str_replace('_', ' ', $data);
                    } else {
                        $q_status[$i] = $data;
                    }
                    $i++;
                }
                $q->whereIn('order_status', $q_status);
            })
            ->when($date_to && $date_from, function ($query) use ($date_from, $date_to) {
                return $query->whereDate('delivery_order.created_date', '>=', $date_from)
                    ->whereDate('delivery_order.created_date', '<=', $date_to);
            })
            ->when($nama_sales, function ($q) use ($nama_sales) {
                $q->where('order_header.sales_name', 'LIKE', '%' . $nama_sales . '%');
            })
            ->when(!in_array("0", $roles), function ($query) use ($customerIdList) {
                $query->whereIn('order_header.customer_id', $customerIdList);
            })
            ->when($custom == 'true', function ($q) {
                $orderNos = OrderCustom::pluck('reference_id');
                $q->whereIn('order_header.order_no', $orderNos);
            })
            ->when($custom == 'false', function ($q) {
                $orderNos = OrderItem::pluck('order_no');
                $q->whereIn('order_header.order_no', $orderNos);
            })
            ->select(['order_header.*', 'delivery_order.created_date as do_date'])
            // ->select(['order_header.*'])
            ->orderBy('order_header.created_date', 'desc')
            ->paginate($per_page, ['*'], 'page', $page);

        $data_order = [];
        $data_order['total_data'] = $orders->total();
        $data_order['size'] = intval($orders->perPage());
        $data_order['active_page'] = $orders->currentPage();
        $data_order['total_page'] = $orders->lastPage();
        foreach ($orders as $order) {
            $data = [
                'order_no' => $order->order_no,
                'order_date' => $order->created_date ?? null,
                'do_date' => $order->do_date ?? "",
                'ref_order' => $order->sales_order_no,
                'nama_akun' => $order->bill_to ?? null,
                'nama_instansi' => $order->customer->instance_name ?? null,
                'total_transaksi' => $order->total_nett,
                // 'total_transaksi' => $order->total,
                'nama_sales' => $order->sales_name ?? null,
                'status_pesanan' => strtoupper($order->order_status),
                'status_pembayaran' => $order->invoice ? $order->invoice->status : 'Belum Dibayar',
                // 'faktur_pajak' => $order->invoice ? ($order->invoice->tax_invoice_file_path ? 'Ada' : 'Tidak Ada') : 'Tidak Ada'
            ];

            $data_order['data'][] = $data;
        }

        return $this->sendSuccess(null, $data_order);
    }

    public function customUploadUrl(Request $request){
        $validator = Validator::make($request->all(), [
            'files' => 'array|required',
        ]);

        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {

            $s3 = \Storage::disk('s3');
            $client = $s3->getDriver()->getAdapter()->getClient();
            $expiry = "+10 minutes";

            $files = $request->input('files');
            $bucket = env('AWS_BUCKET_STAGING', 'bucket-public-careorder');
            $basePath = 'staging/' . env('S3_CUSTOM_LOGO_INTERNAL_FOLDER', 'logo-custom-internal/');

            $result = [];

            if ($request->input('files')) {
                foreach ($files as $filename) {
                    $key = $basePath . $filename;

                    $cmd = $client->getCommand('PutObject', [
                        'Bucket' => $bucket,
                        'Key' => $key,
                    ]);

                    $request = $client->createPresignedRequest($cmd, $expiry);
                    $presignedUrl = (string) $request->getUri();

                    $result[] = [
                        "filepath" => $cmd["Key"],
                        "s3_url" => $presignedUrl,
                    ];
                }
            } else {
                $result[] = [
                    "filepath" => "-",
                    "s3_url" => "-",
                ];
            }

            return $this->sendSuccess('success', $result);

        } catch (\Exception $e) {

            return response()->json([
                'message' => 'Terjadi Kesalahan saat membuat pesanan ' . $e->getMessage(),
            ], 500);

        }
    }

    public function updateTransactionCustom(Request $request, $order_no){
        
        // $validator = Validator::make($request->all(), [
        //     'customer_id' => 'required|numeric',
        //     'customer_shipment' => 'required',
        //     'items' => 'required|array|min:1',
        //     'items.*.article' => 'required|string',
        //     'items.*.tipe_kustom' => 'required|string|in:text,logo,logo & text', // adjust 'in' values as needed
        //     'items.*.harga_text' => 'numeric|min:0',
        //     'items.*.harga_sablon' => 'numeric|min:0',
        //     'items.*.qty' => 'required|integer|min:1',
        //     'items.*.remarks' => 'nullable|string', 
        //     'items.*.attachment_group_id' => 'nullable|string',
        // ]);

        // if ($validator->fails()) {
        //     return response()->json([
        //         'errors' => $validator->errors(),
        //     ], 422);
        // }

        DB::beginTransaction();
        try{

        $custId = $request->customer_id;
        $Customer = Customer::where('customer_id', $custId)->first();
        $Customer->isMasked = false;
        if ($Customer == null) {
            return $this->sendError('Maaf anda tidak bisa membuat order karena data customer anda tidak ditemukan!');
        }
        
        $articleGroups = collect($request->input('article_items', []));
        $customPrice = MasterParameter::where('group_key', 'B2B_CONFIG')->get();
        $logoPrice = $customPrice->where('key', 'CUSTOM_ORDER_LOGO_PRICE')->first()?->value ?? 0;
        $textPrice = $customPrice->where('key', 'CUSTOM_ORDER_TEXT_PRICE')->first()?->value ?? 0;
        
        $transformed = collect($articleGroups)->mapWithKeys(function ($group) use ($logoPrice,$textPrice) {
            $uuid = isset($group['attachment_group_id']) ?  $group['attachment_group_id'] : (string) Str::uuid();
        
            $filePaths = $group['file_path'] ?? [];
            $texts = $group['text'] ?? [];
            $remark = $group['remark'] ?? '';
            $sku_code_c = $group['sku_code_c'] ?? '';
            $order_no = $group['order_no'] ?? '';
            $remove_assets = $group['remove_assets'] ?? '';
        
            $total_logo = count($filePaths) * $logoPrice;
            $total_text = count($texts) * $textPrice;
        
            $items = collect($group['items'] ?? [])->map(function ($item) use ($uuid, $total_logo, $total_text, $sku_code_c, $order_no, $remove_assets) {
                return array_merge($item, [
                    'attachment_group_id' => $uuid,
                    'harga_sablon' => $total_logo,
                    'harga_text' => $total_text,
                    'sku_code_c' => $sku_code_c,
                    'order_no' => $order_no,
                    'remove_assets' => $remove_assets
                ]);
            })->values()->all();
        
            return [
                $uuid => [
                    'items' => $items,
                    'file_path' => $filePaths,
                    'text' => $texts,
                    'remark' => $remark,
                    'sku_code_c' => $sku_code_c
                ]
            ];
        });

        $order_nos = collect($transformed)
        ->pluck('items')
        ->flatten(1)
        ->pluck('order_no')
        ->filter()
        ->unique()
        ->values()
        ->all();

        $attachment_groups_ids = collect($transformed)
        ->pluck('items')
        ->flatten(1)
        ->pluck('attachment_group_id')
        ->filter()
        ->unique()
        ->values()
        ->all();

        OrderCustomAttachment::whereIn('order_custom_id',$attachment_groups_ids )->delete();
        Order::whereIn('order_no', $order_nos)->delete();
        OrderApproval::whereIn('order_no', $order_nos)->delete();
        OrderCustom::whereIn('reference_id',$order_nos)->delete();
        Remark::whereIn('attachment_group_id', $attachment_groups_ids)->delete();

        $uniqueArticles = $transformed
        ->pluck('items')               
        ->flatten(1)                   
        ->pluck('article_id')             
        ->unique()                    
        ->values()                  
        ->all();                    
    
        $cdate = now()->format('Y-m-d');

        $articles = Article::whereIn('article', $uniqueArticles)
        ->with([
            'article_price' => function ($query) use ($cdate) {
                $query->select('sku_code_c', 'amount', 'modified_date', 'valid_from', 'valid_to')
                    ->where('valid_from', '<=', $cdate)
                    ->where('valid_to', '>=', $cdate)
                    ->orderBy('valid_from', 'desc');
            }
        ])
        ->get()
        ->keyBy('article');

        $filteredItems = collect($transformed)
        ->flatMap(function ($group) {
            return $group['items'] ?? [];
        })
        ->map(function ($item) use ($articles) {
            $item['article_detail'] = $articles->get($item['article_id']);
            return $item;
        })
        ->values()
        ->all();

        $total = 0;
        $total_nett = 0;
        $nett_before_tax = 0;
        $total_discount = 0;
        $total_tax = 0;

        $orderNosByDescription = [];
        $item_disc = [];
        $dataItem = collect($filteredItems)->groupBy('attachment_group_id')
        ->flatMap(function ($items, $groupId) use (&$orderNosByDescription, &$total, &$total_nett, &$item_disc, &$subTotal, $Customer) {
            return $items->map(function ($item) use (&$orderNosByDescription, &$total, &$total_nett, $groupId, &$item_disc, &$subTotal, $Customer) {
                $articleDetail = $item['article_detail'];

                $qty = $item['qty'];

                if (!isset($orderNosByDescription[$articleDetail['lvl4_description']])) {
                    $orderNosByDescription[$articleDetail['lvl4_description']] = $item['order_no'] ? $item['order_no'] : strtoupper('INVBCC' . substr(uniqid(), -6));
                }

                $orderNo = $orderNosByDescription[$articleDetail['lvl4_description']];

                $latestPrice = !empty($articleDetail['article_price'])
                    ? $articleDetail['article_price'][0]['amount']
                    : 0;

                $subTotal = $latestPrice * $qty;
                // $total_nett += $total;

                $custom_price = $item['harga_text'] + $item['harga_sablon'];

                $item_disc['is_custom'] = 1;
                $item_disc['data'][] = [
                    'article' => $articleDetail['article'],
                    'custom_price' => $custom_price,
                    'qty' => $qty
                ];
                $total += (($latestPrice * $qty) + ($custom_price * $qty));
                $total_nett += (($latestPrice * $qty) + ($custom_price * $qty));
                $subTotal =  $total_nett;
                // $total = 0;

                return [
                    "id" => (string) Str::uuid(),
                    'reference_id' => $orderNo,
                    'qty' => $qty,
                    'sku' => substr($articleDetail['article'], 0, -3),
                    'article_id' => $articleDetail['article'],
                    // 'is_custom' => 1,
                    'reference_name' => 'order_header',
                    'custom_price' => ($item['harga_text'] + $item['harga_sablon']) * $qty,
                    'position_side' => '-',
                    'generated_file_path' => '-',
                    'attachment_group_id' => $groupId,
                    'created_date' => now(),
                    'created_by' => $Customer->owner_name,
                    'modified_date' => now(),
                    'modified_by' => $Customer->owner_name,
                    'total' => $subTotal,
                ];
            });
        })->values()->toArray();


        $attachmentRemark = collect($transformed)->map(function ($group) {
            unset($group['items']);
            return $group;
        });

        $attachments_data = collect($attachmentRemark)->flatMap(function ($data, $attachmentGroupId) use ($custId,$logoPrice,$textPrice) {
            $sku_code_c = $data['sku_code_c'] ?? null;
        
            $filePathItems = collect($data['file_path'] ?? [])->map(function ($path) use ($attachmentGroupId, $sku_code_c, $custId, $logoPrice,$textPrice) {
                
                if (str_starts_with($path, 'staging/')) {
                    $tf_file = $this->fileTransfer($path, 'logo-custom-internal', true);
                        if ($tf_file['error'] == true) {
                            return $this->sendError($tf_file['message']);
                        }
                        $attachment_file =  $tf_file['filepath'];
                } else{

                    if (strpos($path, 'logo-custom-internal') !== false) {
                        $pathUrl = substr($path, strpos($path, 'logo-custom-internal'));
                    } elseif (strpos($path, 'logo-custom-dev') !== false) {
                        $pathUrl = substr($path, strpos($path, 'logo-custom-dev'));
                    } else {
                        $pathUrl = $path; // fallback if neither is found
                    }
                    $attachment_file = $pathUrl;
                }
                    
                return [
                    "id" => (string) Str::uuid(),
                    "order_custom_id" => $attachmentGroupId,
                    "custom_type" => "logo",
                    "file_path" =>  $attachment_file,
                    "custom_text" => null,
                    "color" => null,
                    "custom_price" => $logoPrice,
                ];
            });
        
            $textItems = collect($data['text'] ?? [])->map(function ($text) use ($attachmentGroupId, $sku_code_c, $custId, $logoPrice,$textPrice) {
                return [
                    "id" => (string) Str::uuid(),
                    "order_custom_id" => $attachmentGroupId,
                    "custom_type" => "text",
                    "file_path" => null,
                    "custom_text" => $text['text'],  
                    "color" => $text['color'],  
                    "custom_price" => $textPrice,
                ];
            });
        
            return $filePathItems->merge($textItems);
        })->values()->toArray();

        $removeAssets = collect($transformed)
        ->pluck('items')
        ->flatten(1)
        ->pluck('remove_assets')
        ->filter()
        ->flatten()
        ->map(fn($url) => basename($url))
        ->unique()
        ->values()
        ->all();

        foreach( $removeAssets as  $removeAsset){
            Storage::disk('s3-public')->delete('staging/' . env('S3_CUSTOM_LOGO_INTERNAL_FOLDER', 'logo-custom-internal/') . $removeAsset);
        }

        OrderCustomAttachment::insert($attachments_data);
        $customerfirstaddress = CustomerShipment::where('customer_shipment_id', $request->customer_shipment)->first();

        $orderNo = array_values($orderNosByDescription);

        $cs_address_name = $customerfirstaddress->address_name != null || $customerfirstaddress->address_name != '' ? $customerfirstaddress->address_name . ', ' : '';
        $cs_address = $customerfirstaddress->address != null || $customerfirstaddress->address != '' ? $customerfirstaddress->address . ', ' : '-, ';
        $cs_city = $customerfirstaddress->city != null || $customerfirstaddress->city != '' ? $customerfirstaddress->city . ', ' : '-, ';
        $cs_province = $customerfirstaddress->province != null || $customerfirstaddress->province != '' ? $customerfirstaddress->province . ', ' : '-, ';
        $cs_zipcode = $customerfirstaddress->zip_code != null || $customerfirstaddress->zip_code != '' ? $customerfirstaddress->zip_code . '.' : '-.';

        $ship_to_address = $cs_address_name . $cs_address . $cs_city . $cs_province . $cs_zipcode;

        $transportation_zone = TransportationZone::where('zone_code', $customerfirstaddress->zone_code)->first();

        $total_count = collect($dataItem);
        $orderGroup = (string) Str::uuid();

        $distributionChannel = $request->distribution_channel;
        $discountPercent = $total > 0 ? (($request->total_discount / $total) * 100) : 0;
        $mappedOrders = array_map(function ($orderNo) use ($custId, $orderGroup, $customerfirstaddress, $Customer, $total_count, $total_discount, $nett_before_tax, $total_tax, $total_nett, $ship_to_address, $request, $transportation_zone, $distributionChannel, $discountPercent) {

            $total_custom = $total_count->where('reference_id', $orderNo)->sum('total') ?? 0;
            $total_discount = ($discountPercent / 100) * $total_custom;
            // $total_discount = $request->total_discount;
            return [
                'order_no' => $orderNo,
                'order_group_id' => $orderGroup,
                'customer_id' => $custId,
                'customer_shipment_id' => strval($customerfirstaddress->customer_shipment_id),
                'order_status' => 'Menunggu Verifikasi', // Assuming OrderStatusWait is 'Wait'
                'total' => $total_custom,
                'total_discount' => $total_discount ?? 0,
                'nett_before_tax' => $nett_before_tax ?? 0,
                'total_tax' => $total_tax ?? 0,
                'total_nett' => ($total_count->where('reference_id', $orderNo)->sum('total') ?? 0) - $total_discount,
                'created_by' => $Customer->owner_name,
                'modified_by' => $custId,
                'sales_id' => isset($Customer->sales->sales_id) ? $Customer->sales->sales_id : '-',
                // 'dp_percentage' => substr($orderNo, 0, 6) == 'INVBCC' ? 50 : 0,
                'dp_percentage' => substr($orderNo, 0, 6) == 'INVBCC' ? 0 : 0,
                // 'dp_amount' => substr($orderNo, 0, 6) == 'INVBCC' ?  $total_custom * 0.5 : 0,
                'dp_amount' => substr($orderNo, 0, 6) == 'INVBCC' ? 0 : 0,
                'distribution_channel' => $distributionChannel,
                'sales_id' => $Customer->sales?->sales_id ?? '-',
                'bill_to' => $Customer->owner_name,
                'bill_to_address' => $Customer->address,
                'bill_to_phone_number' => $Customer->phone_number,
                'currency' => 'IDR',
                'bill_to_email' => $Customer->email,
                'ship_to' => $customerfirstaddress->name,
                'ship_to_address' => $ship_to_address,
                'created_date' => date('Y-m-d H:i:s'),
                'ship_to_phone_number' => $customerfirstaddress->phone_number,
                'location_code' => $transportation_zone->zone_code ?? '-',
                'location_name' => $transportation_zone->description ?? '-'
                // 'dp_due_date' => date('Y-m-d')
            ];
        }, $orderNo);

        $orderApproval = array_map(function ($item) {
            return [
                'order_no' => $item['order_no'],
                'status' => 'Pending', // rename if needed
                'created_by' => $item['created_by'],
            ];
        }, $mappedOrders);
    
        OrderApproval::insert($orderApproval);
        
        $order = Order::insert($mappedOrders);

        $orderCustomData = collect($dataItem)->map(function ($item) {
            unset($item['total']); // if array
            return $item;
        });

        $order_detail = OrderCustom::insert($orderCustomData->toArray());

        $remarks = collect($attachmentRemark)->filter(fn($data) => !empty($data['remark'])) // only keep if remark is not empty
        ->map(function ($data, $attachmentGroupId) {
            return [
                'attachment_group_id' => $attachmentGroupId,
                'remark' => $data['remark'],
            ];
        })
        ->values()
        ->all();

        Remark::insert( $remarks);
        $result = [
            'order_group_id' => $orderGroup,
            'order_no' => $orderNo
        ];

     
        DB::commit();
        return $this->sendSuccess("Order created successfully.", $result);
        }   catch(\Exception $e){
            DB::rollback();
            Log::info('error checkout : '.$e->getMessage());
            return $this->sendError("Sorry system can't create order" . $e->getMessage() . ' on line ' . $e->getLine(), 500);
        }


    }


    public function createTransactionCustom(Request $request)
    {
        // $validator = Validator::make($request->all(), [
        //     'customer_id' => 'required',
        //     'customer_shipment' => 'required',
        //     'items' => 'required|array|min:1',
        //     'items.*.article' => 'required|string',
        //     'items.*.tipe_kustom' => 'required|string|in:text,logo,text&logo', // adjust 'in' values as needed
        //     'items.*.harga_text' => 'numeric|min:0',
        //     'items.*.harga_sablon' => 'numeric|min:0',
        //     'items.*.qty' => 'required|integer|min:1',
        //     'items.*.remarks' => 'nullable|string',
        // ]);

        // if ($validator->fails()) {
        //     return response()->json([
        //         'errors' => $validator->errors(),
        //     ], 422);
        // }

        DB::beginTransaction();
        try {

            $custId = $request->customer_id;
            $Customer = Customer::where('customer_id', $custId)->first();
            $Customer->isMasked = false;
            if ($Customer == null) {
                return $this->sendError('Maaf anda tidak bisa membuat order karena data customer anda tidak ditemukan!');
            }
            
            $articleGroups = collect($request->input('article_items', []));
            $customPrice = MasterParameter::where('group_key', 'B2B_CONFIG')->get();
            $logoPrice = $customPrice->where('key', 'CUSTOM_ORDER_LOGO_PRICE')->first()?->value ?? 0;
            $textPrice = $customPrice->where('key', 'CUSTOM_ORDER_TEXT_PRICE')->first()?->value ?? 0;
            
            $transformed = collect($articleGroups)->mapWithKeys(function ($group) use ($logoPrice,$textPrice) {
                $uuid = (string) Str::uuid();
            
                $filePaths = $group['file_path'] ?? [];
                $texts = $group['text'] ?? [];
                $remark = $group['remark'] ?? '';
                $sku_code_c = $group['sku_code_c'] ?? '';
            
                $total_logo = count($filePaths) * $logoPrice;
                $total_text = count($texts) * $textPrice;
            
                $items = collect($group['items'] ?? [])->map(function ($item) use ($uuid, $total_logo, $total_text, $sku_code_c) {
                    return array_merge($item, [
                        'attachment_group_id' => $uuid,
                        'harga_sablon' => $total_logo,
                        'harga_text' => $total_text,
                        'sku_code_c' => $sku_code_c
                    ]);
                })->values()->all();
            
                return [
                    $uuid => [
                        'items' => $items,
                        'file_path' => $filePaths,
                        'text' => $texts,
                        'remark' => $remark,
                        'sku_code_c' => $sku_code_c
                    ]
                ];
            });


            $uniqueArticles = $transformed
            ->pluck('items')               
            ->flatten(1)                   
            ->pluck('article_id')             
            ->unique()                    
            ->values()                  
            ->all();                    
        
            $cdate = now()->format('Y-m-d');

            $articles = Article::whereIn('article', $uniqueArticles)
            ->with([
                'article_price' => function ($query) use ($cdate) {
                    $query->select('sku_code_c', 'amount', 'modified_date', 'valid_from', 'valid_to')
                        ->where('valid_from', '<=', $cdate)
                        ->where('valid_to', '>=', $cdate)
                        ->orderBy('valid_from', 'desc');
                }
            ])
            ->get()
            ->keyBy('article');

            $filteredItems = collect($transformed)
            ->flatMap(function ($group) {
                return $group['items'] ?? [];
            })
            ->map(function ($item) use ($articles) {
                $item['article_detail'] = $articles->get($item['article_id']);
                return $item;
            })
            ->values()
            ->all();
            
            $total = 0;
            $total_nett = 0;
            $nett_before_tax = 0;
            $total_discount = 0;
            $total_tax = 0;

            $orderNosByDescription = [];
            $item_disc = [];

            
            $dataItem = collect($filteredItems)->groupBy('attachment_group_id')
                ->flatMap(function ($items, $groupId) use (&$orderNosByDescription, &$total, &$total_nett, &$item_disc, &$subTotal, $Customer) {
                    return $items->map(function ($item) use (&$orderNosByDescription, &$total, &$total_nett, $groupId, &$item_disc, &$subTotal, $Customer) {
                        $articleDetail = $item['article_detail'];

                        $qty = $item['qty'];

                        if (!isset($orderNosByDescription[$articleDetail['lvl4_description']])) {
                            $orderNosByDescription[$articleDetail['lvl4_description']] = strtoupper('INVBCC' . substr(uniqid(), -6));
                        }

                        $orderNo = $orderNosByDescription[$articleDetail['lvl4_description']];

                        $latestPrice = !empty($articleDetail['article_price'])
                            ? $articleDetail['article_price'][0]['amount']
                            : 0;

                        $subTotal = $latestPrice * $qty;
                        // $total_nett += $total;

                        $custom_price = $item['harga_text'] + $item['harga_sablon'];

                        $item_disc['is_custom'] = 1;
                        $item_disc['data'][] = [
                            'article' => $articleDetail['article'],
                            'custom_price' => $custom_price,
                            'qty' => $qty
                        ];
                        $total += (($latestPrice * $qty) + ($custom_price * $qty));
                        $total_nett += (($latestPrice * $qty) + ($custom_price * $qty));
                        $subTotal = $total_nett;
                        // $total = 0;

                        return [
                            "id" => (string) Str::uuid(),
                            'reference_id' => $orderNo,
                            'qty' => $qty,
                            'sku' => substr($articleDetail['article'], 0, -3),
                            'article_id' => $articleDetail['article'],
                            // 'is_custom' => 1,
                            'reference_name' => 'order_header',
                            'custom_price' => ($item['harga_text'] + $item['harga_sablon']) * $qty,
                            'position_side' => '-',
                            'generated_file_path' => '-',
                            'attachment_group_id' => $groupId,
                            'created_date' => now(),
                            'created_by' => $Customer->owner_name,
                            'modified_date' => now(),
                            'modified_by' => $Customer->owner_name,
                            'total' => $subTotal,
                        ];
                    });
                })->values()->toArray();
            
            $attachmentRemark = collect($transformed)->map(function ($group) {
                unset($group['items']);
                return $group;
            });

            $attachments_data = collect($attachmentRemark)->flatMap(function ($data, $attachmentGroupId) use ($custId,$logoPrice,$textPrice) {
                // Get sku_code_c from each group
                $sku_code_c = $data['sku_code_c'] ?? null;
            
                $filePathItems = collect($data['file_path'] ?? [])->map(function ($path) use ($attachmentGroupId, $sku_code_c, $custId, $logoPrice,$textPrice) {
                        $tf_file = $this->fileTransfer($path, 'logo-custom-internal', true);
                        if ($tf_file['error'] == true) {
                            return $this->sendError($tf_file['message']);
                        }
                        $attachment_file =  $tf_file['filepath'];
                        
                    return [
                        "id" => (string) Str::uuid(),
                        "order_custom_id" => $attachmentGroupId,
                        "custom_type" => "logo",
                        "file_path" =>  $attachment_file,
                        "custom_text" => null,
                        "color" => null,
                        "custom_price" => $logoPrice,
                    ];
                });
            
                $textItems = collect($data['text'] ?? [])->map(function ($text) use ($attachmentGroupId, $sku_code_c, $custId, $logoPrice,$textPrice) {
                    return [
                        "id" => (string) Str::uuid(),
                        "order_custom_id" => $attachmentGroupId,
                        "custom_type" => "text",
                        "file_path" => null,
                        "custom_text" => $text['text'],  
                        "color" => $text['color'],  
                        "custom_price" => $textPrice,
                    ];
                });
            
                return $filePathItems->merge($textItems);
            })->values()->toArray();

            OrderCustomAttachment::insert($attachments_data);
            
            $customerfirstaddress = CustomerShipment::where('customer_shipment_id', $request->customer_shipment)->first();

            $orderNo = array_values($orderNosByDescription);

            $cs_address_name = $customerfirstaddress->address_name != null || $customerfirstaddress->address_name != '' ? $customerfirstaddress->address_name . ', ' : '';
            $cs_address = $customerfirstaddress->address != null || $customerfirstaddress->address != '' ? $customerfirstaddress->address . ', ' : '-, ';
            $cs_city = $customerfirstaddress->city != null || $customerfirstaddress->city != '' ? $customerfirstaddress->city . ', ' : '-, ';
            $cs_province = $customerfirstaddress->province != null || $customerfirstaddress->province != '' ? $customerfirstaddress->province . ', ' : '-, ';
            $cs_zipcode = $customerfirstaddress->zip_code != null || $customerfirstaddress->zip_code != '' ? $customerfirstaddress->zip_code . '.' : '-.';

            $ship_to_address = $cs_address_name . $cs_address . $cs_city . $cs_province . $cs_zipcode;

            $transportation_zone = TransportationZone::where('zone_code', $customerfirstaddress->zone_code)->first();

            $total_count = collect($dataItem);
            $orderGroup = (string) Str::uuid();

            $distributionChannel = $request->distribution_channel;
            $discountPercent = $total > 0 ? (($request->total_discount / $total) * 100) : 0;
            $mappedOrders = array_map(function ($orderNo) use ($custId, $orderGroup, $customerfirstaddress, $Customer, $total_count, $total_discount, $nett_before_tax, $total_tax, $total_nett, $ship_to_address, $request, $transportation_zone, $distributionChannel, &$discountPercent ) {

                $total_custom = $total_count->where('reference_id', $orderNo)->sum('total') ?? 0;
                
                $total_discount = ($discountPercent / 100) * $total_custom;
                return [
                    'order_no' => $orderNo,
                    'order_group_id' => $orderGroup,
                    'customer_id' => $custId,
                    'customer_shipment_id' => strval($customerfirstaddress->customer_shipment_id),
                    'order_status' => 'Menunggu Verifikasi', // Assuming OrderStatusWait is 'Wait'
                    'total' => $total_custom,
                    'total_discount' => $total_discount ?? 0,
                    'nett_before_tax' => $nett_before_tax ?? 0,
                    'total_tax' => $total_tax ?? 0,
                    'total_nett' => ($total_count->where('reference_id', $orderNo)->sum('total') ?? 0) - $total_discount,
                    'created_by' => $Customer->owner_name,
                    'modified_by' => $custId,
                    'sales_id' => isset($Customer->sales->sales_id) ? $Customer->sales->sales_id : '-',
                    // 'dp_percentage' => substr($orderNo, 0, 6) == 'INVBCC' ? 50 : 0,
                    'dp_percentage' => substr($orderNo, 0, 6) == 'INVBCC' ? 0 : 0,
                    // 'dp_amount' => substr($orderNo, 0, 6) == 'INVBCC' ?  $total_custom * 0.5 : 0,
                    'dp_amount' => substr($orderNo, 0, 6) == 'INVBCC' ? 0 : 0,
                    'distribution_channel' => $distributionChannel,
                    'sales_id' => $Customer->sales?->sales_id ?? '-',
                    'bill_to' => $Customer->owner_name,
                    'bill_to_address' => $Customer->address,
                    'bill_to_phone_number' => $Customer->phone_number,
                    'currency' => 'IDR',
                    'bill_to_email' => $Customer->email,
                    'ship_to' => $customerfirstaddress->name,
                    'ship_to_address' => $ship_to_address,
                    'created_date' => date('Y-m-d H:i:s'),
                    'ship_to_phone_number' => $customerfirstaddress->phone_number,
                    'location_code' => $transportation_zone->zone_code ?? '-',
                    'location_name' => $transportation_zone->description ?? '-'
                    // 'dp_due_date' => date('Y-m-d')
                ];
            }, $orderNo);

            $orderApproval = array_map(function ($item) {
                return [
                    'order_no' => $item['order_no'],
                    'status' => 'Pending', // rename if needed
                    'created_by' => $item['created_by'],
                ];
            }, $mappedOrders);

            OrderApproval::insert($orderApproval);
            
            $order = Order::insert($mappedOrders);

            $orderCustomData = collect($dataItem)->map(function ($item) {
                unset($item['total']); // if array
                return $item;
            });

            $order_detail = OrderCustom::insert($orderCustomData->toArray());

            $remarks = collect($attachmentRemark)->filter(fn($data) => !empty($data['remark'])) // only keep if remark is not empty
            ->map(function ($data, $attachmentGroupId) {
                return [
                    'attachment_group_id' => $attachmentGroupId,
                    'remark' => $data['remark'],
                ];
            })
            ->values()
            ->all();

            Remark::insert( $remarks);
            $result = [
                'order_group_id' => $orderGroup,
                'order_no' => $orderNo
            ];
         
            DB::commit();
            // return $orderApproval;
            return $this->sendSuccess("Order created successfully.", $result);

        } catch (\Exception $e) {
            DB::rollback();
            Log::info('error checkout : ' . $e->getMessage());
            return $this->sendError("Sorry system can't create order" . $e->getMessage() . ' on line ' . $e->getLine(), 500);
        }
    }

    public function uploadCustomImage(Request $request)
    {

    }


    public function getTransactionDetails(Request $request, $order_no)
    {
        $page = !empty($request->input('page')) ? (int) $request->input('page') : 1;
        $per_page = $request->input('limit') ?? 12;

        $order = Order::find($order_no);
        $nomorresi = [];

        if ($order == null) {
            return $this->sendError("Data transaction not found", 404);
        }

        $orderNos = OrderItem::pluck('order_no')->toArray();
        if (in_array($order_no, $orderNos)) {
            // $order_detail = OrderItem::where('order_no', $order_no)->get();
            $order_detail = OrderItem::leftJoin('article', 'order_detail.article_id', '=', 'article.article')
                ->select('order_detail.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_variant_c')
                ->where('order_no', $order->order_no)->orderBy('order_detail.order_detail_id', 'desc')
                ->get();
            $customCheck = false;
        } else {
            // $order_detail = OrderCustom::leftJoin('article', 'order_custom.article_id', '=', 'article.article')
            //     ->leftJoin('article_price', 'order_custom.sku', '=', 'article_price.sku_code_c')
            //     ->select('order_custom.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_size_c', 'article_price.amount')
            //     ->where('reference_id', $order_no)
            //     ->get();
            $order_detail = OrderCustom::leftJoin('article', 'order_custom.article_id', '=', 'article.article')
                ->select('order_custom.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_variant_c', 'article.product_size_c')
                ->where('reference_id', $order->order_no)->orderBy('order_custom.id', 'desc')
                ->get();

            $customCheck = true;
        }

        $customer = DB::table('customers')->where('customer_id', $order->customer_id)->first();

        //checking order custom
        // $orderCustom = Order::join('order_custom', 'order_custom.reference_id', '=', 'order_header.order_no')
        //     ->select('order_header.order_no')
        //     ->get()->toArray();
        // $customCheck = false;
        // foreach ($orderCustom as $orderitems) {
        //     if ($orderitems['order_no'] === $order->order_no) {
        //         $customCheck = true;
        //         break;
        //     }
        // }

        $do = DeliveryOrder::where('sales_order_no', $order->sales_order_no)->first();

        $product_items = [];
        $items = [];
        $total_qty = 0;
        $sub_total = 0;
        $total_kustomisasi = 0;
        if ($do != null) {
            $do_items = $do->items;
            $do_discount = $do->good_issue_date != null ? $do->issued_discount : $do->discount;
            // $stock_collection = RestHelper::stockCache($item->pluck('article')->toArray(), $request->user()->sales->sales_id);
            foreach ($do_items as $item) {
                DB::connection()->enableQueryLog();
                // $stock = RestHelper::searchStock($stock_collection, $item[$i]->article);

                if ($customCheck) {
                    $image = DB::table('article as art')
                        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                        ->where('art.article', $item->article)
                        ->where('ig.is_main_image', 1)
                        ->select('ig.file_path')
                        ->first();

                    $warna = DB::table('master_color')
                        ->where('key', $item->product_variant)
                        ->where('is_active', 1)
                        ->pluck('value')->first();

                    $article = Product::where('article', $item->article)->first();

                    $ocs = OrderCustom::where('reference_id', $order->order_no)
                        ->where('sku', $article->sku_code_c)
                        ->first();

                    $custom_price = OrderCustomAttachment::where('order_custom_id', $ocs->attachment_group_id)->sum('custom_price');

                    $customs = [];
                    $cartAttachments = [];
                    $ocas = OrderCustomAttachment::where('order_custom_id', $ocs->attachment_group_id)->get();
                    $logoQty = 0;
                    $textQty = 0;
                    foreach ($ocas as $oca) {
                        $customs[] = [
                            'custom_type' => $oca->custom_type,
                            'harga_satuan' => (int) $oca->custom_price
                        ];

                        $logoQty += $oca->file_path != null ? 1 : 0;
                        $textQty += $oca->custom_text != null ? 1 : 0;

                        $cartAttachments[] = [
                            'id' => $oca->id,
                            'attachments_group_id' => $oca->order_custom_id,
                            'file_path' => $oca->file_path != null ? env('S3_STREAM_URL') . '/' . $oca->file_path : "",
                            'text' => $oca->custom_text,
                            'color' => $oca->color,
                            'estimate_price' => (int) $oca->custom_price
                        ];
                    }

                    $attachment_qty = 0;
                    $sub_total = 0;
                    $product_items = [];

                    $itemData = new TransactionItem();
                    $itemData->article = $item->article;
                    $itemData->product_name = $item->product_name;
                    $itemData->product_variant = $warna ?? null;
                    $itemData->product_size = $item->product_size;
                    $itemData->qty = $item->qty;
                    $itemData->issued_qty = $item->issued_qty;
                    $itemData->sub_total = $do->good_issue_date == null ? (int) ($item->qty * $article->price->amount) : (int) (($item->issued_qty ?? 0) * $article->price->amount);
                    // $itemData->sub_total = $item->sub_total;
                    array_push($product_items, $itemData);

                    $sub_total += (int) ((int) $item->qty * (int) $article->price->amount);
                    $total_qty += (int) $item->qty;
                    $attachment_qty += (int) $item->qty;

                    $cdate = now()->format('Y-m-d');

                    $flag = [];
                    if (!empty($article)) {
                        if ($article->transfer_date <= $cdate && $article->expired_date >= $cdate) {
                            array_push($flag, 'NEW');
                        }
                        if ($article->is_custom_size || $article->is_custom_logo) {
                            array_push($flag, 'CUSTOM');
                        }
                        array_push($flag, $article->lvl4_description);
                    }

                    $price = DB::table('article_price')
                        ->where('sku_code_c', $article->sku_code_c)
                        ->where('valid_from', '<=', $cdate)
                        ->where('valid_to', '>=', $cdate)
                        ->orderBy('valid_from', 'desc')
                        ->select('amount')
                        ->first();

                    $customs = collect($customs);

                    $productItem = [ 
                        'sku_code_c' => $article->sku_code_c,
                        'attachment_group_id' => $ocs->attachment_group_id,
                        'product_name_c' => $article->product_name_c,
                        'image_url' => @$image->file_path ? env('S3_STREAM_URL') . $image->file_path : null,
                        'flag' => $flag,
                        'base_price' => $article->price->amount,
                        'total_custom_logo' => ($logoQty * ($customs->firstWhere('custom_type', 'logo')['harga_satuan'] ?? 0)) * $attachment_qty,
                        'total_custom_text' => ($textQty * ($customs->firstWhere('custom_type', 'text')['harga_satuan'] ?? 0)) * $attachment_qty,
                        'sub_total' => $sub_total,
                        'custom_type' => $ocs->attachment->pluck('custom_type')->filter()->implode(' + ') ?: null,
                        'customs' => $customs,
                        'cart_attachments' => $cartAttachments,
                        'product_items' => $product_items
                    ];

                    if (in_array($order->order_status, ['Pending', 'On Hold', 'Menunggu Konfirmasi', 'Baru'])) {
                        $productItem['base_price'] = $price->amount;
                    }

                    array_push($items, $productItem);
                    $total_kustomisasi += (int) $custom_price * (int) $attachment_qty;
                } else {
                    $image = DB::table('article as art')
                        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                        ->where('art.article', $item->article)
                        ->where('ig.is_main_image', 1)
                        ->select('ig.file_path')
                        ->first();

                    $article = Product::where('article', $item->article)->first();
                    $warna = DB::table('master_color')
                        ->where('key', $item->product_variant)
                        ->where('is_active', 1)
                        ->pluck('value')->first();

                    $sub_total = 0;
                    $product_items = [];
                    $itemData = new TransactionItem();
                    $itemData->article = $item->article;
                    $itemData->product_name = $item->product_name;
                    $itemData->product_variant = $warna ?? null;
                    $itemData->product_size = $item->product_size;
                    $itemData->qty = $item->qty;
                    $itemData->issued_qty = $item->issued_qty;
                    $itemData->sub_total = $do->good_issue_date == null ? (int) ($item->qty * $article->price->amount) : (int) (($item->issued_qty ?? 0) * $article->price->amount);
                    // $itemData->sub_total = $item->sub_total;
                    array_push($product_items, $itemData);

                    $sub_total += (int) ((int) $item->qty * (int) $article->price->amount);
                    $total_qty += (int) $item->qty;

                    $cdate = now()->format('Y-m-d');

                    $flag = [];
                    if (!empty($article)) {
                        if ($article->transfer_date <= $cdate && $article->expired_date >= $cdate) {
                            array_push($flag, 'NEW');
                        }
                        if ($article->is_custom_size || $article->is_custom_logo) {
                            array_push($flag, 'CUSTOM');
                        }
                        array_push($flag, $article->lvl4_description);
                    }

                    $price = DB::table('article_price')
                        ->where('article_price.sku_code_c', $article->sku_code_c)
                        ->where('valid_from', '<=', $cdate)
                        ->where('valid_to', '>=', $cdate)
                        ->orderBy('valid_from', 'desc')
                        ->select('article_price.amount')
                        ->first();

                    $productItem = [
                        'sku_code_c' => $article->sku_code_c,
                        'product_name_c' => $article->product_name_c,
                        "image_url" => @$image->file_path == null ? null : env('S3_STREAM_URL') . $image->file_path,
                        'flag' => $flag,
                        'base_price' => $article->price->amount,
                        'sub_total' => $sub_total,
                        // 'customs' => [],
                        'product_items' => $product_items
                    ];

                    if (in_array($order->order_status, ['Pending', 'On Hold', 'Menunggu Konfirmasi', 'Baru'])) {
                        $productItem['base_price'] = $price->amount ?? 0;
                    }

                    array_push($items, $productItem);
                }
            }
        } else {
            // $stock_collection = RestHelper::stockCache($order_detail->pluck('article_id')->toArray(), $request->user()->sales->sales_id);
            if (!$customCheck)
                foreach ($order_detail as $item) {
                    DB::connection()->enableQueryLog();
                    // $stock = RestHelper::searchStock($stock_collection, $order_detail[$i]->article_id);
                    $image = DB::table('article as art')
                        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                        ->where('art.article', $item->article_id)
                        ->where('ig.is_main_image', 1)
                        ->select('ig.file_path')
                        ->first();

                    $article = Product::with('price', 'skuStock')->where('article', $item->article_id)->first();
                    $warna = DB::table('master_color')
                        ->where('key', $item->product_variant_c)
                        ->where('is_active', 1)
                        ->pluck('value')->first();

                    $sub_total = 0;
                    $product_items = [];
                    $itemData = new TransactionItem();
                    $itemData->article = $item->article_id;
                    $itemData->product_name = $item->product_name;
                    $itemData->product_variant = $warna ?? null;
                    $itemData->product_size = $item->product_size;
                    $itemData->qty = $item->qty;
                    $itemData->stock = $article?->skuStock?->stock ?? 0;
                    $itemData->base_price = $article->price->amount;
                    $itemData->sub_total = (int) ($item->qty * $article->price->amount);
                    array_push($product_items, $itemData);

                    $sub_total += (int) ((int) $item->qty * (int) $article->price->amount);
                    $total_qty += (int) $item->qty;

                    $cdate = now()->format('Y-m-d');

                    $flag = [];
                    if (!empty($article)) {
                        if ($article->transfer_date <= $cdate && $article->expired_date >= $cdate) {
                            array_push($flag, 'NEW');
                        }
                        if ($article->is_custom_size || $article->is_custom_logo) {
                            array_push($flag, 'CUSTOM');
                        }
                        array_push($flag, $article->lvl4_description);
                    }

                    $price = DB::table('article_price')
                        ->where('article_price.sku_code_c', $item->sku_code_c)
                        ->where('valid_from', '<=', $cdate)
                        ->where('valid_to', '>=', $cdate)
                        ->orderBy('valid_from', 'desc')
                        ->select('article_price.amount')
                        ->first();

                    $productItem = [
                        'sku_code_c' => $item->sku_code_c,
                        'product_name_c' => $item->product_name_c,
                        "image_url" => @$image->file_path == null ? null : env('S3_STREAM_URL') . $image->file_path,
                        'flag' => $flag,
                        'base_price' => $article->price->amount,
                        'sub_total' => $sub_total,
                        // 'customs' => [],
                        'product_items' => $product_items
                    ];
                    if (in_array($order->order_status, ['Pending', 'On Hold', 'Menunggu Konfirmasi', 'Baru'])) {
                        $productItem['base_price'] = $price->amount;
                    }
                    array_push($items, $productItem);
                } else {
                    // return $order_detail->pluck('attachment_group_id')->toArray();
                $catatan = Remark::whereIn('attachment_group_id', $order_detail->pluck('attachment_group_id')->toArray())->get()->groupBy('attachment_group_id');
                foreach ($order_detail->groupBy('attachment_group_id') as $groupId => $groupItems) {
                    DB::connection()->enableQueryLog();
                    // $stock = RestHelper::searchStock($stock_collection, $order_detail[$i]->article_id);
                    $firstItem = $groupItems->first();

                    $image = DB::table('article as art')
                        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                        ->where('art.article', $firstItem->article_id)
                        ->where('ig.is_main_image', 1)
                        ->select('ig.file_path')
                        ->first();

                    $warna = DB::table('master_color')
                        ->where('key', $firstItem->product_variant_c)
                        ->where('is_active', 1)
                        ->pluck('value')->first();
                    
                    $article = Product::with('price', 'skuStock')->where('article', $firstItem->article_id)->first();

                    $ocs = OrderCustom::where('reference_id', $order->order_no)
                        ->where('sku', $article->sku_code_c)
                        ->where('attachment_group_id', $groupId)
                        ->pluck('attachment_group_id')->first();

                    $custom_price = OrderCustomAttachment::where('order_custom_id', $ocs)->sum('custom_price');

                    $customs = [];
                    $cartAttachments = [];
                    $ocas = OrderCustomAttachment::where('order_custom_id', $ocs)->get();
                    $logoQty = 0;
                    $textQty = 0;
                    foreach ($ocas as $oca) {
                        $customs[] = [
                            'custom_type' => $oca->custom_type,
                            'harga_satuan' => (int) $oca->custom_price
                        ];

                        $logoQty += $oca->file_path != null ? 1 : 0;
                        $textQty += $oca->custom_text != null ? 1 : 0;
                        $cartAttachments[] = [
                            'id' => $oca->id,
                            'attachments_group_id' => $oca->order_custom_id,
                            'file_path' => $oca->file_path != null ? env('S3_STREAM_URL') . '/' . $oca->file_path : "",
                            'text' => $oca->custom_text,
                            'color' => $oca->color,
                            'estimate_price' => (int) $oca->custom_price
                        ];
                    }

                    $sub_total = 0;
                    $product_items = [];
                    foreach ($groupItems->groupBy('id') as $itemGroup) {
                        $item = $itemGroup->first();
                        $itemData = new TransactionItem();
                        $itemData->article = $item->article_id;
                        $itemData->product_name = $item->product_name_c;
                        $itemData->product_variant = $warna ?? null;
                        $itemData->product_size = $item->product_size_c;
                        $itemData->qty = $item->qty;
                        $itemData->stock = $article?->skuStock?->stock ?? 0;
                        $itemData->base_price = $article->price->amount;
                        $itemData->sub_total += (int) ((int) $item->qty * (int) $article->price->amount);
                        array_push($product_items, $itemData);

                        $sub_total += (int) ((int) $item->qty * (int) $article->price->amount);
                        $total_qty += (int) $item->qty;
                    }

                    $cdate = now()->format('Y-m-d');

                    $flag = [];
                    if (!empty($article)) {
                        if ($article->transfer_date <= $cdate && $article->expired_date >= $cdate) {
                            array_push($flag, 'NEW');
                        }
                        if ($article->is_custom_size || $article->is_custom_logo) {
                            array_push($flag, 'CUSTOM');
                        }
                        array_push($flag, $article->lvl4_description);
                    }

                    $price = DB::table('article_price')
                        ->where('sku_code_c', $firstItem->sku_code_c)
                        ->where('valid_from', '<=', $cdate)
                        ->where('valid_to', '>=', $cdate)
                        ->orderBy('valid_from', 'desc')
                        ->select('amount')
                        ->first();

                    $customs = collect($customs);

                    $productItem = [
                        'sku_code_c' => $firstItem->sku_code_c,
                        'attachment_group_id' => $groupId,
                        'remark' => $catatan[$groupId] ?? collect(),
                        'product_name_c' => $firstItem->product_name_c,
                        'image_url' => @$image->file_path ? env('S3_STREAM_URL') . $image->file_path : null,
                        'flag' => $flag,
                        'base_price' => $article->price->amount,
                        'total_custom_logo' => ((int) $logoQty * ($customs->firstWhere('custom_type', 'logo')['harga_satuan'] ?? 0)) * $item->qty,
                        'total_custom_text' => ((int) $textQty * ($customs->firstWhere('custom_type', 'text')['harga_satuan'] ?? 0)) * $item->qty,
                        'sub_total' => $sub_total,
                        'custom_type' => $item->attachmentSku->pluck('custom_type')->filter()->implode(' + ') ?: null,
                        'customs' => $customs,
                        'cart_attachments' => $cartAttachments,
                        'product_items' => $product_items
                    ];

                    if (in_array($order->order_status, ['Pending', 'On Hold', 'Menunggu Konfirmasi', 'Baru'])) {
                        $productItem['base_price'] = $price->amount;
                    }

                    array_push($items, $productItem);
                    $total_kustomisasi += (int) $custom_price * (int) $groupItems->sum('qty');
                }
            }
        }

        $allItems[] = $items;

        //grouping array
        $groupedItems = [];
        if (!$customCheck) {
            foreach ($allItems as $item) {
                foreach ($item as $subitem) {
                    if (!isset($groupedItems[$subitem['sku_code_c']])) {
                        $groupedItems[$subitem['sku_code_c']] = [
                            'sku_code_c' => $subitem['sku_code_c'],
                            'product_name_c' => $subitem['product_name_c'],
                            "image_url" => $subitem['image_url'],
                            'flag' => $subitem['flag'],
                            'base_price' => $subitem['base_price'],
                            'sub_total' => 0,
                            // 'customs' => $subitem['customs'],
                            'product_items' => []
                        ];
                    }
                    $groupedItems[$subitem['sku_code_c']]['product_items'] = array_merge($groupedItems[$subitem['sku_code_c']]['product_items'], $subitem['product_items']);
                }
            }
        } else {
            foreach ($allItems as $itemGroup) {
                foreach ($itemGroup as $subitem) {
                    $groupKey = $subitem['sku_code_c'] . '|' . $subitem['attachment_group_id'];

                    if (!isset($groupedItems[$groupKey])) {
                        $groupedItems[$groupKey] = [
                            'sku_code_c' => $subitem['sku_code_c'],
                            'attachment_group_id' => $subitem['attachment_group_id'],
                            'remark' => $subitem['remark']->first()->remark ?? '-',
                            'product_name_c' => $subitem['product_name_c'],
                            'image_url' => $subitem['image_url'],
                            'flag' => $subitem['flag'],
                            'base_price' => $subitem['base_price'],
                            'total_custom_logo' => $subitem['total_custom_logo'],
                            'total_custom_text' => $subitem['total_custom_text'],
                            'sub_total' => 0,
                            'custom_type' => $subitem['custom_type'] ?? null,
                            'customs' => $subitem['customs'],
                            'cart_attachments' => $subitem['cart_attachments'],
                            'product_items' => [],
                        ];
                    }

                    $groupedItems[$groupKey]['product_items'] = array_merge(
                        $groupedItems[$groupKey]['product_items'],
                        $subitem['product_items']
                    );
                }
            }
        }

        foreach ($groupedItems as &$groupedItem) {
            foreach ($groupedItem['product_items'] as $productItem) {
                $groupedItem['sub_total'] += $productItem['sub_total'];
            }
        }

        $groupedItems = array_values($groupedItems);
        $total = count($groupedItems);
        $total_pages = ceil($total / $per_page);
        $page = max($page, 1);
        $page = min($page, $total_pages);
        $offset = ($page - 1) * $per_page;
        if ($offset < 0) {
            $offset = 0;
        }
        $groupedItems = array_slice($groupedItems, $offset, $per_page);

        $customer_shipment = $order->customer->shipments()
            ->select('customer_shipment_id', 'customer_id', 'name', 'address', 'city', 'province', 'district', 'zip_code', 'shipment_type', 'phone_number')
            ->get();

        $DeliveryOrder = DB::table('delivery_order')
            ->leftJoin('order_header', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
            ->leftJoin('invoice', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
            ->leftJoin('proforma_invoice', 'order_header.sales_order_no', '=', 'proforma_invoice.sales_order_no')
            ->where('delivery_order.sales_order_no', $order->sales_order_no)
            ->select('delivery_order.*', 'invoice.invoice_no', 'proforma_invoice.due_date as payment_date', 'invoice.status as payment_status')
            ->first();

        if ($DeliveryOrder != null and $DeliveryOrder->invoice_no != null) {
            $nomorresi = DB::table('delivery_number')
                ->leftJoin('invoice', 'invoice.invoice_no', '=', 'delivery_number.invoice_no')
                ->where('invoice.invoice_no', $DeliveryOrder->invoice_no)
                ->select('delivery_number.delivery_no as noresi', 'delivery_number.delivery_name as namaresi')
                ->get()
                ->toArray();
        }

        $data_dp = Invoice::where('sales_order_no', $order->sales_order_no)->where('invoice_type', 'DOWN PAYMENT')->first();
        $data_bill = Invoice::where('sales_order_no', $order->sales_order_no)->where('invoice_type', 'BILLING')->first();
        $isPaid = Invoice::where('sales_order_no', $order->sales_order_no)->where('invoice_type', 'BILLING')->where('status', 'LUNAS')->first();
        $discount = $do != null ? $do_discount : $order->total_discount;
        $discount_percentage = $discount != 0 && $sub_total != 0 ? round(($discount / ($sub_total + $total_kustomisasi)) * 100, 2) : 0;
        $location_code = $order->location_code ?? null;
        $location_name = $order->location_name ?? null;
        $shipping_charges = $order->shipping_charges ?? 0;
        $envValue = env('S3_STREAM_URL');

        $data_detail = [
            'verifikasi_date' => OrderApproval::where('order_no', $order->order_no)->where('status', 'Approved')->pluck('action_date')->first() ?? null,
            'pembayaran_date' =>  empty($isPaid) ? null : (Invoice::where('sales_order_no', $order->sales_order_no)->pluck('created_date')->first() ?? null),
            'pembayaran_dp_date' => Invoice::where('sales_order_no', $order->sales_order_no)->where('invoice_type', 'DOWN PAYMENT')->pluck('created_date')->first() ?? null,
            'pesanan_diproses_date' => empty($isPaid) ? null : ($DeliveryOrder->created_date ?? null),
            'gi_date' => empty($isPaid) ? null : ($DeliveryOrder->good_issue_date ?? null),
            'pesanan_dikirim_date' => empty($isPaid) ? null  : ($DeliveryOrder->good_issue_date ?? null),
            'pesanan_diterima_date' => $order->completed_date ?? null,
            'pesanan_dibatalkan_date' => $order->status == 'Batal' ? $order->modified_date : null,
            'order_no' => $order->order_no,
            'order_status' => ($order->order_status == 'Diproses' || $order->order_status == 'Dikirim') && empty($isPaid) ? 'Pembayaran' : $order->order_status,
            'sales_order_no' => $order->sales_order_no ?? "",
            'no_resi' => $nomorresi,
            'invoice_no' => $data_bill->invoice_no ?? null,
            'invoice_no_dp' => empty($isPaid) ? null : ($data_dp->invoice_no ?? null),
            'delivery_number' => empty($isPaid) ? null : ($DeliveryOrder->delivery_order_no ?? ""),
            'transaction_date' => $order->created_date ?? null,
            'payment_status' => $DeliveryOrder->payment_status ?? $order->payment_status ?? null,
            'sales_name' => $order->sales_name,
            'bill_to' => $order->bill_to,
            'bill_to_address' => $order->bill_to_address,
            'bill_to_phone_number' => $order->bill_to_phone_number,
            'bill_to_email' => $order->bill_to_email,
            'ship_to' => $order->ship_to,
            'ship_to_address' => $order->ship_to_address,
            'ship_to_phone_number' => $order->ship_to_phone_number,
            // 'sub_total' => (int) $sub_total + $total_kustomisasi,
            'sub_total' => (int) $order->total,
            // 'sub_total' => (int) $order->total_nett,
            'total_discount' => (int) $discount,
            // 'total_discount_percentage' => $discount_percentage,
            'total_discount_percentage' => $customer->discount_percent,
            'total_dp' => (int) $order->dp_amount,
            'dp_percentage' => $order->dp_percentage,
            'total_kustomisasi' => (int) $total_kustomisasi,
            // 'total_nett' => (int) (($sub_total + $total_kustomisasi) + $shipping_charges - $discount + $total_tax),
            'total_nett' => (int) $order->total_nett,
            // 'total_nett' => (int) ($order->total_nett + $total_kustomisasi + $shipping_charges - $discount),
            'customer_status' => $customer->status,
            'transportation_zone' => $location_code . ' - ' . $location_name,
            'pks_file_path' => $order->pks_file_path,
            'pks_file_name' => $order->pks_file_name,
            'is_custom' => $customCheck,
            'can_confirm' => strtolower($customer->status) != 'pending' ? true : false,
            'dp_due_date' => $order->dp_due_date,
            'instance_name' => $customer->instance_name,
            'shipping_charges' => (int) $shipping_charges,
            'total_qty' => $total_qty,
            'payment_file' => $order->payment_file ? $envValue . $order->payment_file : null,
            'distribution_channel' => $order->distribution_channel,
            'customer_shipment_id' => $order->customer_shipment_id,
            'items' => $groupedItems,
            'customer_shipment' => $customer_shipment
        ];

        $response = [
            'total_data' => $total,
            'size' => $per_page,
            'active_page' => $page,
            'total_page' => (int) $total_pages,
            'order_detail' => $data_detail
        ];

        if (in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru, self::OrderStatusVerif])) {
            // $category = $order_detail[0]->product->lvl3_description == 'BAGS' ? 'BAGS' : 'NON BAGS';
            $m_d = DB::table('matrix_discount')
                ->where('is_custom', $customCheck == true ? 1 : 0)
                ->whereRaw('? BETWEEN min_bruto_from AND min_bruto_to', [(int) $order->total])
                ->whereRaw('? BETWEEN qty_from AND qty_to', [(int) $total_qty])
                // ->where('category', $category)
                ->first();

            $websocket = event(new GenericEvent(auth()->user()->sales->sales_id, [
                'total_tax' => 0,
                'total' => $order->total,
                'total_discount' => $m_d->discount * ($order->total / 100),
                'total_nett' => 0,
                'nett_before_tax' => 0
            ], 'simulateSO.new'));

            Log::info($websocket);
        }

        return $this->sendSuccess(null, $response);
    }

    public function downloadTransactions(Request $request)
    {
        try {
            $salesId = auth()->user()->reference_id;
            $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
            $salesIdd = !in_array("0", $roles) ?
                array_map(
                    function ($i) {
                        return $i->sales_id ?? '0';
                    },
                    $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())
                )
                : [];
            array_push($salesIdd, $salesId);
            $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                ->pluck('customer_id')->all();

            $order_no = $request->input('order_no');
            $ref_order = $request->input('ref_order');
            $nama_customer = $request->input('nama_customer');
            $nama_instansi = $request->input('nama_instansi');
            $status = $request->input('status_pesanan');
            $date_to = $request->input('date_to');
            $date_from = $request->input('date_from');
            $nama_sales = $request->input('nama_sales');
            $custom = $request->input('custom');

            $rows = [];
            
            $items = DB::table('order_header')
                ->when($custom === 'true', function ($query) {
                    return $query->leftJoin('order_custom', 'order_header.order_no', '=', 'order_custom.reference_id')
                        ->leftJoin('article', 'order_custom.article_id', '=', 'article.article')
                        ->leftJoin('article_price', function ($join) {
                            $join->on('article.sku_code_c', '=', 'article_price.sku_code_c')
                                ->whereColumn('article_price.valid_from', '<=', 'order_header.created_date')
                                ->whereColumn('article_price.valid_to', '>=', 'order_header.created_date');
                        });
                }, function ($query) {
                    return $query->leftJoin('order_detail', 'order_header.order_no', '=', 'order_detail.order_no')
                    ->leftJoin('article', 'order_detail.article_id', '=', 'article.article');
                })
                ->leftJoin('customers', 'order_header.customer_id', '=', "customers.customer_id")
                ->leftJoin('customer_shipment', 'order_header.customer_shipment_id', '=', "customer_shipment.customer_shipment_id")
                ->leftJoin('sales', 'order_header.sales_id', '=', 'sales.sales_id')
                ->leftJoin('delivery_order', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
                ->leftJoin('delivery_order_detail', function ($j) {
                    $j->on('delivery_order.delivery_order_no', '=', 'delivery_order_detail.delivery_order_no')
                        ->on('article.article', '=', 'delivery_order_detail.article');
                })
                ->leftJoin('invoice', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
                ->leftJoin('invoice_detail', function ($j) {
                    $j->on('invoice.invoice_no', '=', 'invoice_detail.invoice_no')
                        ->on('article.article', '=', 'invoice_detail.article');
                })
                ->whereIn('order_header.distribution_channel', ['B2B', 'W3', 'RE', 'RD'],)
                ->when($status, function ($query) use ($status) {
                    $status = explode(',', $status);
                    $q_status = [];
                    $i = 0;
                    foreach ($status as $data) {
                        $cek = str_contains($data, '_');
                        if ($cek == true) {
                            $q_status[$i] = str_replace('_', ' ', $data);
                        } else {
                            $q_status[$i] = $data;
                        }
                        $i++;
                    }
                    return $query->whereIn('order_header.order_status', $q_status);
                })
                // ->when($dateFrom, function($query) use($dateFrom, $dateTo){
                //     return $query->whereBetween('order_header.created_date', [$dateFrom, $dateTo]);
                // })
                ->when($date_from, function ($query) use ($date_from) {
                    return $query->whereDate('order_header.created_date', ">=", $date_from);
                })
                ->when($date_to, function ($query) use ($date_to) {
                    return $query->whereDate('order_header.created_date', "<=", $date_to);
                })
                ->when($ref_order, function ($query) use ($ref_order) {
                    return $query->where('order_header.sales_order_no', 'like', '%' . $ref_order . '%');
                })
                ->when($nama_customer, function ($query) use ($nama_customer) {
                    return $query->where('order_header.bill_to', 'like', '%' . $nama_customer . '%');
                })
                ->when($nama_instansi, function ($query) use ($nama_instansi) {
                    return $query->where('customers.instance_name', 'like', '%' . $nama_instansi . '%');
                })
                ->when($order_no, function ($query) use ($order_no) {
                    return $query->where('order_header.order_no', 'like', '%' . $order_no . '%');
                })
                ->when($nama_sales, function ($query) use ($nama_sales) {
                    return $query->where('order_header.sales_name', 'like', '%' . $nama_sales . '%');
                })
                ->when(!in_array("0", $roles), function ($query) use ($customerIdList) {
                    $query->whereIn('order_header.customer_id', $customerIdList);
                })
                ->when($custom == 'true', function ($q) {
                    $orderNos = OrderCustom::pluck('reference_id');
                    $q->whereIn('order_header.order_no', $orderNos);
                })
                ->when($custom == 'false', function ($q) {
                    $orderNos = OrderItem::pluck('order_no');
                    $q->whereIn('order_header.order_no', $orderNos);
                })
                // ->when(!$custom || $custom == 'false', function ($q) {
                //     $orderNos = OrderCustom::pluck('reference_id');
                //     $q->whereNotIn('order_header.order_no', $orderNos);
                // })
                ->select(
                    // 'order_detail.is_custom',
                    'order_header.bill_to',
                    'order_header.ship_to as store_name',
                    'order_header.sales_name as sales_name',
                    'order_header.created_date as order_date',
                    'order_header.order_status',
                    'order_header.order_no',
                    'order_header.sales_order_no',
                    'delivery_order.delivery_order_no',
                    'invoice.invoice_no',
                    DB::raw($custom === 'true' ? 'order_custom.article_id as article_id' : 'order_detail.article_id as article_id'),
                    DB::raw($custom === 'true' ? 'article.product_name_c as product_name' : 'order_detail.product_name as product_name'),
                    DB::raw($custom === 'true' ? 'article.product_size_c as product_size' : 'order_detail.product_size as product_size'),
                    DB::raw($custom === 'true' ? 'article.product_variant_c as product_variant' : 'order_detail.product_variant as product_variant'),
                    DB::raw($custom === 'true' ? 'article_price.amount as price' : 'order_detail.price as price'),
                    DB::raw($custom === 'true' ? 'order_custom.qty as qty' : 'order_detail.qty as qty'),
                    'delivery_order_detail.issued_qty as do_detail_issued_qty',
                    'delivery_order_detail.qty as do_detail_qty',
                    'order_header.total_discount as order_header_discount',
                    'delivery_order.discount as delivery_order_discount',
                    'invoice_detail.gross_price as invoice_detail_gp',
                    'invoice_detail.discount_percent as invoice_detail_discount_percent',
                    'article.lvl3_description',
                    'article.product_style',
                    'delivery_order.good_issue_date as gi_date',
                    'invoice.billing_date',
                    'order_header.total as order_header_total',
                    'delivery_order.total as delivery_order_total',
                    'article.sku_code_c',
                    'delivery_order.issued_total as delivery_order_issued_total',
                    'delivery_order.issued_discount as delivery_order_issued_discount'
                )
                ->orderBy('order_header.created_date', 'desc')
                ->chunk(100, function ($data) use (&$rows, &$custom) {
                    foreach ($data as $d) {
                        $ocs_exs = OrderCustom::where('reference_id', $d->order_no)->exists();
                        $custom_price = 0;
                        if ($ocs_exs == true) {
                            $ocs = OrderCustom::where('reference_id', $d->order_no)
                                ->where('sku', $d->sku_code_c)
                                ->pluck('id')->toArray();
                            $custom_price = OrderCustomAttachment::whereIn('order_custom_id', $ocs)
                                ->sum('custom_price');
                        }
                        $oh_disc_percent = ($d->order_header_discount != 0 && $d->order_header_total != 0)
                            ? $d->order_header_discount / $d->order_header_total
                            : 0;
                        $gi_base = $d->delivery_order_issued_total + $d->delivery_order_issued_discount;
                        $gi_disc_percent = ($d->delivery_order_no != null && $d->gi_date != null && $gi_base != 0)
                            ? $d->delivery_order_issued_discount / $gi_base
                            : 0;
                        $do_base = $d->delivery_order_total + $d->delivery_order_discount;
                        $do_disc_percent = ($d->delivery_order_no != null && $do_base != 0)
                            ? $d->delivery_order_discount / $do_base
                            : 0;
                        $qty = $d->delivery_order_no == null ? $d->qty : ($d->delivery_order_no != null && $d->gi_date != null ? $d->do_detail_issued_qty : $d->do_detail_qty);
                        $discount = $d->invoice_no != null ? ($d->invoice_detail_gp + ($custom_price * $qty)) * ($d->invoice_detail_discount_percent / 100) : ($d->delivery_order_no != null && $d->gi_date != null ? (($d->price + $custom_price) * $qty) * ($gi_disc_percent) : ($d->delivery_order_no != null ? (($d->price + $custom_price) * $qty) * ($do_disc_percent) : (($d->price + $custom_price) * $qty) * ($oh_disc_percent)));
                        $row = [
                            'tipe_order' => $ocs_exs == true ? 'Custom' : 'Non-Custom',
                            'nama_akun' => $d->bill_to,
                            'nama_toko' => $d->store_name,
                            'tanggal_pesan' => Carbon::parse($d->order_date)->format('Y-m-d'),
                            'status_pesanan' => $d->order_status,
                            '#order' => $d->order_no,
                            '#ref_order' => $d->sales_order_no,
                            '#dn' => $d->delivery_order_no,
                            '#billing' => $d->invoice_no,
                            'article' => $d->article_id,
                            'article_description' => $d->product_name . ' ' . $d->product_variant . ' ' . $d->product_size,
                            'price' => (int) $d->price,
                            'additional_cost' => $custom_price * $qty,
                            'qty_order' => $qty,
                            'diskon' => (int) $discount,
                            'sub_total' => ((int) $qty * (int) ($d->price + $custom_price)) - (int) $discount,
                            'item_product' => $d->lvl3_description,
                            'product_category' => $d->product_style,
                            'nama_sales' => $d->sales_name,
                            'tanggal_gi' => $d->delivery_order_no == null ? null : Carbon::parse($d->gi_date)->format('Y-m-d'),
                            'tanggal_billing' => $d->invoice_no == null ? null : Carbon::parse($d->billing_date)->format('Y-m-d')
                        ];

                        $rows[] = $row;
                    }
                });

            return $this->sendSuccess('Export Transaction B2B Internal Success', $rows);
        } catch (\Exception $e) {
            Log::info('Export Transaction B2B Internal Failed : ' . $e->getMessage() . '. Line : ' . $e->getLine());
            return $this->sendError('Export Transaction B2B Internal Failed');
        }
    }

    public function transactionApproval(Request $request, SAP $SAP)
    {
        try {

            $param = [];

            $order_approval = OrderApproval::where('order_no', $request->order_no)
                ->first();

            $order = Order::where('order_no', $request->order_no)
                ->first();

            $customer_shipment = CustomerShipment::where('customer_shipment_id', $order->customer_shipment_id)
                ->first();

            $invoices = Invoice::where('customer_id', $order->customer_id)->pluck('status')->toArray();

            $oc_exs = OrderCustom::where('reference_id', $request->order_no)->exists();

            if ($order->location_code == null || ($oc_exs == true && $order->dp_amount > 0 && $order->dp_due_date == null)) {
                return $this->sendError('Transportation Zone or DP Due Date is required', 403);
            }

            if (!$order_approval || !$order) {
                return $this->sendError("Order not found", 403);
            }

            if (in_array('BELUM LUNAS', $invoices) or in_array('BELUM DIBAYAR', $invoices)) {
                return $this->sendError('Maaf pesanan ini tidak dapat diproses karena ada tagihan yang belum dibayarkan');
            }

            $db_customer = DB::table('customers')->where('customer_id', $order->customer_id)->first();

            if ($db_customer->registered_sap_at == null) {
                CreditLimit::updateOrCreate(
                    [
                        'customer_external_id' => $db_customer->customer_id,
                    ],
                    [
                        'credit_limit' => $order->total + 15000000,
                        'credit_limit_used' => $order->total,
                        'credit_limit_remaining' => 15000000,
                        'credit_limit_used_percentage' => ($order->total / (($order->total + 15000000) / 100)),
                        'currency' => 'IDR'
                    ]
                );
            }

            $order_approval->status = $request->status;
            $order_approval->action_by = auth()->user()->reference_id;
            $order_approval->action_date = now()->format('Y-m-d H:i:s');
            $order_approval->modified_by = auth()->user()->reference_id;
            $order_approval->modified_date = now()->format('Y-m-d H:i:s');
            $order_approval->save();

            $order->sales_id = auth()->user()->reference_id;
            $order->sales_name = auth()->user()->sales->sales_name;
            $order->save();

            if ($request->status == 'Approved') {
                // $customer = Customer::where('id',$order->customer_id)->first(); // kedepannya
                $customer = Customer::where('customer_id', $order->customer_id)->first();

                $order_array = $order->toArray();

                if (!$oc_exs) {
                    $order_item = OrderItem::with('product.mainImageVariant')->where('order_no', $request->order_no)->get();
                    foreach ($order_item as $od) {
                        $od['product']['flag'] = $od->product->flag();
                    }

                    $order_array['order_detail'] = $order_item->toArray();
                } else {
                    $order_custom = OrderCustom::with('product.mainImageVariant')->where('reference_id', $request->order_no)->get();
                    foreach ($order_custom as $oc) {
                        $oc['product']['flag'] = $oc->product->flag();
                    }

                    $order_array['order_detail'] = $order_custom->toArray();
                }

                $param['order'] = $order_array;
                $param['customer'] = $customer->toArray();

                $socmed = new GetSocialsRepo();
                $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
                $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
                $twitter = $socmed->getSocialMediaParameters('TWITTER');
                $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
                $youtube = $socmed->getSocialMediaParameters('YOUTUBE');
                $support = $socmed->getSocialMediaParameters('SUPPORT');
                $linkedin = $socmed->getSocialMediaParameters('LINKEDIN');

                $param['social_media'] = [
                    'facebook' => $facebook,
                    'twitter' => $twitter,
                    'instagram' => $instagram,
                    'support' => $support,
                    'linkedin' => $linkedin
                ];

                if ($order->sales_order_no == null) {
                    $create_so = $SAP->salesOrder('i', $request->order_no, $oc_exs);

                    if (isset($create_so['error'])) {
                        $order->order_status = 'Pending';
                        $order->save();
                        return $this->sendError("Error when SO B2B Integration", 500, '', ['type' => 'Error Care OM', 'message' => $create_so['error']]);
                    }

                    $res_so = $create_so->json();

                    if ($create_so->failed()) {
                        $order->order_status = 'Pending';
                        $order->save();

                        return $this->sendError("Error when SO B2B Integration", 500, '', ['type' => 'Error SAP', 'message' => $res_so]);
                    }

                    if ($res_so == null) {
                        $order->order_status = 'Pending';
                        $order->save();
                        return $this->sendError("Error when customer B2B Integration", 500, '', ['type' => 'Error SAP', 'message' => null]);
                    }

                    if ($res_so['message'][0]['type'] == 'E') {
                        $order->order_status = 'On Hold';
                        $order->save();

                        $sap_error = MasterParameter::where('group_key', 'CREATE_SO_ERROR')->pluck('description')->toArray();

                        foreach ($res_so['message'] as $message) {
                            $sap_error = MasterParameter::where('group_key', 'CREATE_SO_ERROR')->where('description', $message['message'])->first();
                            $error_msg[] = $sap_error->value ?? $message['message'];
                        }

                        return $this->sendError("Error when SO B2B Integration", 500, '', ['type' => 'Error SAP', 'message' => $error_msg]);
                    }

                    $order->sales_order_no = ltrim($res_so['message'][0]['vbeln'], "0"); // karena sap ngirim ada 00 depannya
                    $order->total = $res_so['message'][0]['gross'];

                    if ($oc_exs) {
                        $total_kustomisasi = OrderCustom::where('reference_id', $request->order_no)->sum('custom_price');
                        $order->total_nett = $order->total - $order->total_discount + $total_kustomisasi;
                    } else {
                        $order->total_nett = $order->total - $order->total_discount;
                    }
                    
                    $order->save();
                }

                if ($customer->registered_sap_at == null) {
                    $create_customer = $SAP->createCustomer($customer, 'Create', $order_approval, $order);
                } else {
                    $create_customer = $SAP->createCustomer($customer, 'Update', $order_approval, $order);
                }
                
                if (isset($create_customer['error'])) {
                    $order->order_status = 'Pending';
                    $order->save();
                    return $this->sendError("Error when customer B2B Integration", 500, '', ['type' => 'Error Care OM', 'message' => $create_customer['error']]);
                }

                $res_customer = $create_customer->json();

                if ($create_customer->failed()) {
                    $order->order_status = 'Pending';
                    $order->save();
                    return $this->sendError("Error when customer B2B Integration", 500, '', ['type' => 'Error SAP', 'message' => $res_customer]);
                }

                if ($res_customer == null) {
                    $order->order_status = 'Pending';
                    $order->save();
                    return $this->sendError("Error when customer B2B Integration", 500, '', ['type' => 'Error SAP', 'message' => null]);
                }

                if (isset($res_customer['error'])) {
                    $order->order_status = 'Pending';
                    $order->save();
                    return $this->sendError("Error when customer B2B Integration", 500, '', ['type' => 'Error SAP', 'message' => $res_customer['message']]);
                }

                if (isset($res_customer['success']) && $res_customer['success'] == 'false') {
                    $order->order_status = 'Pending';
                    $order->save();
                    return $this->sendError("Error when customer B2B Integration", 500, '', ['type' => 'Error SAP', 'message' => $res_customer['message']]);
                }

                if (isset($res_customer['d']['requeststatus']) && $res_customer['d']['requeststatus'] != 'OK') {
                    $order->order_status = 'On Hold';
                    $order->save();
                    return $this->sendError("Error when customer B2B Integration", 500, '', ['type' => 'Error SAP', 'message' => $res_customer['d']['requeststatus']]);
                }

                // Customer::where('customer_id', $order->customer_id)->update(['status' => 'pending']);
                Customer::where('customer_id', $order->customer_id)->update(['registered_sap_at' => now()]);

                $order->order_status = 'Baru';
                $order->save();

                // $this->notifStore($customer->customer_id, "Pesanan Dibuat", "order", "Pesanan dengan nomor $request->order_no telah berhasil dibuat, klik disini untuk melihat detail", $request->order_no);
                // MailSender::dispatch($customer->email, json_encode($param), 'mail_order_b2b');
            }

            return $this->sendSuccess('Order approval has been updated', $order_approval->toArray());
        } catch (\Exception $e) {
            Log::info('Transaction approval ' . $request->order_no . ' error : ' . $e->getMessage() . ' Line : ' . $e->getLine());
            return $this->sendError('Failed to update order approval');
        }
    }

    public function editOrders(EditOrderRequest $request, $order_no)
    {

        $orderheader = Order::where('order_no', $order_no)->first();
        if (!$orderheader) {
            return $this->sendError('order not found', 400);
        }

        if ($request->dp > 0) {
            if ($request->dp_due_date == null) {
                return $this->sendError('DP Due Date is required', 400);
            }
        }

        $orders = $request->orders;
        $bruto = 0;
        $qty = 0;

        if ($orders != null) {
            foreach ($orders as $order) {
                $item = $order['article'];
                $orderdetail = OrderItem::where('article_id', $item)->where('order_no', $order_no)->first();
                if (!$orderdetail) {
                    return $this->sendError('item not found', 400);
                }

                $article = Product::where('article', $order['article'])->first();
                $custom_price = 0;
                if ($request->is_custom == 1) {
                    $ocs = OrderCustom::where('reference_id', $order_no)
                        ->where('sku', $article->sku_code_c)
                        ->pluck('id')->toArray();
                    $custom_price = OrderCustomAttachment::whereIn('order_custom_id', $ocs)
                        ->sum('custom_price');
                }
                $category = $article->lvl3_description == 'BAGS' ? 'BAGS' : 'NON BAGS';
                $bruto += ($article->price->amount + $custom_price) * $order['quantity'];
                $qty += $order['quantity'];

                $newqty = $order['quantity'];
                switch (true) {
                    case $newqty <= 0:
                        $orderdetail->delete();
                        break;

                    case $newqty > 0:
                        $orderdetail->qty = $newqty;
                        $orderdetail->sub_total = $newqty * $orderdetail->price;
                        $orderdetail->total = $orderdetail->sub_total - ($orderdetail->primary_discount + $orderdetail->additional_discount);
                        $orderdetail->save();
                        break;
                }
            }

            //kalkulasi diskon
            if (!$request->discount) {
                //simulate discount
                $m_d = DB::table('matrix_discount')
                    ->where('is_custom', $request->is_custom ?? 0)
                    ->whereRaw('? BETWEEN min_bruto_from AND min_bruto_to', [(int) $bruto])
                    ->whereRaw('? BETWEEN qty_from AND qty_to', [(int) $qty])
                    ->where('category', $category)
                    ->first();

                $orderheader->total = $bruto;
                $orderheader->total_discount = $m_d->discount * ($bruto / 100);
                $orderheader->total_nett = $orderheader->total - $orderheader->total_discount;
            } else {
                $orderheader->total = $bruto;
                $orderheader->total_discount = $orderheader->total * ($request->discount / 100);
                $orderheader->total_nett = $orderheader->total - $orderheader->total_discount;
            }
        } else {
            $orderheader->total_discount = $orderheader->total * ($request->discount / 100);
            $orderheader->total_nett = $orderheader->total - $orderheader->total_discount;
        }


        //kalkulasi DP
        $orderheader->dp_percentage = $request->dp ?? 0;
        $orderheader->dp_amount = $orderheader->total_nett * ($request->dp / 100);

        //insert transportation_zone
        $orderheader->location_code = $request->location_code;
        $orderheader->location_name = $request->location_name;

        $orderheader->dp_due_date = $request->dp_due_date ?? null;

        $orderheader->save();

        return $this->sendSuccess('data updated');
    }

    //TODO order custom
    public function editCustoms(EditOrderRequest $request, $order_no)
    {

        $orderheader = Order::where('order_no', $order_no)->first();
        if (!$orderheader) {
            return $this->sendError('order not found', 400);
        }

        if ($request->dp > 0) {
            if ($request->dp_due_date == null) {
                return $this->sendError('DP Due Date is required', 400);
            }
        }

        $orders = $request->orders;
        $customs = $request->customs;
        $bruto = 0;
        $qty = 0;

        if ($customs || $orders) {

            foreach ($customs as $customItem) {

                $customData = OrderCustom::where('id', $customItem['custom_id'])->first();
                //upload gambar
                $img_product_custom = $customItem['background_image'];
                $img_ext = ['png', 'svg', 'jpeg', 'jpg'];
                if (!in_array(pathinfo($img_product_custom)['extension'], $img_ext)) {
                    return $this->sendError('Image product custom is not the right format', 404);
                }
                // $img_file_name = pathinfo($img_product_custom)['basename'];
                // $img_file_path = $img_product_custom;
                $img_file_path = $this->fileTransfer($img_product_custom, 'custom_image');
                if ($img_file_path['error'] == true) {
                    return $this->sendError($img_file_path['message']);
                }
                // Storage::disk('s3')->put(substr($img_file_path,1), file_get_contents($img_product_custom));
                //--

                $customData->generated_file_path = '/' . $img_file_path['filepath'];

                foreach ($customItem['attachment_data'] as $attachment) {

                    // $logo_product_custom = $attachment['file_path'];
                    // $img_ext = ['png','svg'];

                    // if (!in_array($logo_product_custom->extension(),$img_ext)) {
                    //     return $this->sendError('Image product custom is not the right format', 404);
                    // }

                    // $img_file_name = $customData->sku.'-'.$customData->position_side.'-'.uniqid().'-custom_logo.'.$logo_product_custom->extension();
                    // $logo_file_path = env('S3_CUSTOM_LOGO_FOLDER').$img_file_name;
                    // Storage::disk('s3')->put(substr($logo_file_path,1), file_get_contents($logo_product_custom));

                    $attachmentData = OrderCustomAttachment::where('id', $attachment['attachment_id'])->first();
                    $attachmentData->position_x = $attachment['position_x'] ?? $attachmentData->getOriginal('position_x');
                    $attachmentData->position_y = $attachment['position_y'] ?? $attachmentData->getOriginal('position_y');
                    $attachmentData->dimension_height = $attachment['height'] ?? $attachmentData->getOriginal('dimension_height');
                    $attachmentData->dimension_width = $attachment['width'] ?? $attachmentData->getOriginal('dimension_width');
                    $attachmentData->notes = $attachment['notes'] ?? $attachmentData->getOriginal('notes');
                    $attachmentData->save();
                }


                $ocAfterUpdate = OrderCustomAttachment::where('order_custom_id', $customItem['custom_id'])->get();
                $customData->custom_price = $ocAfterUpdate->sum('custom_price');
                $customData->save();
            }

            if ($orders) {
                foreach ($orders as $order) {
                    $item = $order['article'];
                    $orderdetail = OrderItem::where('article_id', $item)->where('order_no', $order_no)->first();
                    $orderCustomSum = OrderCustom::where('article_id', $item)->where('reference_id', $order_no)->sum('custom_price');
                    if (!$orderdetail) {
                        return $this->sendError('item not found', 400);
                    }

                    $article = Product::where('article', $order['article'])->first();

                    $newqty = $order['quantity'];
                    switch (true) {
                        case $newqty <= 0:
                            $orderdetail->delete();
                            break;

                        case $newqty > 0:
                            $orderdetail->qty = $newqty;
                            $orderdetail->sub_total = $newqty * $orderdetail->price + $orderCustomSum;
                            $orderdetail->total = $orderdetail->sub_total - ($orderdetail->primary_discount + $orderdetail->additional_discount);
                            $category = $article->lvl3_description == 'BAGS' ? 'BAGS' : 'NON BAGS';
                            $bruto += ($article->price->amount) * $orderdetail->qty;
                            $qty += $orderdetail->qty;
                            $orderdetail->save();
                            break;
                    }
                }

                $ODafterupdate = OrderItem::where('order_no', $order_no)->get();

                //kalkulasi diskon
                //simulate discount
                $m_d = DB::table('matrix_discount')
                    ->where('is_custom', $request->is_custom ?? 0)
                    ->whereRaw('? BETWEEN min_bruto_from AND min_bruto_to', [(int) $bruto])
                    ->whereRaw('? BETWEEN qty_from AND qty_to', [(int) $qty])
                    ->where('category', $category)
                    ->first();

                $orderheader->total = $ODafterupdate->sum('sub_total');
                $orderheader->total_discount = $m_d->discount * ($bruto / 100);
                $orderheader->total_nett = $orderheader->total - $orderheader->total_discount;
                $orderheader->modified_by = auth()->user()->sales->sales_name;


                $orderheader->save();

            }


            if ($request->location_code != null && $request->location_code != null) {

                $ODafterupdate = OrderItem::where('order_no', $order_no)->get();

                if ($request->discount != null) {
                    $orderheader->total = $ODafterupdate->sum('sub_total');
                    $orderheader->total_discount = $orderheader->total * ($request->discount / 100);
                    $orderheader->total_nett = $orderheader->total - $orderheader->total_discount;
                }


                //kalkulasi DP
                $orderheader->dp_percentage = $request->dp ?? 50;
                $orderheader->dp_amount = $orderheader->total_nett * ($orderheader->dp_percentage / 100);

                //insert transportation_zone
                $orderheader->location_code = $request->location_code ?? $orderheader->getOriginal('location_code');
                $orderheader->location_name = $request->location_name ?? $orderheader->getOriginal('location_name');

                //insert dp due date
                $orderheader->dp_due_date = $request->dp_due_date ?? $orderheader->getOriginal('dp_due_date');

                $orderheader->modified_by = auth()->user()->sales->sales_name;

                $orderheader->save();
            }

        }


        return $this->sendSuccess('data updated');

    }


    public function getDataCustom($order_no)
    {

        $customOrderDataArray = []; // Initialize an empty array to hold custom order data

        $customOrders = OrderCustom::where('reference_id', $order_no)->get();

        if (!$customOrders) {
            return $this->sendError('custom order not found');
        }

        if ($customOrders) {
            $sku = [];
            foreach ($customOrders as $customOrder) {
                $customOrderAttachments = OrderCustomAttachment::where('order_custom_id', $customOrder->id)->get();

                // Initialize an empty array to hold attachment data for the current custom order
                $attachmentDataArray = [];

                foreach ($customOrderAttachments as $customOrderAttachment) {
                    $attachmentData = [
                        'attachment_id' => $customOrderAttachment->id,
                        'price' => $customOrderAttachment->custom_price,
                        'position_x' => $customOrderAttachment->position_x,
                        'position_y' => $customOrderAttachment->position_y,
                        'notes' => $customOrderAttachment->notes,
                        'size' => $customOrderAttachment->size,
                        'custom_text' => $customOrderAttachment->custom_text,
                        'custom_type' => $customOrderAttachment->custom_type,
                        'material' => $customOrderAttachment->material,
                        'dimension_width' => $customOrderAttachment->dimension_width,
                        'dimension_height' => $customOrderAttachment->dimension_height,
                        'file_path' => env('S3_STREAM_URL') . $customOrderAttachment->file_path
                    ];
                    // Add the attachment data to the array for this custom order
                    $attachmentDataArray[] = $attachmentData;
                }

                $customData = [
                    'custom_id' => $customOrder->id,
                    'position_side' => $customOrder->position_side,
                    'article_id' => $customOrder->article_id,
                    'generic_id' => $customOrder->sku,
                    'reference_id' => $customOrder->reference_id,
                    'reference_name' => $customOrder->reference_name,
                    'main_background_image' => env('S3_STREAM_URL') . $customOrder->generated_file_path,

                    //attachment data array
                    'attachment_data' => $attachmentDataArray
                ];
                // Add the custom order data to the main array
                $customOrderDataArray[] = $customData;
                $sku[] = $customOrder->sku;
            }

            $articles = Product::whereIn('sku_code_c', $sku)->pluck('article')->toArray();
            $carts = CartDetail::whereIn('article', $articles)->where('cart_id', $order_no)->where('is_custom', 1)->get()->toArray();
            $article_cart = array_column($carts, 'article');
            $customer_id = auth()->user()->sales ? auth()->user()->sales->sales_id : auth()->user()->customer->customer_id;
            $stock_collection = RestHelper::stockCache($article_cart, $customer_id);
            foreach ($carts as &$cart) {
                $article = Product::where('article', $cart['article'])->first();
                $stock = RestHelper::searchStock($stock_collection, $cart['article']);
                if ($article) {
                    $cart['variant'] = Color::getByKey($article->product_variant_c);
                    $cart['size'] = $article->product_size_c;
                    $cart['stock'] = (int) $stock['qty'] ?? 0;
                    $cart['min_qty'] = (int) $stock['moq'] ?? 0;
                    $cart['generic_id'] = $article->sku_code_c;
                }
            }

        } else {
            $customOrderDataArray = null;
        }

        return $this->sendSuccess('data successfully retrieved', ['customs' => $customOrderDataArray, 'carts' => $carts]);

    }

    public function editCustomPrice(Request $request)
    {
        $bodies = $request->input();
        foreach ($bodies as $body) {
            $orderCustomAttachment = OrderCustomAttachment::where('id', $body['id'])->first();
            if (!$orderCustomAttachment) {
                return $this->sendError('orderCustomAttachment Not found');
            }

            $orderCustomAttachment->custom_price = $body['custom_price'] ?? $orderCustomAttachment->getOriginal('custom_price');
            $orderCustomAttachment->modified_by = auth()->user()->sales->sales_name;
            $orderCustomAttachment->save();
        }

        return $this->sendSuccess('custom price updated');
    }

    public function createTransaction(CreateTransactionRequest $request)
    {
        DB::beginTransaction();
        try {
            $Customer = Customer::where('customer_id', $request->customer_id)->first();

            if (!$Customer) {
                return $this->sendError('Customer not found.', 404);
            }

            $items = $request->article_items;
            $orderNosByDescription = [];
            $orderTotals = [];
            $orderDetailsTemp = [];
            $total = 0;
            // $item_disc = [
            //     'is_custom' => 0,
            //     'data' => []
            // ];

            foreach ($items as $item) {
                $article = Product::with([
                    'price' => fn($q) => $q->orderBy('valid_from', 'desc'),
                    'skuStock'
                ])->where('article', $item['article_id'])->first();

                if (!$article) continue;

                $price = $article->price->amount ?? 0;
                $qty = $item['article_qty'];
                $stock = $article->skuStock->stock ?? null;
                $isAvailable = 1;

                if ($stock === 0) $isAvailable = 0;
                if ($stock !== null && $qty > $stock) $qty = $stock;

                if (!isset($orderNosByDescription[$article->lvl4_description])) {
                    $orderNosByDescription[$article->lvl4_description] = strtoupper('INVBNC' . substr(uniqid(), -6));
                }

                $orderNo = $orderNosByDescription[$article->lvl4_description];
                $subTotal = $price * $qty;

                $total += $subTotal;

                $orderTotals[$orderNo]['subtotal'] = ($orderTotals[$orderNo]['subtotal'] ?? 0) + $subTotal;

                $orderDetailsTemp[] = [
                    'order_no' => $orderNo,
                    'article' => $article,
                    'price' => $price,
                    'qty' => $qty,
                    'sub_total' => $subTotal,
                    'is_available' => $isAvailable,
                    'stock' => $stock ?? 0
                ];

                // $item_disc['data'][] = [
                //     'article' => $article->article,
                //     'qty' => $qty
                // ];
            }

            // $customer_discount = ($Customer->discount_percent / 100) * $total;
            // $product_discount = 0;

            // $sim_disc = $this->simulateDiscountB2B($item_disc);
            // if ($sim_disc !== false && isset($sim_disc['total_discount'])) {
            //     $product_discount = $sim_disc['total_discount'];
            // }

            // $total_discount = max($customer_discount, $product_discount);
            $total_discount = $request->total_discount;

            $orderDetails = [];
            foreach ($orderDetailsTemp as $temp) {
                $subTotal = $temp['sub_total'];
                $proportion = $subTotal / $total;
                $primary_discount = $proportion * $total_discount;

                $article = $temp['article'];

                $orderDetails[] = [
                    'order_no' => $temp['order_no'],
                    'article_id' => $article->article,
                    'product_name' => $article->product_name_c,
                    'product_variant' => $article->product_variant_c,
                    'product_size' => $article->product_size_c,
                    'is_new_arrival' => '',
                    'price' => $temp['price'],
                    'qty' => $temp['qty'],
                    'issued_qty' => $temp['qty'],
                    'sub_total' => $subTotal,
                    'additional_discount' => 0,
                    'primary_discount' => $primary_discount,
                    'total' => $subTotal - $primary_discount,
                    'is_available' => $temp['is_available'],
                    'stock' => $temp['stock'],
                    'created_by' => Auth::user()->name,
                    'modified_by' => Auth::user()->name,
                ];

                $orderTotals[$temp['order_no']]['discount'] = ($orderTotals[$temp['order_no']]['discount'] ?? 0) + $primary_discount;
            }

            $orderNoList = array_values($orderNosByDescription);
            $countOrderNoList = count($orderNoList);
            $orderGroup = (string) Str::uuid();

            $customerfirstaddress = CustomerShipment::where('customer_id', $Customer->customer_id)
                ->where('is_selected', true)
                ->first();

            // if ($Customer->registered_sap_at !== null) {
            //     $data = CustomerShipment::where('customer_id', $Customer->customer_id)
            //         ->get()
            //         ->filter(fn($cs) => !Str::isUuid($cs->customer_shipment_id))
            //         ->pluck('customer_shipment_id');

            //     $customerfirstaddress = CustomerShipment::where('customer_id', $Customer->customer_id)
            //         ->whereIn('customer_shipment_id', $data)
            //         ->first();
            // }

            $zone = TransportationZone::where('zone_code', $customerfirstaddress->zone_code)->first();

            $ship_to_address = trim(
                ($customerfirstaddress->address_name ? $customerfirstaddress->address_name . ', ' : '') .
                ($customerfirstaddress->address ? $customerfirstaddress->address . ', ' : '') .
                ($customerfirstaddress->city ? $customerfirstaddress->city . ', ' : '') .
                ($customerfirstaddress->province ? $customerfirstaddress->province . ', ' : '') .
                ($customerfirstaddress->zip_code ?? '-')
            );

            $mappedOrders = array_map(function ($orderNo) use ($Customer, $customerfirstaddress, $zone, $orderGroup, $orderTotals, $ship_to_address, $request, $countOrderNoList) {
                $subtotal = $orderTotals[$orderNo]['subtotal'];
                $discount = $orderTotals[$orderNo]['discount'];

                return [
                    'order_no' => $orderNo,
                    'order_group_id' => $orderGroup,
                    'customer_id' => $Customer->customer_id,
                    'customer_shipment_id' => (string) $customerfirstaddress->customer_shipment_id,
                    'order_status' => self::OrderStatusVerif,
                    'total' => $subtotal,
                    'total_discount' => $discount,
                    'nett_before_tax' => 0,
                    'total_tax' => 0,
                    // 'shipping_charges' => $request->shipping_charge,
                    // 'total_nett' => $subtotal - $discount + $shipping_charges,
                    'total_nett' => $subtotal - $discount,
                    'dp_percentage' => 0,
                    'dp_amount' => 0,
                    'sales_id' => Auth::user()->reference_id ?? '',
                    // 'sales_id' => $Customer->sales?->sales_id ?? '',
                    'sales_name' =>Auth::user()->name ?? '',
                    // 'sales_name' => $Customer->sales?->sales_name ?? '',
                    'distribution_channel' => $request->distribution_channel,
                    'bill_to' => $Customer->owner_name,
                    'bill_to_address' => $Customer->address,
                    'bill_to_email' => $Customer->email,
                    'bill_to_phone_number' => $Customer->phone_number,
                    'currency' => 'IDR',
                    'ship_to' => $customerfirstaddress->name,
                    'ship_to_address' => $ship_to_address,
                    'ship_to_phone_number' => $customerfirstaddress->phone_number,
                    'location_code' => $zone->zone_code ?? '',
                    'location_name' => $zone->description ?? '',
                    'created_by' => Auth::user()->name,
                    'modified_by' => Auth::user()->name,
                ];
            }, $orderNoList);

            Order::insert($mappedOrders);
            OrderItem::insert($orderDetails);
            
            foreach ($orderNoList as $orderNo) {
                OrderApproval::create([
                    'order_no' => $orderNo,
                    'status' => 'Pending',
                    'created_by' => Auth::user()->name,
                    'modified_by' => Auth::user()->name
                ]);
            }

            DB::commit();

            return $this->sendSuccess('Order created successfully.', [
                'order_group_id' => $orderGroup,
                'order_no' => $orderNoList
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Order creation failed: ' . $e->getMessage());
            return $this->sendError('Failed to create order: ' . $e->getMessage(), 500);
        }
    }

    public function updateTransaction($orderNo, UpdateTransactionRequest $request)
    {
        DB::beginTransaction();
        try {
            $order = Order::where('order_no', $orderNo)->first();
    
            if (!$order) {
                return $this->sendError('Order not found.', 404);
            }
    
            $Customer = Customer::where('customer_id', $request->customer_id)->first();
            if (!$Customer) {
                return $this->sendError('Customer not found.', 404);
            }
    
            OrderItem::where('order_no', $orderNo)->delete();
    
            $items = $request->article_items;
            $orderDetailsMap = [];
            $orderTotals = [];
            $totalGlobal = 0;
            // $item_disc = ['is_custom' => 0, 'data' => []];
            $orderNosByDescription = [];
    
            $usedCategories = collect();
    
            foreach ($items as $item) {
                $article = Product::with([
                    'price' => fn($q) => $q->orderBy('valid_from', 'desc'),
                    'skuStock'
                ])->where('article', $item['article_id'])->first();
    
                if (!$article) continue;
    
                $price = $article->price->amount ?? 0;
                $qty = $item['article_qty'];
                $stock = optional($article->skuStock)->stock;
                $isAvailable = 1;
    
                if ($stock === 0) $isAvailable = 0;
                if ($stock !== null && $qty > $stock) $qty = $stock;
    
                $category = $article->lvl4_description;
    
                $useNewOrderNo = $usedCategories->isNotEmpty() && !$usedCategories->contains($category);
    
                if ($useNewOrderNo) {
                    if (!isset($orderNosByDescription[$category])) {
                        $orderNosByDescription[$category] = strtoupper('INVBNC' . substr(uniqid(), -6));
                    }
                    $orderNoToUse = $orderNosByDescription[$category];
                } else {
                    $orderNoToUse = $orderNo;
                }
    
                $usedCategories->push($category);
    
                $subTotal = $price * $qty;
                $totalGlobal += $subTotal;
                $orderTotals[$orderNoToUse]['subtotal'] = ($orderTotals[$orderNoToUse]['subtotal'] ?? 0) + $subTotal;
    
                $orderDetailsMap[$orderNoToUse][] = [
                    'article' => $article,
                    'price' => $price,
                    'qty' => $qty,
                    'sub_total' => $subTotal,
                    'is_available' => $isAvailable,
                    'stock' => $stock ?? 0,
                ];
    
                // $item_disc['data'][] = [
                //     'article' => $article->article,
                //     'qty' => $qty
                // ];
            }
    
            // $customer_discount = ($Customer->discount_percent / 100) * $totalGlobal;
            // $product_discount = 0;
            
            // $sim_disc = $this->simulateDiscountB2B($item_disc);
            // if ($sim_disc !== false && isset($sim_disc['total_discount'])) {
            //     $product_discount = $sim_disc['total_discount'];
            // }

            // $total_discount = max($customer_discount, $product_discount);
            $total_discount = $request->total_discount;
    
            $orderGroupId = $order->order_group_id ?: (string) Str::uuid();
            // $shipping_charges = $request->shipping_charge;
            // $totalOrders = count($orderDetailsMap);
            // $shippingPerOrder = $shipping_charges / max($totalOrders, 1);
    
            // $isShipmentChanged = $request->customer_shipment_id !== $order->customer_shipment_id;
    
            // if ($isShipmentChanged) {
                $customerfirstaddress = CustomerShipment::where('customer_shipment_id', $request->customer_shipment_id)->first();
                $zone = TransportationZone::where('zone_code', $customerfirstaddress->zone_code)->first();
    
                $ship_to = $customerfirstaddress->name;
                $ship_to_address = trim(
                    ($customerfirstaddress->address_name ? $customerfirstaddress->address_name . ', ' : '') .
                    ($customerfirstaddress->address ? $customerfirstaddress->address . ', ' : '') .
                    ($customerfirstaddress->city ? $customerfirstaddress->city . ', ' : '') .
                    ($customerfirstaddress->province ? $customerfirstaddress->province . ', ' : '') .
                    ($customerfirstaddress->zip_code ?? '-')
                );
                $ship_to_phone = $customerfirstaddress->phone_number;
                $location_code = $zone->zone_code ?? '';
                $location_name = $zone->description ?? '';
            // } else {
            //     $ship_to = $order->getOriginal('ship_to');
            //     $ship_to_address = $order->getOriginal('ship_to_address');
            //     $ship_to_phone = $order->getOriginal('ship_to_phone_number');
            //     $location_code = $order->getOriginal('location_code');
            //     $location_name = $order->getOriginal('location_name');
            // }
    
            $newOrderNos = [];
    
            foreach ($orderDetailsMap as $orderNoKey => $details) {
                $subtotal = $orderTotals[$orderNoKey]['subtotal'] ?? 0;
                $orderDetails = [];
                $orderDiscount = 0;
    
                foreach ($details as $detail) {
                    $proportion = $detail['sub_total'] / $totalGlobal;
                    $primary_discount = $proportion * $total_discount;
                    $orderDiscount += $primary_discount;
    
                    $orderDetails[] = [
                        'order_no' => $orderNoKey,
                        'article_id' => $detail['article']->article,
                        'product_name' => $detail['article']->product_name_c,
                        'product_variant' => $detail['article']->product_variant_c,
                        'product_size' => $detail['article']->product_size_c,
                        'is_new_arrival' => '',
                        'price' => $detail['price'],
                        'qty' => $detail['qty'],
                        'issued_qty' => $detail['qty'],
                        'sub_total' => $detail['sub_total'],
                        'additional_discount' => 0,
                        'primary_discount' => $primary_discount,
                        'total' => $detail['sub_total'] - $primary_discount,
                        'is_available' => $detail['is_available'],
                        'stock' => $detail['stock'],
                        'created_by' => Auth::user()->name,
                        'modified_by' => Auth::user()->name,
                    ];
                }
    
                OrderItem::insert($orderDetails);
    
                $orderToUpdate = Order::firstOrNew(['order_no' => $orderNoKey]);
                $orderToUpdate->fill([
                    'order_group_id' => $orderGroupId,
                    'customer_id' => $Customer->customer_id,
                    'customer_shipment_id' => $request->customer_shipment_id,
                    'order_status' => self::OrderStatusVerif,
                    'total' => $subtotal,
                    'total_discount' => $orderDiscount,
                    'nett_before_tax' => 0,
                    'total_tax' => 0,
                    // 'shipping_charges' => $request->shipping_charge,
                    // 'total_nett' => $subtotal - $orderDiscount + $shippingPerOrder,
                    'total_nett' => $subtotal - $orderDiscount,
                    'dp_percentage' => 0,
                    'dp_amount' => 0,
                    'sales_id' => Auth::user()->reference_id ?? '',
                    // 'sales_id' => $Customer->sales?->sales_id ?? '',
                    'sales_name' => Auth::user()->name ?? '',
                    // 'sales_name' => $Customer->sales?->sales_name ?? '',
                    'distribution_channel' => $request->distribution_channel,
                    'bill_to' => $Customer->owner_name,
                    'bill_to_address' => $Customer->address,
                    'bill_to_email' => $Customer->email,
                    'bill_to_phone_number' => $Customer->phone_number,
                    'currency' => 'IDR',
                    'ship_to' => $ship_to,
                    'ship_to_address' => $ship_to_address,
                    'ship_to_phone_number' => $ship_to_phone,
                    'location_code' => $location_code,
                    'location_name' => $location_name,
                    'created_by' => Auth::user()->name,
                    'modified_by' => Auth::user()->name,
                ]);
                $orderToUpdate->save();
    
                OrderApproval::updateOrCreate(
                    ['order_no' => $orderNoKey],
                    [
                        'status' => 'Pending',
                        'created_by' => Auth::user()->name,
                        'modified_by' => Auth::user()->name
                    ]
                );
    
                $newOrderNos[] = $orderNoKey;
            }
    
            DB::commit();
    
            return $this->sendSuccess('Order updated successfully.', [
                'order_group_id' => $orderGroupId,
                'order_nos' => $newOrderNos
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Order update failed: ' . $e->getMessage());
            return $this->sendError('Failed to update order: ' . $e->getMessage(), 500);
        }
    }

    public function simulateDiscountB2B($datas)
    {
        try {
            $bruto = 0;
            $qty = 0;
            foreach ($datas['data'] as $data) {
                $article = Product::where('article', $data['article'])->first();
                if ($article != null) {

                    $category = $article->lvl3_description == 'BAGS' ? 'BAGS' : 'NON BAGS';
                    $bruto += ($article->price->amount + ($data['custom_price'] ?? 0)) * $data['qty'];
                    $qty += $data['qty'];

                } else {
                    $failedMessage[] = [
                        'Article' => $data['article'],
                        'Message' => 'Not Found'
                    ];
                }
            }

            $m_d = DB::table('matrix_discount')
                ->where('is_custom', $datas['is_custom'] ?? 0)
                ->whereRaw('? BETWEEN min_bruto_from AND min_bruto_to', [(int) $bruto])
                ->whereRaw('? BETWEEN qty_from AND qty_to', [(int) $qty])
                ->where('category', $category)
                ->first();
 
            Log::info([
                'total_discount' => $m_d->discount * ($bruto / 100),
                'error' => @$failedMessage ?? null
            ]);

            return [
                'total_discount' => $m_d->discount * ($bruto / 100),
                'category' => $category,
                'discount' => $m_d->discount
            ];

        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }
    }
}