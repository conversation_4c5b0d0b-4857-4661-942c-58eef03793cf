<?php

namespace App\Http\Controllers;

use App\Jobs\SapSO;
use App\Models\Cart;
use App\Models\Order;
use App\Services\SAP;
use App\Mail\SendMail;
use App\Models\Rating;
use App\Models\Remark;
use App\Models\Article;
use App\Models\Invoice;
use App\Models\Product;
use App\Jobs\MailSender;
use App\Models\Customer;
use App\Models\Proforma;
use App\Models\OrderItem;
use App\Models\CartDetail;
use App\Models\ProductSku;
use App\Helpers\RestHelper;
use App\Models\CreditLimit;
use App\Models\OrderCustom;
use App\Models\Transaction;
use Illuminate\Support\Str;
use App\Events\GenericEvent;
use Illuminate\Http\Request;
use App\Jobs\SimulateSOQueue;
use App\Models\CustomerSales;
use App\Models\DeliveryOrder;
use App\Models\InvoiceDetail;
use App\Models\OrderApproval;
use App\Jobs\CreditLimitQueue;
use App\Jobs\StockUpdateBatch;
use App\Models\CartAttachment;
use Illuminate\Support\Carbon;
use App\Models\MasterParameter;
use App\Models\TransactionItem;
use App\Models\CustomerShipment;
use App\Repositories\GetStokRepo;
use App\Models\TransportationZone;
use App\Repositories\GetLimitRepo;
use Illuminate\Support\Facades\DB;
use App\Models\DeliveryOrderDetail;
use Illuminate\Support\Facades\Log;
use App\Http\Middleware\OrderDetail;
use App\Repositories\GetSocialsRepo;
use Illuminate\Support\Facades\Mail;
use App\Models\OrderCustomAttachment;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use App\Repositories\CreateSimulateSORepo;
use Illuminate\Http\JsonResponse;

class TransactionController extends Controller
{
    private $errordata = [];
    private $sap_error = [];
    const OrderStatusWait = 'Menunggu Konfirmasi';
    const OrderStatusPending = 'Pending';
    const OrderStatusOnHold = 'On Hold';
    const OrderStatusOnProcess = 'Diproses';
    const OrderStatusBaru = 'Baru';
    const OrderStatusGI = 'Siap Dikirim';
    const OrderStatusOnShipping = 'Dikirim';
    const OrderStatusDelivered = 'Diterima';
    const OrderStatusFinish = 'Selesai';
    const OrderStatusCancel = 'Batal';
    const OrderStatusPembayaran = 'Pembayaran';
    const OrderStatusSemua = 'Semua';
    const OrderStatusVerif = 'Menunggu Verifikasi';
    const TokoCash = '0';
    
    private $limit = 12; 

    //CHECKOUT PESAN SEKARANG

    public function storeB2B(Request $request){
      
        $validator = Validator::make($request->all(), [
            'is_custom' => 'required',
        ]);

        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        DB::beginTransaction();
        try{
            $custId = auth()->user()->customer->customer_id ;

            $username = auth()->user()->username;
            $Customer = Customer::where('customer_id', $custId)->first();
            if ($Customer == null) {
                return $this->sendError('Maaf anda tidak bisa membuat order karena data customer anda tidak ditemukan!');
            }

            $cdate = now()->format('Y-m-d');

            $cart = \DB::table('cart')->where('customer_id', $custId)->first()->id;
            $cartDetails = CartDetail::with([
                'article_detail' => function ($query) {
                    $query->select('article', 'lvl4_description', 'product_name_c', 'product_size_c', 'sku_code_c');
                },
                'article_detail.sku_stock' => function ($query) {
                    $query->select('sku_id', 'stock'); // Ensure this column name matches your database
                },
                'article_detail.article_price' => function ($query) use ($cdate) {
                    $query->select('sku_code_c', 'amount', 'modified_date', 'valid_from', 'valid_to') // Include filter columns
                    ->where('valid_from', '<=', $cdate)
                    ->where('valid_to', '>=', $cdate)
                    ->orderBy('valid_from', 'desc');
                },
            ])
            ->where('cart_id', $cart)
            ->where('is_custom', $request->is_custom)
            ->where('selected', 1)
            ->where('is_available', 1);
            
           
            $article = $cartDetails->pluck('article')->toArray();
            // RestHelper::syncStock($article, $custId);
            $cartDetails = $cartDetails->select('cart_detail_id', 'qty', 'article', 'attachment_group_id', 'is_custom')
            ->get();

            if(count($cartDetails) == 0){
                return $this->sendError("Anda belum memilih Katalog untuk di pesan !", 400);
            }

            $total = 0;
            $total_nett = 0;
            $nett_before_tax = 0;
            $total_discount = 0;
            $total_tax = 0;
            
            $orderNosByDescription = [];
            $item_disc = [];

        //    return $cartDetails;
            if( $request->is_custom == 0){
                $groupedWithOrderNo = array_map(function ($item) use (&$orderNosByDescription, &$total, &$total_nett, $Customer) {
                    $articleDetail = $item['article_detail'];
                    
                        $isAvailable = 1; // Assuming available unless stock is 0
                        $qty = $item['qty'];
        
                        if (isset($articleDetail['sku_stock'])) {
                            $stock = $articleDetail['sku_stock']['stock'];
                    
                            if ($stock === 0) {
                                $isAvailable = 0;
                            }
              
                            if ($stock !== null && $qty > $stock) {
                                $qty = $stock;
                            }
                        }
        
                        if (!isset($orderNosByDescription[$articleDetail['lvl4_description']])) {
                            $orderNosByDescription[$articleDetail['lvl4_description']] = strtoupper('INVBNC'.substr(uniqid(),-6));
                        }
                        $orderNo = $orderNosByDescription[$articleDetail['lvl4_description']];
        
                        if (!empty($articleDetail['article_price'])) {
                            // usort($articleDetail['article_price'], function ($a, $b) {
                            //     return strtotime($b['modified_date']) - strtotime($a['modified_date']);
                            // });
                            $latestPrice = $articleDetail['article_price'][0]['amount'];
                        } else {
                            $latestPrice = 0;
                        }
                        $subTotal = $latestPrice * $qty;
                        $primaryDiscount = ($Customer->discount_percent / 100) * $subTotal;
                        $total = $subTotal;
                    
                        $item_disc['is_custom'] = 0;
                        $item_disc['data'][] = [
                            'article' => $articleDetail['article'] ?? '',
                            'qty' => $qty
                        ];
                        // $total += $total;
                        $total_nett += $total;
                        return [
                            'order_no' => $orderNo,
                            'article_id' => $articleDetail['article'] ?? '',
                            'product_name' => $articleDetail['product_name_c'] ?? '',
                            'product_size' => $articleDetail['product_size_c'] ?? '',
                            'is_new_arrival' => '', 
                            'is_available' => $isAvailable, 
                            'price' => $latestPrice,
                            'sub_total' => $subTotal,
                            'qty' => $qty, 
                            'issued_qty' => $qty,
                            'primary_discount' => $primaryDiscount,
                            'additional_discount' => 0, 
                            'total' => $subTotal - $primaryDiscount,
                            'stock' => $stock
                        ];
                    }, $cartDetails->toArray());
            }else{
                $attachments = CartAttachment::whereIn('attachment_group_id', $cartDetails->pluck('attachment_group_id')->toArray())->get();
                
                $groupedWithOrderNo = $cartDetails
                ->groupBy('attachment_group_id')
                ->flatMap(function ($items, $groupId) use (&$orderNosByDescription, &$total, &$total_nett, $attachments, &$item_disc, &$subTotal, $Customer) {
                    return $items->map(function ($item) use (&$orderNosByDescription, &$total, &$total_nett, $attachments, $groupId,  &$item_disc, &$subTotal, $Customer) {
                        $articleDetail = $item['article_detail'];

                        $isAvailable = 1;
                        $qty = $item['qty'];

                        if (isset($articleDetail['sku_stock'])) {
                            $stock = $articleDetail['sku_stock']['stock'];

                            if ($stock === 0) {
                                $isAvailable = 0;
                            }

                            if ($stock !== null && $qty > $stock) {
                                $qty = $stock;
                            }
                        }

                        if (!isset($orderNosByDescription[$articleDetail['lvl4_description']])) {
                            $orderNosByDescription[$articleDetail['lvl4_description']] = strtoupper('INVBCC' . substr(uniqid(), -6));
                        }

                        $orderNo = $orderNosByDescription[$articleDetail['lvl4_description']];

                        $latestPrice = !empty($articleDetail['article_price']) 
                            ? $articleDetail['article_price'][0]['amount'] 
                            : 0;

                        // $subTotal = $latestPrice * $qty;
                        // $total_nett += $total;

                        $custom_price = $attachments->where('attachment_group_id',$groupId)
                        ->sum('custom_price');
                    
                        $item_disc['is_custom'] = 1;
                        $item_disc['data'][] = [
                            'article' => $articleDetail['article'],
                            'custom_price' => $custom_price,
                            'qty' => $qty
                        ];
                        $total += (($latestPrice*$qty)+($custom_price*$qty));
                        $total_nett += (($latestPrice*$qty)+($custom_price*$qty));
                        $subTotal = $total;
                        $total = 0;
                        $customAttachment = $attachments
                            ->where('attachment_group_id', $groupId);

                        return [
                            "id" => (string) Str::uuid(),
                            'reference_id'   => $orderNo,
                            'qty'   => $qty,
                            'sku'            => substr($articleDetail['article'], 0, -3),
                            'article_id'     => $articleDetail['article'],
                            'reference_name' => 'order_header',
                            'custom_price'   => $customAttachment->sum('estimate_price') * $qty,
                            'position_side'  => '-',
                            'generated_file_path'  => '-',
                            'attachment_group_id'  => $groupId,
                            'created_date' => now(),
                            'created_by' => $Customer->owner_name,
                            'modified_date' => now(),
                            'modified_by' => $Customer->owner_name,
                            'total' => $subTotal,
                        ];
                    });
                })->values()->toArray();

                $attachments_data = $attachments->map(function ($item) {
                    return [
                        "id" => (string) Str::uuid(),
                        "order_custom_id" => $item->attachment_group_id,
                        "custom_type" => $item->text ? "text" : "logo",
                        "custom_price" => $item->estimate_price,
                        "file_path" => $item->file_path,
                        "custom_text" => $item->text,
                        "color" => $item->color
                    ];
                });

                // return $attachments_data;
                OrderCustomAttachment::insert($attachments_data->toArray());
                
                
            }
            // return  $item_disc;
            
            // return $groupedWithOrderNo;
            $customerfirstaddress = CustomerShipment::where('customer_id', $custId)->where('is_primary', true)->first();
                
            // if ($Customer->registered_sap_at != null) {
            //     $customerShipment = CustomerShipment::where('customer_id', $custId)->get();
            //     $data = [];
            //     foreach ($customerShipment as $cs) {
            //         if (!Str::isUuid($cs->customer_shipment_id)) {
            //             $data[] = $cs->customer_shipment_id;
            //         }
            //     }
            //     $customerfirstaddress = CustomerShipment::where('customer_id', $custId)
            //                             ->whereIn('customer_shipment_id',$data)->first();
            // }

            $orderNo = array_values($orderNosByDescription);

            // $sim_disc = $this->simulateDiscountB2B($item_disc);

            // if ($sim_disc != false) {
            //     $total_discount = $sim_disc['total_discount'];
            //     $total_nett = $total_nett - $total_discount;
            // }

            $cs_address_name = $customerfirstaddress->address_name != null || $customerfirstaddress->address_name != '' ? $customerfirstaddress->address_name.', ' : '';
            $cs_address = $customerfirstaddress->address != null || $customerfirstaddress->address != '' ? $customerfirstaddress->address.', ' : '-, ';
            $cs_city = $customerfirstaddress->city != null || $customerfirstaddress->city != '' ? $customerfirstaddress->city.', ' : '-, ';
            $cs_province = $customerfirstaddress->province != null || $customerfirstaddress->province != '' ? $customerfirstaddress->province.', ' : '-, ';
            $cs_zipcode = $customerfirstaddress->zip_code != null || $customerfirstaddress->zip_code != '' ? $customerfirstaddress->zip_code.'.' : '-.';

            $ship_to_address = $cs_address_name.$cs_address.$cs_city.$cs_province.$cs_zipcode;

            $transportation_zone = TransportationZone::where('zone_code', $customerfirstaddress->zone_code)->first();
            $total_count = collect($groupedWithOrderNo);
            
            $orderGroup = (string) Str::uuid();
            $mappedOrders = array_map(function($orderNo) use ($custId, $orderGroup ,$customerfirstaddress, $Customer, $total_count, $total_discount, $nett_before_tax, $total_tax, $total_nett, $ship_to_address, $request, $transportation_zone) {
                $totalAll = $total_count->where('order_no', $orderNo)->sum('sub_total');
                $discount = ($Customer->discount_percent / 100) * $totalAll;
               if($request->is_custom == 1){
                $total_custom = ($total_count->where('reference_id', $orderNo)->sum('total') + $total_count->where('reference_id', $orderNo)->sum('custom_price'));
                
                
                $discount = ($Customer->discount_percent / 100) * $total_custom;
                }
                return [
                    'order_no' => $orderNo,
                    'order_group_id' => $orderGroup,
                    'customer_id' => $custId,
                    'customer_shipment_id' => strval($customerfirstaddress->customer_shipment_id),
                    'order_status' => self::OrderStatusWait, // Assuming OrderStatusWait is 'Wait'
                    'total' => $request->is_custom == 0 ? $totalAll : $total_custom,
                    'total_discount' => $discount ?? 0,
                    'nett_before_tax' => $nett_before_tax ?? 0,
                    'total_tax' => $total_tax ?? 0,
                    // 'total_nett' => $request->is_custom == 0 ? $totalAll - $discount : $total_custom - $discount,
                    'total_nett' =>$request->is_custom == 0 ? $totalAll : $total_custom,
                    'created_by' => $Customer->owner_name,
                    'modified_by' => $custId,
                    // 'dp_percentage' => substr($orderNo, 0, 6) == 'INVBCC' ? 50 : 0,
                    'dp_percentage' => substr($orderNo, 0, 6) == 'INVBCC' ? 0 : 0,
                    // 'dp_amount' => substr($orderNo, 0, 6) == 'INVBCC' ?  $total_custom * 0.5 : 0,
                    'dp_amount' => substr($orderNo, 0, 6) == 'INVBCC' ?  0 : 0,
                    // 'distribution_channel' => $Customer->distribution_channel,
                    'distribution_channel' => 'W3',
                    'sales_id'   => $Customer->sales?->sales_id ?? '',
                    'sales_name' => $Customer->sales?->sales_name ?? '',
                    'bill_to' => $Customer->owner_name,
                    'bill_to_address' => $Customer->address,
                    'bill_to_phone_number' => $Customer->phone_number,
                    'bill_to_email' => $Customer->email,
                    'currency' => 'IDR',
                    'ship_to' => $customerfirstaddress->name,
                    'ship_to_address' => $ship_to_address,
                    'ship_to_phone_number' => $customerfirstaddress->phone_number,
                    'location_code' => $transportation_zone->zone_code ?? '-',
                    'location_name' => $transportation_zone->description ?? '-'
                    // 'dp_due_date' => date('Y-m-d')
                ];
            }, $orderNo);
           

            $totalNettAllOrders = collect($mappedOrders)->sum('total_nett');

            // $creditLimit = CreditLimit::where('customer_external_id', $custId)->first();
            // if ($creditLimit && $creditLimit->credit_limit_remaining < $totalNettAllOrders) {
            //     return $this->sendError('Maaf anda tidak bisa membuat order karena credit limit anda tidak mencukupi');
            // }

            $order = Order::insert($mappedOrders);

            $dummy_customer_id = MasterParameter::where('group_key','B2B_NEW_CUSTOMER')->where('key','CUSTOMER_ID')->first()->value;
            $customerSapId = Customer::where('customer_id', collect($mappedOrders)->first()['customer_id'] )->first()->sap_id;
            if($request->is_custom == 0){
                    if($customerSapId != null){
                        $dummy_customer_id = $customerSapId;
                    }
                    collect($orderNo)->each(function ($orderno) use ($dummy_customer_id) {
                        $job = new SimulateSOQueue( $dummy_customer_id,  $orderno);
                        $job->handle();
                    });
                $order_detail = OrderItem::insert($groupedWithOrderNo);
      
            }else{
                $groupedWithOrderNo = collect($groupedWithOrderNo)->map(function ($item) {
                    unset($item['total']); // if array
                    return $item;
                });
                if($customerSapId != null){
                    $dummy_customer_id = $customerSapId;
                }
                
                collect($orderNo)->each(function ($orderno) use ($dummy_customer_id) {
                    $job = new SimulateSOQueue( $dummy_customer_id,  $orderno, 's' , true);
                    $job->handle();
                });

                $order_detail = OrderCustom::insert($groupedWithOrderNo->toArray());
            }

            CartDetail::whereIn('cart_detail_id', $cartDetails->pluck('cart_detail_id')->toArray())->delete();
            
            foreach ($orderNo as $order) {
                //sales notif
                $msg = 'Ada pesanan baru masuk dengan nomor pesanan #' . $order . '. Klik pesan ini untuk melihat detail.';
                $mp = MasterParameter::where('group_key','SALES_NOTIF')->where('key','B2B')->first();
                $this->notifStore($mp->value, 'Pesanan Terbaru', 'order-b2b-internal', $msg, $order, 'Transaksi', 'B2B', 'info');
            }

            $result = [
                'order_group_id' => $orderGroup,
                'order_no' => $orderNo
            ];
            DB::commit();
            
            return $this->sendSuccess("Order created successfully.",$result);
            
        }
        catch(\Exception $e){
            DB::rollback();
            Log::info('error checkout : '.$e->getMessage());
            return $this->sendError("Sorry system can't create order" . $e->getMessage() . ' on line ' . $e->getLine(), 500);
        }
    }

    public function storeWholesales(Request $request){
        DB::beginTransaction();   
        try{
            $custId = auth()->user()->customer->customer_id ;

            $username = auth()->user()->username;
            $Customer = Customer::where('customer_id', $custId)->where('is_active',1)->first();
            if ($Customer == null) {
                return $this->sendError('Maaf anda tidak bisa membuat order karena data customer anda tidak aktif!');
            }
 
            $cart = \DB::table('cart')->where('customer_id', $custId)->first()->id;
            $cartDetails = CartDetail::with([
                'article_detail' => function ($query) {
                    $query->select('article', 'lvl4_description', 'product_name_c', 'product_variant_c', 'product_size_c', 'sku_code_c');
                },
                'article_detail.sku_stock' => function ($query) {
                    $query->select('sku_id', 'stock'); // Ensure this column name matches your database
                },
                'article_detail.article_price' => function ($query) {
                    $query->select('sku_code_c', 'amount', 'valid_from');
                },
            ])
            ->where('cart_id', $cart)
            ->where('selected', 1)
            ->where('is_available', 1);
            
           
            $article = $cartDetails->pluck('article')->toArray();
            RestHelper::syncStock($article, $custId);
            $cartDetails = $cartDetails->select('cart_detail_id', 'qty', 'article')
            ->get();
            
            if(count($cartDetails) == 0){
                return $this->sendError("Anda belum memilih Katalog untuk di pesan !", 400);
            }

            if( $this->checkTopIsPendingPayment($Customer)){
                $msg = "Terdapat tagihan Anda yang sudah jatuh tempo/melewati tempo pembayaran selesaikan pembayaran untuk melanjutkan proses pemesanan.";
                $this->notifStore($custId, 'Pending payment', null, $msg, null, 'Transaksi', 'WHOLESALES', 'error');
                DB::commit();
                return $this->sendError("Terdapat tagihan Anda yang sudah jatuh tempo/melewati tempo pembayaran,"
                ." cek tagihan di menu tagihan", 403);
            }
            
            $orderNosByDescription = [];
            
            $groupedWithOrderNo = array_map(function ($item) use (&$orderNosByDescription) {
                $articleDetail = $item['article_detail'];
                
                $isAvailable = 1; // Assuming available unless stock is 0
                $qty = $item['qty'];

                if (isset($articleDetail['sku_stock'])) {
                    $stock = $articleDetail['sku_stock']['stock'];
            
                    if ($stock === 0) {
                        $isAvailable = 0;
                    }
      
                    if ($stock !== null && $qty > $stock) {
                        $qty = $stock;
                    }
                }

                if (!isset($orderNosByDescription[$articleDetail['lvl4_description']])) {
                    $orderNosByDescription[$articleDetail['lvl4_description']] = strtoupper('INV' . substr(uniqid(), -8));
                }
                $orderNo = $orderNosByDescription[$articleDetail['lvl4_description']];

                if (!empty($articleDetail['article_price'])) {
                    usort($articleDetail['article_price'], function ($a, $b) {
                        return strtotime($b['valid_from']) - strtotime($a['valid_from']);
                    });
                    $latestPrice = $articleDetail['article_price'][0]['amount'];
                } else {
                    $latestPrice = 0;
                }

                $subTotal = $latestPrice * $qty;
                $total = $subTotal; 
            
                return [
                    'order_no' => $orderNo,
                    'article_id' => $articleDetail['article'] ?? '',
                    'product_name' => $articleDetail['product_name_c'] ?? '',
                    'product_variant' => $articleDetail['product_variant_c'] ?? '',
                    'product_size' => $articleDetail['product_size_c'] ?? '',
                    'is_new_arrival' => '', // Placeholder
                    'price' => $latestPrice,
                    'qty' => $qty, // Adjusted quantity
                    'issued_qty' => $qty,
                    'sub_total' => $subTotal,
                    // 'discount_code' => null,
                    'additional_discount' => 0, // Placeholder
                    'primary_discount' => 0, // Placeholder
                    'total' => $total,
                    'is_available' => $isAvailable, // Set availability
                    'stock' => $articleDetail['sku_stock']['stock'] ?? 0
                ];
            }, $cartDetails->toArray());
            
            if($request->customer_shipment_id != ''){
                $customerfirstaddress = CustomerShipment::where('customer_shipment_id', $request->customer_shipment_id)->first();
            }else{
                $customerfirstaddress = CustomerShipment::where('customer_id', $custId)->first();
            }

            $orderNo = array_values($orderNosByDescription);

            $cs_address_name = $customerfirstaddress->address_name != null || $customerfirstaddress->address_name != '' ? $customerfirstaddress->address_name.', ' : '';
            $cs_address = $customerfirstaddress->address != null || $customerfirstaddress->address != '' ? $customerfirstaddress->address.', ' : '-, ';
            $cs_city = $customerfirstaddress->city != null || $customerfirstaddress->city != '' ? $customerfirstaddress->city.', ' : '-, ';
            $cs_province = $customerfirstaddress->province != null || $customerfirstaddress->province != '' ? $customerfirstaddress->province.', ' : '-, ';
            $cs_zipcode = $customerfirstaddress->zip_code != null || $customerfirstaddress->zip_code != '' ? $customerfirstaddress->zip_code.'.' : '-.';

            $ship_to_address = $cs_address_name.$cs_address.$cs_city.$cs_province.$cs_zipcode;
            
            $total = 0;
            $total_nett = 0;
            $nett_before_tax = 0;
            $total_discount = 0;
            $total_tax = 0;
            
            $total_count = collect($groupedWithOrderNo);
            $orderGroup = (string) Str::uuid();
           
            $mappedOrders = array_map(function($orderNo) use ($custId, $orderGroup ,$customerfirstaddress, $Customer, $total_count, $total_discount, $nett_before_tax, $total_tax, $total_nett, $ship_to_address) {
                return [
                    'order_no' => $orderNo,
                    'order_group_id' => $orderGroup,
                    'customer_id' => $custId,
                    'customer_shipment_id' => strval($customerfirstaddress->customer_shipment_id),
                    'order_status' => self::OrderStatusWait, // Assuming OrderStatusWait is 'Wait'
                    'total' => $total_count->where('order_no', $orderNo)->sum('total'),
                    'total_discount' => $total_discount ?? 0,
                    'nett_before_tax' => $nett_before_tax ?? 0,
                    'total_tax' => $total_tax ?? 0,
                    'total_nett' => $total_count->where('order_no', $orderNo)->sum('total') ?? 0,
                    'created_by' => $Customer->owner_name,
                    'modified_by' => $custId,
                    'sales_id' => @$Customer->sales->sales_id,
                    'dp_percentage' => substr($orderNo, 0, 6) == 'INVBCC' ? 50 : 0,
                    'dp_amount' => substr($orderNo, 0, 6) == 'INVBCC' ? $total_nett * 0.5 : 0,
                    'distribution_channel' => $Customer->distribution_channel,
                    'sales_name' => @$Customer->sales->sales_name,
                    'bill_to' => $Customer->owner_name,
                    'bill_to_address' => $Customer->address,
                    'bill_to_phone_number' => $Customer->phone_number,
                    'currency' => 'IDR',
                    'bill_to_email' => $Customer->email,
                    'ship_to' => $customerfirstaddress->name,
                    'ship_to_address' => $ship_to_address,
                    'ship_to_phone_number' => $customerfirstaddress->phone_number
                ];
            }, $orderNo);
            
            $totalNettAllOrders = collect($mappedOrders)->sum('total_nett');

            $creditLimit = CreditLimit::where('customer_external_id', $custId)->first();
            if ($creditLimit && $creditLimit->credit_limit_remaining < $totalNettAllOrders) {
                return $this->sendError('Maaf anda tidak bisa membuat order karena credit limit anda tidak mencukupi');
            }

            $order = Order::insert($mappedOrders);
            $order_detail = OrderItem::insert($groupedWithOrderNo);
            CartDetail::whereIn('cart_detail_id', $cartDetails->pluck('cart_detail_id')->toArray())->delete();
            
            foreach ($orderNo as $order) {
                //sales notif
                $msg = 'Ada pesanan baru masuk dengan nomor pesanan #' . $order . '. Klik pesan ini untuk melihat detail.';
                $sales = CustomerSales::where('customer_id', $custId)->first();
                $this->notifStore($sales->sales_id, 'Pesanan Terbaru', 'order-internal', $msg, $order, 'Transaksi', 'WHOLESALES', 'info');
            }

            // $msg = 'Ada pesanan baru masuk dengan nomor pesanan '.implode(',', $orderNo).'. Klik pesan ini untuk melihat detail.';
            // $sales = CustomerSales::where('customer_id',$custId)->first();
            // $this->notifStore($sales->sales_id,'Pesanan Terbaru','order',$msg,implode(',', $orderNo));

            $result = [
                'order_group_id' => $orderGroup,
                'order_no' => $orderNo
            ];
            DB::commit();
            
            return $this->sendSuccess("Order created successfully.",$result);
        }catch(\Exception $e){
            DB::rollback();
            Log::info('error checkout : '.$e->getMessage());
            return $this->sendError("Sorry system can't create order" . $e->getMessage() . ' on line ' . $e->getLine(), 500);
        }
    }
    

    public function storeB2BCustom (Request $request){
        
        DB::beginTransaction();
        
        try {
            $custId = auth()->user()->customer->customer_id ;

            $username = auth()->user()->username;
            $Customer = Customer::where('customer_id', $custId)->where('is_active',1)->first();
            if ($Customer == null) {
                return $this->sendError('Maaf anda tidak bisa membuat order karena data customer anda tidak aktif!');
            }

            $cdate = now()->format('Y-m-d');

            $cart = \DB::table('cart')->where('customer_id', $custId)->first()->id;
            $cartDetails = CartDetail::with([
                'article_detail' => function ($query) {
                    $query->select('article', 'lvl4_description', 'product_name_c', 'product_size_c', 'sku_code_c');
                },
                'article_detail.sku_stock' => function ($query) {
                    $query->select('sku_id', 'stock'); // Ensure this column name matches your database
                },
                'article_detail.article_price' => function ($query) use ($cdate) {
                    $query->select('sku_code_c', 'amount', 'modified_date', 'valid_from', 'valid_to') // Include filter columns
                    ->where('valid_from', '<=', $cdate)
                    ->where('valid_to', '>=', $cdate)
                    ->orderBy('valid_from', 'desc');
                },
            ])
            ->where('cart_id', $cart)
            ->where('is_custom', 1)
            ->where('selected', 1)
            ->where('is_available', 1);
            
           
            $article = $cartDetails->pluck('article')->toArray();

            RestHelper::syncStock($article, $custId);
            $cartDetails = $cartDetails->select('cart_detail_id', 'qty', 'article', 'attachment_group_id')
            ->get();
            
            if(count($cartDetails) == 0){
                return $this->sendError("Anda belum memilih Katalog untuk di pesan !", 400);
            }

            $total = 0;
            $total_nett = 0;
            $nett_before_tax = 0;
            $total_discount = 0;
            $total_tax = 0;

            $attachments = CartAttachment::whereIn('attachment_group_id', $cartDetails->pluck('attachment_group_id')->toArray())->get();
            // return $attachments;
            $orderNosByDescription = [];
            $item_disc = [];
            $groupedWithOrderNo = array_map(function ($item) use (&$orderNosByDescription, &$total, &$total_nett, &$attachments) {
            $articleDetail = $item['article_detail'];
            
                $isAvailable = 1; // Assuming available unless stock is 0
                $qty = $item['qty'];

                if (isset($articleDetail['sku_stock'])) {
                    $stock = $articleDetail['sku_stock']['stock'];
            
                    if ($stock === 0) {
                        $isAvailable = 0;
                    }
      
                    if ($stock !== null && $qty > $stock) {
                        $qty = $stock;
                    }
                }

                if (!isset($orderNosByDescription[$articleDetail['lvl4_description']])) {
                    $orderNosByDescription[$articleDetail['lvl4_description']] = strtoupper('INVBCC'.substr(uniqid(),-6));
                }
                $orderNo = $orderNosByDescription[$articleDetail['lvl4_description']];

                if (!empty($articleDetail['article_price'])) {
                    // usort($articleDetail['article_price'], function ($a, $b) {
                    //     return strtotime($b['modified_date']) - strtotime($a['modified_date']);
                    // });
                    $latestPrice = $articleDetail['article_price'][0]['amount'];
                } else {
                    $latestPrice = 0;
                }
                $subTotal = $latestPrice * $qty;
                $total = $subTotal; 
            
                $item_disc['is_custom'] = 0;
                $item_disc['data'][] = [
                    'article' => $articleDetail['article'] ?? '',
                    'qty' => $qty
                ];
                // $total += $total;
                $total_nett += $total;
                $custom_price = $attachments->where('attachment_group_id', $item['attachment_group_id'])->sum('estimate_price');
                return [
                    'reference_id' => $orderNo,
                    'sku' => substr($articleDetail['article'], 0, -3),
                    'article_id' => $articleDetail['article'],
                    'reference_name' => 'order_header',
                    'custom_price' =>  $custom_price,
                    'potition_side' => '-'
                ];
            }, $cartDetails->toArray());
            
            return $groupedWithOrderNo;

            
        } catch(\Exception $e){
            DB::rollback();
            Log::info('error checkout : '.$e->getMessage());
            return $this->sendError("Sorry system can't create order" . $e->getMessage(), 500);
        }
    }
    public function store(Request $request)
    {
        DB::beginTransaction();   
        try{
            // check customer active
            $custId = auth()->user()->customer->customer_id;
            $username = auth()->user()->username;
            $Customer = Customer::where('customer_id', $custId)->where('is_active',1)->first();
            if ($Customer == null) {
                return $this->sendError('Maaf anda tidak bisa membuat order karena data customer anda tidak aktif!');
            }
            $distribution_channel = auth()->user()->customer->distribution_channel;    
            // if ($Customer == null) {
            //     return $this->sendError("Sorry customer not allowed to create order", 403);
            // }


            if(auth()->user()->customer->distribution_channel == 'WHOLESALES'){
                $custId = auth()->user()->customer->customer_id;
    
                $cart = Cart::where('customer_id', $custId)->first()->id;
                $cartDetail = CartDetail::where('cart_id', $cart)->where('selected', true)->where('is_available', 1)->get('cart_detail_id');
                $request->merge(['items' => $cartDetail->toArray()]);

                if(count($request->items) == 0){
                    return $this->sendError("Anda belum memilih Katalog untuk di pesan !", 400);
                }
            }
            // if(count(array_filter($request->items, fn($i) => ($i['stock'] < $i['qty']))) > 0){
            //     return $this->sendError("Terdapat katalog yang melebihi ketersediaan stock, silahkan cek kembali keranjang", 400);
            // }
            $validate = $this->validateCartCategory(($request->input('items')));
            if($validate['error'] == true){
                return $this->sendError($validate['message'], 400);
            }
            // Log::channel('stderr')->info($Customer);
                $orderNo = strtoupper('INV'.substr(uniqid(),-8));

            if (auth()->user()->customer->distribution_channel == 'WHOLESALES') {
                if( $this->checkTopIsPendingPayment($Customer)){
                    //check toko tempo / bukan
                    return $this->sendError("Terdapat tagihan Anda yang sudah jatuh tempo/melewati tempo pembayaran,"
                    ."selesaikan pembayaran untuk melanjutkan proses pemesanan. Cek tagihan anda disini", 403);
                }
    
                $orderNo = strtoupper('INV'.substr(uniqid(),-8));

                $customerfirstaddress = CustomerShipment::where('customer_id', $custId)->first();
            } 
            
            elseif (auth()->user()->customer->distribution_channel == 'B2B') {
                $is_custom = [];

                $items = $request->input('items');
                $ids = collect($items)->pluck('cart_detail_id')->toArray();
                $cds = CartDetail::whereIn('cart_detail_id',$ids)->get();
                foreach ($cds as $cd) {
                    $sku = Product::where('article',$cd->article)->pluck('sku_code_c')->first();
                    $oc = OrderCustom::where('reference_name','cart')
                        ->where('reference_id',$cd->cart_id)
                        ->where('sku', $sku)
                        ->exists();
                    
                    $is_custom[] = $oc;
                }

                $u_is_custom = array_unique($is_custom);
                if (count($u_is_custom) > 1) {
                    return $this->sendError('Checkout hanya bisa di lakukan dengan tipe order yang sama!',403);
                }

                if (in_array(true,$u_is_custom) && $request->total < 20000000) {
                    return $this->sendError('Pesanan total minimal 20 Juta');
                }

                $orderNo = in_array(true,$u_is_custom) ? strtoupper('INVBCC'.substr(uniqid(),-6)) : strtoupper('INVBNC'.substr(uniqid(),-6));
                
                $customerfirstaddress = CustomerShipment::where('customer_id', $custId)->first();
                
                if ($Customer->registered_sap_at != null) {
                    $customerShipment = CustomerShipment::where('customer_id', $custId)->get();
                    $data = [];
                    foreach ($customerShipment as $cs) {
                        if (!Str::isUuid($cs->customer_shipment_id)) {
                            $data[] = $cs->customer_shipment_id;
                        }
                    }
                    $customerfirstaddress = CustomerShipment::where('customer_id', $custId)
                                            ->whereIn('customer_shipment_id',$data)->first();
                }
            }

            $total = 0;
            $total_nett = 0;
            $nett_before_tax = 0;
            $total_discount = 0;
            $total_tax = 0;

            $orderDetail = array(); 
            $a_od = [];
            $item_disc = [];
            for ($i = 0; $i < count($request->items); $i++) {
                $pr = CartDetail::where('cart_detail_id', $request->items[$i]['cart_detail_id'])->first();
                if($pr && !in_array($pr->article,$a_od)){
                    $article = Product::where('article', $pr->article)->first();
                    array_push($orderDetail, array(
                        'order_no' => $orderNo,
                        'article_id' => $article->article??'',
                        'product_name' => $article->product_name_c??'',
                        'product_size' => $article->product_size_c??'',
                        'is_new_arrival' => '',
                        'price' => $article->price->amount, 
                        'sub_total' => $article->price->amount*$pr->qty,
                        'qty' => $pr->qty,
                        'issued_qty' => $pr->qty,
                        'primary_discount' => 0,
                        'additional_discount' => 0,
                        'total' => $article->price->amount*$pr->qty
                    ));
                    $a_od[] = $pr->article;

                    if (str_contains($orderNo,'INVBCC')) {
                        $ocs = OrderCustom::where('reference_id',$pr->cart_id)
                        ->where('sku', $article->sku_code_c)
                        ->pluck('id')->toArray();

                        $custom_price = OrderCustomAttachment::whereIn('order_custom_id',$ocs)
                            ->sum('custom_price');
                        
                        $item_disc['is_custom'] = 1;
                        $item_disc['data'][] = [
                            'article' => $pr->article,
                            'custom_price' => $custom_price,
                            'qty' => $pr->qty
                        ];
                        $total += (($article->price->amount*$pr->qty)+($custom_price*$pr->qty));
                        $total_nett += (($article->price->amount*$pr->qty)+($custom_price*$pr->qty));
                    } else {
                        $item_disc['is_custom'] = 0;
                        $item_disc['data'][] = [
                            'article' => $pr->article,
                            'qty' => $pr->qty
                        ];
                        $total += $article->price->amount*$pr->qty;
                        $total_nett += $article->price->amount*$pr->qty;
                    }
                }
            }

            // Log::info('data harga total',[
            //     'total' => $total??0,
            //     'total_nett' => $total_nett??0,
            //     'is_custom' => str_contains($orderNo,'INVBCC'),
            //     'item_disc' => $item_disc
            // ]);
            if (auth()->user()->customer->distribution_channel == 'B2B') {
                $sim_disc = $this->simulateDiscountB2B($item_disc);

                if ($sim_disc != false) {
                    $total_discount = $sim_disc['total_discount'];
                    $total_nett = $total_nett - $total_discount;
                }
            }

            $cs_address_name = $customerfirstaddress->address_name != null || $customerfirstaddress->address_name != '' ? $customerfirstaddress->address_name.', ' : '';
            $cs_address = $customerfirstaddress->address != null || $customerfirstaddress->address != '' ? $customerfirstaddress->address.', ' : '-, ';
            $cs_city = $customerfirstaddress->city != null || $customerfirstaddress->city != '' ? $customerfirstaddress->city.', ' : '-, ';
            $cs_province = $customerfirstaddress->province != null || $customerfirstaddress->province != '' ? $customerfirstaddress->province.', ' : '-, ';
            $cs_zipcode = $customerfirstaddress->zip_code != null || $customerfirstaddress->zip_code != '' ? $customerfirstaddress->zip_code.'.' : '-.';

            $ship_to_address = $cs_address_name.$cs_address.$cs_city.$cs_province.$cs_zipcode;

            Log::info('checkout fetch',$request->all());
            $order = Order::create([
                'order_no' => $orderNo,
                'customer_id' => $custId,
                'customer_shipment_id' => strval($customerfirstaddress->customer_shipment_id),
                'order_status' => self::OrderStatusWait,
                'total' => $total,
                'total_discount' => $total_discount??0,
                'nett_before_tax' => $nett_before_tax??0,
                'total_tax' => $total_tax??0,
                'total_nett' => $total_nett??0,
                'created_by' => $Customer->owner_name,
                'modified_by' => $custId,
                'sales_id' => @$Customer->sales->sales_id,
                // 'shipping_date' => $request->shipping_date,
                // 'shipping_method' => $request->shipping_method,
                // 'completed_date' => $request->completed_date,
                // 'sales_organization' => $request->sales_organization,
                'dp_percentage' => substr($orderNo,0,6) == 'INVBCC' ? 50 : 0,
                'dp_amount' => substr($orderNo,0,6) == 'INVBCC' ? $total_nett*0.5 : 0,
                'distribution_channel' => $Customer->distribution_channel,
                'sales_name' => @$Customer->sales->sales_name,
                // 'division' => $request->division,
                'bill_to' => $Customer->owner_name,
                'bill_to_address' => $Customer->address,
                'bill_to_phone_number' => $Customer->phone_number,
                'currency' => 'IDR',
                'bill_to_email' => $Customer->email,
                'ship_to' => $customerfirstaddress->name,
                'ship_to_address' => $ship_to_address,
                'ship_to_phone_number' => $customerfirstaddress->phone_number
            ]);
            Log::info('Order Created',$order->toArray());
            

            OrderItem::insert($orderDetail);
            $items = $request->input('items');
            $ids = collect($items)->pluck('cart_detail_id')->toArray();

            if (auth()->user()->customer->distribution_channel == 'B2B') {
                $cds = CartDetail::whereIn('cart_detail_id',$ids)->get();
                $oc_exs = OrderCustom::where('reference_name','cart')
                            ->where('reference_id',$cds[0]->cart_id)
                            ->exists();
                if($oc_exs == true)
                {
                    foreach ($cds as $cd) {
                        $sku = Product::where('article',$cd->article)->pluck('sku_code_c')->first();
                        $oc = OrderCustom::where('reference_name','cart')
                            ->where('reference_id',$cd->cart_id)
                            ->where('sku', $sku)
                            ->update([
                                'reference_name' => 'order_header',
                                'reference_id' => $orderNo
                            ]);
                    }
                }
            }

            $id_integer = array_map('intval', $ids);
            CartDetail::whereIn('cart_detail_id', $ids)->delete();
            
            // $cartId = Cart::where('customer_id', $custId)->first();
            // if($cartId){
            //     CartDetail::where('cart_id', $cartId->id)->delete();
            //     $cartId->delete();
            // }

            $result = [
                'order_no'=>$orderNo
            ];
            
            DB::commit();

            

            if ($distribution_channel == 'B2B') {
                $mp = MasterParameter::where('group_key','SALES_NOTIF')->where('key','B2B')->first();
                $msg = 'Ada pesanan baru masuk dengan nomor pesanan #'.$orderNo.'. Klik pesan ini untuk melihat detail.';
                $this->notifStore($mp->value, 'Pesanan Terbaru', 'order-b2b-internal', $msg, $orderNo, 'Transaksi', 'B2B', 'info');
            } else {
                $msg = 'Ada pesanan baru masuk dengan nomor pesanan #'.$orderNo.'. Klik pesan ini untuk melihat detail.';
                $sales = CustomerSales::where('customer_id',$custId)->first();
                $this->notifStore($sales->sales_id, 'Pesanan Terbaru', 'order', $msg, $orderNo, 'Transaksi', 'WHOLESALES', 'info');
            }

            return $this->sendSuccess("Order created successfully.",$result);
        

        }catch(\Exception $e){
            DB::rollback();
            Log::info('error checkout : '.$e->getMessage());
            return $this->sendError("Sorry system can't create order" . $e->getMessage(), 500);
        }
    }

    public function removePartial(Request $request){
        try{
            $param = $request->collect();
            $custid = auth()->user()->customer->customer_id;
            $order_validate = Order::validateConfirm($param, $custid);
            if($order_validate !== true) return $this->sendError($order_validate, 500, 'error');        
            $items = OrderItem::select('article_id', 'qty')->where('order_no', $param['order_no'])->whereIn('article_id', $param['items'])->get();
            if($items != null && count($items) > 0){
                foreach($items as $i){
                    $rq = new \stdClass;
                    $rq->article = $i->article_id;
                    $rq->qty = $i->qty;
                    $this->cartStore($custid, $rq);
                }
            }
            if(isset($param['items_changed']) && count($param['items_changed']) > 0){
                foreach($param['items_changed'] as $i){
                    $dt = OrderItem::where(['order_no' => $param['order_no'], 'article_id' => $i['article']])->first();
                      if($dt){
                            $dt->issued_qty = $i['qty']??$dt->qty;
                            $dt->sub_total = $i['qty']??$dt->qty * $dt->price;
                            $dt->save();
                        }
                }
            }
            OrderItem::select('article_id as article', 'qty')->where('order_no', $param['order_no'])->whereIn('article_id', $param['items'])->delete();
            return $this->sendSuccess("Partial remove success", []);
    
        }
    catch(\Exception $e){
        return $this->sendError($e->getMessage(), 500);
    }

    }


     public function getStock($article)
    {
   
        $details[] = [
            'source' => 'CAREOM',
            'destination' => 'STK',
            'article' => $article,
            // 'site' => '1200'
        ];
    

        $data = [
                "source"=> "CAREOM",
                "destination" => "STK",
                "detail" => $details
            ];

        $getStokRepo = new GetStokRepo();
        $response = $getStokRepo->getStock($data, auth()->user()->customer->customer_id);
        $qty = 0;
        //note sap 
        
        try{
            $qty = $getStokRepo->getQty($response);
        } catch (\Exception $e) {
            // \Log::error($e->getMessage());
            $qty = 0;

        }
        return $qty;
    
    }


    public function confirmWolesales(Request $request){
        $custId = auth()->user()->customer->customer_id;
        $Customer = Customer::where('customer_id', $custId)->first();
        $orders_no = Order::where('order_group_id', $request->order_group_id);

        $hasOtherStatus = $orders_no->pluck('order_status')->contains(function ($status) {
            return $status !== 'Menunggu Konfirmasi' &&
                $status !== Self::OrderStatusOnHold &&
                $status !== Self::OrderStatusPending;
        });
        
        if($hasOtherStatus){
            return $this->sendError("Pesanan Sudah dikonfirmasi", 400, '', ["is_checkout" => false]);
        }
        $customerShipment = CustomerShipment::where('customer_shipment_id', $request->customer_shipment_id)->first();
        $orders = $orders_no->get('order_no')->map(function ($item) use($request) {
            return [
                'order_no' => $item->order_no,
                'customer_shipment_id' => $request->customer_shipment_id,
            ];
        });

        $order_no = $orders_no->pluck('order_no')->toArray();
        // $article = OrderItem::whereIn('order_no', $order_no)->get()->groupBy('order_no');
        $cs_address_name = $customerShipment->address_name != null || $customerShipment->address_name != '' ? $customerShipment->address_name.', ' : '-, ';
        $cs_address = $customerShipment->address != null || $customerShipment->address != '' ? $customerShipment->address.', ' : '-, ';
        $cs_city = $customerShipment->city != null || $customerShipment->city != '' ? $customerShipment->city.', ' : '-, ';
        $cs_province = $customerShipment->province != null || $customerShipment->province != '' ? $customerShipment->province.', ' : '-, ';
        $cs_zipcode = $customerShipment->zip_code != null || $customerShipment->zip_code != '' ? $customerShipment->zip_code.'.' : '-.';

        $ship_to_address = $cs_address_name.$cs_address.$cs_city.$cs_province.$cs_zipcode;
        // $article = OrderItem::whereIn('order_no', $order_no)->get()->groupBy('order_no');
      
        $ordet = OrderItem::with('product.mainImageVariant')->whereIn('order_no', $order_no)->get();
        // $orders = OrderItem::leftJoin('article', 'order_detail.article_id', '=', 'article.article')
        // ->select('order_detail.*', 'article.sku_code_c', 'article.product_name_c' )
        // ->whereIn('order_detail.order_no', $order_no)
        // ->where('is_available', 1)
        // ->orderBy('order_detail.order_detail_id', 'desc')
        // ->get();

        // $sales = DB::table('order_header as oh')
        // ->join('customer_sales as cs', 'cs.customer_id', '=', 'oh.customer_id')
        // ->join('sales', 'sales.sales_id', '=', 'cs.sales_id')
        // ->select('sales.sap_username', 'oh.order_no')
        // ->whereIn('oh.order_no', $order_no)
        // ->get();

        // return collect($sales->where('order_no', 'INV5CADC91C'))->first()->order_no;
        // return $ordet;
        // return $Customer;
        $confirm = [];
        foreach ( $orders as $key => $order){
            Order::where('order_no', $order['order_no'])->update([
                "customer_shipment_id" => $request->customer_shipment_id,
                "ship_to" => $customerShipment->name,
                "ship_to_address" => $ship_to_address,
                "ship_to_phone_number" => $customerShipment->phone_number
            ]);
            $newRequest = new Request();
            $newRequest->merge($order);
            $confirmResult = $this->confirm($newRequest, $ordet, $Customer, $customerShipment);

            $responseData = $confirmResult->getData(true);
            $confirm[] = [
                "order_no" => $order['order_no'],
                "status" => $responseData['error'] == true ? 'Error' : 'Success',
                "error" => $responseData['error'],
                "description_message" => $responseData,
                "message" => isset($responseData['data']['message']) ? $responseData['data']['message'] : 'SO berhasil dibuat'
            ];
            // if($orders_no->where('order_no',$order['order_no'])->first()){
            //     if(Order::where('order_no', $order['order_no'])->first()->total > 0){
            //         // if($responseData['error'] == true){
            //         //     if(isset($article[$order['order_no']])){
            //         //         $article = $article[$order['order_no']]->pluck('article_id')->toArray(); 
            //         //     $orderItem = OrderItem::where('order_no', $order['order_no'])->get(['order_detail_id', 'article_id', 'qty'])->toArray();
            //         //     RestHelper::syncStock($article, $custId);
            //         //     $stock = ProductSku::whereIn('sku_id', $article)->get(['sku_id', 'stock'])->groupBy('sku_id');
            //         //     $mappedOrders = array_map(function ($item) use ($stock){
            //         //         return [
            //         //             'order_detail_id' => $item['order_detail_id'], // Rename or transform fields
            //         //             'article_id' => $item['article_id'],
            //         //             'qty' => $item['qty'] > $stock[$item['article_id']][0]->stock ? $stock[$item['article_id']][0]->stock : $item['qty'],
            //         //             'is_available' => $stock[$item['article_id']][0]->stock == 0 ? 0 : 1,
            //         //             'modified_date' => now(),
            //         //         ];
            //         //     }, $orderItem);
            //         //     OrderItem::upsert($mappedOrders, ['order_detail_id'], ['qty', 'is_available', 'modified_date']);
            //         //     // $confirmResult = $this->confirm($newRequest);
            //         //     // $responseData = $confirmResult->getData(true);
            //         //     }     
            //         // }
        
                   
            //     }
            // }
            
        }

        
        $discount = $orders_no->where('order_status', 'Baru')->sum('total_discount');
        $total = $orders_no->where('order_status', 'Baru')->sum('total');
        $result = [
            'order_no'=>$order_no,
            'customer_name'=>$customerShipment->name,
            'store_name'=>$Customer->owner_name,
            'address'=>$customerShipment->address,
            'sub_total' => (int)$orders_no->sum('total_nett'),
            'total_discount' => (int)$discount,
            'total' => (int) $total - (int)$discount,

        ];

        foreach ($order_no as $order) {
            //customer notif
            $msg = 'Pesanan dengan nomor #' . $order . ' telah berhasil dibuat.  Detail pesanan dan estimasi pengiriman dapat dilihat di menu "Pesanan Saya".';
            $customerId = auth()->user()->customer->customer_id;
            $distribution_channel = auth()->user()->customer->distribution_channel;
            $this->notifStore($customerId, 'Pesanan Dibuat', 'order', $msg, $order, 'Transaksi', $distribution_channel, 'success');
        }

        // MailSender::dispatch(auth()->user()->email, json_encode($param), 'mail_order');
        //reset timeout
        Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
        CreditLimitQueue::dispatch($custId);
        return $this->sendSuccess("Confirm order success ",['result' => $result, 'data'=> $confirm]);
    
    }
    
    //Konfirmasi Order
    public function confirm(Request $request, $ordetParams = null, $customerParams = null, $shipmentParams = null)
    {
        
        if (auth()->user()->customer->distribution_channel == 'B2B') {
            return $this->confirmB2B($request);
        }

        $this->sap_error = MasterParameter::where('group_key','CREATE_SO_ERROR')->pluck('description')->toArray();

        try{
            $param = [];
            $order_no =  $request->input('order_no');
            // $ord = Order::where('order_no', $order_no)->first()->toArray();
            $ordet = $ordetParams == null ? OrderItem::with('product.mainImageVariant')->where('order_no', $order_no)->get() : $ordetParams->where("order_no",$order_no);
            foreach($ordet as $od){
                $od['product']['flag'] = $od->product->flag();
            }

            // return $ordet;
            // $ord['order_detail'] = $ordet->toArray();
            // $custId = auth()->user()->refrence_id;
            $custId = auth()->user()->customer->customer_id;
            $order_status = self::OrderStatusBaru;
            $simulate = "";

            $username = auth()->user()->username;
            $Customer = $customerParams == null ? Customer::where('customer_id', $custId)->first() : $customerParams;    
            
            // email data
            $param['customer'] = $Customer->toArray();
            // $param['order'] = $ord;

            $socmed = new GetSocialsRepo();
            $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
            $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
            $twitter = $socmed->getSocialMediaParameters('TWITTER');
            $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
            $youtube = $socmed->getSocialMediaParameters('YOUTUBE');
            $support = $socmed->getSocialMediaParameters('SUPPORT');
            $linkedin = $socmed->getSocialMediaParameters('LINKEDIN');


            $param['social_media'] = [
                'facebook' => $facebook,
                'twitter' => $twitter,
                'instagram' => $instagram,
                'support' => $support,
                'linkedin'=> $linkedin
            ];

            if ($Customer == null) {
                Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
                return $this->sendError("Sorry customer not allowed to confirm order", 403);
            }

            if ($request->input('customer_shipment_id')==''|| $request->input('customer_shipment_id')==null) {
                Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
                return $this->sendError("Sorry customer shipment id is required", 403);
            }


            $table  = Order::where('order_no', $order_no)->first();
            $total_all  = $table->total_nett;
            $total_sub  = $table->total;

            if(!$table->count() > 0) {
                Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
                return $this->sendError("Sorry order number not found to confirm order", 403);
            }

            if($table->order_status == self::OrderStatusBaru && ($table->sales_order_no!=null ||  $table->sales_order_no!='')) {
                Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
                return $this->sendError("Sorry this order is already confirm ", 403);
            }

            //TODO : check limit
            // $dataLimit = $this->getLimitCustomer();
            // $trx->data_limit = $dataLimit ?? [];
            //    if(!$orders->count() > 0){
            //     return $this->sendError("Sorry limit", 403);
            // }

            try{
                // $simulate = $this->createSimulateSO("s", $order_no,$custId);
                $create_so = $this->createSimulateSO("i", $order_no,$custId,$custId);
            }catch(\Exception $e){
                Log::info($e->getMessage());
                if(isset($this->errordata['message'])){
                    if(in_array($this->errordata['message'], $this->sap_error) == true){
                        $desc = MasterParameter::where('description',$this->errordata['message'])->first();
                        $returnvalue = $desc->value ?? $this->errordata['message'];

                        
                        // SimulateSOQueue::dispatch(auth()->user()->customer->customer_id, $order_no, 'i');

                        Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
                        $this->updateOrderToOnhold($order_no);
                        return $this->sendError("Error when simulate SO", 500, '', ['type' => 'E', 'message' => 'Stock Tidak Tersedia']);
                    }
                    else{
                        // SimulateSOQueue::dispatch(auth()->user()->customer->customer_id, $order_no, 'i');
                        $this->updateOrderToOnhold($order_no);
                    }
                    }

                
                // MailSender::dispatch($Customer->email, json_encode($param), 'mail_order');
                Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
                $this->updateOrderToOnhold($order_no);
                return $this->sendError("Error when simulate SO", 500, '',  $e->getMessage());

            }

            // if($simulate==""){
            //     $order_status = self::OrderStatusOnHold;
            // }

            // check customer active
        

            // if( $this->checkTopIsPendingPayment($Customer)){
            //     return $this->sendError("Terdapat tagihan Anda yang sudah jatuh tempo/melewati tempo pembayaran,"
            //     ."selesaikan pembayaran untuk melanjutkan proses pemesanan. Cek tagihan anda disini", 403);
            // }
            DB::beginTransaction();   

            $salesID = DB::table('customer_sales')
                        ->where('customer_id', $custId)
                        ->value('sales_id');

            $customer_shipment_id = $request->customer_shipment_id;
            $customer_shipment = $shipmentParams == null ? CustomerShipment::where('customer_shipment_id', $customer_shipment_id)->first(): $shipmentParams;
            
            $cs_address_name = $customer_shipment->address_name != null || $customer_shipment->address_name != '' ? $customer_shipment->address_name.', ' : '-, ';
            $cs_address = $customer_shipment->address != null || $customer_shipment->address != '' ? $customer_shipment->address.', ' : '-, ';
            $cs_city = $customer_shipment->city != null || $customer_shipment->city != '' ? $customer_shipment->city.', ' : '-, ';
            $cs_province = $customer_shipment->province != null || $customer_shipment->province != '' ? $customer_shipment->province.', ' : '-, ';
            $cs_zipcode = $customer_shipment->zip_code != null || $customer_shipment->zip_code != '' ? $customer_shipment->zip_code.'.' : '-.';

            $ship_to_address = $cs_address_name.$cs_address.$cs_city.$cs_province.$cs_zipcode;

            if ($table) {
                // $table->customer_shipment_id = $customer_shipment_id;
                $table->sales_id = $salesID;
                $table->sales_order_no = $create_so['vbeln'];
                $table->order_status =$order_status;
                // $table->ship_to = $customer_shipment->name;
                // $table->ship_to_address = $ship_to_address;
                // $table->ship_to_phone_number = $customer_shipment->phone_number;
                $table->modified_date = now();
                $table->save();
            }
            
            $customerShipment = $shipmentParams == null ? CustomerShipment::where('customer_shipment_id', $customer_shipment_id)->first(): $shipmentParams;
            // $order_detail = OrderItem::where('order_no', $order_no)->sum('qty');
            $result = [
                'order_no'=>$order_no,
                'customer_name'=>$customerShipment->name,
                'store_name'=>$Customer->owner_name,
                'address'=>$customerShipment->address,
                'sub_total' => (int)$total_sub,
                'total_discount' => (int)$table->total_discount,
                'total' => (int)$total_all,

            ];
            DB::commit();
            // $this->notifStore(auth()->user()->customer->customer_id, "Pesanan Dibuat", "order", "Pesanan dengan nomor $order_no telah berhasil dibuat, klik disini untuk melihat detail", $order_no);
            // MailSender::dispatch(auth()->user()->email, json_encode($param), 'mail_order');
            //reset timeout
            Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
            CreditLimitQueue::dispatch($custId);
            return $this->sendSuccess("Confirm order success ",$result);
        

        }catch(\Exception $e){
            DB::rollback();
            Log::info($e->getMessage());
            Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
            $this->updateOrderToPending($order_no);
            //testing purpose
            // MailSender::dispatch($Customer->email, json_encode($param), 'mail_order');
            return $this->sendError("Sorry system can't confirm order " . $e->getMessage(), 500);
        }
    }


    public function confirmB2B($request)
    {
        if ($request->input('customer_shipment_id')==''|| $request->input('customer_shipment_id')==null) {
            Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
            return $this->sendError("Sorry customer shipment id is required", 403);
        }

        $table  = Order::where('order_no', $request->order_no)->first();
        
        if(!$table->count() > 0) {
            Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
            return $this->sendError("Sorry order number not found to confirm order", 403);
        }

        if($table->order_status == self::OrderStatusBaru && ($table->sales_order_no!=null ||  $table->sales_order_no!='')) {
            Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
            return $this->sendError("Sorry this order is already confirm ", 403);
        }

        try {
            $param = [];
            $order_no =  $request->input('order_no');
            $ord = Order::where('order_no', $order_no)->first()->toArray();
            $ordet = OrderItem::with('product.mainImageVariant')->where('order_no', $order_no)->get();
            foreach($ordet as $od){
                $od['product']['flag'] = $od->product->flag();
            }
            $ord['order_detail'] = $ordet->toArray();
            // $custId = auth()->user()->refrence_id;
            $custId = auth()->user()->customer->customer_id;

            $Customer = Customer::where('customer_id', $custId)->first();    
            
            // email data
            $param['customer'] = $Customer->toArray();
            $param['order'] = $ord;

            $socmed = new GetSocialsRepo();
            $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
            $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
            $twitter = $socmed->getSocialMediaParameters('TWITTER');
            $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
            $youtube = $socmed->getSocialMediaParameters('YOUTUBE');
            $support = $socmed->getSocialMediaParameters('SUPPORT');
            $linkedin = $socmed->getSocialMediaParameters('LINKEDIN');


            $param['social_media'] = [
                'facebook' => $facebook,
                'twitter' => $twitter,
                'instagram' => $instagram,
                'support' => $support,
                'linkedin'=> $linkedin
            ];

            $customer_shipment = CustomerShipment::where('customer_shipment_id', $request->input('customer_shipment_id'))->first();
            
            $cs_address_name = $customer_shipment->address_name != null || $customer_shipment->address_name != '' ? $customer_shipment->address_name.', ' : '';
            $cs_address = $customer_shipment->address != null || $customer_shipment->address != '' ? $customer_shipment->address.', ' : '-, ';
            $cs_city = $customer_shipment->city != null || $customer_shipment->city != '' ? $customer_shipment->city.', ' : '-, ';
            $cs_province = $customer_shipment->province != null || $customer_shipment->province != '' ? $customer_shipment->province.', ' : '-, ';
            $cs_zipcode = $customer_shipment->zip_code != null || $customer_shipment->zip_code != '' ? $customer_shipment->zip_code.'.' : '-.';

            $ship_to_address = $cs_address_name.$cs_address.$cs_city.$cs_province.$cs_zipcode;

            $Customer = Customer::where('customer_id', auth()->user()->reference_id)->first(); 

            $table->order_status = 'Menunggu Verifikasi';
            $table->customer_shipment_id = $request->customer_shipment_id;
            $table->ship_to = $customer_shipment->name;
            $table->ship_to_address = $ship_to_address;
            $table->ship_to_phone_number = $customer_shipment->phone_number;
            $table->modified_date = now();
            $table->save();

            OrderApproval::create([
                'order_no' => $request->order_no,
                'status' => 'Pending',
                'created_by' => $table->customer->owner_name
            ]);

            $response = [
                'order_no' => $request->order_no,
                'customer_name' => $table->customer->owner_name,
                'total_discount' => (int)$table->total_discount,
                'total' => (int)$table->total_nett,
            ];
            
            // foreach ($order_no as $order) {
            //     //customer notif
            //     $msg = 'Pesanan dengan nomor #' . $order . ' telah berhasil dibuat.  Detail pesanan dan estimasi pengiriman dapat dilihat di menu "Pesanan Saya".';
            //     $customerId = auth()->user()->customer->customer_id;
            //     $this->notifStore($customerId, 'Pesanan Dibuat', 'order', $msg, $order);
            // }

            // $this->notifStore(auth()->user()->customer->customer_id, "Pesanan Dibuat", "order", "Pesanan dengan nomor #$order_no telah berhasil dibuat, klik disini untuk melihat detail", $order_no, 'Transaksi', 'B2B', 'success');

            // MailSender::dispatch(auth()->user()->email, json_encode($param), 'mail_received_b2b');
                        //email pesanan diterima (501)
                        $count = OrderItem::where('order_no', $order_no)->sum('qty');
                        $param['orders'] = [
                            'order_no'=>$request->order_no,
                            'count'=>$count,
                            'name'=>$Customer->owner_name,
                            'phone_no'=>$Customer->phone_number,
                            'address'=>$table->ship_to_address,
                            'subtotal'=>$table->total,
                            'total'=>$table->total_nett,
                            'total_discount'=>$table->total_discount,
                        
                            //GET SOCIAL MEDIA ACCOUNTS
                            'facebook' => $facebook,
                            'twitter' => $twitter,
                            'instagram' => $instagram,
                            'support' => $support,
                            'linkedin'=> $linkedin
                            ];
                            //MailSender::dispatch(auth()->user()->email, json_encode($param), 'mail_item_received_b2b');
                            //end email pesanan diterima
                        
            Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
            return $this->sendSuccess('Confirm order success',$response);
        } catch (\Exception $e) {
            Cache::forget('transaction-timeout'.'-'.auth()->user()->customer->customer_id);
            Log::info($request->order_no.' order B2B confirm order error : '.$e->getMessage());
            return $this->sendError("Sorry system can't confirm order", 500);
        }
    }

    public function confirmRefactor(Request $request){
        $param = $request->collect();

        //debug auth customer id
        $custId = auth()->user()->customer->customer_id??100225;
        
        //handling validasi order
        $order_validate = Order::validateConfirm($request, $custId);
        if($order_validate !== true) return $this->sendError($order_validate, 500, 'error');
    

        //send ke job simulate so
        SapSO::dispatch("i", $param, $custId)->onConnection('redis')->onQueue('rqueue');
        return $this->sendSuccess("Order sedang diproses, mohon tunggu");

    }

    public function getTransactions(Request $request): JsonResponse
    {
        $custId = auth()->user()->customer->customer_id;
        $username = auth()->user()->username;
        $status = $request->input('status');
        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');
        $text = $request->input('text');
        $page = (int) $request->input('page', 1);
        $limit = (int) $request->input('per_page', 12);
    
        $OrdersQuery = Order::leftJoin('order_detail', 'order_header.order_no', '=', 'order_detail.order_no')
            ->leftJoin('article', 'order_detail.article_id', '=', 'article.article')
            ->leftJoin('image_generic', 'image_generic.sku_code_c', '=', 'article.sku_code_c')
            ->when($text, function ($query) use ($text) {
                return $query->where(function ($q) use ($text) {
                    $q->where('article.product_name_c', 'like', "%$text%")
                      ->orWhere('order_header.order_no', 'like', "%$text%") ;
                });
            })
            ->when($dateFrom && $dateTo, function ($query) use ($dateFrom, $dateTo) {
                return $query->whereNotIn('order_header.order_status', ['Baru', 'Pending', 'On Hold', 'Batal', 'Menunggu Konfirmasi'])
                             ->whereBetween(DB::raw('DATE(order_header.created_date)'), [$dateFrom, $dateTo]);
            })
            ->where('order_header.customer_id', $custId)
            ->select(
                'order_header.*',
                'article.product_name_c as product_name_oh',
                'article.lvl3_description as product_category_oh',
                'image_generic.url',
                'image_generic.sku_code_c as sku_generic'
            )
            ->groupBy('order_header.order_no')
            ->orderBy('order_header.created_date', 'desc');
    
        $OrdersRaw = $OrdersQuery->get();
    
        $sku_generic = $OrdersRaw->pluck('sku_generic');
        $data_generics = DB::table('image_generic')
            ->whereIn('sku_code_c', $sku_generic)
            ->where('image_generic.is_main_image', 1)
            ->select('image_generic.file_path', 'image_generic.sku_code_c')
            ->get()
            ->groupBy('sku_code_c');
    
        $salesOrderNos = $OrdersRaw->pluck('sales_order_no')->filter()->unique();
        $orderNos = $OrdersRaw->pluck('order_no')->filter()->unique();
    
        $DeliveryOrders = DeliveryOrder::whereIn('sales_order_no', $salesOrderNos)->get()->keyBy('sales_order_no');
        $DeliveryOrderDetails = DeliveryOrderDetail::whereIn('delivery_order_no', $DeliveryOrders->pluck('delivery_order_no'))
            ->with('product.price')
            ->get()
            ->groupBy('delivery_order_no');
    
        $Invoices = Invoice::whereIn('sales_order_no', $salesOrderNos)->get()->keyBy('sales_order_no');
        $OrderItem = OrderItem::whereIn('order_no', $orderNos)->get()->groupBy('order_no');
    
        $filtered = collect();
        $custTopDays = auth()->user()->customer->top_days;
    
        foreach ($OrdersRaw as $order) {
            $sku_generic = $order->sku_generic;
            $data_generic = $data_generics[$sku_generic][0] ?? null;
            $orderItems = 0;
            $trx = new Transaction();
    
            $trx->product_name = $order->product_name_oh;
            $trx->product_category = $order->product_category_oh;
            $trx->main_image = ($data_generic && $data_generic->file_path) ? env('S3_STREAM_URL') . $data_generic->file_path : null;
            $trx->order_no = $order->order_no;
            $trx->sales_order_no = (int) $order->sales_order_no;
            $trx->customer_id = (int) $order->customer_id;
    
            $invoice = $Invoices[$order->sales_order_no] ?? null;
            $DeliveryOrder = $DeliveryOrders[$order->sales_order_no] ?? null;
    
            if (!in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusBaru, self::OrderStatusWait, self::OrderStatusPembayaran, self::OrderStatusCancel])) {
                $total_do = 0;
                if ($DeliveryOrder != null) {
                    $doi = $DeliveryOrderDetails[$DeliveryOrder->delivery_order_no] ?? collect();
                    $total_do = $doi->sum(function ($item) {
                        return (int) $item->product->price->amount * (int) $item->qty;
                    });
                }
    
                $order_price = $invoice ? $invoice->nett_price : ($DeliveryOrder ? $total_do - $DeliveryOrder->discount : $order->total_nett);
    
                $trx->sub_total = 0;
                $trx->total_discount = 0;
                $trx->total_nett = (int) $order->total_nett;
                $trx->customer_shipment_id = $DeliveryOrder ? (int) $DeliveryOrder->customer_shipment_id : 0;
                $trx->delivery_order_no = $DeliveryOrder->delivery_order_no ?? "";
                $trx->gi_date = $DeliveryOrder->good_issue_date ?? "";
                $orderItems = $DeliveryOrder ? $DeliveryOrderDetails[$DeliveryOrder->delivery_order_no]->sum('qty') ?? 0 : 0;
            } else {
                $trx->sub_total = (int) $order->sub_total;
                $trx->total_discount = (int) $order->total_discount;
                $trx->total_nett = (int) $order->total_nett;
                $trx->customer_shipment_id = (int) $order->customer_shipment_id;
                $trx->delivery_order_no = "";
                $trx->gi_date = "";
                $orderItems = isset($OrderItem[$order->order_no]) ? $OrderItem[$order->order_no]->sum('qty') : 0;
            }
    
            $trx->total = (int) $order->total;
            $trx->transaction_date = $order->created_date;
    
            if (in_array($custTopDays, ['0', 'Cash']) && !in_array($order->order_status, [
                self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait,
                self::OrderStatusBaru, self::OrderStatusCancel, self::OrderStatusOnProcess
            ])) {
                $trx->status = ($invoice && $invoice->status === 'LUNAS') ? $order->order_status : self::OrderStatusPembayaran;
            } else {
                $trx->status = $order->order_status;
            }
            $trx->status = $order->order_status;
    
            if ($status && $status !== 'Semua' && $trx->status !== $status) {
                continue;
            }
    
            $trx->inovice_no = "";
            $trx->order_date = $order->created_date;
            $trx->total_item = $orderItems;
            $trx->order_group_id = $order->order_group_id;
    
            $filtered->push($trx);
        }
    
        $paged = $filtered->forPage($page, $limit);
    
        return $this->sendSuccess(null, [
            'total_data' => $filtered->count(),
            'size' => $limit,
            'active_page' => $page,
            'total_page' => (int) ceil($filtered->count() / $limit),
            'data' => $paged->values()
        ]);
    }

    public function getTransactionDetailRevamp(Request $request, $order_no){
        // ADIK ADIK KITA BUAT FUNGSINYA PER STEP YA BIAR GA PUSING
        // GAPERLU VALIDASI KARENA UDAH DI VALIDASI DI MIDDLEWARE
        $order = Order::where('order_no', $order_no)->first();

        

    }

    public function detailCheckoutB2B(Request $request, $order_group_id){
        $custId = auth()->user()->customer->customer_id;
        $trx = new Transaction();
        $allItems= [];
        $limit=12;
        $page = $request->input('page');
        if($perPage = $request->query('limit')){
                $limit = $perPage;
        }

        $order_group = Order::where('order_group_id', $order_group_id)->get('order_no');


        // $dummy_customer_id = MasterParameter::where('group_key','B2B_NEW_CUSTOMER')->where('key','CUSTOMER_ID')->first()->value;
      
        // $customerSapId = Customer::where('customer_id', $order->customer_id)->first()->sap_id;
        //     if($customerSapId != null){
        //         $dummy_customer_id = $customerSapId;
        //     }
        //     $order_group->each(function ($order) use ($dummy_customer_id) {
        //         $job = new SimulateSOQueue( $dummy_customer_id,  $order->order_no);
        //         $job->handle();
        //     });
    
        $orders = Order::with('items:order_detail_id,order_no,article_id,is_custom,is_available,product_name,product_variant,product_size,price,sub_total,qty,issued_qty,primary_discount,additional_discount,total', 'customer_shipment')
        ->where('order_group_id', $order_group_id)->get();

        $selectedShipment = CustomerShipment::where('customer_id', $custId)->where('is_selected', true)->first();
        foreach ($orders as $item) {
            // if ($item->customer_shipment_id !== $selectedShipment->customer_shipment_id) {
                $cs_address_name = $selectedShipment->address_name != null || $selectedShipment->address_name != '' ? $selectedShipment->address_name.', ' : '';
                $cs_address = $selectedShipment->address != null || $selectedShipment->address != '' ? $selectedShipment->address.', ' : '-, ';
                $cs_city = $selectedShipment->city != null || $selectedShipment->city != '' ? $selectedShipment->city.', ' : '-, ';
                $cs_province = $selectedShipment->province != null || $selectedShipment->province != '' ? $selectedShipment->province.', ' : '-, ';
                $cs_zipcode = $selectedShipment->zip_code != null || $selectedShipment->zip_code != '' ? $selectedShipment->zip_code.'.' : '-.';

                $ship_to_address = $cs_address_name.$cs_address.$cs_city.$cs_province.$cs_zipcode;

                $item->update([
                    'customer_shipment_id' => $selectedShipment->customer_shipment_id,
                    'ship_to_address' => $ship_to_address
                ]);
            // }
        }

        $hasOtherStatus = $orders->pluck('order_status')->contains(function ($status) {
            return $status !== 'Menunggu Konfirmasi';
        });
        $is_confirm = false;
        if($hasOtherStatus){
            $is_confirm = true;
        }
        
        $OrderNos = $orders->pluck('order_no')->toArray();

        $order = $orders->first();
        
        $trx->payment_date = "";
        $location_code = $order->location_code ?? null;
        $location_name = $order->location_name ?? null;
        $trx->sap_response = null;
        $trx->is_confirm = $is_confirm;
        $trx->order_no = $orders->pluck('order_no')->toArray();
        $trx->order_group_id = $order_group_id;
        $trx->sub_total = (int)$orders->sum('total') - (int)$orders->sum('total_discount');
        $trx->total_discount = (int)$orders->sum('total_discount');
        $trx->total = (int)$orders->sum('total');
        $trx->ongkos_kirim = (int) $order->shipping_charges;
        $trx->company_email = auth()->user()->customer->email;
        $trx->pajak = 0;
        $trx->total_nett = (int)$orders->sum('total') - (int)$orders->sum('total_discount');
        $trx->customer_id = (int) $order->customer_id;
        $trx->status = self::OrderStatusWait;
        $trx->delivery_order_no = "";
        $trx->transaction_date = $order->created_date;
        $trx->do_date  = "";
        $trx->gi_date  = "";
        $trx->shipping_date = $order->shipping_date ?? "";
        $trx->received_date = $order->completed_date ?? "";
        $trx->invoice_no =  "";
        $trx->invoice_no_dp = null;
        $trx->order_summary =  "Belum Lunas";
        $trx->no_resi = "";
        $trx->bill_to = $order->bill_to;
        $trx->bill_to_address = $order->bill_to_address;
        $trx->bill_to_phone_number = $order->bill_to_phone_number;
        $trx->bill_to_email = $order->bill_to_email;
        $trx->ship_to = $order->ship_to;

        $trx->customer_shipment_id = $order->customer_shipment_id ?? null;
        $trx->ship_to_address = $order->ship_to_address;

        $trx->ship_to_phone_number = $order->ship_to_phone_number;
        $trx->distribution_channel = $order->distribution_channel;
        $trx->transportation_zone = $location_code . ' - ' . $location_name;
        $trx->payment_dp_date = null;

        $orderItems = OrderItem::leftJoin('article', 'order_detail.article_id', '=', 'article.article')
        ->select('order_detail.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_variant_c')
        ->whereIn('order_no', $OrderNos)->orderBy('order_detail.order_detail_id', 'desc')
        ->get();

        $is_custom = false;
        $remark = [];
        if(count($orderItems) < 1){
            $orderItems = OrderCustom::leftJoin('article', 'order_custom.sku', '=', 'article.sku_code_c')
            ->select('order_custom.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_variant_c', 'article.product_size_c')
            ->whereIn('reference_id', $OrderNos)
            ->get();
            
            $remark = Remark::whereIn('attachment_group_id', $orderItems->pluck('attachment_group_id')->toArray())->get()->groupBy('attachment_group_id');
            $is_custom = true;
        }

        // return $orderItems;
        $items = [];
        $total_qty = 0;

        if($is_custom == false){
            
            foreach ($orderItems as $item) {
                $image = DB::table('article as art')
                    ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                    ->where('art.article', $item->article_id)
                    ->where('ig.is_main_image', 1)
                    ->select('ig.file_path')
                    ->first();
    
                $article = Product::where('article', $item->article_id)->first();
                $articleStock = ProductSku::where('sku_id', $article->article) ->pluck('stock')->first();

                $warna = DB::table('master_color')
                    ->where('key', $item->product_variant_c)
                    ->where('is_active', 1)
                    ->pluck('value')->first();
    
                $product_items = [];
                $itemData = new TransactionItem();
                $itemData->article = $item->article_id;
                $itemData->product_name = $item->product_name;
                $itemData->product_variant = $warna ?? null;
                $itemData->product_size = $item->product_size;
                $itemData->qty = $item->qty;
                $itemData->stock = $articleStock;
                $itemData->sub_total = (int) ($item->qty * $article->price->amount);
                array_push($product_items, $itemData);
                $flag = $this->getFlag($item->article_id);
                $cdate = now()->format('Y-m-d');
    
    
                $price = DB::table('article_price')
                    ->where('article_price.sku_code_c', $item->sku_code_c)
                    ->where('valid_from', '<=', $cdate)
                    ->where('valid_to', '>=', $cdate)
                    ->orderBy('valid_from', 'desc')
                    ->select('article_price.amount')
                    ->first();
                    
                $productItem = [
                    'sku_code_c' => $item->sku_code_c,
                    'product_name_c' => $item->product_name_c,
                    "image_url" => @$image->file_path == null ? null : env('S3_STREAM_URL') . $image->file_path,
                    'flag' => $flag,
                    'base_price' => $article->price->amount,
                    'sub_total' => 0,
                    'customs' => [],
                    'product_items' => $product_items
                ];
                if (in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru])) {
                    $productItem['base_price'] = $price->amount;
                }
                array_push($items, $productItem);
                $total_qty += $item->qty;
            }
    
        }else{
            $total_kustomisasi = 0;
            $sub_total = 0;
            // return 'disini';
            // $customerSapId = Customer::where('customer_id', $order->customer_id)->first()->sap_id;
            // if($customerSapId != null){
            //     $dummy_customer_id = $customerSapId;
            // }
            // $order_group->each(function ($order) use ($dummy_customer_id) {

            //     $job = new SimulateSOQueue( $dummy_customer_id,  $order->order_no, 's' ,true);
            //     $job->handle();
            // });
            foreach ($orderItems->groupBy('attachment_group_id') as $groupId => $groupItems) {
                $firstItem = $groupItems->first(); // get one item to pull shared info
            
                $image = DB::table('article as art')
                    ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                    ->where('art.article', $firstItem->article_id)
                    ->where('ig.is_main_image', 1)
                    ->select('ig.file_path')
                    ->first();
            
                $article = Product::where('article', $firstItem->article_id)->first();
                $articleStock = ProductSku::where('sku_id', $article->article) ->pluck('stock')->first();

                $warna = DB::table('master_color')
                    ->where('key', $firstItem->product_variant_c)
                    ->where('is_active', 1)
                    ->pluck('value')->first();
            
                $ocs = OrderCustom::where('reference_id', $firstItem->reference_id)
                    ->where('sku', $article->sku_code_c)
                    ->where('attachment_group_id', $groupId)
                    ->pluck('attachment_group_id')->toArray();
            
                $custom_price = OrderCustomAttachment::whereIn('order_custom_id', $ocs)->sum('custom_price');
            
                $product_items = [];
                $totalSku = 0;
                foreach ($groupItems->groupBy('id') as $itemGroup) {
                    $item = $itemGroup->first();
                    $itemData = new TransactionItem();
                    $itemData->article = $item->article_id;
                    $itemData->product_name = $item->product_name_c;
                    $itemData->product_variant = $warna ?? null;
                    $itemData->product_size = $item->product_size_c;
                    $itemData->qty = $item->qty;
                    $itemData->stock = $articleStock;
                    $itemData->sub_total += (int)((int)$item->qty * (int)$article->price->amount);
                    $sub_total += (int)((int)$item->qty * (int)$article->price->amount);
                    $product_items[] = $itemData;
                    $total_qty += (int)$item->qty;
                }
            
                $flag = $this->getFlag($firstItem->article_id);
                $cdate = now()->format('Y-m-d');
            
                $price = DB::table('article_price')
                    ->where('sku_code_c', $firstItem->sku_code_c)
                    ->where('valid_from', '<=', $cdate)
                    ->where('valid_to', '>=', $cdate)
                    ->orderBy('valid_from', 'desc')
                    ->select('amount')
                    ->first();
            
                $ocas = OrderCustomAttachment::whereIn('order_custom_id', $ocs)->get();
                // return $ocas;
                $customs = [];
                $cartAttachments = [];
                foreach ($ocas as $oca) {
                    $oc = OrderCustom::where('attachment_group_id', $oca->order_custom_id)->first();
                    $articles = Product::where('sku_code_c', $oc->sku)->pluck('article')->toArray();
                    $customs[] = [
                        'harga_satuan' => (int)$oca->custom_price   
                    ];

                    $cartAttachments[] = [
                        'id' => $oca->id,
                        'attachments_group_id' => $oca->order_custom_id,
                        'file_path' => $oca->file_path,
                        'text' => $oca->custom_text,
                        'estimate_price' => (int)$oca->custom_price
                    ];
                }
            
                $productItem = [
                    'sku_code_c' => $firstItem->sku_code_c,
                    'attachment_group_id' => $groupId,
                    'product_name_c' => $firstItem->product_name_c,
                    'image_url' => @$image->file_path ? env('S3_STREAM_URL') . $image->file_path : null,
                    'flag' => $flag,
                    'base_price' => $article->price->amount,
                    'sub_total' => (int)$orders->sum('total_nett'),
                    'customs' => $customs,
                    'remark' => $remark[$groupId][0]->remark ?? '-',
                    'cart_attachments' => $cartAttachments,
                    'product_items' => $product_items
                ];
            
                if (in_array($order->order_status, [
                    self::OrderStatusPending,
                    self::OrderStatusOnHold,
                    self::OrderStatusWait,
                    self::OrderStatusBaru
                ])) {
                    $productItem['base_price'] = $price->amount;
                }
            
                $items[] = $productItem;
                $total_kustomisasi += (int)$custom_price * (int)$groupItems->sum('qty');
            }
            
        }
    //    return $items;
        // return $items;
      
        $allItems[] = $items; 

        $groupedItems = [];

        if($is_custom == false){
            foreach ($allItems as $item) {
                foreach ($item as $subitem) {
                    if (!isset($groupedItems[$subitem['sku_code_c']])) {
                        $groupedItems[$subitem['sku_code_c']] = [
                            'sku_code_c' => $subitem['sku_code_c'],
                            'product_name_c' => $subitem['product_name_c'],
                            "image_url" => $subitem['image_url'],
                            'flag' => $subitem['flag'],
                            'base_price' => $subitem['base_price'],
                            'sub_total' => 0,
                            'customs' => $subitem['customs'],
                            'product_items' => []
                        ];
                    }
                    $groupedItems[$subitem['sku_code_c']]['product_items'] = array_merge($groupedItems[$subitem['sku_code_c']]['product_items'], $subitem['product_items']);
                }
            }
        }else{
            foreach ($allItems as $itemGroup) {
                foreach ($itemGroup as $subitem) {
                    // Create a unique key using both sku_code_c and attachment_group_id
                    $groupKey = $subitem['sku_code_c'] . '|' . $subitem['attachment_group_id'];
            
                    if (!isset($groupedItems[$groupKey])) {
                        $groupedItems[$groupKey] = [
                            'sku_code_c' => $subitem['sku_code_c'],
                            'attachment_group_id' => $subitem['attachment_group_id'],
                            'image_url' => $subitem['image_url'],
                            'flag' => $subitem['flag'],
                            'base_price' => $subitem['base_price'],
                            'sub_total' => 0,
                            'customs' => $subitem['customs'],
                            'remark' => $subitem['remark'],
                            'cart_attachments' => $subitem['cart_attachments'],
                            'harga_custom' => collect($subitem['customs'])->sum('harga_satuan'),
                            'product_items' => [],
                        ];
                    }
            
                    $groupedItems[$groupKey]['product_items'] = array_merge(
                        $groupedItems[$groupKey]['product_items'],
                        $subitem['product_items']
                    );
                }
            }

        $trx->total = $sub_total;
        }


        $article = collect($groupedItems)->pluck('product_items')->flatten(1)->pluck('article')->toArray();

        $trx->total_kustomisasi  = 0;
        foreach ($groupedItems as &$groupedItem) {
            foreach ($groupedItem['product_items'] as $productItem) {
                $groupedItem['sub_total'] += $productItem['sub_total'];
            }
            
            if($is_custom == true){
                $hargaCustom = $groupedItem['harga_custom'] * collect($groupedItem['product_items'])->sum('qty');
                $groupedItem['sub_total'] += $hargaCustom;
                $trx->total_kustomisasi += $hargaCustom;
            }
         
        }

        $groupedItems = array_values($groupedItems);
        $offset = ($page - 1) * $limit;
        $total = count($groupedItems);
        $groupedItems = array_slice($groupedItems, $offset, $limit);

        $trx->items = $groupedItems;
        $trx->company_name = auth()->user()->customer->instance_name;

        if ($order->customer->registered_sap_at != null) {
            $customerShipment = CustomerShipment::where('customer_id', $order->customer_id)->get();
            $data = [];
            foreach ($customerShipment as $cs) {
                if (!Str::isUuid($cs->customer_shipment_id)) {
                    $data[] = $cs->toArray();
                }
            }
            $trx->customer_shipment = $data;
        } else {
            $trx->customer_shipment = $order->customer->shipments()
                ->select('customer_shipment_id', 'customer_id', 'name', 'address', 'city', 'province', 'district', 'zip_code', 'shipment_type', 'phone_number')
                ->get();
        }

        $fisrt = ($page - 1) * $limit + 1;
        $last = min($page * $limit, count($groupedItems));

        $data = [
            'total_data' => $total,
            'size' => (int) $limit,
            'active_page' => (int) $page,
            'total_page' => ceil($total / $limit),
            'order_detail' => $trx,
        ];
        return $this->sendSuccess(null, $data);

    }

    public function detailCheckout(Request $request, $order_group_id){
        $limit = 12;
        $page = $request->input('page');
        if ($perPage = $request->query('limit')) {
            $limit = $perPage;
        }
        $order_group = Order::where('order_group_id', $order_group_id)->get('order_no');
        $order_group->each(function ($order) {
                $job = new SimulateSOQueue(auth()->user()->customer->customer_id,  $order->order_no);
                $job->handle();
        });
        
        $orders = Order::with('items:order_detail_id,order_no,article_id,is_custom,is_available,product_name,product_variant,product_size,price,sub_total,qty,issued_qty,primary_discount,additional_discount,total', 'customer_shipment')
        ->where('order_group_id', $order_group_id)->get();

        $hasOtherStatus = $orders->pluck('order_status')->contains(function ($status) {
            return $status !== 'Menunggu Konfirmasi' &&
            $status !== Self::OrderStatusOnHold &&
            $status !== Self::OrderStatusPending;
        });
        
        $is_confirm = false;
        $status = '';
        if($hasOtherStatus){
            $is_confirm = true;
            $status = $orders->first()->order_status;
        }

        $orderStatus = $orders->whereIn('order_status', [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait]);
       
        $orderItems = OrderItem::leftJoin('article', 'order_detail.article_id', '=', 'article.article')
        ->select('order_detail.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_variant_c', 'article.lvl4_description')
        ->whereIn('order_no', $orders->pluck('order_no')->toArray())->orderBy('order_detail.order_detail_id', 'desc')
        ->get();

        $imageData = DB::table('article as art')
        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
        ->whereIn('art.article', $orderItems->pluck('article_id')->toArray())
        ->where('ig.is_main_image', 1)
        ->select('ig.file_path', 'art.article')
        ->get()->groupBy('article');
        
        $articleData = Product::whereIn('article', $orderItems->pluck('article_id')->toArray())->get()->groupBy('article');

        $dataWarna = DB::table('master_color')
        ->whereIn('key', $orderItems->pluck('product_variant_c')->toArray())
        ->where('is_active', 1)
        ->get()
        ->groupBy('key') // 'key' corresponds to 'product_variant_c'
        ->map(function ($group) {
            return $group->pluck('value');
        });
        
        $cdate = now()->format('Y-m-d');

        $priceData =  DB::table('article_price')
        ->where('article_price.sku_code_c', $orderItems->pluck('sku_code_c')->toArray())
        ->where('valid_from', '<=', $cdate)
        ->where('valid_to', '>=', $cdate)
        ->orderBy('valid_from', 'desc')
        ->select('article_price.amount', 'article_price.sku_code_c')
        ->get()->groupBy('sku_code_c');
        // return $article;
            
        // foreach ($orders as $order) {
        //     if ($order->items != null && count($order->items) > 0) {
        //         $article_map = array_map(function ($item) {
        //             return [
        //                 'source' => 'CAREOM',
        //                 'destination' => 'STK',
        //                 'article' => $item['article_id']
        //             ];
        //         }, $order->items->toArray());
        //         StockUpdateBatch::dispatch($article_map, auth()->user()->customer->customer_id);
        //     }
        // }
      
        $total_barang = $orderItems->sum('qty');
        $statusTrue = $orders->whereIn('order_status', [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru, self::OrderStatusCancel]);
       

 
        $total_discount = $orders->sum('total_discount');
        $order = $orders->first();

        $items = [];
        $allItems = [];
        $total_sub = 0;

        foreach ($orderItems as $item) {
            $image = collect($imageData[$item->article_id] ?? collect())->first();
          
        //   DB::table('article as art')
        //     ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
        //     ->where('art.article', )
        //     ->where('ig.is_main_image', 1)
        //     ->select('ig.file_path')
        //     ->first();

        //   $article = Product::where('article', $item->article_id)->first();
        $article = collect( $articleData[$item->article_id] ?? collect())->first();

        //   return $article;
        //   return  $article;

        //   $warna = DB::table('master_color')
        //     ->where('key', $item->product_variant_c)
        //     ->where('is_active', 1)
        //     ->pluck('value')->first();
          $warna = $dataWarna[$item->product_variant_c][0] ?? null;
        //   return "test lima";
          $product_items = [];
          $total_sub += (int) ($item->qty * ($article?->price?->amount ?? 0));
          $itemData = new TransactionItem();
          $itemData->article = $item->article_id;
          $itemData->product_name = $item->product_name;
          $itemData->product_variant = $warna ?? null;
          $itemData->product_size = $item->product_size;
          $itemData->issued_qty = $item->issued_qty;
          $itemData->qty = $item->qty;
          $itemData->sub_total = (int)($item->qty * ($article?->price ?$article->price->amount : 0));
          array_push($product_items, $itemData);
        //   $flag = $this->getFlag($item->article_id);
          



        //   $price =  DB::table('article_price')
        //     ->where('article_price.sku_code_c', $item->sku_code_c)
        //     ->where('valid_from', '<=', $cdate)
        //     ->where('valid_to', '>=', $cdate)
        //     ->orderBy('valid_from', 'desc')
        //     ->select('article_price.amount')
        //     ->first();
        $price =   isset($priceData[$item->sku_code_c])
        ? $priceData[$item->sku_code_c]->first()
        : null;
        //   return $article->price;

          $productItem = [
            'sku_code_c' => $item->sku_code_c,
            'product_name_c' => $item->product_name_c,
            "image_url" => @$image->file_path == null ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL') . $image->file_path,
            // 'flag' => $flag,
            'flag' =>[ $item->lvl4_description],
            'product_category' => $item->lvl4_description,
            'base_price' => $item->price,
            'sub_total' =>  0,
            'customs' => [],
            'product_items' => $product_items
          ];
          if (in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru])) {
            $productItem['base_price'] = $price ? $price->amount : 0;
          }
          array_push($items, $productItem);
        }

        $allItems[] = $items;

        $groupedItems = [];
        foreach ($allItems as $item) {
            foreach ($item as $subitem) {
            if (!isset($groupedItems[$subitem['sku_code_c']])) {
                $groupedItems[$subitem['sku_code_c']] = [
                'sku_code_c' => $subitem['sku_code_c'],
                'product_name_c' => $subitem['product_name_c'],
                "image_url" =>   $subitem['image_url'],
                'flag' => $subitem['flag'],
                'product_category' => $subitem['product_category'],
                'base_price' => $subitem['base_price'],
                'sub_total' => 0,
                'customs' => $subitem['customs'],
                'product_items' => []
                ];
            }
            $groupedItems[$subitem['sku_code_c']]['product_items'] = array_merge($groupedItems[$subitem['sku_code_c']]['product_items'], $subitem['product_items']);
            }
        }
  
        foreach ($groupedItems as &$groupedItem) {
            foreach ($groupedItem['product_items'] as $productItem) {
            $groupedItem['sub_total'] += $productItem['sub_total'];
            }
        }

        $groupedItems = array_values($groupedItems);
        $offset = ($page - 1) * $limit;
        $total = count($groupedItems);
        $groupedItems = array_slice($groupedItems, $offset, $limit);

        $location_code = $order->location_code ?? null;
        $location_name = $order->location_name ?? null;
        $data = [
            "order_group_id" => $order_group_id,
            "payment_date" => "",
            "sap_response" => null,
            "is_confirm" => $is_confirm,
            "order_no" => $orders->pluck('order_no')->toArray(),
            "customer_shipment_id" => $order->customer_shipment->customer_shipment_id,
            "sub_total" => $total_sub,
            "total_discount" => $total_discount,
            "total_nett" =>  $total_sub -  $total_discount,
            "total_dp" => $orders->sum('dp_amount'),
            "customer_id" => $order->customer_shipment->customer_id,
            "sales_order_no" => $order->sales_order_no,
            "status" => self::OrderStatusWait,
            "order_summary" => "Belum Lunas",
            "delivery_order_no" => "",
            "transaction_date" => $order->created_date,
            // $trx->so_date = $order->created_date;
            "do_date" => "",
            "gi_date" => "",
            "shipping_date" => $order->shipping_date,
            "received_date" => $order->completed_date,
            "invoice_no" => "",
            "no_resi" => $order->awb_no ?? "",
            "bill_to" => $order->bill_to,
            "bill_to_address" => $order->bill_to_address,
            "bill_to_phone_number" => $order->bill_to_phone_number,
            "bill_to_email" => $order->bill_to_email,
            "ship_to" => $order->ship_to,
            "ship_to_address" => $order->ship_to_address,
            "ship_to_phone_number" => $order->ship_to_phone_number,
            "distribution_channel" => $order->distribution_channel,
            "total_barang" => $total_barang,
            "transportation_zone" => $location_code . ' - ' . $location_name,
            "pesanan_dibatalkan_date" => $order->status == 'Batal' ? $order->modified_date : null,
            "items" => $groupedItems,
            "customer_shipment" => $order->customer->shipments()->select('customer_shipment_id', 'customer_id', 'name', 'address', 'city', 'province', 'district', 'zip_code', 'shipment_type', 'phone_number')->get()
        ];

        $fisrt = ($page - 1) * $limit + 1;
        $last = min($page * $limit, count($groupedItems));

        $result = [
        'total_data' => $total,
        'size' => (int)$limit,
        'active_page' => (int)$page,
        'total_page' => ceil($total / $limit),
        'order_detail' => $data,
    ];  
    
        return $this->sendSuccess(null, $result);
    
    }
    // kodingan production buat wholesales, ke getTransactionDetailB2B untuk B2B
    public function getTransactionDetail(Request $request, $order_no)
    {
    // $order_customer = DB::table('order_header')
    // ->where('order_header.order_no', $order_no)
    // ->select('order_header.customer_id', 'order_header.customer_shipment_id')
    // ->first();

    // $simulate = $this->createSimulateSO("s", $order_no, $order_customer->customer_id);

    $trx = new Transaction();
    $allItems = [];
    $limit = 12;
    $page = $request->input('page');
    if ($perPage = $request->query('limit')) {
      $limit = $perPage;
    }

    //new
    $custId = auth()->user()->customer->customer_id;
    $order = Order::with('items:order_detail_id,order_no,article_id,is_custom,product_name,product_variant,product_size,price,sub_total,qty,issued_qty,primary_discount,additional_discount,total')
    ->where('order_no', $order_no)->first();
    $totalHarga = $order->total == 0 ? $order->items->sum('total') : $order->total;
    $trx->order_group_id = $order->order_group_id ? $order->order_group_id : null;
    //old
    // $order = Order::with('items')->where('order_no', $order_no)->first();
    $invoice_data = DB::table('invoice')
      ->where('invoice.order_no', $order_no)
      ->where('invoice.status', 'LUNAS')
      ->select('invoice.modified_date', 'invoice.status')
      ->first();

    if ($order == null) {
      return $this->sendError("Data transaction not found", 404);
    }

    // if (in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait])) {
    //     if ($order->items != null && count($order->items) > 0) {
    //     $article_map = array_map(function ($b) {
    //       return [
    //         'source' => 'CAREOM',
    //         'destination' => 'STK',
    //         'article' => $b['article_id']
    //       ];
    //     }, $order->items->toArray());
    //     StockUpdateBatch::dispatch($article_map, auth()->user()->customer->customer_id);
    //   }
    // }

    $total_sub = 0;
    $total_all = 0;
    if ($order->distribution_channel == 'B2B') {
        return $this->getTransactionDetailB2B($request,$order_no);
    }

    $allOrderItems = OrderItem::where('order_no', $order->order_no);
    $unavailableItems = $allOrderItems->sum('qty') - $allOrderItems->sum('issued_qty');
    // $jumlahBarang = OrderItem::available(true)->where('order_no', $order->order_no)->orderBy('order_detail.order_detail_id', 'desc')->get();
    // $dataLimit = $this->getLimitCustomer();

    $DeliveryOrder = DB::table('delivery_order')
      ->leftJoin('order_header', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
      ->leftJoin('invoice', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
      ->leftJoin('proforma_invoice', 'order_header.sales_order_no', '=', 'proforma_invoice.sales_order_no')
      ->where('delivery_order.sales_order_no', $order->sales_order_no)
      ->orderBy('delivery_order.created_date','desc')
      ->select('delivery_order.*', 'invoice.invoice_no', 'proforma_invoice.id as pi_id', 'proforma_invoice.created_date as payment_date')
      ->first();

    if (!in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru, self::OrderStatusCancel]) && $DeliveryOrder)
    {
    
      $DeliveryOrderDetail = DeliveryOrderDetail::leftJoin('article as p', 'p.article', '=', 'd.article')
        ->from('delivery_order_detail as d')
        ->select('d.*', 'p.sku_code_c', 'p.product_name_c', 'p.article', 'p.product_variant_c')
        ->when($request->is_available == 'false', function ($query) {
            $query->whereNotNull('d.issued_qty')
            ->whereColumn('d.qty', '>', 'd.issued_qty');
        })
        ->where('d.delivery_order_no', $DeliveryOrder->delivery_order_no ?? "")
        ->orderBy('d.delivery_order_detail_id', 'desc')
        ->get();

        // $totalHarga = $DeliveryOrder->total;
        $tokoType = auth()->user()->customer->top_days;
        $isCash = in_array($tokoType, ['Cash', '0']);
        $isLunas = isset($invoice_data) && $invoice_data->status === 'LUNAS';
        
        if (!$isCash || $isLunas) {
            $trx->payment_date = $invoice_data->modified_date ?? "";
            $trx->gi_date = $DeliveryOrder->good_issue_date ?? "";
            $trx->status = $order->order_status;
        } else {
            $trx->payment_date = "";
            $trx->gi_date = "";
            $trx->status = $order->order_status == 'Dikirim' ? 'Pembayaran' : $order->order_status;
        }
        // $trx->status = $order->order_status;

        //   if (isset($invoice_data)) {
        //     $trx->payment_date = in_array($order->order_status,[self::OrderStatusWait, self::OrderStatusBaru,self::OrderStatusOnProcess]) ? "" : $invoice_data->modified_date;
        //   } else {
        //     $trx->payment_date = "";
        //   }

      $total_all =  (int)($DeliveryOrder->total ?? 0);
      $location_code = $order->location_code ?? null;
      $location_name = $order->location_name ?? null;
      $trx->order_no = $order->order_no;
      $trx->customer_shipment_id = isset($DeliveryOrder->customer_shipment_id) ? (int)$DeliveryOrder->customer_shipment_id : (int)$order->customer_shipment_id;
      $trx->sub_total = (int)$order->sub_total;
      // $trx->total_discount = $order->order_status === self::OrderStatusBaru ? (int)($order->total_discount??0) : (int)($DeliveryOrder->discount??0);
      $trx->total_discount = in_array($order->order_status, ['Baru', 'Menunggu Verifikasi', 'Menunggu Konfirmasi']) ? (int)$order->total_discount : ($DeliveryOrder != null ? (int)$DeliveryOrder->discount : 0);
      $trx->total_discount_pesanan_percentage = in_array($order->order_status, ['Baru', 'Menunggu Verifikasi', 'Menunggu Konfirmasi']) ? ($order->total_discount != 0 ? ($order->total_discount ?? 0) / ($order->total / 100) : 0) : ($DeliveryOrder != null ? ($DeliveryOrder->discount != 0 ? ($DeliveryOrder->discount ?? 0) / ($order->total / 100) : 0) : 0);
      $trx->sales_order_no = $DeliveryOrder->sales_order_no ?? "";
      $trx->total = (int)($DeliveryOrder->total ?? 0);
      $trx->total_dp = (int)$order->dp_amount;
      $trx->dp_percentage = $order->dp_percentage;
      $trx->customer_id = $DeliveryOrder->customer_id ?? "";
    //   $trx->status = $order->order_status;
      $trx->delivery_order_no = $DeliveryOrder->delivery_order_no ?? "";
      $trx->transaction_date = $order->created_date;
      $trx->do_date = $DeliveryOrder->created_date ?? "";
    //   $trx->gi_date = $DeliveryOrder->good_issue_date ?? "";
      $trx->shipping_date = $order->shipping_date ?? "";
      $trx->received_date = $order->completed_date ?? "";
      $trx->invoice_no = $DeliveryOrder->invoice_no ?? "";
      $trx->proforma_invoice_no = $DeliveryOrder->pi_id ?? "";
      $trx->order_summary = $invoice_data ? $invoice_data->status : ($order->payment_status ?? "Belum Lunas");
      $trx->no_resi = $order->awb_no ?? "";
      $trx->bill_to = $order->bill_to;
      $trx->bill_to_address = $order->bill_to_address;
      $trx->bill_to_phone_number = $order->bill_to_phone_number;
      $trx->bill_to_email = $order->bill_to_email;
      $trx->ship_to = $order->ship_to;
      $trx->ship_to_address = $order->ship_to_address;
      $trx->ship_to_phone_number = $order->ship_to_phone_number;
      $trx->distribution_channel = $order->distribution_channel;
      $trx->verifikasi_date = OrderApproval::where('order_no', $order->order_no)->where('status', 'Approved')->pluck('action_date')->first();
      $trx->pesanan_dibatalkan_date = $order->status == 'Batal' ? $order->modified_date : null;
      $trx->transportation_zone = $location_code . ' - ' . $location_name;
      $trx->total_barang = $DeliveryOrderDetail->sum('qty');

      $items = [];

      $trx->data_limit = [];

      foreach ($DeliveryOrderDetail as $item) {
            $data_order_detail = DB::table('order_detail')
            ->leftJoin('order_header', 'order_header.order_no', '=', 'order_detail.order_no')
            ->where('order_header.sales_order_no', $order->sales_order_no)
            ->where('order_detail.article_id', $item->article)
            ->select('order_detail.price', 'order_detail.issued_qty')
            ->first();
            $article = Product::where('article', $item->article)->first();
            $image = DB::table('article as art')
            ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
            ->where('art.article', $item->article)
            ->where('ig.is_main_image', 1)
            ->select('ig.file_path')
            ->first();
            $warna = DB::table('master_color')
            ->where('key', $item->product_variant_c)
            ->where('is_active', 1)
            ->pluck('value')->first();

            $total_sub += (int)($item->qty * ($article->price ?$article->price->amount : 0));
            $product_items = [];
            $sub_total_price = 0;
            $itemData = new TransactionItem();
            $itemData->article = $item->article;
            // $itemData->image_url = ($image['url'] ??"");
            // $itemData->article = $item->article;
            // $trx->unavailable_items = $data_order_detail->sum('qty') -  $data_order_detail->sum('issued_qty');
            $problem_qty = $item->qty -  $data_order_detail->issued_qty;
            $itemData->product_name = $item->product_name;
            $itemData->product_variant = $warna ?? null;
            $itemData->product_size = $item->product_size;
            $itemData->qty = $request->is_available == 'false' ? $problem_qty  : $item->qty;
            $itemData->issued_qty = $item->issued_qty;
            $itemData->merchandise_category = $item->merchandise_category;
            $itemData->sub_total = (int)(($request->is_available == 'false' ? $problem_qty : $item->qty) * $article->price->amount);
            $itemData->location = $item->location;
            array_push($product_items, $itemData);

            $flag = $this->getFlag($item->article);

            $productItem = [
            'sku_code_c' => $item->sku_code_c,
            'product_name_c' => $item->product_name_c,
            "image_url" => @$image->file_path == null ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL') . $image->file_path,
            'flag' => $flag,
            'product_category' => $article ? $article->lvl4_description : '-',
            'base_price' => $data_order_detail->price ?: 0,
            'sub_total' => 0,
            'customs' => [],
            'product_items' => $product_items
            ];
            array_push($items, $productItem);
      }
      $allItems[] = $items;
    } else {
      if ($order->order_status == self::OrderStatusWait) {
        SimulateSOQueue::dispatch(auth()->user()->customer->customer_id, $order_no);
      }

    //   $order = Order::where('order_no', $order_no)->first();
    //   $totalHarga = $order->total;
      $data = DB::table('delivery_order')
        ->leftJoin('order_header', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
        ->leftJoin('invoice', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
        ->leftJoin('proforma_invoice', 'order_header.sales_order_no', '=', 'proforma_invoice.sales_order_no')
        ->where('delivery_order.sales_order_no', $order->sales_order_no)
        ->select('delivery_order.*', 'invoice.invoice_no', 'proforma_invoice.id as pi_id', 'proforma_invoice.created_date as payment_date')
        ->first();

      if (isset($invoice_data)) {
        $trx->payment_date = in_array($order->order_status,[self::OrderStatusWait, self::OrderStatusBaru,self::OrderStatusOnProcess]) ? "" : $invoice_data->modified_date;
      } else {
        $trx->payment_date = "";
      }

      $location_code = $order->location_code ?? null;
      $location_name = $order->location_name ?? null;
      $trx->sap_response = null;
      $trx->order_no = $order->order_no;
      $trx->customer_shipment_id = $order ? $order->customer_shipment_id : null;
      $trx->sub_total = (int)$order->sub_total;
      $trx->total_discount = (int)$order->total_discount;
      $trx->total_discount_pesanan_percentage = $order->total_discount != 0 ? ($order->total_discount ?? 0) / ($order->total / 100) : 0;
    //  $trx->total = (int)$order->total;
      $trx->total_nett = (int)$order->total_nett - (int)$order->total_discount;
      $trx->total_dp = (int)$order->dp_amount;
      $trx->dp_percentage = $order->dp_percentage;
      $trx->customer_id = (int)$order->customer_id;
      $trx->sales_order_no = $order->sales_order_no ?? "";
      $trx->status = $order->order_status;
      $trx->delivery_order_no = $order->delivery_no ?? "";
      $trx->transaction_date = $order->created_date;
      // $trx->so_date = $order->created_date;
      $trx->do_date =  "";
      $trx->gi_date = "";
      $trx->shipping_date = $order->shipping_date ?? "";
      $trx->received_date = $order->completed_date ?? "";
      $trx->invoice_no = $data->invoice_no ?? "";
      $trx->proforma_invoice_no = $data->pi_id ?? "";
      $trx->order_summary = $order->payment_status ?? "Belum Lunas";
      $trx->no_resi = $order->awb_no ?? "";
      $trx->bill_to = $order->bill_to;
      $trx->bill_to_address = $order->bill_to_address;
      $trx->bill_to_phone_number = $order->bill_to_phone_number;
      $trx->bill_to_email = $order->bill_to_email;
      $trx->ship_to = $order->ship_to;
      $trx->ship_to_address = $order->ship_to_address;
      $trx->ship_to_phone_number = $order->ship_to_phone_number;
      $trx->distribution_channel = $order->distribution_channel;
      $trx->verifikasi_date = OrderApproval::where('order_no', $order->order_no)->where('status', 'Approved')->pluck('action_date')->first();
      $trx->transportation_zone = $location_code . ' - ' . $location_name;
      $trx->pesanan_dibatalkan_date = $order->status == 'Batal' ? $order->modified_date : null;
      $trx->total_barang = $allOrderItems->sum('qty') - $unavailableItems;
      $trx->data_limit =  [];

      //get order detail
      $orderItems = OrderItem::available($request->is_available == 'false' ? false : true)->leftJoin('article', 'order_detail.article_id', '=', 'article.article')
        ->select('order_detail.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_variant_c')
        ->where('order_no', $order->order_no)->orderBy('order_detail.order_detail_id', 'desc')
        ->get();
        
      
    //   $trx->unavailable_items = $jumlahBarang->sum('qty') -  $jumlahBarang->sum('issued_qty');
      $items = [];
      $total_sub = 0;

      $articleIds = $orderItems->pluck('article_id')->unique();

        $images = DB::table('article as art')
        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
        ->whereIn('art.article', $articleIds)
        ->where('ig.is_main_image', 1)
        ->select('art.article', 'ig.file_path')
        ->get()
        ->keyBy('article'); // So you can access by article ID

        $articles = Product::whereIn('article', $articleIds)
        ->get()
        ->keyBy('article');
      
        $productVariantCs = $orderItems->pluck('product_variant_c')->unique();

        $colors = DB::table('master_color')
        ->whereIn('key', $productVariantCs)
        ->where('is_active', 1)
        ->pluck('value', 'key'); 
        $skuCodeCs = $orderItems->pluck('sku_code_c')->unique();

        $cdate = now()->format('Y-m-d');
        $prices = DB::table('article_price')
            ->whereIn('sku_code_c', $skuCodeCs)
            ->where('valid_from', '<=', $cdate)
            ->where('valid_to', '>=', $cdate)
            ->orderBy('valid_from', 'desc')
            ->get()
            ->groupBy('sku_code_c')
            ->map(function ($priceGroup) {
                return $priceGroup->first(); // Get the latest by valid_from DESC
            });

        foreach ($orderItems as $item) {
            $image = $images[$item->article_id] ?? null;
            $article = $articles[$item->article_id] ?? null;
            $warna = $colors[$item->product_variant_c] ?? null;

          $problem_qty = $item->qty - $item->issued_qty;
          $product_items = [];
          $total_sub += (int)($item->qty * ($article->price ?$article->price->amount : 0));
          $itemData = new TransactionItem();
          $itemData->article = $item->article_id;
          $itemData->product_name = $item->product_name;
          $itemData->product_variant = $warna ?? null;
          $itemData->product_size = $item->product_size;
          $itemData->issued_qty = $item->issued_qty;
          $itemData->qty = $request->is_available == 'false' ? $problem_qty : $item->issued_qty;
          $itemData->sub_total = (int)(($request->is_available == 'false' ? $problem_qty : $item->issued_qty) * ($article->price ?$article->price->amount : 0));
          array_push($product_items, $itemData);
        //   $flag = $this->getFlag($item->article_id);
        
        //   $total_all = $order->total;

          $price = $prices[$item->sku_code_c];
          $productItem = [
            'sku_code_c' => $item->sku_code_c,
            'product_name_c' => $item->product_name_c,
            "image_url" => @$image->file_path == null ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL') . $image->file_path,
            'flag' => '-',
            'product_category' => $article ? $article->lvl4_description : '-',
            'base_price' => $item->price,
            'sub_total' => 0,
            // 'total_barang' => 0,
            'customs' => [],
            'product_items' => $product_items
          ];
          if (in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru])) {
            $productItem['base_price'] = $price ? $price->amount : 0;
          }
          array_push($items, $productItem);
        }
        $total_all = $totalHarga;
      $allItems[] = $items;
    }

    //grouping array
    $groupedItems = [];
    foreach ($allItems as $item) {
      foreach ($item as $subitem) {
        if (!isset($groupedItems[$subitem['sku_code_c']])) {
          $groupedItems[$subitem['sku_code_c']] = [
            'sku_code_c' => $subitem['sku_code_c'],
            'product_name_c' => $subitem['product_name_c'],
            "image_url" =>   $subitem['image_url'],
            'flag' => $subitem['flag'],
            'product_category' => $subitem['product_category'],
            'base_price' => $subitem['base_price'],
            'sub_total' => 0,
            // 'total_barang' => 0,
            'customs' => $subitem['customs'],
            'product_items' => []
          ];
        }
        $groupedItems[$subitem['sku_code_c']]['product_items'] = array_merge($groupedItems[$subitem['sku_code_c']]['product_items'], $subitem['product_items']);
      }
    }

    //count sub total per sku
    foreach ($groupedItems as &$groupedItem) {
      foreach ($groupedItem['product_items'] as $productItem) {
        // $problem_qty = $productItem['qty'] - $productItem['issued_qty'];
        $groupedItem['sub_total'] += $productItem['sub_total'];
        // $groupedItem['total_barang'] += $productItem['qty'];
      }
    }

    $groupedItems = array_values($groupedItems);
    $offset = ($page - 1) * $limit;
    $total = count($groupedItems);
    $groupedItems = array_slice($groupedItems, $offset, $limit);
    $trx->unavailable_items = $unavailableItems;
    // $trx->sub_total = $total_sub;
    $trx->sub_total = $totalHarga;
    
    // $trx->sub_nett = $total_sub;
    $trx->sub_nett = $totalHarga;
    $trx->total = (int) $total_all;
    $trx->total_nett = (int) $trx->sub_total - (int) $trx->total_discount;
    $trx->items = $groupedItems;

    // $ship = new CustomerShipment();
    // $customerShipment = CustomerShipment::where('customer_shipment_id', $order->customer_shipment_id)->first();
    // if($customerShipment)
    // {
    //     $ship->customer_shipment_id = (int)$customerShipment->customer_shipment_id; 
    //     $ship->customer_id = (int)$customerShipment->customer_id;
    //     $ship->store = $customerShipment->name;
    //     $ship->name = $customerShipment->name;
    //     $ship->address = $customerShipment->address;
    //     $ship->city = $customerShipment->city;
    //     $ship->province = $customerShipment->province;
    //     $ship->district = $customerShipment->district;
    //     $ship->zip_code = $customerShipment->zip_code;
    //     $ship->shipment_type = $customerShipment->shipment_type;
    //     $ship->phone_number = $customerShipment->phone_number;
    //     $trx->customer_shipment = $ship;
    // }
    
    $trx->customer_shipment = CustomerShipment::where('customer_id', $custId)->get();

    $fisrt = ($page - 1) * $limit + 1;
    $last = min($page * $limit, count($groupedItems));
    // $simulate = $this->createSimulateSO("s", $order_no, $trx->customer_id);

    $data = [
      // 'pagination' => [
      //     'total' => $total,
      //     'per_page' => (int)$perPage,
      //     'current_page' => (int)$page,
      //     'last_page' => ceil($total/$perPage),
      //     'from' => $fisrt,
      //     'to' => $last
      // ],
      'total_data' => $total,
      'size' => (int)$limit,
      'active_page' => (int)$page,
      'total_page' => ceil($total / $limit),
      'order_detail' => $trx,
    ];
    //Log::channel('stderr')->info($trx);
    return $this->sendSuccess(null, $data);
    }


    public function getTransactionDetailB2B(Request $request, $order_no)
    {
        // $order_customer = DB::table('order_header')
        // ->where('order_header.order_no', $order_no)
        // ->select('order_header.customer_id', 'order_header.customer_shipment_id')
        // ->first();
        
        // $simulate = $this->createSimulateSO("s", $order_no, $order_customer->customer_id);

        $trx = new Transaction();
        $allItems= [];
        $limit=12;
        $page = $request->input('page');
        if($perPage = $request->query('limit')){
                $limit = $perPage;
        }

        $order = Order::with('items:order_detail_id,order_no,article_id,is_custom,product_name,product_variant,product_size,price,sub_total,qty,issued_qty,primary_discount,additional_discount,total')
        ->where('order_no', $order_no)->first();

        $data_dp = Invoice::where('sales_order_no',$order->sales_order_no)->where('invoice_type','DOWN PAYMENT')->first();
        $data_bill = Invoice::where('sales_order_no',$order->sales_order_no)->where('invoice_type','BILLING')->first();

        $oc_exists = OrderCustom::where('reference_id',$order->order_no)->exists();

        $invoice_data = DB::table('invoice')
        ->where('invoice.sales_order_no', $order->sales_order_no)
        ->where('invoice.invoice_type','BILLING')
        ->where('invoice.status', 'LUNAS')
        ->select('invoice.modified_date')
        ->first();

        if ($order == null) {
            return $this->sendError("Data transaction not found", 404);
        } 

        if (($order->dp_amount > 0 && !in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru, self::OrderStatusCancel, self::OrderStatusVerif, self::OrderStatusPembayaran]))
            || ($order->dp_amount == 0 && !in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru, self::OrderStatusCancel, self::OrderStatusVerif]))) {
            
            $DeliveryOrder = DB::table('delivery_order')
            ->leftJoin('order_header', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
            ->leftJoin('invoice', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
            ->leftJoin('proforma_inv oice', 'order_header.sales_order_no', '=', 'proforma_invoice.sales_order_no')
            ->where('delivery_order.sales_order_no', $order->sales_order_no)
            ->select('delivery_order.*', 'invoice.invoice_no', 'proforma_invoice.created_date as payment_date')
            ->first();

            $DeliveryOrderDetail = DeliveryOrderDetail::leftJoin('article as p', 'p.article', '=', 'd.article')
                                            ->from('delivery_order_detail as d')
                                            ->select('d.*', 'p.sku_code_c', 'p.product_name_c', 'p.article', 'p.product_variant_c' )
                                            ->where('d.delivery_order_no', $DeliveryOrder->delivery_order_no??"")
                                            ->orderBy('d.delivery_order_detail_id', 'desc')
                                            ->get();
            if(isset($invoice_data)){
                $trx->payment_date = in_array($order->order_status,[self::OrderStatusWait, self::OrderStatusBaru,self::OrderStatusOnProcess,self::OrderStatusVerif]) ? : $invoice_data->modified_date;
            }else{
                $trx->payment_date = "";
            }

            $total = 0;
            if ($DeliveryOrder != null) {
                foreach ($DeliveryOrderDetail as $item) {
                    $article = Product::where('article',$item->article)->first();
                    $od = OrderItem::where('order_no',$order_no)->where('article_id',$item->article)->first();
                    $custom_price = 0;
                    if ($oc_exists == true) {
                        $ocs = OrderCustom::where('reference_id',$order->order_no)->where('sku',$article->sku_code_c)->pluck('id')->toArray();
                        $custom_price = OrderCustomAttachment::where('order_custom_id',$ocs)->sum('custom_price');
                    }
                    
                    $qty = $DeliveryOrder->good_issue_date != null ? $item->issued_qty : $item->qty;
                    $total += ($od->price+$custom_price)*$qty;
                    Log::info('itungan detail',['article_price' => $article->price->amount,'custom_price' => $custom_price,'qty' => $qty,'total' => $total]);
                }
            }
            $discount = $DeliveryOrder != null && $DeliveryOrder->good_issue_date != null ? $DeliveryOrder->issued_discount : ( $DeliveryOrder != null && $DeliveryOrder->good_issue_date == null ? $DeliveryOrder->discount : 0 ) ;
            $discount_percentage = $discount == 0 ? 0 : ($discount/$total)*100;
            $payment_dp_date = $order->dp_amount > 0 ? (DB::table('down_payment_request')->where('sales_order_no',$order->sales_order_no)->where('status','PAID')->pluck('modified_date')->first()??null) : null;

            $location_code = $order->location_code??null;
            $location_name = $order->location_name??null;
            $trx->order_no = $order->order_no;
            $trx->customer_shipment_id = isset($DeliveryOrder->customer_shipment_id) ? (int)$DeliveryOrder->customer_shipment_id : (int)$order->customer_shipment_id;
            $trx->sub_total = (int)$total - (int)$discount;
            // $trx->total_discount = $order->order_status === self::OrderStatusBaru ? (int)($order->total_discount??0) : (int)($DeliveryOrder->discount??0);
            $trx->total_discount = (int)$discount;
            $trx->total_discount_pesanan_percentage = $discount_percentage;
            $trx->sales_order_no = $DeliveryOrder->sales_order_no??"";
            $trx->total = (int)$total;
            $trx->total_dp = (int)$order->dp_amount;
            $trx->dp_percentage = $order->dp_percentage;
            $trx->customer_id = $DeliveryOrder->customer_id??"";
            // $trx->status = ($order->order_status == "Dikirim" || $order->order_status == 'Diproses') && empty($invoice_data) ? 'Pembayaran' : $order->order_status;
            $trx->status = $order->order_status;
            $trx->delivery_order_no = empty($invoice_data) ? "" : ($DeliveryOrder->delivery_order_no??"");
            $trx->transaction_date = $order->created_date;
            $trx->do_date = empty($invoice_data) ? "" : ($DeliveryOrder->created_date??"");
            $trx->gi_date = empty($invoice_data) ? "" : ($DeliveryOrder->good_issue_date??"");
            $trx->shipping_date = $order->shipping_date??"";
            $trx->received_date = $order->completed_date??"";
            $trx->invoice_no = $data_bill->invoice_no??null;
            $trx->invoice_no_dp = $data_dp->invoice_no??null;
            $trx->order_summary = $order->payment_status??"Belum Lunas";
            $trx->no_resi = $order->awb_no??"";
            $trx->bill_to = $order->bill_to;
            $trx->bill_to_address = $order->bill_to_address;
            $trx->bill_to_phone_number = $order->bill_to_phone_number;
            $trx->bill_to_email = $order->bill_to_email;
            $trx->ship_to = $order->ship_to;
            $trx->ship_to_address = $order->ship_to_address;
            $trx->ship_to_phone_number = $order->ship_to_phone_number;
            $trx->distribution_channel = $order->distribution_channel;
            $trx->verifikasi_date = OrderApproval::where('order_no',$order->order_no)->where('status','Approved')->pluck('action_date')->first();
            $trx->pesanan_dibatalkan_date = $order->status == 'Batal' ? $order->modified_date : null ;
            $trx->transportation_zone = $location_code.' - '.$location_name;
            $trx->payment_dp_date = $payment_dp_date;
            
            $orderCustom = Order::join('order_custom', 'order_custom.reference_id','=','order_header.order_no')
            ->select('order_header.order_no')
            ->get()->toArray();
            $customCheck = false;
            foreach ($orderCustom as $orderitems) {
                if ($orderitems['order_no'] === $order->order_no) {
                    $customCheck = true;
                    break;
                }
            }
            $trx->is_custom = $customCheck;
            
            $items = [];

            $trx->data_limit = [];

            $total_kustomisasi = 0;
            $total_qty = 0;

            if ($customCheck == true) {
                foreach($DeliveryOrderDetail as $item){
                    $data_order_detail = DB::table('order_detail')
                    ->leftJoin('order_header', 'order_header.order_no', '=', 'order_detail.order_no')
                    ->where('order_header.sales_order_no', $order->sales_order_no)
                    ->where('order_detail.article_id', $item->article)
                    ->select('order_detail.price')
                    ->first();
                    $article = Product::where('article',$item->article)->first();
                    $ocs = OrderCustom::where('reference_id',$order->order_no)
                            ->where('sku',$article->sku_code_c)
                            ->pluck('id')->toArray();
                    $custom_price = OrderCustomAttachment::whereIn('order_custom_id',$ocs)
                                                        ->sum('custom_price');
                        $image = DB::table('article as art')
                        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                        ->where('art.article', $item->article)
                        ->where('ig.is_main_image',1)
                        ->select('ig.file_path')
                        ->first();
                        $warna = DB::table('master_color')
                                ->where('key',$item->product_variant_c)
                                ->where('is_active',1)
                                ->pluck('value')->first();
    
                        $product_items = [];
                        $sub_total_price = 0;
                        $itemData = new TransactionItem();
                        $itemData->article = $item->article;
                        // $itemData->image_url = ($image['url'] ??"");
                        // $itemData->article = $item->article;
                        $itemData->product_name = $item->product_name;
                        $itemData->product_variant = $warna??null;
                        $itemData->product_size = $item->product_size;
                        $itemData->qty = $item->qty;
                        $itemData->issued_qty = $item->issued_qty;
                        $itemData->merchandise_category = $item->merchandise_category;
                        $itemData->sub_total = $DeliveryOrder->good_issue_date != null ? (int)($item->issued_qty*$data_order_detail->price) : (int)($item->qty*$data_order_detail->price);
                        $itemData->location = $item->location;   
                        array_push($product_items,$itemData);
                        
                        $flag= $this->getFlag($item->article);

                        $ocas = OrderCustomAttachment::whereIn('order_custom_id',$ocs)
                                                    ->get();
                        $customs = [];
                        foreach($ocas as $oca)
                        {
                            $oc = OrderCustom::where('id',$oca->order_custom_id)->first();
                            $articles = Product::where('sku_code_c',$oc->sku)->pluck('article')->toArray();
                            $qty = $DeliveryOrder->good_issue_date != null 
                                ? DeliveryOrderDetail::where('delivery_order_no',$DeliveryOrder->delivery_order_no)->sum('issued_qty')
                                : DeliveryOrderDetail::where('delivery_order_no',$DeliveryOrder->delivery_order_no)->sum('qty');
                            $customs[] = [
                                'deskripsi_kustomisasi' => 'BIAYA KUSTOMISASI - '.$oc->position_side.' - '.$oca->size.' - '.$oca->material,
                                'harga_satuan' => (int)$oca->custom_price,
                                'kuantiti' => (int)$qty,
                                'total_harga' => (int)($qty*$oca->custom_price)
                            ];
                        }
    
                        $productItem = [
                            'sku_code_c' => $item->sku_code_c,
                            'product_name_c' => $item->product_name_c,
                            "image_url" => @$image->file_path == null ? null : env('S3_STREAM_URL').$image->file_path,
                            'flag' =>$flag,
                            'base_price' => $data_order_detail->price ?: 0,
                            'sub_total' => 0,
                            'customs' => $customs,
                            'product_items' => $product_items
                        ];
                        array_push($items,$productItem);
                    $total_qty += $DeliveryOrder->good_issue_date != null ? $item->issued_qty : $item->qty;
                    $total_kustomisasi += $custom_price*($DeliveryOrder->good_issue_date != null ? $item->issued_qty : $item->qty);
                }
            } else {
                foreach($DeliveryOrderDetail as $item){
                    $data_order_detail = DB::table('order_detail')
                    ->leftJoin('order_header', 'order_header.order_no', '=', 'order_detail.order_no')
                    ->where('order_header.sales_order_no', $order->sales_order_no)
                    ->where('order_detail.article_id', $item->article)
                    ->select('order_detail.price')
                    ->first();
                    $article = Product::where('article',$item->article)->first();
                        $image = DB::table('article as art')
                        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                        ->where('art.article', $item->article)
                        ->where('ig.is_main_image',1)
                        ->select('ig.file_path')
                        ->first();
                        $warna = DB::table('master_color')
                                ->where('key',$item->product_variant_c)
                                ->where('is_active',1)
                                ->pluck('value')->first();
    
                        $product_items = [];
                        $sub_total_price = 0;
                        $itemData = new TransactionItem();
                        $itemData->article = $item->article;
                        // $itemData->image_url = ($image['url'] ??"");
                        // $itemData->article = $item->article;
                        $itemData->product_name = $item->product_name;
                        $itemData->product_variant = $warna??null;
                        $itemData->product_size = $item->product_size;
                        $itemData->qty = $item->qty;
                        $itemData->issued_qty = $item->issued_qty;
                        $itemData->merchandise_category = $item->merchandise_category;
                        $itemData->sub_total = $DeliveryOrder->good_issue_date != null ? (int)($item->issued_qty*$data_order_detail->price) : (int)($item->qty*$data_order_detail->price);
                        $itemData->location = $item->location;   
                        array_push($product_items,$itemData);
                        
                        $flag= $this->getFlag($item->article);
    
                        $productItem = [
                            'sku_code_c' => $item->sku_code_c,
                            'product_name_c' => $item->product_name_c,
                            "image_url" => @$image->file_path == null ? null : env('S3_STREAM_URL').$image->file_path,
                            'flag' =>$flag,
                            'base_price' => $data_order_detail->price ?: 0,
                            'sub_total' => 0,
                            'customs' => [],
                            'product_items' => $product_items
                        ];
                        array_push($items,$productItem);
                        $total_qty += $item->qty;
                }
            }

            // websocket simulate b2b bang
            $category = $DeliveryOrderDetail[0]->product->lvl3_description ?? 'BAGS' == 'BAGS' ? 'BAGS' : 'NON BAGS';
            $m_d = DB::table('matrix_discount')
                            ->where('is_custom',$customCheck == true ? 1 : 0)
                            ->whereRaw('? BETWEEN min_bruto_from AND min_bruto_to',[(int)$order->total])
                            ->whereRaw('? BETWEEN qty_from AND qty_to',[(int)$total_qty])
                            ->where('category',$category)
                            ->first();

                            if($order->order_status == self::OrderStatusWait){
                                if($order->items != null && count($order->items) > 0){
                                    $article_map = array_map(function ($b) {
                                        return [
                                        'source' => 'CAREOM',
                                        'destination' => 'STK',
                                        'article' => $b['article_id'],
                                        'site' => '1200'
                                    ];
                                    }, $order->items->toArray()); 
                                    StockUpdateBatch::dispatch($article_map, auth()->user()->customer->customer_id);
                                }
                            }

            $trx->total_kustomisasi = $total_kustomisasi;
            $allItems[] = $items; 

        } else {

            $order = Order::where('order_no', $order_no)->first();
            $data = DB::table('delivery_order')
            ->leftJoin('order_header', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
            ->leftJoin('invoice', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
            ->where('delivery_order.sales_order_no', $order->sales_order_no)
            ->select('delivery_order.*', 'invoice.invoice_no' )
            ->first();

            if(isset($invoice_data)){
                $trx->payment_date = in_array($order->order_status,[self::OrderStatusWait, self::OrderStatusBaru,self::OrderStatusOnProcess,self::OrderStatusVerif]) ? "" : $invoice_data->modified_date;
            }else{
                $trx->payment_date = "";
            }

            $location_code = $order->location_code??null;
            $location_name = $order->location_name??null;
            $trx->sap_response = null;
            $trx->order_no = $order->order_no;
            $trx->customer_shipment_id = $order->customer_shipment_id??null;
            $trx->sub_total = (int)$order->total - (int)$order->total_discount;
            $trx->total_discount = (int)$order->total_discount;
            $trx->total_discount_pesanan_percentage = $order->total_discount != 0 ? ($order->total_discount??0)/($order->total/100) : 0;
            $trx->total = (int)$order->total;
            $trx->ongkos_kirim = (int)$order->shipping_charges;
            $trx->company_email = auth()->user()->customer->email;
            $trx->pajak = 0;
            $trx->total_nett = (int)$order->total_nett - (int)$order->total_discount;
            $trx->total_dp = (int)$order->dp_amount;
            $trx->dp_percentage = $order->dp_percentage;
            $trx->customer_id = (int)$order->customer_id;
            $trx->sales_order_no = $order->sales_order_no??"";
            $trx->status = $order->order_status;
            $trx->delivery_order_no = $order->delivery_no??"";
            $trx->transaction_date = $order->created_date;
           // $trx->so_date = $order->created_date;
            $trx->do_date = $data->created_date??"";
            $trx->gi_date = $data->good_issue_date??"";
            $trx->shipping_date = $order->shipping_date??"";
            $trx->received_date = $order->completed_date??"";
            $trx->invoice_no = $data->invoice_no??"";
            $trx->invoice_no_dp = $data_dp->invoice_no??null;
            $trx->order_summary = $order->payment_status??"Belum Lunas";
            $trx->no_resi = $order->awb_no??"";
            $trx->bill_to = $order->bill_to;
            $trx->bill_to_address = $order->bill_to_address;
            $trx->bill_to_phone_number = $order->bill_to_phone_number;
            $trx->bill_to_email = $order->bill_to_email;
            $trx->ship_to = $order->ship_to;
            $trx->ship_to_address = $order->ship_to_address;
            $trx->ship_to_phone_number = $order->ship_to_phone_number;
            $trx->distribution_channel = $order->distribution_channel;
            $trx->verifikasi_date = OrderApproval::where('order_no',$order->order_no)->where('status','Approved')->pluck('action_date')->first();
            $trx->transportation_zone = $location_code.' - '.$location_name;
            $trx->pesanan_dibatalkan_date = $order->status == 'Batal' ? $order->modified_date : null ;
            $trx->payment_dp_date = null;
            $orderCustom = Order::join('order_custom', 'order_custom.reference_id','=','order_header.order_no')
            ->select('order_header.order_no')
            ->get()->toArray();
            $customCheck = false;
            foreach ($orderCustom as $orderitems) {
                if ($orderitems['order_no'] === $order->order_no) {
                    $customCheck = true;
                    break;
                }
            }
            $trx->is_custom = $customCheck;

            $trx->data_limit =  [];

            //get order detail
            $orderItems = OrderItem::leftJoin('article', 'order_detail.article_id', '=', 'article.article')
            ->select('order_detail.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_variant_c' )
            ->where('order_no', $order->order_no)->orderBy('order_detail.order_detail_id', 'desc')
            ->get();

         

            $items = [];

            $total_kustomisasi = 0;
            $total_qty = 0;

            if ($customCheck == true) {
                $orderItems = OrderCustom::leftJoin('article', 'order_custom.sku', '=', 'article.sku_code_c')
                ->select('order_custom.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_variant_c')
                ->where('reference_id', $order->order_no)
                ->get();

                $attachmentGroupIds = $orderItems->pluck('attachment_group_id')->filter()->unique()->toArray();

                $remarks = Remark::whereIn('attachment_group_id', $attachmentGroupIds)->get()->groupBy('attachment_group_id');
                
                foreach ($orderItems->groupBy('attachment_group_id') as $groupId => $groupItems) {
                    $firstItem = $groupItems->first(); // get one item to pull shared info
                    $firstItem->remark = $remarks['$groupId']->remark ?? '-';
                    $image = DB::table('article as art')
                        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                        ->where('art.article', $firstItem->article_id)
                        ->where('ig.is_main_image', 1)
                        ->select('ig.file_path')
                        ->first();
                
                    $article = Product::where('article', $firstItem->article_id)->first();
                    $warna = DB::table('master_color')
                        ->where('key', $firstItem->product_variant_c)
                        ->where('is_active', 1)
                        ->pluck('value')->first();
                
                    $ocs = OrderCustom::where('reference_id', $order->order_no)
                        ->where('sku', $article->sku_code_c)
                        ->where('attachment_group_id', $groupId)
                        ->pluck('attachment_group_id')->toArray();
                
                    $custom_price = OrderCustomAttachment::whereIn('order_custom_id', $ocs)->sum('custom_price');
                
                    $product_items = [];
                    foreach ($groupItems->groupBy('id') as $itemGroup) {
                        $item = $itemGroup->first();
                        $itemData = new TransactionItem();
                        $itemData->article = $item->article_id;
                        $itemData->product_name = $item->product_name;
                        $itemData->product_variant = $warna ?? null;
                        $itemData->product_size = $item->product_size;
                        $itemData->qty = $item->qty;
                        $itemData->sub_total = (int)($item->qty * $article->price->amount);
                        $product_items[] = $itemData;
                        $total_qty += $item->qty;
                    }
                
                    $flag = $this->getFlag($firstItem->article_id);
                    $cdate = now()->format('Y-m-d');
                
                    $price = DB::table('article_price')
                        ->where('sku_code_c', $firstItem->sku_code_c)
                        ->where('valid_from', '<=', $cdate)
                        ->where('valid_to', '>=', $cdate)
                        ->orderBy('valid_from', 'desc')
                        ->select('amount')
                        ->first();
                
                    $ocas = OrderCustomAttachment::whereIn('order_custom_id', $ocs)->get();
                    // return $ocas;
                    $customs = [];
                    foreach ($ocas as $oca) {
                        $oc = OrderCustom::where('attachment_group_id', $oca->order_custom_id)->first();
                        $articles = Product::where('sku_code_c', $oc->sku)->pluck('article')->toArray();
                        $customs[] = [
                            'harga_satuan' => (int)$oca->custom_price
                        ];
                    }
                
                    $productItem = [
                        'sku_code_c' => $firstItem->sku_code_c,
                        'attachment_group_id' => $groupId,
                        'product_name_c' => $firstItem->product_name_c,
                        'image_url' => @$image->file_path ? env('S3_STREAM_URL') . $image->file_path : null,
                        'flag' => $flag,
                        'base_price' => $article->price->amount,
                        'sub_total' => 0,
                        'customs' => $customs,
                        'product_items' => $product_items
                    ];
                
                    if (in_array($order->order_status, [
                        self::OrderStatusPending,
                        self::OrderStatusOnHold,
                        self::OrderStatusWait,
                        self::OrderStatusBaru
                    ])) {
                        $productItem['base_price'] = $price->amount;
                    }
                
                    $items[] = $productItem;
                    $total_kustomisasi += $custom_price * $groupItems->sum('qty');
                }
            } else {
                foreach ($orderItems as $item) {
                    $image = DB::table('article as art')
                        ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
                        ->where('art.article', $item->article_id)
                        ->where('ig.is_main_image',1)
                        ->select('ig.file_path')
                        ->first();
                    
                    $article = Product::where('article',$item->article_id)->first();
                    $warna = DB::table('master_color')
                            ->where('key',$item->product_variant_c)
                            ->where('is_active',1)
                            ->pluck('value')->first();
    
                    $product_items = [];
                    $itemData = new TransactionItem();
                    $itemData->article = $item->article_id;
                    $itemData->product_name = $item->product_name;
                    $itemData->product_variant = $warna??null;
                    $itemData->product_size = $item->product_size;
                    $itemData->qty = $item->qty;
                    $itemData->sub_total = (int)($item->qty*$article->price->amount);
                    array_push($product_items,$itemData); 
                    $flag= $this->getFlag($item->article_id);
                    $cdate = now()->format('Y-m-d');
                
    
                    $price =  DB::table('article_price')
                    ->where('article_price.sku_code_c', $item->sku_code_c)
                    ->where('valid_from', '<=', $cdate)
                    ->where('valid_to', '>=', $cdate)
                    ->orderBy('valid_from', 'desc')
                    ->select('article_price.amount')
                    ->first();
                    $productItem = [
                        'sku_code_c' => $item->sku_code_c,
                        'product_name_c' => $item->product_name_c,
                        "image_url" => @$image->file_path == null ? null : env('S3_STREAM_URL').$image->file_path,
                        'flag' =>$flag,
                        'base_price' => $article->price->amount,
                        'sub_total' => 0,
                        'customs' => [],
                        'product_items' => $product_items
                    ];
                    if(in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru])){
                        $productItem['base_price'] = $price->amount;
                    }
                    array_push($items,$productItem);
                    $total_qty += $item->qty;
                }
            }

            // websocket simulate b2b bang
            $category = $orderItems[0]->product->lvl3_description ?? 'BAGS' == 'BAGS' ? 'BAGS' : 'NON BAGS';
            if ($order->order_status != self::OrderStatusCancel) {
                $m_d = DB::table('matrix_discount')
                ->where('is_custom',$customCheck == true ? 1 : 0)
                ->whereRaw('? BETWEEN min_bruto_from AND min_bruto_to',[(int)$order->total])
                ->whereRaw('? BETWEEN qty_from AND qty_to',[(int)$total_qty])
                ->where('category',$category)
                ->first();
                
                $websocket = event(new GenericEvent(auth()->user()->customer->customer_id, [
                    'total_tax' => 0,
                    'total' => $order->total,
                    'total_discount' => $m_d->discount*($order->total/100),
                    'total_nett' => 0,
                    'nett_before_tax' => 0
                    ], 'simulateSO.new'));

                Log::info($websocket);
            }
            $trx->total_kustomisasi = $total_kustomisasi;
            $allItems[] = $items;          
        }   

        //grouping array
        $groupedItems = [];
        foreach ($allItems as $item) {
            foreach ($item as $subitem) {
                if (!isset($groupedItems[$subitem['sku_code_c']])) {
                    $groupedItems[$subitem['sku_code_c']] = [
                        'sku_code_c' => $subitem['sku_code_c'],
                        'product_name_c' => $subitem['product_name_c'],
                        "image_url" =>   $subitem['image_url'],
                        'flag' => $subitem['flag'],
                        'base_price' => $subitem['base_price'],
                        'sub_total' => 0,
                        'customs' => $subitem['customs'],
                        'product_items' => []
                    ];
                }
                $groupedItems[$subitem['sku_code_c']]['product_items'] = array_merge($groupedItems[$subitem['sku_code_c']]['product_items'], $subitem['product_items']);
            }
        }

        //count sub total per sku
        $article = collect($groupedItems)->pluck('product_items')->flatten(1)->pluck('article')->toArray();

        $stock = $request->is_available == false ?  ProductSku::whereIn('sku_id', $article)->where('stock', 0)->pluck('sku_id') : ProductSku::whereIn('sku_id', $article)->where('stock', '>', 0)->pluck('sku_id');
        $groupedItems = collect($groupedItems)->filter(function ($item) use ($stock) {
            return collect($item['product_items'])->pluck('article')->intersect($stock)->isNotEmpty();
        })->toArray();
        
        foreach ($groupedItems as &$groupedItem) {
            foreach ($groupedItem['product_items'] as $productItem) {
                $groupedItem['sub_total'] += $productItem['sub_total'];
            }
        }

        $groupedItems = array_values($groupedItems);
        $offset = ($page - 1) * $limit;
        $total = count($groupedItems);
        $groupedItems = array_slice($groupedItems, $offset, $limit);

        $trx->items = $groupedItems;
        $trx->available = ProductSku::whereIn('sku_id', $article)->where('stock', '>', 0)->count();
        $trx->unavailable = ProductSku::whereIn('sku_id', $article)->where('stock', 0)->count();
        $trx->company_name = auth()->user()->customer->instance_name;
        
        if ($order->customer->registered_sap_at != null) {
            $customerShipment = CustomerShipment::where('customer_id', $order->customer_id)->get();
            $data = [];
            foreach ($customerShipment as $cs) {
                if (!Str::isUuid($cs->customer_shipment_id)) {
                    $data[] = $cs->toArray();
                }
            }
            $trx->customer_shipment = $data;
        } else {
            $trx->customer_shipment = $order->customer->shipments()
                        ->select('customer_shipment_id', 'customer_id', 'name', 'address', 'city', 'province', 'district', 'zip_code', 'shipment_type', 'phone_number')
                        ->get();
        }

        $fisrt = ($page - 1) * $limit + 1;
        $last = min($page * $limit, count($groupedItems));
        
        $data = [
            // 'pagination' => [
            //     'total' => $total,
            //     'per_page' => (int)$perPage,
            //     'current_page' => (int)$page,
            //     'last_page' => ceil($total/$perPage),
            //     'from' => $fisrt,
            //     'to' => $last
            // ],
            'total_data' =>$total,
            'size' => (int)$limit,
            'active_page' => (int)$page,
            'total_page' => ceil($total/$limit),
            'order_detail' => $trx,
        ];
        //Log::channel('stderr')->info($trx);
        return $this->sendSuccess(null, $data);
    }

 


    public function checkTopIsPendingPayment(Customer $customer)
    {
        if($customer->top_days!=self::TokoCash)
        {
            if($customer->is_pending_payment==1){
                return true;
            }
        }
        return false;
    }


    public function getOrderStatusList()
    {
        $custId = auth()->user()->customer->customer_id;
        $customer = Customer::where('customer_id', $custId)->first();    
        
        if( $customer->top_days!=self::TokoCash){
        
            $status = [
                self::OrderStatusSemua,
                self::OrderStatusWait,
                self::OrderStatusPending,
                self::OrderStatusOnHold,             
                self::OrderStatusBaru,
                self::OrderStatusOnProcess,
                self::OrderStatusGI,
                self::OrderStatusOnShipping,
                self::OrderStatusFinish,
                self::OrderStatusCancel,
            ];
        }else {
            $status = [
                self::OrderStatusSemua,
                self::OrderStatusPending,
                self::OrderStatusOnHold,
                self::OrderStatusBaru,
                self::OrderStatusOnProcess,
                self::OrderStatusPembayaran,
                self::OrderStatusGI,
                self::OrderStatusOnShipping,
                self::OrderStatusFinish,
                self::OrderStatusCancel,
            ];
        }

        return $this->sendSuccess("Data List Status Order", $status);
    
    }

    public function createSimulateSO($type, $order_no, $customer_id, $customer_shipment_id = 0, $ordersParams = null)
    {
        // $orders = $orderItems;
        // if($orderItems==""){
            $orders = OrderItem::leftJoin('article', 'order_detail.article_id', '=', 'article.article')
            ->select('order_detail.*', 'article.sku_code_c', 'article.product_name_c' )
            ->where('order_detail.order_no', $order_no)
            ->where('is_available', 1)
            ->orderBy('order_detail.order_detail_id', 'desc')
            ->get();

            $sales = DB::table('order_header as oh')
            ->join('customer_sales as cs', 'cs.customer_id', '=', 'oh.customer_id')
            ->join('sales', 'sales.sales_id', '=', 'cs.sales_id')
            ->select('sales.sap_username')
            ->where('oh.order_no', $order_no)
            ->first();
            if($sales){
                if($sales->sap_username !== null ){
                    $sap_username = env('ARTICLE_STOCK_SAPUSER');
                }elseif($sales->sap_username == null){
                    $sap_username = env('ARTICLE_STOCK_SAPUSER');
                } 
            }
            else{
                $sap_username = env('ARTICLE_STOCK_SAPUSER');
            }
            

        $custId = auth()->user()->customer->customer_id;
        $client = new CreateSimulateSORepo();
        //Log::channel('stderr')->info('[ITEMS REQUEST KE SAP]',$item);
        $soDetail = [];
        
        $date = Carbon::now();
        $yymmdd = $date->format('Ymd');
        $no = 1;
        foreach ($orders as $item) {
            $so = [
                'destination' => 'SLO',
                //'flag' => $type,
                'flag' => $type,
                'itmnumber' => sprintf("%02d", $no),
                'article' => $item->article_id,
                'targetqty'=> STRVAL($item->issued_qty && $item->issued_qty > 0 ?$item->issued_qty : $item->qty),
                'uom'=>'PC',

            ];
            array_push($soDetail,$so);
            $no = $no+1;
        }
        // dd($customer_shipment_id ? str_pad($customer_shipment_id, 10, '0', STR_PAD_LEFT) : strval($custId));die();
            $soDetailSet=$soDetail;
            $soPartnerSet = ($type == "i" && $custId != $customer_shipment_id) ? [
                [
                'destination' => 'SLO',
                'partnrole' => 'SH',
                'partnnumb'=> strval($custId)
                ],
                [
                'destination' => 'SLO',
                'partnrole' => 'SH',
                'partnnumb'=> strval($customer_shipment_id)
                ],
            ] : 
            [
                [
                'destination' => 'SLO',
                'partnrole' => 'SH',
                'partnnumb'=> strval($custId)
                ]
            ];
            $soReturnSet= [];

            $data = [
                "source"=> "CAREOM",
                "destination" => "SLO",
                "flag"=> $type,
                "doctype"=> "ZESD",
                "salesorg"=> "1000",
                "distrchan"=> "W1",
                "division"=> "00",
                "reqdate"=> $yymmdd,
                "externalno" => $order_no,
                "salesorder"=> "",
                "soDetailSet"=>$soDetailSet,
                "soPartnerSet"=>$soPartnerSet,
                "soReturnSet" =>$soReturnSet,
                "sap_username" =>$sap_username,
            ];

           $result=[];
        //    try{
            $response = $client->createSO($data);
        //    }
        //    catch(Exception $e){
        //     $data['soPartnerSet'][0]['partnnumb'] = strval($custId);
        //     $response = $client->createSO($data);
        //    }
            $result = $client->getData($response);
            if($result){
                if($type=="i"){
                    // $res = $client->getData($response);
                    $this->updateTableOrderCreateSO($result, $order_no);
                }
                //simulate
                if($type=="s"){
                    // $res = $client->getDataWhole($response);
                    $this->updateTableOrder($result, $order_no); 
                }
            }
            // dd($result);
            // try {
            //     $response = $client->createSO($data);
            //  //   $statusCode = $response->getStatusCode();
            //     $result = $client->getData($response);
                
            //     // Log::channel('stderr')->info('[RESPONSE SAP]',$result);
            //     Log::channel('stderr')->info($result);

            //     if($result){
            //         if($type=="i"){
            //             $this->updateTableOrderCreateSO($result, $order_no);
            //         }
            //         //simulate
            //         if($type=="s"){
            //             $this->updateTableOrder($result, $order_no); 
            //         }
            //     }
            // } catch (\Exception $e) {
            //         Log::error($e->getMessage());
            //         // return error response
            //     // return $data;

            // }
        
            return $result;
    
    }

    //simulate SO
    public function updateTableOrder($input, $order_no)   
    {
        // dd($input);die();
        //$order = OrderHeader::find($id);
        $order = Order::where('order_no', $order_no)->first();
        
        if (!$order) {
           // return response()->json(['error' => 'Order not found'], 404);
             Log::info("Order not found when update simulate SO ". $order_no);
        }
        

        if(isset($input['tax'])){
            $order->total_tax = $input['tax'] !== null or $input['tax'] !== "" ? $input['tax'] : $order->total_tax;
            $order->total = $input['gross'] !== null or $input['gross'] !== "" ? $input['gross'] : $order->total;
            $order->total_discount = $input['discount'] !== null or $input['discount'] !== "" ? $input['discount'] : $order->total_discount;
            $order->total_nett = $input['net'] !== null or $input['net'] !== "" ? $input['net'] : $order->total_nett;
            $order->nett_before_tax = $input['netbtax'] !== null or $input['netbtax'] !== "" ? $input['netbtax'] : $order->nett_before_tax;
            $order->save();
        }
      

        return response()->json(['message' => 'Order updated successfully']);
    }

    public function updateTableOrderCreateSO($data, $order_no)   
    {
        //$order = OrderHeader::find($id);
        // $jsonData = json_decode($data, true);
        // Log::info("Order not found when update Create SO ". $data);
        Log::info('DATA UPDATE SO:');
        Log::info($data);
        $this->errordata = $data;
        $vbeln = ltrim($data['vbeln'],'0');
        $order = Order::where('order_no', $order_no)->first();
        if (!$order) {
           // return response()->json(['error' => 'Order not found'], 404);
             Log::info("Order not found when update Create SO ". $data);
        }

        if(array_key_exists('discount', $data) && array_key_exists('gross', $data)){
            // $order->total = str_replace(".", "", $data['gross']);
            $order->total_discount = str_replace(".", "", $data['discount']);
        }

        if(array_key_exists('detail', $data)){
            if(!empty($data['detail'])){
                foreach($data['detail'] as $i){
                    if($i['qty'] < 1){
                        if(auth()->user()->customer->customer_id){
                            $rq = new \stdClass;
                            $rq->article = $i['article'];
                            $rq->qty =  OrderItem::where(['order_no' => $order_no, 'article_id' => $i['article']])->first()->qty??1;
                            $rq->is_custom = 0;
                            $this->cartStore(auth()->user()->customer->customer_id, $rq);
                        }
                        OrderItem::where(['order_no' => $order_no, 'article_id' => $i['article']])->delete(); 
                    }
                    else{
                        $dt = OrderItem::where(['order_no' => $order_no, 'article_id' => $i['article']])->first();
                        $stock = ProductSku::where('sku_id', $i['article'])->update(['stock' => $i['qty']]);
                        if($dt){
                            $dt->issued_qty = $i['qty']??$dt->qty;
                            $dt->sub_total = $i['qty']??$dt->qty * $dt->price;
                            $dt->save();
                        }
                    }

                }
            }
        }
        

        $order->sales_order_no = $vbeln;
        // $order->order_status = self::OrderStatusOnHold;
        $order->save();

        //return response()->json(['message' => 'Order updated successfully']);
    }

    //terima pesanan
    public function receiveOrder(Request $request)   
    {
        
        $userid = $request->user()->reference_id;

        //$order = OrderHeader::find($id);
        $order_no =  $request->input('order_no');
        $order = Order::leftJoin('invoice', 'invoice.order_no' ,'=','order_header.order_no')->where('order_header.order_no', $order_no)
        ->where('invoice_type','BILLING')->first();
        
        $data_bill = Invoice::where('sales_order_no',$order->sales_order_no)->where('invoice_type','BILLING')->first();
        if ($data_bill != null) {
            $det = InvoiceDetail::query()
            ->where('invoice_no', '=',$data_bill->invoice_no)
            ->select(DB::raw('sum(gross_price) as subtotal'),
                DB::raw('sum(discount_percent/100*gross_price) as discount'),
                DB::raw('sum(nett_price) as total'))
            ->first();

            // $qty_items = InvoiceDetail::where('invoice_no',$data_bill->invoice_no)->sum('qty');
            $sub_total = $det->subtotal;
            $discount = $det->discount;
            $total = $det->total;
        }   
        //dd($order);

        if (empty($order)) {
        
            Log::info("Order not found when receiveOrder". $order_no);
            return $this->sendError("Data transaction not found", 404);
            
        }
        $order->order_status = self::OrderStatusFinish;
        $order->completed_date = date('Y-m-d H:i:s');
    
        $order->save();

        $customer = Customer::where('customer_id', $order->customer_id)->first();
        $customer_shipment = CustomerShipment::where('customer_shipment_id',$order->customer_shipment_id)->first();
        //$customer_email = '<EMAIL>';
        //$order_count = OrderItem::where('order_no', $order->order_no)->count();
        $order_count = fn(array $arr) => array_sum(array_map(function ($i) {
            $sum = is_null($i['delivery_order']) ? 
            array_reduce($i['items'], function($total, $order_items)
            {
                return $total += $order_items['qty'] ?? 0;
            }, 0) :
            array_reduce($i['delivery_order']['items'], function($total, $order_items)
            {
                return $total += is_null($order_items['issued_qty']) ? $order_items['qty'] : $order_items['issued_qty'];
            }, 0);
            return $sum;
        }, $arr));
        $count = $order_count(Order::with('items', 'delivery_order.items')->where('order_no', $order_no)->get()->toArray());
        $customer_email = $request->user()->email;

        //GET SOCIAL MEDIA ACCOUNTS
        $socmed = new GetSocialsRepo();
        $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
        $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
        $twitter = $socmed->getSocialMediaParameters('TWITTER');
        $tiktok = $socmed->getSocialMediaParameters('TIKTOK');
        $youtube = $socmed->getSocialMediaParameters('YOUTUBE');
        $support = $socmed->getSocialMediaParameters('SUPPORT');
        $linkedin = $socmed->getSocialMediaParameters('LINKEDIN');

        if($customer_email){

            // $totaldiscount = $order->gross_price - $order->nett_price;
        
            $param['orders'] = [
                'order_no'=>$order->order_no,
                'count'=>$count,
                'name'=>$customer->owner_name,
                'phone_no'=>$customer->phone_number,
                'address'=>$order->ship_to_address,
                'subtotal'=>$sub_total,
                'total'=>$total,
                'total_discount'=>$discount,

                //GET SOCIAL MEDIA ACCOUNTS
                'facebook' => $facebook,
                'twitter' => $twitter,
                'instagram' => $instagram,
                'support' => $support,
                'linkedin'=> $linkedin
            ];

            $msg = 'Pesanan anda dengan nomor pesanan #'.$order->order_no.'. Segera periksa kelengkapan dan mengkonfirmasi kedatangan pesanan anda.';
            if(in_array($order->distribution_channel, ['B2B', 'RE', 'RD', 'W3'])){
                MailSender::dispatch($customer_email, json_encode($param), 'mail_item_received_b2b');
            }else {
                MailSender::dispatch($customer_email, json_encode($param), 'mail_item_received');
            }
            $this->notifStore($userid, 'Pesanan Telah Sampai', 'order', $msg, $order->order_no, 'Transaksi', $order->distribution_channel, 'success');
        }

        // $param = [];
        //     $order_no =  $request->input('order_no');
        //     $ord = Order::where('order_no', $order_no)->first()->toArray();
        //     $ordet = OrderItem::where('order_no', $order_no)->get()->toArray();
        //     $ord['order_detail'] = $ordet;
            
        //     $custId = auth()->user()->customer->customer_id;
        //     $Customer = Customer::where('customer_id', $custId)->first();    
            
        //     // merge customer, order ke 1 array
        //     $param['customer'] = $Customer->toArray();
        //     $param['order'] = $ord;


        // MailSender::dispatch(auth()->user()->email, json_encode($param), 'mail_order');
        return $this->sendSuccess("Received order success.","200");
    }

    public function cancelOrder(Request $request)
    {
        $orderNo =  $request->input('order_no');
        
        $orderHeader = Order::where('order_no',$orderNo)->first();

        // if(is_array($order_no)){
        //     $oh = Order::whereIn('order_no',$order_no);
        // }

        if (empty($orderHeader)) {
            Log::info("Order not found when receiveOrder". $orderNo);
            return $this->sendError("Data transaction not found", 404);
        }

        // if (is_array($order_no) ? $oh->first()->distribution_channel : $oh->distribution_channel == 'WHOLESALES') {
        //     if (!in_array(is_array($order_no) ? $oh->first()->order_status : $oh->order_status , [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait])) return $this->sendError("Transaction tidak dapat dibatalkan", 404);
        //     return $this->cancelWholesales($oh, is_array($order_no));
        // }

        if ($orderHeader->distribution_channel == 'WHOLESALES') {
            if (!in_array($orderHeader->order_status , [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait])) {
                return $this->sendError("Transaction tidak dapat dibatalkan", 404);
            }

            return $this->cancelWholesales($orderHeader);
        }

        // if (is_array($order_no) ? $oh->first()->distribution_channel : $oh->distribution_channel == 'B2B') {
        //     return $this->cancelB2B($request);
        // }

        if (in_array($orderHeader->distribution_channel, ['B2B', 'RE', 'RD', 'W3'])) {
            return $this->cancelB2B($request);
        }
    }

    public function cancelWholesales($order){
        // $userid = $isArray ? $oh->first()->customer_id : $oh->customer_id;
        $userid = $order->customer_id;

        $order->update(['order_status' => self::OrderStatusCancel]);

        // if($isArray)
        // {
        //     foreach($oh as $order){
        //         if($order->items != null && count($order->items) > 0){
        //             foreach($order->items as $i){
        //                 $rq = new \stdClass;
        //                 $rq->article = $i->article_id;
        //                 $rq->qty = $i->qty;
        //                 $rq->is_custom = 0;
        //                 $this->cartStore($userid, $rq);
        //             }
        //         }
        //     }
        // }else{
        if ($order->items != null && count($order->items) > 0) {
            foreach($order->items as $i){
                $rq = new \stdClass;
                $rq->article = $i->article_id;
                $rq->qty = $i->qty;
                $rq->is_custom = 0;
                $this->cartStore($userid, $rq);
            }
        }
        // }

        // $msg = 'Pesanan anda dengan nomor pesanan '. $isArray ? $oh->pluck('order_no')->implode(', ') : $oh->order_no.' Dibatalkan. Segera periksa kelengkapan dan lakukan order ulang.';
        // $this->notifStore($userid, 'Pesanan Dibatalkan', 'order', $msg, $isArray ? $oh->pluck('order_no')->implode(', ') : $oh->order_no, 'Transaksi', 'WHOLESALES', 'error');

        $msg = 'Pesanan anda dengan nomor pesanan #'. $order->order_no.' dibatalkan. Segera periksa kelengkapan dan lakukan order ulang.';
        $this->notifStore($userid, 'Pesanan Dibatalkan', 'order', $msg, $order->order_no, 'Transaksi', 'WHOLESALES', 'error');

        return $this->sendSuccess("Cancel order successfully.","200");
    }

    public function cancelB2B($request){
        if (auth()->user()->reference_object == 'customer' && auth()->user()->customer->distribution_channel != 'B2B') {
            return $this->sendError('You are not allowed to cancel this order');
        }

        $order_no =  $request->input('order_no');
        $userid = $request->user()->reference_id;
        $order = Order::where('order_no',$order_no)->first();
        $orderItems = Order::with('items')->where('order_no', $order_no)->first();
        if (empty($order)) {
            Log::info("Order not found when cancel order". $order_no);
            return $this->sendError("Data transaction not found", 404);
        }

        if (auth()->user()->reference_object == 'customer' && $order->sales_order_no != null) {
            Log::info("Cancel order failed : SO already exist". $order_no);
            return $this->sendError('Cancel order failed : SO already exist',404);
        }

        // if (auth()->user()->reference_object == 'sales' && $order->order_status != 'Reject') {
        //     Log::info("Cancel order failed : order status is not reject". $order_no);
        //     return $this->sendError('Cancel order failed : order status is not reject',404);
        // }

        $oh = Order::where('order_no',$order_no)->first();
        
        $oh->order_status = self::OrderStatusCancel;
        $oh->modified_by = auth()->user()->name;
        $oh->save();

        OrderApproval::where('order_no',$order_no)->delete();
        Customer::where('customer_id',$order->customer_id)->update(['status' => null]);

        $oc_exs = OrderCustom::where('reference_id',$order_no)->exists();

        if($orderItems->items != null && count($orderItems->items) > 0 && $oc_exs == true){
            foreach($orderItems->items as $i){
                $rq = new \stdClass;
                $rq->article = $i->article_id;
                $rq->qty = $i->qty;
                $rq->is_custom = $oc_exs;
                $this->cartStore($userid, $rq);   
            }

            $cart = Cart::where('customer_id',$order->customer_id)->first();
            $ocs = OrderCustom::where('reference_id',$order_no)->get();
            foreach ($ocs as $oc) {
                $oc_copy = OrderCustom::updateOrCreate([
                    'sku' => $oc->sku,
                    'reference_name' => 'cart',
                    'reference_id' => $cart->id,
                    'position_side' => $oc->position_side,
                ],[
                    'generated_file_path' => $oc->generated_file_path,
                    'created_by' => auth()->user()->reference_object == 'customer' ? auth()->user()->customer->owner_name : auth()->user()->sales->sales_name,
                    'modified_by' => auth()->user()->reference_object == 'customer' ? auth()->user()->customer->owner_name : auth()->user()->sales->sales_name
                ]);
                $q_oc_copy = OrderCustom::where([
                    ['sku', '=', $oc_copy->sku],
                    ['article_id' , '=',$oc_copy->article_id],
                    ['reference_name', '=', $oc_copy->reference_name],
                    ['reference_id', '=', $oc_copy->reference_id],
                    ['position_side', '=', $oc_copy->position_side],
                    ['generated_file_path', '=', $oc_copy->generated_file_path],
                    ['created_by', '=', $oc_copy->created_by],
                    ['created_date', '=', $oc_copy->created_date]
                ])->first();

                $ocas = OrderCustomAttachment::where('order_custom_id',$oc->id)->get();
                foreach($ocas as $oca){
                    OrderCustomAttachment::create([
                        'order_custom_id' => $q_oc_copy->id,
                        'custom_type' => $oca->custom_type,
                        'custom_price' => $oca->custom_price,
                        'position_x' => $oca->position_x,
                        'position_y' => $oca->position_y,
                        'dimension_width' => $oca->dimension_width,
                        'dimension_height' => $oca->dimension_height,
                        'file_path' => $oca->file_path,
                        'notes' => $oca->notes,
                        'size' => $oca->size,
                        'custom_text' => $oca->custom_text,
                        'material' => $oca->material,
                        'created_date' => now()->format('Y-m-d H:i:s'),
                        'created_by' => auth()->user()->reference_object == 'customer' ? auth()->user()->customer->owner_name : auth()->user()->sales->sales_name,
                        'modified_date' => now()->format('Y-m-d H:i:s'),
                        'modified_by' => auth()->user()->reference_object == 'customer' ? auth()->user()->customer->owner_name : auth()->user()->sales->sales_name
                    ]);
                }
            }

        } else {
            foreach($orderItems->items as $i){
                $rq = new \stdClass;
                $rq->article = $i->article_id;
                $rq->qty = $i->qty;
                $rq->is_custom = $oc_exs;
                $this->cartStore($userid, $rq);
            }
        }
        
        $msg = 'Pesanan anda dengan nomor pesanan #'. $order_no . ' dibatalkan. Segera periksa kelengkapan dan lakukan order ulang.';
        $this->notifStore($order->customer_id, 'Pesanan Dibatalkan', 'order', $msg, $order_no, 'Transaksi', 'B2B', 'error');

        //email batalkan pesanan
        $param = [];
        $ord = Order::where('order_no', $order_no)->first()->toArray();
        $ordet = OrderItem::with('product.mainImageVariant')->where('order_no', $order_no)->get();
        foreach($ordet as $od){
            $od['product']['flag'] = $od->product->flag();
        }
        $ord['order_detail'] = $ordet->toArray();

        if (auth()->user()->reference_object == 'customer') {
            $Customer = Customer::where('customer_id', $userid)->first();
        } else {
            $Customer = Customer::where('customer_id', $ord['customer_id'])->first();
        }
        
        // email data
        $param['customer'] = $Customer->toArray();
        $param['order'] = $ord;
        $socmed = new GetSocialsRepo();
        $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
        $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
        $twitter = $socmed->getSocialMediaParameters('TWITTER');
        $support = $socmed->getSocialMediaParameters('SUPPORT');
        $linkedin = $socmed->getSocialMediaParameters('LINKEDIN');
        $param['social_media'] = [
            'facebook' => $facebook,
            'twitter' => $twitter,
            'instagram' => $instagram,
            'support' => $support,
            'linkedin'=> $linkedin
        ];
        MailSender::dispatch($Customer->email, json_encode($param), 'mail_order_reject');
        if ($order->sales_order_no != null or $order->sales_order_no != '') {
            $sap = new SAP();
    
            $delete_so = $sap->salesOrder('d', $order_no);
        
            if (isset($delete_so['error'])) {
                return $this->sendError("Error when SO B2B Integration", 500, '', ['type' => 'Error Care OM', 'message' => $delete_so['error']]);
            }
        
            $res_so = $delete_so->json();
        
            if ($delete_so->failed()) {
                return $this->sendError("Error when SO B2B Integration", 500, '', ['type' => 'Error SAP', 'message' => $res_so]);
            }
        
            if ($res_so == null) {
                return $this->sendError("Error when customer B2B Integration", 500, '', ['type' => 'Error SAP', 'message' => null]);
            }
        
            if ($res_so['message'][0]['type'] == 'E') {
                $sap_error = MasterParameter::where('group_key', 'CREATE_SO_ERROR')->pluck('description')->toArray();
        
            foreach ($res_so['message'] as $message) {
                $sap_error = MasterParameter::where('group_key', 'CREATE_SO_ERROR')->where('description', $message['message'])->first();
                $error_msg[] = $sap_error != null ? $sap_error->value : $message['message'];
            }
        
                return $this->sendError("Error when SO B2B Integration", 500, '', ['type' => 'Error SAP', 'message' => $error_msg]);
            }
        }
        return $this->sendSuccess('Order Canceled successfully', $oh->toArray());
    }

    public function updateOrderToOnhold($order_no)   
    {

        $order = Order::where('order_no', $order_no);
        $detail = OrderItem::where('order_no', $order_no);
        if ($order->first()) {
            $order->update(['order_status' => self::OrderStatusCancel]);
        }
        else{
            Log::info("Order not found when updateOrderToOnhold ". $order_no);
            return $this->sendError("Data transaction not found", 404);
            
        }
        return $this->sendSuccess("Received order success.","200");
    }

    public function updateOrderToPending($order_no)   
    {

        $order = Order::where('order_no', $order_no)->first();
       
        if (empty($order)) {
           
            Log::info("Order not found when updateOrderToPending ". $order);
            return $this->sendError("Data transaction not found", 404);
            
        }

        $order->order_status = self::OrderStatusCancel;
    
        $order->save();
        return $this->sendSuccess("Received order success.","200");
    }

    //terima pesanan
    public function reOrder(Request $request)   
    {
        $order_no =  $request->input('order_no');
        $order = Order::find($order_no);
        $custId = auth()->user()->customer->customer_id;
        $orders = OrderItem::where('order_no', $order_no)->get();
        if ($order->distribution_channel == 'B2B') {
            return $this->reOrderB2B($request,$orders);
        }

        DB::beginTransaction();
        try {
            if (empty($orders)) {

            // return response()->json(['error' => 'Order not found'], 404);
                Log::info("Order not found when ReOrder ". $order_no);
                return $this->sendError("Data transaction not found", 404);
            }
            foreach ($orders as $item) {
                $cart = Cart::updateOrCreate(
                    ['customer_id' => $custId],
                    []
                );

                $cart_header_id = $cart->id;
                $cartDetail = CartDetail::updateOrInsert(
                    [
                    'cart_id' => $cart_header_id,
                    'article' => $item->article_id,
                    ],
                    [
                    'qty' => $item->qty,
                    ]
                );
            }
            DB::commit();
            return $this->sendSuccessCreated("Cart created/updated successfully.", $cartDetail);
        } catch (\Exception $e) {
            
            DB::rollback();
            Log::info($e->getMess4age());
            return $this->sendError("Sorry system can't create/update checkout,  ", 500);
        }
     
    }

    public function reOrderB2B($request,$orders)
    {
        $custId = auth()->user()->customer->customer_id;
        DB::beginTransaction();
        try {
            if (empty($orders)) {

            // return response()->json(['error' => 'Order not found'], 404);
                Log::info("Order not found when ReOrder ". $request->order_no);
                return $this->sendError("Data transaction not found", 404);
            }

            $oc_exs = OrderCustom::where('reference_id',$request->order_no)->exists();
            
            if ($oc_exs == true) {
                foreach ($orders as $item) {
                    $cart = Cart::updateOrCreate(
                        ['customer_id' => $custId],
                        []
                    );
    
                    $cart_header_id = $cart->id;
                    $cartDetail = CartDetail::updateOrInsert(
                        [
                        'cart_id' => $cart_header_id,
                        'article' => $item->article_id,
                        ],
                        [
                        'qty' => $item->qty,
                        'is_custom' => $oc_exs
                        ]
                    );
                }
                $cart = Cart::where('customer_id',$custId)->first();
                $ocs = OrderCustom::where('reference_id',$request->order_no)->get();
                foreach ($ocs as $oc) {
                    $custom_price = OrderCustomAttachment::where('order_custom_id',$oc->id)->sum('custom_price');
                    $oc_copy = OrderCustom::create([
                        'sku' => $oc->sku,
                        'reference_name' => 'cart',
                        'reference_id' => $cart->id,
                        'position_side' => $oc->position_side,
                        'generated_file_path' => $oc->generated_file_path,
                        'created_by' => auth()->user()->reference_object == 'customer' ? auth()->user()->customer->owner_name : auth()->user()->sales->sales_name,
                        'modified_by' => auth()->user()->reference_object == 'customer' ? auth()->user()->customer->owner_name : auth()->user()->sales->sales_name,
                        'custom_price' => $custom_price
                    ]);
                    $q_oc_copy = OrderCustom::where([
                        ['sku', '=', $oc_copy->sku],
                        ['article_id' , '=',$oc_copy->article_id],
                        ['reference_name', '=', $oc_copy->reference_name],
                        ['reference_id', '=', $oc_copy->reference_id],
                        ['custom_price', '=', $oc_copy->custom_price],
                        ['position_side', '=', $oc_copy->position_side],
                        ['generated_file_path', '=', $oc_copy->generated_file_path],
                        ['created_by', '=', $oc_copy->created_by],
                        ['created_date', '=', $oc_copy->created_date]
                    ])->first();

                    $ocas = OrderCustomAttachment::where('order_custom_id',$oc->id)->get();
                    foreach($ocas as $oca){
                        OrderCustomAttachment::create([
                            'order_custom_id' => $q_oc_copy->id,
                            'custom_type' => $oca->custom_type,
                            'custom_price' => $oca->custom_price,
                            'position_x' => $oca->position_x,
                            'position_y' => $oca->position_y,
                            'dimension_width' => $oca->dimension_width,
                            'dimension_height' => $oca->dimension_height,
                            'file_path' => $oca->file_path,
                            'notes' => $oca->notes,
                            'size' => $oca->size,
                            'custom_text' => $oca->custom_text,
                            'material' => $oca->material,
                            'created_date' => now()->format('Y-m-d H:i:s'),
                            'created_by' => auth()->user()->reference_object == 'customer' ? auth()->user()->customer->owner_name : auth()->user()->sales->sales_name,
                            'modified_date' => now()->format('Y-m-d H:i:s'),
                            'modified_by' => auth()->user()->reference_object == 'customer' ? auth()->user()->customer->owner_name : auth()->user()->sales->sales_name
                        ]);
                    }
                }
            } else {
                foreach ($orders as $item) {
                    $cart = Cart::updateOrCreate(
                        ['customer_id' => $custId],
                        []
                    );
    
                    $cart_header_id = $cart->id;
                    $cartDetail = CartDetail::updateOrInsert(
                        [
                        'cart_id' => $cart_header_id,
                        'article' => $item->article_id,
                        ],
                        [
                        'qty' => $item->qty,
                        ]
                    );
                }
            }
            DB::commit();
            return $this->sendSuccessCreated("Cart created/updated successfully.", $cartDetail);
        } catch (\Exception $e) {
            
            DB::rollback();
            Log::info($e->getMessage());
            return $this->sendError("Sorry system can't create/update checkout,  ", 500);
        }
    }

    public function getFlag($article)
    {
        Log::info("[GETFLAG] ArticleID ". $article);
        
        $flag = [];
        $date = now()->format('Y-m-d');
        $articleData = Product::Where('article', $article)->first();
        if(!empty($articleData)){
            if ($articleData->transfer_date <= $date && $articleData->expired_date >= $date) {
                array_push($flag, 'NEW');
            }
            if ($articleData->is_custom_logo || $articleData->is_custom_size) {
                array_push($flag, 'CUSTOM');
            }
            array_push($flag, $articleData->lvl4_description);
        }
        return $flag;
    }

    public function getLimitCustomer()
    {
        $custId = auth()->user()->customer->customer_id;
        $client = new GetLimitRepo();

        $request = [
                "source" => "CAREOM",
                "destination" => "PLF",
                "detailPlafond" => [
                    [
                        "destination" => "PLF",
                        "customer" => $custId
                    ]
                ]
            ];
        $dataResult =[];
        try {

                $response = $client->getLimit($request, $custId);
                $dataResult = $client->getData($response);
                // Log::channel('stderr')->info($limit);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
        
        return $dataResult;
    
    }

    public function getSalesAssigment($initialSalesId){
        $salesAssignment = $initialSalesId;
        for ($i = 1; $i <= 3; $i++) {
            // Mengambil data atasan dari sales ID sebelumnya
            $superior = DB::table('sales_assignment')
                        ->where('sales_id', $initialSalesId)
                        ->value('sales_direct_to_id');

            // Jika tidak ada atasan, maka keluar dari loop
            if (!$superior) {
                break;
            }

            // Menampilkan atasan
            $salesAssignment = $salesAssignment."#".$superior;

            // Mengubah sales ID awal menjadi atasan
            $initialSalesId = $superior;
        }

        return $salesAssignment;

    }


    function sendEmail($to, $subject, $message) {
        $data = array(
            'to' => $to,
            'subject' => $subject,
            'message' => $message
        );
        
        Mail::to($to)->send(new SendMail($data));
    }

    public function simulateDiscountB2B($datas){
        try {
            $bruto = 0;
            $qty = 0;
            foreach($datas['data'] as $data)
            {
                $article = Product::where('article',$data['article'])->first();
                if($article != null)
                {

                    $category = $article->lvl3_description == 'BAGS' ? 'BAGS' : 'NON BAGS';
                    $bruto += ($article->price->amount+($data['custom_price']??0))*$data['qty'];
                    $qty += $data['qty'];
                    
                } else {
                    $failedMessage[] = [
                        'Article' => $data['article'],
                        'Message' => 'Not Found'
                    ];
                }
            }

            $m_d = DB::table('matrix_discount')
                                ->where('is_custom',$datas['is_custom']??0)
                                ->whereRaw('? BETWEEN min_bruto_from AND min_bruto_to',[(int)$bruto])
                                ->whereRaw('? BETWEEN qty_from AND qty_to',[(int)$qty])
                                ->where('category',$category)
                                ->first();

            Log::info([
                'total_discount' => $m_d->discount*($bruto/100),
                'error' => @$failedMessage??null
            ]);

            return [
                'total_discount' => $m_d->discount*($bruto/100),
                'category' => $category,
                'discount' => $m_d->discount
            ];

        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }
    }

    public function rating(Request $request){
        $validator = Validator::make($request->all(), [
           'rating_value' => 'required|integer|between:0,5', // Ensure 'data_update' is an array
           'order_no' => 'required', // Ensure 'data_update' is an array
        ]);

        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try{

            $rating = Rating::create([
                "order_no" => $request->order_no,
                "rating_value" => $request->rating_value,
                "comment" => $request->comment
            ]);

            return $this->sendSuccess("Rating Created successfully", $rating);
        } catch (\Exception $e) {
            $errors = $e->getMessage();
            return $this->sendError($errors, 400);
        }
    }

}