<?php

namespace App\Http\Controllers;

use App\Exports\TransactionsExport;
use DateTime;
use Exception;
use Carbon\Carbon;
use App\Models\Order;
use App\Services\SAP;
use App\Models\Article;
use App\Models\Invoice;
use App\Models\Product;
use App\Models\Customer;
use App\Models\OrderItem;
use App\Helpers\FileHelper;
use App\Models\Transaction;
use App\Models\ImageVariant;

use Illuminate\Http\Request;
use App\Models\CustomerSales;
use App\Models\DeliveryOrder;
use App\Models\InvoiceDetail;
use App\Models\MasterParameter;
use App\Models\SalesAssignment;
use App\Models\TransactionItem;
use App\Models\CustomerShipment;
use App\Repositories\GetLimitRepo;
use Illuminate\Support\Facades\DB;
use App\Models\DeliveryOrderDetail;
use App\Models\TransactionInternal;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Http\Resources\DownloadCSVResource;
use App\Models\OrderApproval;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use Symfony\Component\HttpFoundation\StreamedResponse;

class TransactionInternalController extends Controller
{
  use FileHelper;
  const OrderStatusWait = 'Menunggu Konfirmasi';
  const OrderStatusPending = 'Pending';
  const OrderStatusOnHold = 'On Hold';
  const OrderStatusOnProcess = 'Diproses';
  const OrderStatusBaru = 'Baru';
  const OrderStatusGI = 'Siap Dikirim';
  const OrderStatusOnShipping = 'Dikirim';
  const OrderStatusDelivered = 'Diterima';
  const OrderStatusFinish = 'Selesai';
  const OrderStatusCancel = 'Batal';
  const OrderStatusPembayaran = 'Pembayaran';
  const OrderStatusSemua = 'Semua';
  const TokoCash = 'Cash';


  public function getTransactionsForInternal(Request $request)
  {
    $salesId = auth()->user()->reference_id;
    Log::channel('stderr')->info($salesId);

    $status = $request->input('status');
    $dateFrom = $request->input('date_from');
    $dateTo = $request->input('date_to');
    $text = $request->input('text');
    $page = $request->input('page');
    $perPage = $request->input('per_page');

    $data = [];

    $Orders = Order::leftJoin('order_detail', 'order_header.order_no', '=', 'order_detail.order_no')
      ->leftJoin('delivery_order', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
      ->leftJoin('customers', 'order_header.customer_id', '=', 'customers.customer_id')
      ->when($status, function ($query) use ($status) {
        return $query->where('order_header.order_status', '=', $status);
      })
      ->when($dateFrom, function ($query) use ($dateFrom) {
        return $query->whereDate('order_header.created_date', ">=", $dateFrom);
      })
      ->when($dateTo, function ($query) use ($dateTo) {
        return $query->whereDate('order_header.created_date', "<=", $dateTo);
      })
      ->when($text, function ($query) use ($text) {
        return $query->where('order_header.bill_to', 'like', '%' . $text . '%');
      })
      ->where('order_header.sales_id', $salesId)
      ->select('order_header.*', 'order_header.bill_to as customer_name', DB::raw('COALESCE(delivery_order.total, 0) as delivery_total'))
      ->groupBy('order_header.order_no')
      ->paginate($perPage, ['*'], 'page', $page);

    foreach ($Orders as $order) {
      $trx = new TransactionInternal();
      $trx->customer_name = $order->customer_name;
      $trx->order_no = $order->order_no;
      $trx->transaction_date = $order->created_date;
      $trx->status = $order->order_status;
      $trx->total = (int)$order->total;

      if ($order->order_status != self::OrderStatusOnHold) {
        $trx->total = (int)($order->delivery_total);
      }

      $data[] = $trx;
    }

    $response = [
      'data' => $data,
      'pagination' => [
        'total' => $Orders->total(),
        'per_page' => (int)$Orders->perPage(),
        'current_page' => $Orders->currentPage(),
        'last_page' => $Orders->lastPage(),
        'from' => (int)$Orders->firstItem(),
        'to' => (int)$Orders->lastItem()
      ]
    ];

    return $this->sendSuccess(null, $response);
  }

  public function getTransactionsForInternalSummary(Request $request)
  {
    $dateFrom = $request->input('date_from');
    $dateTo = $request->input('date_to');
    $salesId = auth()->user()->reference_id;

    $data = [];
    $customerIdList = CustomerSales::where('sales_id', $salesId)->pluck('customer_id');

    $Orders = Order::leftJoin('delivery_order', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
      ->leftJoin('customers', 'order_header.customer_id', '=', 'customers.customer_id')
      ->leftJoin('customer_shipment', 'customers.customer_id', '=', 'customer_shipment.customer_id')
      ->whereIn('order_header.customer_id', $customerIdList)
      ->when($dateFrom, function ($query) use ($dateFrom) {
        return $query->whereDate('order_header.created_date', ">=", $dateFrom);
      })
      ->when($dateTo, function ($query) use ($dateTo) {
        return $query->whereDate('order_header.created_date', "<=", $dateTo);
      })
      ->select('order_header.*', 'order_header.bill_to as customer_name', 'delivery_order.delivery_order_no', 'order_header.ship_to as store_name', DB::raw('COALESCE(delivery_order.total, 0) as delivery_total'))
      ->orderBy('order_header.created_date', 'desc')->get();

    $total_amount = 0;
    $total_sales = 0;
    foreach ($Orders as $order) {
      $trx = new TransactionInternal();
      $trx->customer_name = $order->customer_name;
      $trx->store_name = $order->store_name ?? "";
      $trx->order_no = $order->order_no;
      $trx->transaction_date = $order->created_date;
      $trx->status = $order->order_status;

      if ($order->order_status == self::OrderStatusOnHold) {
        $trx->total = (int)($order->total ?? 0);
        $total_amount = $total_amount + ($order->total ?? 0);

        $orderItems = OrderItem::where('order_no', $order->order_no)->get();
        if ($orderItems != null) {
          $total_sales = $total_sales + $orderItems->sum('qty');
        }
      } else {
        $trx->total = (int)($order->delivery_total ?? 0);
        $total_amount = $total_amount + ($order->delivery_total ?? 0);

        $orderItems = DeliveryOrderDetail::where('delivery_order_no', $order->order_no)->get();
        if ($orderItems != null) {
          $total_sales = $total_sales + $orderItems->sum('issued_qty');
        }
      }

      $data[] = $trx;
    }

    $total_transactions = $Orders->count() ?? 0;
    $sales_average = 0;
    if ($total_transactions != 0) {
      $sales_average = $total_amount / $total_transactions;
    }

    $limitededData = array_slice($data, 0, 5);

    $response = [
      'total_transactions' => $total_transactions,
      'total_transactions_amount' => $total_amount,
      'total_sales' => $total_sales,
      'sales_average' => round($sales_average, 0),
      'orders' => $limitededData,
    ];

    return $this->sendSuccess(null, $response);
  }

  public function getTransactionDetail(Request $request, $order_no)
  {

    $trx = new Transaction();
    $allItems = [];
    $nomorresi = [];
    $limit = 500;
    $page = $request->input('page');
    if ($perPage = $request->query('limit')) {
      $limit = $perPage;
    }

    $order = Order::with('items')->where('order_no', $order_no)->first();
    // $order = Order::with('items:order_detail_id,order_no,article_id,is_custom,product_name,product_variant,product_size,price,sub_total,qty,issued_qty,primary_discount,additional_discount,total');
    // $totalHarga = $order->total == 0 ? $order->items->sum('total') : $order->total;
    // $trx->order_group_id = $order->order_group_id ? $order->order_group_id : null;
    
    $invoice_data = DB::table('invoice')
      ->where('invoice.order_no', $order_no)
      ->where('invoice.status', 'LUNAS')
      ->first();

    if ($order == null) {
      return $this->sendError("Data transaction not found", 404);
    }

    $dataLimit = [];

    // $total_sub = 0;
    // $total_all = 0;
    $allOrderItems = OrderItem::where('order_no', $order->order_no);
    $unavailableItems = $allOrderItems->sum('qty') - $allOrderItems->sum('issued_qty');

    if (!in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru, self::OrderStatusCancel])) {
      $DeliveryOrder = DB::table('delivery_order')
        ->leftJoin('order_header', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
        ->leftJoin('invoice', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
        ->leftJoin('proforma_invoice', 'order_header.sales_order_no', '=', 'proforma_invoice.sales_order_no')
        ->where('delivery_order.sales_order_no', $order->sales_order_no)
        ->orderBy('delivery_order.created_date','desc')
        ->select('delivery_order.*', 'invoice.invoice_no', 'proforma_invoice.id as pi_id', 'proforma_invoice.created_date as payment_date')
        ->first();

      if ($DeliveryOrder != null and $DeliveryOrder->invoice_no != null) {
        $nomorresi = DB::table('delivery_number')
          ->leftJoin('invoice', 'invoice.invoice_no', '=', 'delivery_number.invoice_no')
          ->where('invoice.invoice_no', $DeliveryOrder->invoice_no)
          ->select('delivery_number.delivery_no as noresi', 'delivery_number.delivery_name as namaresi')
          ->get()
          ->toArray();
      }

      $DeliveryOrderDetail = DeliveryOrderDetail::leftJoin('article as p', 'p.article', '=', 'd.article')
        ->from('delivery_order_detail as d')
        ->select('d.*', 'p.sku_code_c', 'p.product_name_c', 'p.article', 'p.product_variant_c')
        ->where('d.delivery_order_no', $DeliveryOrder->delivery_order_no ?? "")
        ->orderBy('d.delivery_order_detail_id', 'desc')
        ->get();

      $tokoType = $order->customer->top_days;
      $isCash = in_array($tokoType, ['Cash', '0']);
      $isLunas = isset($invoice_data) && $invoice_data->status === 'LUNAS';
      
      if (!$isCash || $isLunas) {
          $trx->payment_date = $invoice_data->modified_date ?? "";
          $trx->gi_date = $DeliveryOrder->good_issue_date ?? "";
          $trx->status = $order->order_status;
      } else {
          $trx->payment_date = "";
          $trx->gi_date = "";
          $trx->status = $order->order_status !== 'Diproses' ? 'Pembayaran' : $order->order_status;
      }

      // $total_all =  (int)($DeliveryOrder->total ?? 0);
      $location_code = $order->location_code ?? null;
      $location_name = $order->location_name ?? null;
      $trx->order_no = $order->order_no;
      $trx->customer_shipment_id = isset($DeliveryOrder->customer_shipment_id) ? (int)$DeliveryOrder->customer_shipment_id : (int)$order->customer_shipment_id;
      $trx->bill_to = $order->bill_to;
      $trx->bill_to_address = $order->bill_to_address;
      $trx->bill_to_phone_number = $order->bill_to_phone_number;
      $trx->bill_to_email = $order->bill_to_email;
      $trx->ship_to = $order->ship_to;
      $trx->ship_to_address = $order->ship_to_address;
      $trx->ship_to_phone_number = $order->ship_to_phone_number;
      $trx->sales_order_no = $DeliveryOrder->sales_order_no ?? "";
      $trx->total_discount = in_array($order->order_status, ['Baru', 'Menunggu Verifikasi', 'Menunggu Konfirmasi']) ? (int)$order->total_discount : ($DeliveryOrder != null ? (int)$DeliveryOrder->discount : 0);
      $trx->total_do = (int)($DeliveryOrder->total ?? 0);
      $trx->total_nett = (int)$order->total_nett;
      $trx->customer_id = $DeliveryOrder->customer_id ?? "";
      // $trx->status = $order->order_status;
      $trx->delivery_order_no = $DeliveryOrder->delivery_order_no ?? "";
      $trx->transaction_date = $order->created_date;
      $trx->do_date = $DeliveryOrder->created_date ?? "";
      // $trx->gi_date = $DeliveryOrder->good_issue_date ?? "";
      $trx->shipping_date = $order->shipping_date ?? "";
      $trx->received_date = $order->completed_date ?? "";
      $trx->invoice_no = $DeliveryOrder->invoice_no ?? "";
      $trx->proforma_invoice_no = $DeliveryOrder->pi_id ?? "";
      $trx->order_summary = $invoice_data->status ?? "";
      $trx->no_resi = $nomorresi;
      $trx->pks_file_path = $order->pks_file_path ?? "";
      $trx->pks_file_name = $order->pks_file_name ?? "";
      $trx->distribution_channel = $order->distribution_channel;
      $trx->verifikasi_date = OrderApproval::where('order_no', $order->order_no)->where('status', 'Approved')->pluck('action_date')->first();
      $trx->cancel_date = $order->status == 'Batal' ? $order->modified_date : null;
      $trx->transportation_zone = $location_code . ' - ' . $location_name;
      $trx->total_available = $DeliveryOrderDetail->sum('qty');
      $trx->total_unavailable = $unavailableItems;
      $trx->customer_shipment = $order->customer->shipments()
        ->select('customer_shipment_id', 'customer_id', 'name', 'address', 'city', 'province', 'district', 'zip_code', 'shipment_type', 'phone_number')
        ->get();
      $trx->customer_sales = $order->customer_sales->sales()
        ->select('sales_name', 'phone_number')
        ->first();
      $trx->customer_type = $isCash ? 'Cash' : 'Tempo';

      $items = [];

      $trx->data_limit = $dataLimit ?? [];
      
      $sub_total = 0;
      foreach ($DeliveryOrderDetail as $item) {
        $data_order_detail = DB::table('order_detail')
          ->leftJoin('order_header', 'order_header.order_no', '=', 'order_detail.order_no')
          ->where('order_header.sales_order_no', $order->sales_order_no)
          ->where('order_detail.article_id', $item->article)
          ->select('order_detail.price', 'order_detail.is_available', 'order_detail.stock', 'order_detail.total', 'order_detail.qty', 'order_detail.issued_qty')
          ->first();

        $article = Product::where('article', $item->article)->first();
        // $total_sub += (int)($item->qty * ($article->price ?$article->price->amount : 0));

        $image = DB::table('article as art')
          ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
          ->where('art.article', $item->article)
          ->where('ig.is_main_image', 1)
          ->select('ig.file_path')
          ->first();

        $warna = DB::table('master_color')
          ->where('key', $item->product_variant)
          ->where('is_active', 1)
          ->pluck('value')->first();
        
        $problem_qty = $data_order_detail->qty - $data_order_detail->issued_qty;
        // $total_sub += (int)($item->qty * ($article->price ? $article->price->amount : 0));
        $product_items = [];
        $itemData = new TransactionItem();
        $itemData->article = $item->article;
        $itemData->product_name = $item->product_name;
        $itemData->product_variant = $warna ?? $item->product_variant;
        $itemData->product_size = $item->product_size;
        $itemData->stock = $data_order_detail->stock ?? 0;
        // $itemData->qty = $item->qty;
        $itemData->qty = $request->is_available == 'false' ? $problem_qty  : $data_order_detail->issued_qty;
        $itemData->issued_qty = $data_order_detail->issued_qty;
        // $itemData->is_available = $data_order_detail->is_available ?? 0;
        $itemData->merchandise_category = $item->merchandise_category;
        $itemData->sub_total = $item->sub_total != 0 ? $item->sub_total : $data_order_detail->total;
        // $itemData->sub_total = (int) $request->is_available == 'false' ? ($problem_qty * $article->price->amount) : ($item->sub_total != 0 ? $item->sub_total : $data_order_detail->total);
        $itemData->location = $item->location;
        $sub_total += $itemData->sub_total;
        array_push($product_items, $itemData);

        $flag = $this->getFlag($item->article);

        $productItem = [
          'sku_code_c' => $item->sku_code_c,
          'product_name_c' => $item->product_name_c,
          "image_url" => @$image->file_path == null ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL') . $image->file_path,
          'flag' => $flag,
          'base_price' => $data_order_detail->price ?? 0,
          'sub_total' => 0,
          'total_product' => 0,
          'product_items' => $product_items
        ];
        array_push($items, $productItem);
      }

      $trx->sub_total_do = $sub_total;
      $trx->total = $order->total == 0 ? $order->items->sum('total') : $order->total;
      
      // $counts = array_reduce($items, function ($carry, $item) {
      //     foreach ($item['product_items'] as $product) {
      //         if ($product['is_available']) {
      //             $carry['available'] += $product['qty'];
      //         } else {
      //             $carry['unavailable'] += $product['qty'];
      //         }
      //     }
      //     return $carry;
      // }, ['available' => 0, 'unavailable' => 0]);

      // $trx->total_available = $counts['available'];
      // $trx->total_unavailable = $counts['unavailable'];

      // $isAvailable = filter_var($request->query('is_available', true), FILTER_VALIDATE_BOOLEAN);
      // $isAvailableInt = $isAvailable ? 1 : 0;
      
      // $filteredItems = array_map(function ($item) use ($isAvailableInt) {
      //   $filteredProductItems = array_filter($item['product_items'], fn($product) => 
      //       $product['is_available'] === $isAvailableInt
      //   );

      //   if (empty($filteredProductItems)) {
      //       return null;
      //   }

      //   $item['product_items'] = array_values($filteredProductItems);
      //   return $item;
      // }, $items);

      // $filteredItems = array_values(array_filter($filteredItems));

      $allItems[] = $items;

    } else {
      $simulate = [];
      $order = Order::where('order_no', $order_no)->first();
      // $total_all = $order->total;
      $data = DB::table('delivery_order')
        ->leftJoin('order_header', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
        ->leftJoin('invoice', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
        ->leftJoin('proforma_invoice', 'order_header.sales_order_no', '=', 'proforma_invoice.sales_order_no')
        ->where('delivery_order.sales_order_no', $order->sales_order_no)
        ->select('delivery_order.*', 'invoice.invoice_no', 'proforma_invoice.id as pi_id', 'proforma_invoice.created_date as payment_date')
        ->first();

      if ($data != null and $data->invoice_no != null) {
        $nomorresi = DB::table('delivery_number')
          ->leftJoin('invoice', 'invoice.invoice_no', '=', 'delivery_number.invoice_no')
          ->where('invoice.invoice_no', $data->invoice_no)
          ->select('delivery_number.delivery_no as noresi', 'delivery_number.delivery_name as namaresi')
          ->get()
          ->toArray();
      }

      if (isset($invoice_data) && $invoice_data->status === 'LUNAS') {
        $trx->payment_date = $invoice_data->modified_date;
      } else {
        $trx->payment_date = "";
      }

      $location_code = $order->location_code ?? null;
      $location_name = $order->location_name ?? null;
      $trx->sap_response = $simulate ?? null;
      $trx->order_no = $order->order_no;
      $trx->customer_shipment_id = $order->customer_shipment_id ?? null;
      $trx->bill_to = $order->bill_to;
      $trx->bill_to_address = $order->bill_to_address;
      $trx->bill_to_phone_number = $order->bill_to_phone_number;
      $trx->bill_to_email = $order->bill_to_email;
      $trx->ship_to = $order->ship_to;
      $trx->ship_to_address = $order->ship_to_address;
      $trx->ship_to_phone_number = $order->ship_to_phone_number;
      // $trx->sub_total = (int)$order->sub_total;
      $trx->total = (int)$order->total;
      $trx->total_discount = (int)$order->total_discount ?? 0;
      $trx->total_nett = (int)$order->total_nett;
      $trx->customer_id = (int)$order->customer_id;
      $trx->sales_order_no = $order->sales_order_no ?? "";
      $trx->status = $order->order_status;
      $trx->delivery_order_no = $order->delivery_no ?? "";
      $trx->payment_date = "";
      $trx->transaction_date = $order->created_date;
      $trx->do_date = $data->created_date ?? "";
      $trx->gi_date = $data->good_issue_date ?? "";
      $trx->shipping_date = $order->shipping_date ?? "";
      $trx->received_date = $order->completed_date ?? "";
      $trx->invoice_no = $data->invoice_no ?? "";
      $trx->proforma_invoice_no = $data->pi_id ?? "";
      $trx->order_summary = $invoice_data->status ?? "";
      $trx->no_resi = $nomorresi;
      $trx->distribution_channel = $order->distribution_channel;
      $trx->verifikasi_date = OrderApproval::where('order_no', $order->order_no)->where('status', 'Approved')->pluck('action_date')->first();
      $trx->transportation_zone = $location_code . ' - ' . $location_name;
      $trx->cancel_date = $order->status == 'Batal' ? $order->modified_date : null;
      $trx->total_available= $allOrderItems->sum('qty') - $unavailableItems;
      $trx->total_unavailable = $unavailableItems;
      $trx->data_limit =  $dataLimit ?? [];

      //get order detail
      $orderItems = OrderItem::leftJoin('article', 'order_detail.article_id', '=', 'article.article')
        ->select('order_detail.*', 'article.sku_code_c', 'article.product_name_c', 'article.product_variant_c')
        ->where('order_no', $order->order_no)->orderBy('order_detail.order_detail_id', 'desc')
        ->get();

      $items = [];
      $total_sub = 0;
      foreach ($orderItems as $item) {
        $image = DB::table('article as art')
          ->leftJoin('image_generic as ig', 'ig.sku_code_c', '=', 'art.sku_code_c')
          ->where('art.article', $item->article_id)
          ->where('ig.is_main_image', 1)
          ->select('ig.file_path')
          ->first();

        $article = Product::where('article', $item->article_id)->first();
        $warna = DB::table('master_color')
          ->where('key', $item->product_variant_c)
          ->where('is_active', 1)
          ->pluck('value')->first();

        $problem_qty = $item->qty - $item->issued_qty;
        $product_items = [];
        // $total_sub += (int)($item->qty * ($article->price ?$article->price->amount : 0));
        $itemData = new TransactionItem();
        $itemData->article = $item->article_id;
        $itemData->product_name = $item->product_name;
        $itemData->product_variant = $warna ?? $item->product_variant_c;
        $itemData->product_size = $item->product_size;
        $itemData->stock = $item->stock ?? 0;
        $itemData->issued_qty = $item->issued_qty;
        // $itemData->qty = $item->qty;
        $itemData->qty = $request->is_available == 'false' ? $problem_qty : $item->issued_qty;
        // $itemData->sub_total = (int) $request->is_available == 'false' ? $problem_qty * ($article->price ? $article->price->amount : 0) : ($item->sub_total ?? 0);
        // $itemData->is_available = $item->is_available ?? 0;
        $itemData->sub_total = $item->sub_total ?? 0;
        array_push($product_items, $itemData);

        $flag = $this->getFlag($item->article_id);
        
        $productItem = [
          'sku_code_c' => $item->sku_code_c,
          'product_name_c' => $item->product_name_c,
          "image_url" => @$image->file_path == null ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL') . $image->file_path,
          'flag' => $flag,
          'base_price' => $item->price ?? 0,
          'sub_total' => 0,
          'total_product' => 0,
          'product_items' => $product_items,
        ];
        
        $cdate = now()->format('Y-m-d');
        $price =  DB::table('article_price')
          ->where('article_price.sku_code_c', $item->sku_code_c)
          ->where('valid_from', '<=', $cdate)
          ->where('valid_to', '>=', $cdate)
          ->orderBy('valid_from', 'desc')
          ->select('article_price.amount')
          ->first();

        if (in_array($order->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru])) {
          $productItem['base_price'] = $price->amount;
        }

        array_push($items, $productItem);
      }

      // $counts = array_reduce($items, function ($carry, $item) {
      //     foreach ($item['product_items'] as $product) {
      //         if ($product['is_available']) {
      //             $carry['available'] += $product['qty'];
      //         } else {
      //             $carry['unavailable'] += $product['qty'];
      //         }
      //     }
      //     return $carry;
      // }, ['available' => 0, 'unavailable' => 0]);

      // $trx->total_available = $counts['available'];
      // $trx->total_unavailable = $counts['unavailable'];

      // $isAvailable = filter_var($request->query('is_available', true), FILTER_VALIDATE_BOOLEAN);
      // $isAvailableInt = $isAvailable ? 1 : 0;

      // $filteredItems = array_map(function ($item) use ($isAvailableInt) {
      //   $filteredProductItems = array_filter($item['product_items'], fn($product) => 
      //       $product['is_available'] === $isAvailableInt
      //   );

      //   if (empty($filteredProductItems)) {
      //       return null;
      //   }

      //   $item['product_items'] = array_values($filteredProductItems);
      //   return $item;
      // }, $items);

      // $filteredItems = array_values(array_filter($filteredItems));
      // $total_all = $totalHarga;
      $allItems[] = $items;
    }

    //grouping array
    $groupedItems = [];
    foreach ($allItems as $item) {
      foreach ($item as $subitem) {
        if (!isset($groupedItems[$subitem['sku_code_c']])) {
          $groupedItems[$subitem['sku_code_c']] = [
            'sku_code_c' => $subitem['sku_code_c'],
            'product_name_c' => $subitem['product_name_c'],
            "image_url" =>   $subitem['image_url'],
            'flag' => $subitem['flag'],
            'base_price' => $subitem['base_price'],
            'sub_total' => 0,
            'total_product' => 0,
            'product_items' => []
          ];
        }
        $groupedItems[$subitem['sku_code_c']]['product_items'] = array_merge($groupedItems[$subitem['sku_code_c']]['product_items'], $subitem['product_items']);
      }
    }

    //count sub total and total products per sku
    foreach ($groupedItems as &$groupedItem) {
      foreach ($groupedItem['product_items'] as $productItem) {
        $groupedItem['sub_total'] += $productItem['sub_total'];
        $groupedItem['total_product'] += $productItem['issued_qty'];
        // if ($productItem['is_available'] == 0 || $productItem['is_available'] == 1) {
        //   $groupedItem['total_product']++;
        // }
      }
    }

    $groupedItems = array_values($groupedItems);
    $offset = ($page - 1) * $limit;
    $total = count($groupedItems);
    $groupedItems = array_slice($groupedItems, $offset, $limit);
    // $trx->sub_total = $totalHarga;
    // $trx->sub_nett = $totalHarga;
    // $trx->total = (int) $total_all;
    // $trx->total_nett = (int) $trx->sub_total - (int) $trx->total_discount;
    $trx->items = $groupedItems;

    $trx->customer_shipment = $order->customer->shipments()
      ->select('customer_shipment_id', 'customer_id', 'name', 'address', 'city', 'province', 'district', 'zip_code', 'shipment_type', 'phone_number')
      ->get();
    $trx->customer_sales = $order->customer_sales->sales()
      ->select('sales_name', 'phone_number')
      ->first();
    $trx->customer_type = $order->customer->top_days == '0' ? 'cash' : 'tempo' ?? 'tempo';

    $fisrt = ($page - 1) * $limit + 1;
    $last = min($page * $limit, count($groupedItems));

    $data = [
      'total_data' => $total,
      'size' => (int)$limit,
      'active_page' => (int)$page,
      'total_page' => ceil($total / $limit),
      'order_detail' => $trx,
    ];
    return $this->sendSuccess(null, $data);
  }

  public function getTransactions(Request $request)
  {
      $salesId = auth()->user()->reference_id;
      $roles = @auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
      Log::channel('stderr')->info($roles);
  
      $status = $request->input('status');
      $dateFrom = $request->input('date_from');
      $dateTo = $request->input('date_to');
      $page = $request->input('page');
      $order_no = $request->input('order_no');
      $owner_name = $request->input('owner_name');
      $store_type = $request->input('store_type');
      $store_name = $request->input('store_name');
      $ref_order = $request->input('ref_order');
      $sales_name = $request->input('sales_name');
      $limit = $request->query('limit', 12);
      $data = [];
  
      $salesIdd = !in_array("0", $roles)
          ? array_map(function ($i) {
              return $i->sales_id ?? '0';
          }, $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first()))
          : [];
      array_push($salesIdd, $salesId);
  
      $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)->pluck('customer_id')->all();
  
      $Orders = Order::with('invoice')
          ->leftJoin('invoice', 'order_header.sales_order_no', '=', 'invoice.sales_order_no')
          ->leftJoin('order_detail', 'order_header.order_no', '=', 'order_detail.order_no')
          ->leftJoin('article', 'order_detail.article_id', '=', 'article.article')
          ->leftJoin('delivery_order', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
          ->leftJoin('customers', 'order_header.customer_id', '=', 'customers.customer_id')
          ->leftJoin('customer_shipment', 'customers.customer_id', '=', 'customer_shipment.customer_id')
          ->leftJoin('customer_sales', 'order_header.customer_id', '=', 'customer_sales.customer_id')
          ->leftJoin('sales', 'customer_sales.sales_id', '=', 'sales.sales_id')
          ->where('order_header.distribution_channel', 'WHOLESALES')
          ->when($store_type, function ($query) use ($store_type) {
              if ($store_type == 'toko_cash') {
                  $query->where(function ($q) {
                      $q->where('customers.top_days', 'LIKE', '0')->orWhereNull('customers.top_days');
                  });
              } elseif ($store_type == 'toko_tempo') {
                  $query->where('customers.top_days', 'NOT LIKE', '0');
              }
          })
          ->when($dateFrom, fn($q) => $q->whereDate('delivery_order.created_date', ">=", $dateFrom))
          ->when($dateTo, fn($q) => $q->whereDate('delivery_order.created_date', "<=", $dateTo))
          ->when($ref_order, fn($q) => $q->where('order_header.sales_order_no', 'like', '%' . $ref_order . '%'))
          ->when($owner_name, fn($q) => $q->where('order_header.bill_to', 'like', '%' . $owner_name . '%'))
          ->when($order_no, fn($q) => $q->where('order_header.order_no', 'like', '%' . $order_no . '%'))
          ->when($store_name, fn($q) => $q->where('order_header.ship_to', 'like', '%' . $store_name . '%'))
          ->when($sales_name, fn($q) => $q->where('order_header.sales_name', 'like', '%' . $sales_name . '%'))
          ->when($status, function ($query) use ($status) {
              $q_status = collect(explode(',', $status))->map(fn($s) => str_replace('_', ' ', $s))->toArray();
              $placeholders = implode(',', array_fill(0, count($q_status), '?'));
              return $query->havingRaw("LOWER(display_status) in ({$placeholders})", $q_status);
          });
  
      if (!in_array("0", $roles)) {
          $Orders->whereIn('order_header.customer_id', $customerIdList);
      }
  
      $Orders = $Orders
        ->selectRaw("
          order_header.*,
          customers.top_days as top_days,
          order_header.bill_to as owner_name,
          order_header.ship_to as store_name,
          order_header.sales_name as nama_sales,
          CASE
              WHEN (customers.top_days IS NULL OR customers.top_days = '0')
                  AND order_header.order_status NOT IN (?, ?, ?, ?, ?, ?)
                  AND (invoice.status IS NULL OR invoice.status != 'LUNAS')
              THEN ?
              ELSE order_header.order_status
          END as display_status
          ", [
            self::OrderStatusPending,
            self::OrderStatusOnHold,
            self::OrderStatusWait,
            self::OrderStatusBaru,
            self::OrderStatusCancel,
            self::OrderStatusOnProcess,
            self::OrderStatusPembayaran
          ])  
        ->groupBy('order_header.order_no')
        ->orderBy('order_header.created_date', 'desc')
        ->paginate($limit, ['*'], 'page', $page);
  
      foreach ($Orders as $order) {
          $DeliveryOrders = DeliveryOrder::where('sales_order_no', $order->sales_order_no)->orderBy('created_date', 'desc')->first();
  
          if ($DeliveryOrders) {
              $doi = DeliveryOrderDetail::where('delivery_order_no', $DeliveryOrders->delivery_order_no)
                  ->with('product.price')->get()->toArray();
              $total_do = array_sum(array_map(fn($item) => (int)$item['product']['price']['amount'] * (int)$item['qty'], $doi)) ?? 0;
          }
  
          $trx = new Transaction();
          $trx->order_no = $order->order_no;
          $trx->transaction_date = Carbon::parse($order->created_date)->format('Y-m-d H:i:s');
          $trx->do_date = $DeliveryOrders?->created_date ? Carbon::parse($DeliveryOrders->created_date)->format('Y-m-d H:i:s') : "";
          $trx->store_type = ($order->top_days == null || $order->top_days == "0") ? "cash" : "tempo";
          $trx->ref_order = $order->sales_order_no;
          $trx->customer_name = $order->owner_name;
          $trx->store_name = $order->store_name;
          $trx->order_status = $order->display_status;
          $trx->sales_name = $order->nama_sales ?? 'Tidak Ada';
          $trx->total_transaksi = (int)$order->total_nett;
          $trx->total_nett = $order->invoice != null ? (int)$order->invoice->nett_price : ($DeliveryOrders != null ? ($total_do - $DeliveryOrders->discount) : (int)$order->total_nett);
          $trx->total_discount = $DeliveryOrders != null ? $DeliveryOrders->discount : ((int)$order->total_discount ?? 0);
          $trx->gross = $DeliveryOrders != null ? $total_do : ((int)$order->total ?? 0);
          $data[] = $trx;
      }
  
      $latest = $Orders->pluck('modified_date')->sortByDesc('modified_date')->first() ?? date('Y-m-d H:i:s');
  
      return $this->sendSuccess(null, [
          'latest_update' => $latest,
          'total_data' => $Orders->total(),
          'size' => intval($Orders->perPage()),
          'active_page' => $Orders->currentPage(),
          'total_page' => $Orders->lastPage(),
          'data' => $data
      ]);
  }  

  public function getStoreType($cust_id)
  {

    $customer = Customer::where('customer_id', $cust_id)->first();

    if ($customer->top_days != self::TokoCash) {
      return "Tempo";
    } else {
      return "Cash";
    }
  }
  public function downloadTransactionsNewBk(Request $request)
  {
    try {
      $salesId = auth()->user()->reference_id;
      $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
      $salesIdd = !in_array("0", $roles) ?
        array_map(
          function ($i) {
            return $i->sales_id ?? '0';
          },
          $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())
        )
        : [];
      array_push($salesIdd, $salesId);
      $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
        ->pluck('customer_id')->all();

      Log::channel('stderr')->info($salesId);
      $status = $request->input('status');
      $dateFrom = $request->input('date_from');
      $dateTo = $request->input('date_to');
      $order_no = $request->input('order_no');
      $owner_name = $request->input('owner_name');
      $store_type = $request->input('store_type');
      $store_name = $request->input('store_name');
      $ref_order = $request->input('ref_order');
      $sales_name = $request->input('sales_name');

      $datas = [];

      $items = DB::table('order_detail')
        ->leftJoin('order_header', 'order_detail.order_no', '=', 'order_header.order_no')
        ->leftJoin('article', 'order_detail.article_id', '=', 'article.article')
        ->leftJoin('customers', 'order_header.customer_id', '=', "customers.customer_id")
        ->leftJoin('customer_shipment', 'order_header.customer_shipment_id', '=', "customer_shipment.customer_shipment_id")
        ->leftJoin('customer_sales', 'order_header.customer_id', '=', 'customer_sales.customer_id')
        ->leftJoin('sales', 'order_header.sales_id', '=', 'sales.sales_id')
        ->leftJoin('delivery_order', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
        ->leftJoin('delivery_order_detail', function ($j) {
          $j->on('delivery_order.delivery_order_no', '=', 'delivery_order_detail.delivery_order_no')
            ->on('order_detail.article_id', '=', 'delivery_order_detail.article');
        })
        ->leftJoin('invoice', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
        ->leftJoin('invoice_detail', function ($j) {
          $j->on('invoice.invoice_no', '=', 'invoice_detail.invoice_no')
            ->on('order_detail.article_id', '=', 'invoice_detail.article');
        })
        ->where('order_header.distribution_channel', 'WHOLESALES')
        ->when($status, function ($query) use ($status) {
          $status = explode(',', $status);
          $q_status = [];
          $i = 0;
          foreach ($status as $data) {
            $cek = str_contains($data, '_');
            if ($cek == true) {
              $q_status[$i] = str_replace('_', ' ', $data);
            } else {
              $q_status[$i] = $data;
            }
            $i++;
          }
          return $query->whereIn('order_header.order_status', $q_status);
        })

        ->when($store_type, function ($query) use ($store_type) {
          if ($store_type == 'toko_cash') {
            $query->where(function ($q) {
              $q->where('customers.top_days', 'LIKE', '0')
                ->orWhereNull('customers.top_days');
            });
          } elseif ($store_type == 'toko_tempo') {
            $query->where(function ($q) {
              $q->where('customers.top_days', 'NOT LIKE', '0');
            });
          } else {
          }
        })
        ->when($dateFrom, function ($query) use ($dateFrom) {
          return $query->whereDate('order_header.created_date', ">=", $dateFrom);
        })
        ->when($dateTo, function ($query) use ($dateTo) {
          return $query->whereDate('order_header.created_date', "<=", $dateTo);
        })
        ->when($ref_order, function ($query) use ($ref_order) {
          return $query->where('order_header.sales_order_no', 'like', '%' . $ref_order . '%');
        })
        ->when($owner_name, function ($query) use ($owner_name) {
          return $query->where('order_header.bill_to', 'like', '%' . $owner_name . '%');
        })
        ->when($order_no, function ($query) use ($order_no) {
          return $query->where('order_header.order_no', 'like', '%' . $order_no . '%');
        })
        ->when($store_name, function ($query) use ($store_name) {
          return $query->where('order_header.ship_to', 'like', '%' . $store_name . '%');
        })
        ->when($sales_name, function ($query) use ($sales_name) {
          return $query->where('order_header.sales_name', 'like', '%' . $sales_name . '%');
        })
        ->when(!in_array("0", $roles), function ($query) use ($customerIdList) {
          return $query->whereIn('order_header.customer_id', $customerIdList);
        })
        ->select(
          'customers.top_days',
          'order_header.bill_to as owner_name',
          'order_header.ship_to as store_name',
          'order_header.sales_name as sales_name',
          'order_header.created_date as order_date',
          'order_header.order_status',
          'order_header.order_no',
          'order_header.sales_order_no',
          'delivery_order.delivery_order_no',
          'invoice.invoice_no',
          'order_detail.article_id',
          'order_detail.product_name',
          'order_detail.product_size',
          'order_detail.product_variant',
          'order_detail.price',
          'order_detail.qty as order_detail_qty',
          'delivery_order_detail.issued_qty as do_detail_issued_qty',
          'delivery_order_detail.qty as do_detail_qty',
          'order_header.total_discount as order_header_discount',
          'delivery_order.discount as delivery_order_discount',
          'invoice_detail.gross_price as invoice_detail_gp',
          'invoice_detail.discount_percent as invoice_detail_discount_percent',
          'article.lvl3_description',
          'article.product_style',
          'delivery_order.good_issue_date as gi_date',
          'invoice.billing_date'
        )
        ->orderBy('order_header.created_date', 'desc')
        ->chunk(100, function ($data) use (&$datas) {
          foreach ($data as $d) {
            // $qty = $d->delivery_order_no == null ? $d->order_detail_qty : ($d->delivery_order_no != null and $d->gi_date != null ? $d->do_detail_issued_qty : $d->do_detail_qty);
            if ($d->delivery_order_no == null) {
                $qty = $d->order_detail_qty;
            } elseif ($d->delivery_order_no != null && $d->gi_date != null) {
                $qty = $d->do_detail_issued_qty;
            } else {
                $qty = $d->do_detail_qty;
            }
            // $discount = $d->invoice_no != null ? $d->invoice_detail_gp * $d->invoice_detail_discount_percent : ($d->delivery_order_discount != null ? $d->delivery_order_discount : $d->order_header_discount);
            if ($d->invoice_no != null) {
              $discount = $d->invoice_detail_gp * ($d->invoice_detail_discount_percent/100);
            } elseif ($d->delivery_order_discount != null) {
                $discount = $d->delivery_order_discount;
            } else {
                $discount = $d->order_header_discount;
            }
            $row = [
              'nama_akun' => $d->owner_name,
              'nama_toko' => $d->store_name,
              'tanggal_pesan' => Carbon::parse($d->order_date)->format('Y-m-d'),
              'status_pesanan' => $d->order_status,
              '#order' => $d->order_no,
              '#ref_order' => $d->sales_order_no,
              '#dn' => $d->delivery_order_no,
              '#billing' => $d->invoice_no,
              'article' => $d->article_id,
              'article_description' => $d->product_name . ' ' . $d->product_variant . ' ' . $d->product_size,
              'price' => (int)$d->price,
              'qty_order' => (int)$qty,
              'diskon' => (int)$discount,
              'sub_total' => ((int)$qty * (int)$d->price) - (int)$discount,
              'item_product' => $d->lvl3_description,
              'product_category' => $d->product_style,
              'nama_sales' => $d->sales_name,
              'tanggal_gi' => $d->delivery_order_no == null ? null : Carbon::parse($d->gi_date)->format('Y-m-d'),
              'tanggal_billing' => $d->invoice_no == null ? null : Carbon::parse($d->billing_date)->format('Y-m-d'),
              'gross' => (int)$qty * (int)$d->price
            ];
            $datas[] = $row;
          }
        });

        return $this->sendSuccess('Export Transaction Wholesales Internal Success',$datas);
    } catch (\Exception $e) {
      Log::info('Export Transaction Wholesales Internal Failed : '.$e->getMessage().'. Line : '.$e->getLine());
      return $this->sendError('Export Transaction Wholesales Internal Failed');
    }
  }

  //TODO DEPRECATED
  public function downloadTransactionsNew(Request $request)
  {


    try {
      $salesId = auth()->user()->reference_id;
      $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
      $salesIdd = !in_array("0", $roles) ?
        array_map(
          function ($i) {
            return $i->sales_id ?? '0';
          },
          $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())
        )
        : [];
      array_push($salesIdd, $salesId);
      $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
        ->pluck('customer_id')->all();

      Log::channel('stderr')->info($salesId);
      $status = $request->input('status');
      $dateFrom = $request->input('date_from');
      $dateTo = $request->input('date_to');
      $order_no = $request->input('order_no');
      $owner_name = $request->input('owner_name');
      $store_type = $request->input('store_type');
      $store_name = $request->input('store_name');
      $ref_order = $request->input('ref_order');
      $sales_name = $request->input('sales_name');

      $datas = [];

      $items = Order::with('items.product', 'delivery_order.items.product', 'customer', 'invoice.detail')
      ->when(!in_array("0", $roles), function ($query) use ($customerIdList) {
          return $query->whereIn('order_header.customer_id', $customerIdList);
      })
        ->where('order_header.distribution_channel', 'WHOLESALES')
        ->when($status, function ($query) use ($status) {
          $status = explode(',', $status);
          $q_status = [];
          $i = 0;
          foreach ($status as $data) {
            $cek = str_contains($data, '_');
            if ($cek == true) {
              $q_status[$i] = str_replace('_', ' ', $data);
            } else {
              $q_status[$i] = $data;
            }
            $i++;
          }
          return $query->whereIn('order_header.order_status', $q_status);
        })

        ->when($store_type, function ($query) use ($store_type) {
          if ($store_type == 'toko_cash') {
            $query->whereHas('customer', function ($query) {
              return $query->where('top_days', 'LIKE', '0')->orWhereNull('customers.top_days');
          });
      
          } elseif ($store_type == 'toko_tempo') {
           $query->whereHas('customer', function ($query) {
              return $query->where('top_days', 'NOT LIKE', '0');
          });
          } else {
          }
        })
        ->when($dateFrom, function ($query) use ($dateFrom) {
          return $query->whereDate('order_header.created_date', ">=", $dateFrom);
        })
        ->when($dateTo, function ($query) use ($dateTo) {
          return $query->whereDate('order_header.created_date', "<=", $dateTo);
        })
        ->when($ref_order, function ($query) use ($ref_order) {
          return $query->where('order_header.sales_order_no', 'like', '%' . $ref_order . '%');
        })
        ->when($owner_name, function ($query) use ($owner_name) {
          return $query->where('order_header.bill_to', 'like', '%' . $owner_name . '%');
        })
        ->when($order_no, function ($query) use ($order_no) {
          return $query->where('order_header.order_no', 'like', '%' . $order_no . '%');
        })
        ->when($store_name, function ($query) use ($store_name) {
          return $query->where('order_header.ship_to', 'like', '%' . $store_name . '%');
        })
        ->when($sales_name, function ($query) use ($sales_name) {
          return $query->where('order_header.sales_name', 'like', '%' . $sales_name . '%');
        })
        ->when(!in_array("0", $roles), function ($query) use ($customerIdList) {
          return $query->whereIn('order_header.customer_id', $customerIdList);
        })
        ->orderBy('order_header.created_date', 'desc')
        ->chunk(100, function ($data) use (&$datas) {
          $prorate_discount = fn($o = 0, $i = 0, $d = 0) => (intval($d) == 0 || intval($i) == 0 || intval($o) == 0) ? 0 : round($i * $d/$o,0);
          foreach($data->toArray() as $i){
            if(is_null($i['delivery_order'])){
              foreach($i['items'] as $order_items){
                    $datas[] = [
                      'nama_akun' => $i['bill_to']??'-',
                      'nama_toko' => $i['ship_to']??'-',
                      'tanggal_pesan' => array_key_exists('created_date',$i)?Carbon::parse($i['created_date']??'')->format('Y-m-d'):'',
                      'status_pesanan' => $i['order_status']??'',
                      '#order' => $i['order_no']??'',
                      '#ref_order' => $i['sales_order_no']??'',
                      '#dn' => '',
                      '#billing' => !is_null($i['invoice']) ? $i['invoice']['invoice_no']??'-' : '-',
                      'article' => $order_items['article_id'],
                      'article_description' => !is_null($order_items['product']) ? $order_items['product_name'] . ' ' . $order_items['product_variant'] . ' ' . $order_items['product_size'] : '-',
                      'price' => (int)$order_items['price'],
                      'qty_order' => (int)$order_items['qty'],
                      'diskon' => (int)$prorate_discount($i['total']??0, $order_items['sub_total']??0, $i['total_discount']??0),
                      'item_product' => $order_items['product']['lvl3_description']??'-',
                      'product_category' => $order_items['product']['product_style']??'-',
                      'nama_sales' => $i['sales_name'],
                      'tanggal_gi' => '-',
                      'tanggal_billing' => '-',
                      'gross' => (int)$order_items['sub_total'],
                      'nett' => (int)$order_items['sub_total'] - $prorate_discount($i['total']??0, $order_items['sub_total']??0, $i['total_discount']??0),
                    ];
                
              }
            }
            else{
              foreach($i['delivery_order']['items'] as $order_items){
                $invoice = !is_null($i['invoice']) ? collect($i['invoice']['detail'])->firstWhere('article', $order_items['article']??'') : [];
                $qty = is_null($order_items['issued_qty']) ? $order_items['qty'] : $order_items['issued_qty'];
                $original = collect($i['items'])->firstWhere('article_id', $order_items['article']??'-')??null;
                $gross = !is_null($i['invoice']) ? $invoice['gross_price']??0 : 
                $qty * ($original ? $original['price'] : 0);
                $net =   !is_null($i['invoice']) ? $invoice['nett_price']??0 : 
                $qty * ($original ? $original['price'] : 0) - $prorate_discount($i['delivery_order']['total']??0, $qty * ($original ? $original['price'] : 0), $i['delivery_order']['discount']??0);
                $discount = !is_null($i['invoice']) ? $gross - $net  :  
                $prorate_discount($i['delivery_order']['total']??0, $qty * ($original ? $original['price'] : 0), $i['delivery_order']['discount']??0);
              
  
  
                $datas[] = [
                  'nama_akun' => $i['bill_to']??'-',
                    'nama_toko' => $i['ship_to']??'-',
                    'tanggal_pesan' => array_key_exists('created_date',$i)?Carbon::parse($i['created_date']??'')->format('Y-m-d'):'',
                    'status_pesanan' => $i['order_status']??'',
                    '#order' => $i['order_no']??'',
                    '#ref_order' => $i['sales_order_no']??'',
                    '#dn' => $i['delivery_order']['delivery_order_no'],
                    '#billing' => !is_null($i['invoice']) ? $i['invoice']['invoice_no']??'-' : '-',
                    'article' => $order_items['article'],
                    'article_description' => !is_null($original) ? $original['product_name'] . ' ' . $original['product_variant'] . ' ' . $original['product_size'] : '-',
                    'price' => (int)($original ? $original['price'] : 0),
                    'qty_order' => $qty,
                    'diskon' => (int)$discount,
                    'item_product' => $order_items['product']['lvl3_description']??'-',
                    'product_category' => $order_items['product']['product_style']??'-',
                    'nama_sales' => $i['sales_name'],
                    'tanggal_gi' =>  is_null($i['delivery_order']['good_issue_date'])?'-': Carbon::parse($i['delivery_order']['good_issue_date'])->format('Y-m-d'),
                    'tanggal_billing' => is_null($i['invoice'])?'-': Carbon::parse($i['invoice']['billing_date'])->format('Y-m-d'),
                    'gross' => $gross,
                    'nett' => $net,
                  ];
              }
            }
       
        };

        });

        return $this->sendSuccess('Export Transaction Wholesales Internal Success',$datas);
    } catch (\Exception $e) {
      Log::info('Export Transaction Wholesales Internal Failed : '.$e->getMessage().'. Line : '.$e->getLine());
      return $e;
    }
  }
    //DEPRECATED

  public function downloadTransactionCSV(Request $request){
    try {
      $salesId = auth()->user()->reference_id;
      $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
      
      $salesIdd = !in_array("0", $roles) 
            ? array_map(fn($i) => $i->sales_id ?? '0', $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())) 
            : [];
      $salesIdd[] = $salesId;

      $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)->pluck('customer_id')->all();

      $filters = [
        'status'      => $request->input('status'),
        'dateFrom'    => $request->input('date_from'),
        'dateTo'      => $request->input('date_to'),
        'order_no'    => $request->input('order_no'),
        'owner_name'  => $request->input('owner_name'),
        'store_type'  => $request->input('store_type'),
        'store_name'  => $request->input('store_name'),
        'ref_order'   => $request->input('ref_order'),
        'sales_name'  => $request->input('sales_name'),
      ];

      if (empty($filters['dateFrom']) || empty($filters['dateTo'])) {
        throw new Exception('Filter date diperlukan. Harap berikan parameter date from dan date to');
      }

      $start = new DateTime($filters['dateFrom']);
      $end = new DateTime($filters['dateTo']);
      if ($start->diff($end)->days > 90) {
        throw new Exception('Rentang tanggal tidak boleh melebihi 90 hari');
      }

      $query = DB::table('wholesales_download_csv')
        ->when($filters['dateFrom'], fn($q) => $q->whereDate('tanggal_pesan', ">=", $filters['dateFrom']))
        ->when($filters['dateTo'], fn($q) => $q->whereDate('tanggal_pesan', "<=", $filters['dateTo']))
        ->when($filters['ref_order'], fn($q) => $q->where('ref_order', 'like', "%{$filters['ref_order']}%"))
        ->when($filters['owner_name'], fn($q) => $q->where('nama_akun', 'like', "%{$filters['owner_name']}%"))
        ->when($filters['order_no'], fn($q) => $q->where('no_order', 'like', "%{$filters['order_no']}%"))
        ->when($filters['store_name'], fn($q) => $q->where('nama_toko', 'like', "%{$filters['store_name']}%"))
        ->when($filters['sales_name'], fn($q) => $q->where('nama_sales', 'like', "%{$filters['sales_name']}%"))
        ->when($filters['store_type'] && $filters['store_type'] !== "semua", fn($q) => $q->where('tipe_toko', $filters['store_type']))
        ->when($filters['status'], function ($q) use ($filters) {
            $statuses = array_map(fn($s) => str_replace('_', ' ', $s), explode(',', $filters['status']));
            return $q->whereIn('status_pesanan', $statuses);
        })
        ->when(!in_array("0", $roles), fn($q) => $q->whereIn('customer_id', $customerIdList))
        ->get()->toArray();

      $data = DownloadCSVResource::collection($query);

      $spreadsheet = new Spreadsheet();
      $sheet = $spreadsheet->getActiveSheet();

      $headers = [
          'Nama Akun', 'Nama Toko', 'Tanggal Pesan', 'Status Pesanan',
          'Order No', 'Ref Order', 'DN', 'Billing', 'Article',
          'Article Description', 'Price', 'Qty Order', 'Diskon',
          'Item Product', 'Product Category', 'Nama Sales',
          'Tanggal GI', 'Tanggal Billing', 'Gross', 'Nett'
      ];

      $sheet->fromArray([$headers], null, 'A1');

      $sheet->getStyle('A1:T1')->applyFromArray([
        'fill' => [
            'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
            'startColor' => ['argb' => '808080']
        ],
        'font' => [
            'bold' => true,
            'color' => ['argb' => '000000']
        ],
      ]);

      $row = 2;
      foreach ($data as $item) {
        $sheet->fromArray([
          $item->nama_akun, $item->nama_toko, $item->tanggal_pesan, $item->status_pesanan,
          $item->no_order, $item->ref_order, $item->no_dn, $item->no_billing, '',
          $item->article_description, $item->price, $item->qty_order, $item->diskon,
          $item->item_product, $item->product_category, $item->nama_sales,
          $item->tanggal_gi, $item->tanggal_billing, $item->gross, $item->nett
        ], null, "A$row");

        $sheet->setCellValueExplicit("I$row", $item->article, DataType::TYPE_STRING);
        $row++;
      }

      $highestRow = $sheet->getHighestRow();
      $highestColumn = $sheet->getHighestColumn();
      $sheet->getStyle("A1:$highestColumn$highestRow")->applyFromArray([
          'borders' => [
              'allBorders' => [
                  'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                  'color' => ['argb' => '000000'],
              ],
          ],
      ]);

      foreach (range('A', $highestColumn) as $columnID) {
          $sheet->getColumnDimension($columnID)->setAutoSize(true);
      }

      $writer = new Xls($spreadsheet);
      $response = new StreamedResponse(fn() => $writer->save('php://output'));

      $response->headers->set('Content-Type', 'application/vnd.ms-excel');
      $response->headers->set('Content-Disposition', 'attachment; filename="Export Wholesales Transactions.xls"');
      $response->headers->set('Cache-Control', 'max-age=0');

      return $response;
      
    } catch (Exception $e){
      Log::error($e->getMessage());
      return $this->sendError($e->getMessage(), 400);
    }
  }

  public function downloadTransactions(Request $request)
  {
    $salesId = auth()->user()->reference_id;
    $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
    $salesIdd = !in_array("0", $roles) ?
      array_map(
        function ($i) {
          return $i->sales_id ?? '0';
        },
        $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id', $salesId)->first())
      )
      : [];
    array_push($salesIdd, $salesId);
    $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
      ->pluck('customer_id')->all();

    Log::channel('stderr')->info($salesId);
    $status = $request->input('status');
    $dateFrom = $request->input('date_from');
    $dateTo = $request->input('date_to');
    $order_no = $request->input('order_no');
    $owner_name = $request->input('owner_name');
    $store_type = $request->input('store_type');
    $store_name = $request->input('store_name');
    $ref_order = $request->input('ref_order');
    $sales_name = $request->input('sales_name');

    $col = [
      'Nama Akun', 'Nama Toko', 'Tanggal Pesan', 'Status Pesanan', '#Order', '#Ref Order',
      '#DN', '#Billing', 'Article', 'Article Description', 'Price', 'Qty Order', 'Diskon',
      'Sub Total', 'Item Product', 'Product Category', 'Nama Sales', 'Tanggal GI',
      'Tanggal Billing'
    ];

    $handle = fopen('Export Transactions Wholesales (Internal).csv', 'w');

    fputcsv($handle, $col);

    $items = DB::table('order_detail')
      ->leftJoin('order_header', 'order_detail.order_no', '=', 'order_header.order_no')
      ->leftJoin('article', 'order_detail.article_id', '=', 'article.article')
      ->leftJoin('customers', 'order_header.customer_id', '=', "customers.customer_id")
      ->leftJoin('customer_shipment', 'order_header.customer_shipment_id', '=', "customer_shipment.customer_shipment_id")
      ->leftJoin('customer_sales', 'order_header.customer_id', '=', 'customer_sales.customer_id')
      ->leftJoin('sales', 'order_header.sales_id', '=', 'sales.sales_id')
      ->leftJoin('delivery_order', 'order_header.sales_order_no', '=', 'delivery_order.sales_order_no')
      ->leftJoin('delivery_order_detail', function ($j) {
        $j->on('delivery_order.delivery_order_no', '=', 'delivery_order_detail.delivery_order_no')
          ->on('order_detail.article_id', '=', 'delivery_order_detail.article');
      })
      ->leftJoin('invoice', 'delivery_order.delivery_order_no', '=', 'invoice.delivery_order_no')
      ->leftJoin('invoice_detail', function ($j) {
        $j->on('invoice.invoice_no', '=', 'invoice_detail.invoice_no')
          ->on('order_detail.article_id', '=', 'invoice_detail.article');
      })
      ->where('order_header.distribution_channel', 'WHOLESALES')
      ->when($status, function ($query) use ($status) {
        $status = explode(',', $status);
        $q_status = [];
        $i = 0;
        foreach ($status as $data) {
          $cek = str_contains($data, '_');
          if ($cek == true) {
            $q_status[$i] = str_replace('_', ' ', $data);
          } else {
            $q_status[$i] = $data;
          }
          $i++;
        }
        return $query->whereIn('order_header.order_status', $q_status);
      })

      ->when($store_type, function ($query) use ($store_type) {
        if ($store_type == 'toko_cash') {
          $query->where(function ($q) {
            $q->where('customers.top_days', 'LIKE', '0')
              ->orWhereNull('customers.top_days');
          });
        } elseif ($store_type == 'toko_tempo') {
          $query->where(function ($q) {
            $q->where('customers.top_days', 'NOT LIKE', '0');
          });
        } else {
        }
      })
      ->when($dateFrom, function ($query) use ($dateFrom) {
        return $query->whereDate('order_header.created_date', ">=", $dateFrom);
      })
      ->when($dateTo, function ($query) use ($dateTo) {
        return $query->whereDate('order_header.created_date', "<=", $dateTo);
      })
      ->when($ref_order, function ($query) use ($ref_order) {
        return $query->where('order_header.sales_order_no', 'like', '%' . $ref_order . '%');
      })
      ->when($owner_name, function ($query) use ($owner_name) {
        return $query->where('order_header.bill_to', 'like', '%' . $owner_name . '%');
      })
      ->when($order_no, function ($query) use ($order_no) {
        return $query->where('order_header.order_no', 'like', '%' . $order_no . '%');
      })
      ->when($store_name, function ($query) use ($store_name) {
        return $query->where('order_header.ship_to', 'like', '%' . $store_name . '%');
      })
      ->when($sales_name, function ($query) use ($sales_name) {
        return $query->where('order_header.sales_name', 'like', '%' . $sales_name . '%');
      })
      ->when(!in_array("0", $roles), function ($query) use ($customerIdList) {
        return $query->whereIn('order_header.customer_id', $customerIdList);
      })
      ->select(
        'customers.top_days',
        'order_header.bill_to as owner_name',
        'order_header.ship_to as store_name',
        'order_header.sales_name as sales_name',
        'order_header.created_date as order_date',
        'order_header.order_status',
        'order_header.order_no',
        'order_header.sales_order_no',
        'delivery_order.delivery_order_no',
        'invoice.invoice_no',
        'order_detail.article_id',
        'order_detail.product_name',
        'order_detail.product_size',
        'order_detail.product_variant',
        'order_detail.price',
        'order_detail.qty as order_detail_qty',
        'delivery_order_detail.issued_qty as do_detail_issued_qty',
        'delivery_order_detail.qty as do_detail_qty',
        'order_header.total_discount as order_header_discount',
        'delivery_order.discount as delivery_order_discount',
        'invoice_detail.gross_price as invoice_detail_gp',
        'invoice_detail.discount_percent as invoice_detail_discount_percent',
        'article.lvl3_description',
        'article.product_style',
        'delivery_order.good_issue_date as gi_date',
        'invoice.billing_date'
      )
      ->orderBy('order_header.created_date', 'desc')
      ->chunk(100, function ($data) use ($handle) {
        foreach ($data as $d) {
          $qty = $d->delivery_order_no == null ? $d->order_detail_qty : ($d->delivery_order_no != null and $d->gi_date != null ? $d->do_detail_issued_qty : $d->do_detail_qty);
          $discount = $d->invoice_no != null ? $d->invoice_detail_gp * $d->invoice_detail_discount_percent : ($d->delivery_order_discount != null ? $d->delivery_order_discount : $d->order_header_discount);
          $row = [
            'nama_akun' => $d->owner_name,
            'nama_toko' => $d->store_name,
            'tanggal_pesan' => Carbon::parse($d->order_date)->format('Y-m-d'),
            'status_pesanan' => $d->order_status,
            '#order' => $d->order_no,
            '#ref_order' => $d->sales_order_no,
            '#dn' => $d->delivery_order_no,
            '#billing' => $d->invoice_no,
            'article' => $d->article_id,
            'article_description' => $d->product_name . ' ' . $d->product_variant . ' ' . $d->product_size,
            'price' => (int)$d->price,
            'qty_order' => (int)$qty,
            'diskon' => (int)$discount,
            'sub_total' => ((int)$qty * (int)$d->price) - (int)$discount,
            'item_product' => $d->lvl3_description,
            'product_category' => $d->product_style,
            'nama_sales' => $d->sales_name,
            'tanggal_gi' => $d->delivery_order_no == null ? null : Carbon::parse($d->gi_date)->format('Y-m-d'),
            'tanggal_billing' => $d->invoice_no == null ? null : Carbon::parse($d->billing_date)->format('Y-m-d')
          ];
          fputcsv($handle, $row);
        }
      });

    fclose($handle);

    return response()->download('Export Transactions Wholesales (Internal).csv')->deleteFileAfterSend(true);
  }

  public function getFlag($article)
  {
    Log::info("[GETFLAG] ArticleID " . $article);

    $flag = [];
    $date = now()->format('Y-m-d');
    $articleData = Product::Where('article', $article)->first();
    if (!empty($articleData)) {
      if ($articleData->transfer_date <= $date && $articleData->expired_date >= $date) {
        array_push($flag, 'NEW');
      }
      if ($articleData->is_custom_logo || $articleData->is_custom_size) {
        array_push($flag, 'CUSTOM');
      }
      array_push($flag, $articleData->lvl4_description);
    }
    return $flag;
  }

  public function uploadPks(Request $request, $order_no) {
    Log::info($order_no);
    $inv = DB::table('order_header')
        ->where('order_no','=',$order_no)
        ->first();

    if($inv == null){
        return $this->sendError('order not found', 404);
    }

    try{
        DB::beginTransaction();
        if ($request->has('file')) {
            $file = $request->file;
            $file_name = pathinfo($file)['basename'];

            //check s3-public then move to s3
            $pks_file_path = $this->fileTransfer($file, 'pks');
            if ($pks_file_path['error'] == true) {
                return $this->sendError($pks_file_path['message']);
            }

            // $file_path = env('S3_PKS_FOLDER').$file_name;
            // Storage::disk('s3')->put(substr($file_path,1), file_get_contents($file));
            DB::table('order_header')
                ->where('order_no','=',$order_no)
                ->update([
                    'pks_file_path' => $file,
                    'pks_file_name' => $file_name
                ]);
        }
        DB::commit();
        return $this->sendSuccess('success', $file);
    } catch (\Exception $e){
        DB::rollBack();
        return $this->sendError($e->getMessage());
    }
  }
}