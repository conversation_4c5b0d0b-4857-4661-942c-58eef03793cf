<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Order;
use App\Models\Product;
use App\Models\Customer;
use App\Models\OrderItem;
use App\Helpers\RestHelper;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\LogIntegration;
use App\Models\MasterParameter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use App\Services\LoggerIntegration;
use App\Http\Resources\WAB\TransactionListWABResource;
use Symfony\Component\HttpFoundation\StreamedResponse;

class TransactionWABController extends Controller
{
    public function getTransactions(Request $request){
        $order_no = $request->input('order_no');
        $ref_order = $request->input('ref_order');
        $nama_akun = $request->input('nama_akun');
        $location_code = $request->input('location_code');
        $status = $request->input('status_pesanan');
        $date_to = $request->input('date_to');
        $date_from = $request->input('date_from');
        $page = $request->input('page');

        $master_param = MasterParameter::where('group_key','CHANNEL_CODE')->where('value','WAB')->first();

        $orders = Order::where('distribution_channel',$master_param->key)
                                ->when($order_no, function ($q) use ($order_no){
                                    $q->where('order_no','LIKE','%'.$order_no.'%');
                                })
                                ->when($ref_order, function ($q) use ($ref_order){
                                    $q->where('external_order_no','LIKE','%'.$ref_order.'%');
                                })
                                ->when($nama_akun, function ($q) use ($nama_akun){
                                    $q->where('bill_to','LIKE','%'.$nama_akun.'%');
                                })
                                ->when($location_code, function ($q) use ($location_code){
                                    $q->where('location_code','LIKE','%'.$location_code.'%');
                                })
                                ->when($status, function ($q) use ($status){
                                    $status = explode(',',$status);
                                    $q->whereIn('order_status', $status);
                                })
                                ->when($date_to, function ($q) use ($date_to){
                                    $date_to = Carbon::parse($date_to)->format('Y-m-d H:i:s');
                                    $q->whereDate('created_date','<=',$date_to);
                                })
                                ->when($date_from, function ($q) use ($date_from){
                                    $date_from = Carbon::parse($date_from)->format('Y-m-d H:i:s');
                                    $q->whereDate('created_date','>=',$date_from);
                                })
                                ->orderBy('created_date','desc')
                                ->paginate(15,['*'],'page',$page);

        $data_order = [];

        // $customer = Customer::find($orders[1]->customer_id);

        // dd($orders[1]->customer_id);
        
        $data_order['total_data'] = $orders->total();
        $data_order['size'] = intval($orders->perPage());
        $data_order['active_page'] = $orders->currentPage();
        $data_order['total_page'] = $orders->lastPage();

        for ($i=0; $i < count($orders) ; $i++) {
            $data_order['data'][$i] = [
                'order_no' => $orders[$i]->order_no,
                'order_date' => $orders[$i]->created_date,
                'ref_order' => $orders[$i]->external_order_no,
                'nama_akun' => $orders[$i]->bill_to??null,
                'location_code' => $orders[$i]->location_code,
                'total_transaksi' => $orders[$i]->total_nett,
                'status_pesanan' => strtoupper($orders[$i]->order_status)
            ];
        }

        return $this->sendSuccess(null,$data_order);

    }

    //referensi refactor kodingan getTransactions di atas ^
    public function getTransactionsNew(Request $request){
        
        //convert parameter query ke collection
        $request_collection = $request->collect();
        $hashURL = RestHelper::hashURL($request);
        if (Cache::has($hashURL)) {
            $orders = Cache::get($hashURL);
            return $orders;

        }
        else{
            $orders = Order::filterHelper('transaction_internal', $request_collection)
                    ->orderBy('created_date','desc')
                    ->redisPaginate($request_collection['items']??12,['*'], 'page',$request_collection['page']);
            $resp = $this->sendSuccess(null, $orders, null, false, TransactionListWABResource::class);
            Cache::put($hashURL, $resp, 3600);
            return $resp;
        }
        
                    

    }

    public function getTransactionsNewNR(Request $request){

        //convert parameter query ke collection
        $request_collection = $request->collect();
        $orders = Order::filterHelper('transaction_internal', $request_collection)
                    ->orderBy('created_date','desc')
                    ->paginate($request_collection['items']??12,['*'], 'page',$request_collection['page']);
        
        return $this->sendSuccess(null, $orders, null, false, TransactionListWABResource::class);

    }

    /*
    Kodingan testStore ini setara dengan:
                $log - new LogIntegration()
                $log->reference_no = $request->reference_no;
                $log->module = $request->module;
                $log->name = $request->name;
                $log->type = $request->type;
                $log->status = $request->status;
                $log->description = $request->description;
                $log->save();
                return response()->json([
                    'error' => false,
                    'status' => '200 OK',
                    'message' => 'Banner updated successfully'
                ], 200);

                ditambah kodingan diatas ga handling validationerror 
    
    */
    public function testStore(Request $request){
        return $this->doStore(LogIntegration::class, $request, [], false, true);
    }

    public function downloadTransactionsNew(Request $request)
    {
        try {
            $order_no = $request->input('order_no');
            $ref_order = $request->input('ref_order');
            $nama_akun = $request->input('nama_akun');
            $location_code = $request->input('location_code');
            $status = $request->input('status_pesanan');
            $date_to = $request->input('date_to');
            $date_from = $request->input('date_from');

            $master_param = MasterParameter::where('group_key','CHANNEL_CODE')->where('value','WAB')->first();
            
            $datas = [];

            $orders = DB::table('order_detail')
                        ->leftJoin('order_header','order_detail.order_no','=','order_header.order_no')
                        ->leftJoin('customers', 'order_header.customer_id','=','customers.customer_id')
                        ->leftJoin('article', 'order_detail.article_id', '=', 'article.article')
                        ->where('order_header.distribution_channel',$master_param->key)
                        ->when($order_no, function ($q) use ($order_no){
                            $q->where('order_header.order_no','LIKE','%'.$order_no.'%');
                        })
                        ->when($ref_order, function ($q) use ($ref_order){
                            $q->where('order_header.external_order_no','LIKE','%'.$ref_order.'%');
                        })
                        ->when($nama_akun, function ($q) use ($nama_akun){
                            $q->where('order_header.bill_to','LIKE','%'.$nama_akun.'%');
                        })
                        ->when($location_code, function ($q) use ($location_code){
                            $q->where('location_code','LIKE','%'.$location_code.'%');
                        })
                        ->when($status, function ($q) use ($status){
                            $status = explode(',',$status);
                            $q->whereIn('order_status', $status);
                        })
                        ->when($date_to, function ($q) use ($date_to){
                            $date_to = Carbon::parse($date_to)->format('Y-m-d H:i:s');
                            $q->whereDate('order_header.created_date','<=',$date_to);
                        })
                        ->when($date_from, function ($q) use ($date_from){
                            $date_from = Carbon::parse($date_from)->format('Y-m-d H:i:s');
                            $q->whereDate('order_header.created_date','>=',$date_from);
                        })
                        ->select('order_header.bill_to','order_header.bill_to as owner_name','order_header.location_code','order_header.created_date as order_date',
                                'order_header.order_status','order_header.order_no','order_header.external_order_no',
                                'order_detail.article_id','order_detail.product_name','order_detail.product_variant',
                                'order_detail.product_size','order_detail.price','order_detail.qty')
                        ->orderBy('order_header.created_date','desc')
                        ->chunk(100, function ($data) use (&$datas){
                            foreach ($data as $d) {
                                $row = [
                                    'nama_akun' => $d->bill_to,
                                    'kode_toko' => $d->location_code,
                                    'tanggal_pesan' => Carbon::parse($d->order_date)->format('Y-m-d'),
                                    'status_pesanan' => $d->order_status,
                                    '#order' => $d->order_no,
                                    '#ref_order' => $d->external_order_no,
                                    'article' => $d->article_id,
                                    'article_description' => $d->product_name.' '.$d->product_variant.' '.$d->product_size,
                                    'price' => $d->price,
                                    'qty_order' => $d->qty,
                                    'diskon' => '',
                                    'sub_total' => ''
                                ];
                                $datas[] = $row;
                            }
                        });

            return $this->sendSuccess('Export Transaction WAB Internal Success',$datas);
        } catch (\Exception $e) {
            Log::info('Export Transaction WAB Internal Failed : '.$e->getMessage().'. Line : '.$e->getLine());
            return $this->sendError('Export Transaction WAB Internal Failed');
        }
    }

    public function downloadTransactions(Request $request)
    {
        $order_no = $request->input('order_no');
        $ref_order = $request->input('ref_order');
        $nama_akun = $request->input('nama_akun');
        $location_code = $request->input('location_code');
        $status = $request->input('status_pesanan');
        $date_to = $request->input('date_to');
        $date_from = $request->input('date_from');

        $master_param = MasterParameter::where('group_key','CHANNEL_CODE')->where('value','WAB')->first();

        $col = ['Nama Akun', 'Kode Toko', 'Tanggal Pesan', 'Status Pesanan', '#Order',
                '#Ref Order', 'Article', 'Article Description', 'Price', 'Qty Order',
                'Diskon', 'Sub Total'];
        
        $handle = fopen('Export Transactions WAB (Internal).csv','w');

        fputcsv($handle,$col);

        $orders = DB::table('order_detail')
                    ->leftJoin('order_header','order_detail.order_no','=','order_header.order_no')
                    ->leftJoin('customers', 'order_header.customer_id','=','customers.customer_id')
                    ->leftJoin('article', 'order_detail.article_id', '=', 'article.article')
                    ->where('order_header.distribution_channel',$master_param->key)
                    ->when($order_no, function ($q) use ($order_no){
                        $q->where('order_header.order_no','LIKE','%'.$order_no.'%');
                    })
                    ->when($ref_order, function ($q) use ($ref_order){
                        $q->where('order_header.external_order_no','LIKE','%'.$ref_order.'%');
                    })
                    ->when($nama_akun, function ($q) use ($nama_akun){
                        $q->where('order_header.bill_to','LIKE','%'.$nama_akun.'%');
                    })
                    ->when($location_code, function ($q) use ($location_code){
                        $q->where('location_code','LIKE','%'.$location_code.'%');
                    })
                    ->when($status, function ($q) use ($status){
                        $status = explode(',',$status);
                        $q->whereIn('order_status', $status);
                    })
                    ->when($date_to, function ($q) use ($date_to){
                        $date_to = Carbon::parse($date_to)->format('Y-m-d H:i:s');
                        $q->whereDate('order_header.created_date','<=',$date_to);
                    })
                    ->when($date_from, function ($q) use ($date_from){
                        $date_from = Carbon::parse($date_from)->format('Y-m-d H:i:s');
                        $q->whereDate('order_header.created_date','>=',$date_from);
                    })
                    ->select('order_header.bill_to','order_header.bill_to as owner_name','order_header.location_code','order_header.created_date as order_date',
                            'order_header.order_status','order_header.order_no','order_header.external_order_no',
                            'order_detail.article_id','order_detail.product_name','order_detail.product_variant',
                            'order_detail.product_size','order_detail.price','order_detail.qty')
                    ->orderBy('order_header.created_date','desc')
                    ->chunk(100, function ($data) use ($handle){
                        foreach ($data as $d) {
                            $row = [
                                'nama_akun' => $d->bill_to,
                                'kode_toko' => $d->location_code,
                                'tanggal_pesan' => Carbon::parse($d->order_date)->format('Y-m-d'),
                                'status_pesanan' => $d->order_status,
                                '#order' => $d->order_no,
                                '#ref_order' => $d->external_order_no,
                                'article' => $d->article_id,
                                'article_description' => $d->product_name.' '.$d->product_variant.' '.$d->product_size,
                                'price' => $d->price,
                                'qty_order' => $d->qty,
                                'diskon' => '',
                                'sub_total' => ''
                            ];
                            fputcsv($handle,$row);
                        }
                    });
        
        fclose($handle);

        return response()->download('Export Transactions WAB (Internal).csv')->deleteFileAfterSend(true);
        
        // $data_order = [];

        // // dd(count($orders->items));
        
        // $i = 0;
        // foreach ($orders as $order) {
        //     foreach ($order->items as $item) {
        //         $product = Product::where('article',$item->article_id)->first();
        //         $data_order[$i] = [
        //             'nama_akun' => $order->customer->owner_name ?: null,
        //             'kode_toko' => $order->location_code,
        //             'tanggal_pesan' => Carbon::parse($order->created_date)->format('Y-m-d'),
        //             'status_pesanan' => $order->order_status,
        //             '#order' => $order->order_no,
        //             '#ref_order' => $order->external_order_no,
        //             'article' => $product == null ? null :  $product->article,
        //             'article_description' => $item->product_name.' '.$item->product_variant.' '.$item->product_size,
        //             'price' => $item->price,
        //             'qty_order' => $item->qty,
        //             'diskon' => '',
        //             'sub_total' => ''
        //         ];
        //         $i++;
        //     }
        // }

    }
    
    public function getTransactionDetails(Request $request, $order_no)
    {
        $page = !empty($request->input('page')) ? (int)$request->input('page') : 1;

        $order = Order::find($order_no);

        if (is_null($order)) {
            return $this->sendError('There is no data with order no : '.$order_no, Response::HTTP_UNPROCESSABLE_ENTITY);
        }

        $order_detail = OrderItem::where('order_no',$order_no)->get();

        $stock_item = [];

        for ($i=0; $i < count($order_detail); $i++) { 
            $stock_item[$i] = [
                'source' => 'CAREOM',
                'destination' => 'STK',
                'article' => $order_detail[$i]->article_id,
                // 'site' => '1200'
            ];
        }

        $stock_req = [
            'source' => 'CAREOM',
            'destination' => 'STK',
            'detail' => $stock_item
        ];

        $stock_sap = Http::withHeaders([
            'Accept' => 'application/json',
            'sapuser' => env('ARTICLE_STOCK_SAPUSER')
        ])
        ->withBasicAuth(env('ARTICLE_STOCK_USERNAME'),env('ARTICLE_STOCK_PASSWORD'))
        ->post(env('ARTICLE_STOCK_API_URL'), $stock_req);

        $data_stock = $stock_sap->json();

        if ($stock_sap->failed()) {
            $logs = [
                'reference_no'=> $order_no,
                'module' => 'SAP',
                'name' => 'Master Stock (In WAB Detail Transaction)',
                'type'=> 'Outbound',
                'status' => 'failed',
                'description' => [
                    'payload' => $stock_req,
                    'response' => $data_stock
                ]
            ];
    
            (new LoggerIntegration())->InsertLogger($logs);

            $groupedStock = null;
        }

        if ($stock_sap->successful()) {
            $logs = [
                'reference_no'=> $order_no,
                'module' => 'SAP',
                'name' => 'Master Stock (In WAB Detail Transaction)',
                'type'=> 'Outbound',
                'status' => 'success',
                'description' => [
                    'payload' => $stock_req,
                    'response' => $data_stock
                ]
            ];
    
            (new LoggerIntegration())->InsertLogger($logs);
    
            $groupedStock = [];
            foreach ($data_stock['data'] as $sub_stock) {
                if (!isset($groupedStock[$sub_stock['article']])) {
                    $groupedStock[$sub_stock['article']] = [
                        'site' => $sub_stock['site'],
                        'stock' => $sub_stock['qty']
                    ];
                }
            }
        }

        $product_items = [];
        $items = [];
        
        for ($i=0; $i < count($order_detail) ; $i++) { 
            $article_c = Product::where('article', $order_detail[$i]->article_id)->first();

            $items[$i] = [
                'article' => $order_detail[$i]->article_id,
                'product_name' => $order_detail[$i]->product_name,
                'product_variant' => $order_detail[$i]->product_variant,
                'product_size' => $order_detail[$i]->product_size,
                'product_stock' => !is_null($groupedStock) ? $groupedStock[$order_detail[$i]->article_id]['stock'] : 0,
                'quantity' => $order_detail[$i]->qty,
                'total' => $order_detail[$i]->total,
            ];

            $date = now()->format('Y-m-d');
            $flag = [];
            if(!empty($article_c)){
                if ($article_c->transfer_date <= $date && $article_c->expired_date >= $date) {
                    array_push($flag, 'NEW');
                }
                if ($article_c->is_custom_size || $article_c->is_custome_logo) {
                    array_push($flag, 'CUSTOM');
                }
                array_push($flag, $article_c->lvl4_description);
            }
            // $base_price = ProductPrice::where('sku_code_c',$article_c->sku_code_c)->latest()->first();

            $product_items[$i] = [
                'sku_code_c' => $article_c->sku_code_c,
                'product_name_c' => $article_c->product_name_c,
                'image' => @$article_c->mainImageGeneric->file_path == null ? null : env('S3_STREAM_URL').$article_c->mainImageGeneric->file_path,
                'flag' => $flag,
                'base_price' => (int)$order_detail[$i]->price,
                'discount_price' => $order_detail[$i]->additional_discount != 0 ? $order_detail[$i]->price - ($order_detail[$i]->additional_discount/$order_detail[$i]->qty) : null,
                'sub_total' => 0,
                'product_items' => $items[$i], 
            ];
        }

        $groupedItems = [];

        foreach ($product_items as $item) {
            if (!isset($groupedItems[$item['sku_code_c']])) {
                $groupedItems[$item['sku_code_c']] = [
                    'sku_code_c' => $item['sku_code_c'],
                    'product_name_c' => $item['product_name_c'],
                    'image' => $item['image'],
                    'flag' => $item['flag'],
                    'base_price' => $item['base_price'],
                    'discount_price' => $item['discount_price'],
                    'sub_total' => 0,
                    'product_items' => [] 
                ];
                $i = 0;
            }
            $groupedItems[$item['sku_code_c']]['product_items'][$i] = $item['product_items'];
            $i++;
        }

        foreach ($groupedItems as $sku => $groupedItem) {
            $sub = 0;
            foreach ($groupedItem['product_items'] as $productItem) {
                $sub += $productItem['total'];
            }
            $groupedItems[$sku]['sub_total'] = $sub;
        }

        $groupedItems = array_values($groupedItems);
        $total = count($groupedItems);
        $limit = 6;
        $total_pages = ceil($total/$limit);
        $page = max($page,1);
        $page = min($page, $total_pages);
        $offset = ($page - 1) * $limit;
        if ($offset < 0 ) {
            $offset = 0;
        }
        $groupedItems = array_slice($groupedItems,$offset,$limit);

        $data_detail = [
            'order_no' => $order->order_no,
            'order_status' => $order->order_status,
            'reference_no' => $order->external_order_no,
            'tanggal_pesan' => $order->created_date,
            'bill_to' => $order->bill_to,
            'bill_to_address' => $order->bill_to_address,
            'bill_to_phone_number' => $order->bill_to_phone_number,
            'bill_to_email' => $order->bill_to_email,
            'ship_to' => $order->ship_to,
            'ship_to_address' => $order->ship_to_address,
            'ship_to_phone_number' => $order->ship_to_phone_number,
            'delivery_number' => !is_null($order->delivery_no) ? $order->delivery_no : "-",
            'sub_total' => $order->total,
            'discount_type' => $order->discount_type,
            'total_discount' => $order->total_discount,
            'total' => $order->total_nett,
            'ongkos_kirim' => $order->shipping_charges,
            'items' => $groupedItems
        ];

        $proses_berjalan = [
            'Baru' => true,
            'baru_date' => Carbon::parse($order->created_date)->format('Y-m-d H:i:s'),
            'Pembayaran' => true,
            'pembayaran_date' => Carbon::parse($order->created_date)->format('Y-m-d H:i:s'),
            'Siap_Dikirim' => $order->order_status == 'Siap Dikirim' ? true : ($order->order_status == 'Dikirim' ? true : false),
            'siap_dikirim_date' => $order->delivery_date == null ? null : Carbon::parse($order->delivery_date)->format('Y-m-d H:i:s'),
            'Dikirim' => $order->order_status == 'Dikirim' ? true : false,
            'dikirim_date' => $order->completed_date == null ? null : Carbon::parse($order->completed_date)->format('Y-m-d H:i:s')
        ];

        $response = [
            'total_data' => $total,
            'size' => $limit,
            'active_page' => $page,
            'total_page' => (int)$total_pages,
            'proses_berjalan' => $proses_berjalan,
            'order_detail' => $data_detail
        ];
        
        return $this->sendSuccess(null,$response);

    }
}
