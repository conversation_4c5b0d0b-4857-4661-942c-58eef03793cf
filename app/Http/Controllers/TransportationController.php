<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\TransportationService;
use App\Traits\ResponseAPI;

class TransportationController extends Controller
{
    protected $transportationService;

    public function __construct(TransportationService $transportationService) 
    {
        $this->transportationService = $transportationService;
    }

    public function getTransportations(Request $request) 
    {
        try {
            $result['data'] = $this->transportationService->getTransportations($request);
        } catch (RequestException $ex) {
            return $this->sendError($ex->getMessage(), 401);
        }

        if (!$result['data']->items()) {
            return $this->sendError("Data not found!", 404);
        }
        
        $data = response()->json([
            'data' => $result['data']->items(),
            'pagination' => [
                'total' => $result['data']->total(),
                'per_page' => (int)$result['data']->perPage(),
                'current_page' => $result['data']->currentPage(),
                'last_page' => $result['data']->lastPage(),
                'from' => (int)$result['data']->firstItem(),
                'to' => (int)$result['data']->lastItem()
            ],
        ]);

        return $this->sendSuccess("Get transportations success", $data);
    }
}
