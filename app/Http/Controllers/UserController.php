<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use Illuminate\Http\Request;
use App\Interfaces\UserInterface;
use App\Http\Requests\LoginRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\ChangePasswordRequest;
use App\Http\Requests\ChangeUserDataRequest;
use App\Http\Requests\ForgotPasswordRequest;
use App\Http\Requests\InitChangePasswordRequest;
use App\Http\Requests\ChangeForgotPasswordRequest;
use App\Http\Requests\CreateShipmentAddressRequest;
use App\Http\Requests\EditProfileRequest;
use App\Http\Requests\UpdateShipmentAddressRequest;

class UserController extends Controller
{
    private $userRepo;

    public function __construct(UserInterface $userRepo)
    {
        $this->userRepo = $userRepo;
    }

    /**
     * Handle an authentication attempt.
     *
     * @param  \App\Http\Requests\LoginRequest  $req
     * @return \Illuminate\Http\Response
     */
    public function authenticate(LoginRequest $req)
    {
        $validated = $req->validated();

        return $this->userRepo->login($req);
    }

    public function logout(Request $req)
    {
        try {
            if ($req->header('Authorization') && strpos(strtolower($req->header('Authorization')), 'bearer') === 0) {
                $cookie = Cookie::forget('jwt');
                $req->user()->currentAccessToken()->delete();
                return $this->sendSuccessWithCookies('Revoke current token has been successfull.', [], $cookie);
            }

            if ($req->header('X-Xsrf-Token')) {
                // Invalidate the session
                $req->session()->invalidate();

                // Regenerate the session token
                $req->session()->regenerateToken();

                // Clear Sanctum token
                $response = response()->json(['message' => 'Logged out']);

                // Clear the XSRF-TOKEN cookie
                $response->headers->setCookie(cookie()->forget('XSRF-TOKEN'));

                // Clear the Laravel session cookie
                $response->headers->setCookie(cookie()->forget('laravel_session'));

                return $response;
            }
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function initChangePassword(InitChangePasswordRequest $req)
    {
        $validated = $req->validated();

        return $this->userRepo->initChangePassword($req);
    }

    public function getStoreList(Request $req)
    {
        $customer = $req->user()->customer;

        return $this->userRepo->storeList($customer);
    }

    public function userActivation(Request $req)
    {
        $user = $req->user();

        return $this->userRepo->activation($user);
    }

    public function profile(Request $req)
    {
        $user = $req->user();

        return $this->userRepo->getProfile($user);
    }

    public function changePassword(ChangePasswordRequest $req)
    {
        $validated = $req->validated();

        return $this->userRepo->changePassword($req);
    }

    public function creditLimit(Request $req)
    {
        return $this->userRepo->getCreditLimit($req->user()->customer->customer_id);
    }

    public function forgotPassword(ForgotPasswordRequest $req)
    {
        $validated = $req->validated();

        return $this->userRepo->generateTokenForgotPassword($req);
    }

    public function changeForgotPassword(ChangeForgotPasswordRequest $req)
    {
        $req->validated();
        return $this->userRepo->forgotChangePassword($req);
    }

    public function checkPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required', // Ensure 'data_update' is an array
        ]);

        if ($validator->fails()) {
            return $this->sendError($validator->errors(), 422);
        }

        try {
            $match = false;
            $userPassword = auth()->user()->password;
            if (Hash::check($request->password, $userPassword))
                $match = true;

            return $this->sendSuccess("Password Berhasil dicek", ['isMatch' => $match]);

        } catch (\Exception $e) {
            $errors = $e->getMessage();
            return $this->sendError($errors, 400);
        }
    }

    public function editProfile(EditProfileRequest $request)
    {
        $request->validated();
        return $this->userRepo->updateProfile($request);
    }

    public function addShipmentAddress(CreateShipmentAddressRequest $request)
    {
        $request->validated();
        return $this->userRepo->addShipmentAddress($request);
    }

    public function editShipmentAddress($id, UpdateShipmentAddressRequest $request)
    {
        $request->validated();
        return $this->userRepo->editShipmentAddress($id, $request);
    }

    public function changeShipmentAddress($id, Request $request)
    {
        return $this->userRepo->changeShipmentAddress($id, $request);
    }

    public function deleteShipmentAddress($id, Request $request)
    {
        return $this->userRepo->deleteShipmentAddress($id, $request);
    }
}