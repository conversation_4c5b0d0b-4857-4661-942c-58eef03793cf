<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use App\Http\Resources\OrderResource;
use App\Models\MasterParameter;

class WabDashboardController extends Controller
{
    public $master_param_key;

    public function __construct()
    {
        $master_param = MasterParameter::where('value','WAB')->where('group_key','CHANNEL_CODE')->first();
        $this->master_param_key = $master_param->key;
    }

    public function summary(Request $req)
    {
        $total_sales = OrderItem::select('qty');
        $transaction_totals = Order::where('distribution_channel',$this->master_param_key)->where('payment_status', 'Lunas');
        $transaction_amount = Order::select('total_nett')->where('payment_status', 'Lunas')->where('distribution_channel',$this->master_param_key);
        $this->periodQuery($req, $start, $to);
        $total_sales = $total_sales->whereHas('order', function($query) use($start, $to){
            $query->where('distribution_channel',$this->master_param_key)->where('payment_status', 'Lunas')
                    ->whereDate('created_date', '>=', $start)->whereDate('created_date', '<=', $to);
        })->get()->sum('qty');
        $transaction_totals = $transaction_totals->whereDate('created_date', '>=', $start)->whereDate('created_date', '<=', $to)->count();
        $transaction_amount = $transaction_amount->whereDate('created_date', '>=', $start)->whereDate('created_date', '<=', $to)->sum('total_nett');
        if ($transaction_amount < 1) {
            $transaction_average = 0;
        } else {
            $transaction_average = $transaction_amount / $transaction_totals;
        }
        return $this->sendSuccess('', [
            'total_sales' => $total_sales,
            'transaction_totals' => $transaction_totals,
            'transaction_amount' => $transaction_amount,
            'transaction_average' => round($transaction_average)
        ]);
    }

    public function orders(Request $req)
    {
        $limit = 5;
        if ($req->has('limit')) {
            $limit = $req->query('limit');
        }

        $orders = Order::where('distribution_channel',$this->master_param_key)->orderBy('created_date', 'desc')->limit($limit)->get();

        return $this->sendSuccess('', OrderResource::collection($orders));
    }

    public function customers(Request $req)
    {
        $limit = 5;
        if ($req->has('limit')) {
            $limit = $req->query('limit');
        }
        $orders = Order::where('order_header.distribution_channel',$this->master_param_key)->join('customers', 'order_header.customer_id', '=', 'customers.customer_id')
                        ->selectRaw('order_header.customer_id, sum(total) as total_transaksi, DATE_FORMAT(order_header.created_date,"%Y-%m-%d %H:%i:%s") as order_date, owner_name, phone_number')
                        ->groupBy('customer_id')->orderBy('order_header.created_date', 'desc')
                        ->orderBy('owner_name', 'asc')
                        ->limit($limit)->get();

        return $this->sendSuccess('', $orders);
    }

    public function periodQuery($req, &$start, &$to)
    {
        if ($req->has('due_date_from') && $req->has('due_date_to')) {
            $start = $req->query('due_date_from');
            $to = $req->query('due_date_to');
        } elseif ($req->has('due_date_from') && !$req->has('due_date_to')) {
            $start = $req->query('due_date_from');
            $to = now()->format('Y-m-d');
        } elseif (!$req->has('due_date_from') && $req->has('due_date_to')) {
            $start = now()->format('Y-m-d');
            $to = $req->query('due_date_to');
        } else {
            $start = now()->subDays(30)->format('Y-m-d');
            $to = now()->format('Y-m-d');
        }
    }
}
