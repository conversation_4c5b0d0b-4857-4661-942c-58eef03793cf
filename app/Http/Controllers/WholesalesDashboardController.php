<?php

namespace App\Http\Controllers;

use App\Models\CustomerSales;
use App\Models\SalesAssignment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Order;
use App\Models\OrderItem;
use App\Http\Resources\OrderResource;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Support\Facades\DB as FacadesDB;


class WholesalesDashboardController extends Controller
{
    public function summary(Request $req)
    {
        $salesId = auth()->user()->reference_id;
        $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
        $salesIdd = !in_array("0", $roles) ? 
        array_map(function($i) {return $i->sales_id??'0';}, $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id',$salesId)->first())
        )
        : [];
        array_push($salesIdd, $salesId);
        $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                            ->pluck('customer_id')->all();

        $this->periodQuery($req, $start, $to);
        $typeSales = ["WHOLESALES"];
        $transaction_totals = DB::table('order_header')
        ->whereDate('order_date', '>=', $start)
        ->whereDate('order_date', '<=', $to)
        ->whereIn('distribution_channel', $typeSales)
        ->whereIn('order_status', ['Baru', 'Diproses', 'Pembayaran', 'Siap Dikirim', 'Dikirim', 'Selesai']);
        if(!in_array("0", $roles)){
            $transaction_totals = $transaction_totals->whereIn('customer_id', $customerIdList);
        }

        $transaction_totals = $transaction_totals
        ->count('order_no');
        
        $total_sales = DB::table('order_header')
        ->leftJoin('order_detail', 'order_header.order_no', '=', 'order_detail.order_no' )
        ->whereDate('order_date', '>=', $start)
        ->whereDate('order_date', '<=', $to)
        ->whereIn('distribution_channel', $typeSales)
        ->whereIn('order_status', ['Baru', 'Diproses', 'Pembayaran', 'Siap Dikirim', 'Dikirim', 'Selesai']);
        if(!in_array("0", $roles)){
            $total_sales = $total_sales->whereIn('customer_id', $customerIdList);
        }
        $total_sales = $total_sales->sum('order_detail.qty');

        $transaction_amount = DB::table('order_header')
        ->whereDate('order_date', '>=', $start)
        ->whereDate('order_date', '<=', $to)
        ->whereIn('distribution_channel', $typeSales)
        ->whereIn('order_status', ['Baru', 'Diproses', 'Pembayaran', 'Siap Dikirim', 'Dikirim', 'Selesai']);
        if(!in_array("0", $roles)){
            $transaction_amount = $transaction_amount->whereIn('customer_id', $customerIdList);
        }
        $transaction_amount = $transaction_amount->sum('total_nett');

        if ($transaction_amount < 1) {
            $transaction_average = 0;
        } else {
            $transaction_average = $transaction_amount / $transaction_totals;
        }

        return $this->sendSuccess('', [
            'total_sales' => $total_sales,
            'transaction_totals' => $transaction_totals,
            'transaction_amount' => $transaction_amount,
            'transaction_average' => $transaction_average,
            'transaction_gross' => $transaction_amount->sum('nett_before_tax')

        ]);
    }

    public function orders (Request $req)
    {
        $salesId = auth()->user()->reference_id;
        $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
        $salesIdd = !in_array("0", $roles) ? 
        array_map(function($i) {return $i->sales_id??'0';}, $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id',$salesId)->first())
        )
        : [];
        array_push($salesIdd, $salesId);
        $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                            ->pluck('customer_id')->all();

        $this->periodQuery($req, $start, $to);
        $typeSales = ["WHOLESALES"];
        $limit = 5;
        if ($req->has('limit')) {
            $limit = $req->query('limit');
        }

        $orders = Order::whereIn('order_status', ['Baru', 'Diproses', 'Pembayaran', 'Siap Dikirim', 'Dikirim'])
        ->where('distribution_channel', $typeSales)
        ->whereBetween('order_date', [$start, $to]);

        if(!in_array("0", $roles)){
            $orders = $orders->whereIn('customer_id', $customerIdList);
        }

        $orders = $orders->groupBy('OH.order_no')
        ->orderBy('OH.order_date', 'desc')
        ->limit($limit)
        ->get();

        return $this->sendSuccess('', OrderResource::collection($orders));
    }

    public function customers (Request $req)
    {

        $salesId = auth()->user()->reference_id;
        $roles = auth()->user()->roles()->pluck('tier_level')->toArray() ?: [];
        $salesIdd = !in_array("0", $roles) ? 
        array_map(function($i) {return $i->sales_id??'0';}, $this->customerId_recursive(SalesAssignment::with('child')->where('sales_id',$salesId)->first())
        )
        : [];
        array_push($salesIdd, $salesId);
        $customerIdList = CustomerSales::whereIn('sales_id', $salesIdd)
                            ->pluck('customer_id')->all();

        $this->periodQuery($req, $start, $to);
        $typeSales = ["WHOLESALES"];
        $limit = 5;
        if ($req->has('limit')) {
            $limit = $req->query('limit');
        }
        $customers = DB::table('order_header as OH')
        ->leftJoin('customers as C', 'OH.customer_id', '=', 'C.customer_id')
        ->leftJoin('delivery_order as DO', 'OH.sales_order_no', '=', 'DO.sales_order_no')
        ->leftJoin('invoice as I', function ($join) 
        {
            $join->on('DO.delivery_order_no', '=', 'I.delivery_order_no');
            // $join->on('I.status', '!=', DB::RAW("'PAID'"));
        })
        ->whereDate('OH.order_date', '>=', $start)
        ->whereDate('OH.order_date', '<=', $to)  
        ->whereIn('OH.distribution_channel', $typeSales)
        ->whereIn('OH.order_status', ['Baru', 'Diproses', 'Pembayaran', 'Siap Dikirim', 'Dikirim']);
        if(!in_array("0", $roles)){
            $customers = $customers->whereIn('customer_id', $customerIdList);
        }
        $customers = $customers->select('C.owner_name as customer_name',DB::raw('SUM(OH.total) AS total_transaksi'),DB::raw('SUM(I.nett_price) AS credit_used') ,'OH.created_date as last_date_order','C.credit_limit_used_percentage as credit_limit')
        ->groupBy('OH.customer_id')
        ->orderBy('OH.created_date', 'desc')
        ->limit($limit)
        ->get();
        
        return $this->sendSuccess('', $customers);
    }

    public function periodQuery($req, &$start, &$to)
    {
        if ($req->has('due_date_from') && $req->has('due_date_to')) {
            $start = $req->query('due_date_from');
            $to = $req->query('due_date_to');
        } elseif ($req->has('due_date_from') && !$req->has('due_date_to')) {
            $start = $req->query('due_date_from');
            $to = now()->format('Y-m-d');
        } elseif (!$req->has('due_date_from') && $req->has('due_date_to')) {
            $start = now()->format('Y-m-d');
            $to = $req->query('due_date_to');
        } else {
            $start = now()->subDays(30)->format('Y-m-d');
            $to = now()->format('Y-m-d');
        }
    }
}