<?php

namespace App\Http;

use Illuminate\Foundation\Http\Kernel as HttpKernel;

class <PERSON>el extends HttpKernel
{
    /**
     * The application's global HTTP middleware stack.
     *
     * These middleware are run during every request to your application.
     *
     * @var array<int, class-string|string>
     */
    protected $middleware = [
        // \App\Http\Middleware\TrustHosts::class,
        \App\Http\Middleware\TrustProxies::class,
        \Fruitcake\Cors\HandleCors::class,
        \App\Http\Middleware\PreventRequestsDuringMaintenance::class,
        \Illuminate\Foundation\Http\Middleware\ValidatePostSize::class,
        \App\Http\Middleware\TrimStrings::class,
        \Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull::class,
        \App\Http\Middleware\HideServerHeader::class,
    ];

    /**
     * The application's route middleware groups.
     *
     * @var array<string, array<int, class-string|string>>
     */
    protected $middlewareGroups = [
        'web' => [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            // \Illuminate\Session\Middleware\AuthenticateSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\VerifyCsrfToken::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],

        'api' => [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            "throttle:500,1",
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
        ],
    ];

    /**
     * The application's route middleware.
     *
     * These middleware may be assigned to groups or used individually.
     *
     * @var array<string, class-string|string>
     */
    protected $routeMiddleware = [
        'auth' => \App\Http\Middleware\Authenticate::class,
        'auth.basic' => \App\Http\Middleware\AuthBasic::class, //\Illuminate\Auth\Middleware\AuthenticateWithBasicAuth::class,
        'cache.headers' => \Illuminate\Http\Middleware\SetCacheHeaders::class,
        'can' => \Illuminate\Auth\Middleware\Authorize::class,
        'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
        'password.confirm' => \Illuminate\Auth\Middleware\RequirePassword::class,
        'signed' => \Illuminate\Routing\Middleware\ValidateSignature::class,
        'throttle' => \Illuminate\Routing\Middleware\ThrottleRequests::class,
        'verified' => \Illuminate\Auth\Middleware\EnsureEmailIsVerified::class,
        'abilities' => \Laravel\Sanctum\Http\Middleware\CheckAbilities::class,
        'ability' => \Laravel\Sanctum\Http\Middleware\CheckForAnyAbility::class,
        'verified-customer' => \App\Http\Middleware\VerifiedCustomer::class,
        'order-detail' => \App\Http\Middleware\OrderDetail::class,
        'invoice-detail' => \App\Http\Middleware\InvoiceDetail::class,
        'api-key' => \App\Http\Middleware\ApiKey::class,
        'sales' => \App\Http\Middleware\Sales::class,
        'salesreseller' => \App\Http\Middleware\SalesReseller::class,
        'preCache' => \App\Http\Middleware\PreCache::class,
        'postCache' => \App\Http\Middleware\PostCache::class,
        'reseller' => \App\Http\Middleware\Reseller::class,
        'reseller-register' => \App\Http\Middleware\ResellerRegister::class,
        'auth-device' => \App\Http\Middleware\AuthDevice::class,
        'customer-reseller' => \App\Http\Middleware\CustomerReseller::class,
        'reseller-link' => \App\Http\Middleware\ResellerLink::class,
        'transaction' => \App\Http\Middleware\TransactionThrottling::class,
        'transaction-timeout' => \App\Http\Middleware\TransactionTimeout60::class,
        'reseller-order' => \App\Http\Middleware\ResellerOrder::class,
        'auth-s3' => \App\Http\Middleware\AuthS3::class,
        'verified-otp' => \App\Http\Middleware\VerifiedOTP::class
        // 'api.version' => \App\Http\Middleware\APIversioning::class,

    ];
}
