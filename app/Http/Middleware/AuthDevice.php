<?php

namespace App\Http\Middleware;

use App\Models\ResellerToken;
use Closure;
use Illuminate\Http\Request;

class AuthDevice
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $authDevice = ResellerToken::where('auth_device_token',$request->header('X-AUTH-DEVICE'))->exists();
        if (!$request->header('X-AUTH-DEVICE') || $authDevice != true) {
            return response()->json([
                    'error'   => true,
                    'status'  => 'Access denied!',
                    'message' => 'You are not allowed.',
                    'data'    => []
                ],
                403,
                [],
                JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
            );
        }

        return $next($request);
    }
}
