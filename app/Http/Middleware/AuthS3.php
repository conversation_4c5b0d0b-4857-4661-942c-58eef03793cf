<?php

namespace App\Http\Middleware;

use App\Models\Reseller;
use App\Models\OrderReseller;
use App\Traits\ResponseAPI;
use Closure;

class AuthS3
{
    use ResponseAPI;
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $redirectToRoute
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse|null
     */
    public function handle($request, Closure $next)
    {    
        //generate signature (ts+baseurl+filename)
        // $timestamp =  $request->header('timestamp');
        // $baseUrl = env('APP_URL');
        // $filename = $request->name;
        // $value = $timestamp.$baseUrl.$filename;
        // $generateSignature = hash('sha256', $value);
        // if($generateSignature != $request->header('x-care-signature')){
        //     return $this->sendError("signature not match", 400);
        // }

        return $next($request);
    }
}
