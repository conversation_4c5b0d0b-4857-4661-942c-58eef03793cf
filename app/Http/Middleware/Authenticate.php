<?php

namespace App\Http\Middleware;

use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\Middleware\Authenticate as Middleware;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        if (!$request->expectsJson()) {
            return route('/login');
        }
    }

    protected function unauthenticated($request, array $guards)
    {
        if ($request->is(config('app.api_version') . "/" . config('app.api_deployment') . '/api/*') || $request->is('api/*')) {
            // abort(
            //     response()->json(
            //         [
            //             'error' => true,
            //             'status' => '401 Bad Request.',
            //             'message' => 'Unauthenticated',
            //             'data' => []
            //         ],
            //         401,
            //         [],
            //         JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT
            //     )
            // );
            throw new AuthenticationException('Unauthenticated');
        }


        return parent::unauthenticated($request, $guards);
    }
}