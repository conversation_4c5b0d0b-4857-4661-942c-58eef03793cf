<?php

namespace App\Http\Middleware;

use App\Models\Invoice;
use App\Models\Order;
use Closure;
use App\Traits\ResponseAPI;
class InvoiceDetail
{
    use ResponseAPI;

    //VALIDASI POKOKNYA DISINI SEMUA CONG
    public function handle($request, Closure $next)
    {
        if(!isset($request->route()->parameters['invoice_no'])) return $this->sendError('invoice_no is required.');

        $invoice_no = $request->route()->parameters['invoice_no'];
        $model = Invoice::where('invoice_no', $invoice_no);
        if (strpos($request->route()->uri, 'b2b') !== false) {
            if(!$model->exists()){
                $orderHeader = Order::where('order_no',$invoice_no);
                if(!$orderHeader->exists()) return $this->sendError('Invoice not exist.');
                if($orderHeader->first()->customer_id != $request->user()->reference_id) return $this->sendError('Invoice not exist!');
                return $next($request);
            }
        }
        
        if(!$model->exists()) return $this->sendError('Invoice not exist.');

        if($model->first()->customer_id != $request->user()->reference_id) return $this->sendError('Invoice not exist!');

        return $next($request);
    }
}
