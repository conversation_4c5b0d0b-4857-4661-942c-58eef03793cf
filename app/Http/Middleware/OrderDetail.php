<?php

namespace App\Http\Middleware;

use Closure;
use App\Traits\ResponseAPI;
use App\Models\Order;
class OrderDetail
{
    use ResponseAPI;

    //VALIDASI POKOKNYA DISINI SEMUA CONG
    public function handle($request, Closure $next)
    {
        // 1. Validasi Order no di /api/transaction/{order_no}
        if($request->isMethod('get') && !isset($request->route()->parameters['order_no'])) return $this->sendError('Order no is required.');
        if($request->isMethod('post') && !isset($request->order_no)) return $this->sendError('Order no is required.');

        $order_no = $request->isMethod('get') ? $request->route()->parameters['order_no'] : $request->order_no;
        $model = Order::where('order_no', $order_no);
        
        //2. Validasi Order ada di database
        if(!$model->exists()) return $this->sendError('Order not exist.');

        //3. Validasi Order ini punya customer yang akses
        if($model->first()->customer_id != $request->user()->reference_id) return $this->sendError('Order not exist!');


        return $next($request);
    }
}
