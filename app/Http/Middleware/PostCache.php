<?php

namespace App\Http\Middleware;
use App\Helpers\RestHelper;
use Illuminate\Support\Facades\Cache;
use Closure;

class PostCache
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $redirectToRoute
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse|null
     */
    public function handle($request, Closure $next, $duration = 3600)
    {
        $hashURL = RestHelper::hashURL($request);
        $response = $next($request);
        if (!Cache::has($hashURL)) {
            if($response->status() == 200 && $request->input('page', 1) != 1){
                Cache::put($hashURL, $response, $duration);
            }
        }
        return $response;
     }
}
