<?php

namespace App\Http\Middleware;
use App\Helpers\RestHelper;
use Illuminate\Support\Facades\Cache;
use Closure;

class PreCache
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $redirectToRoute
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse|null
     */
    public function handle($request, Closure $next)
    {
        $hashURL = RestHelper::hashURL($request);
        if (Cache::has($hashURL) && $request->input('page', 1) != 1) {
            return Cache::get($hashURL);
        }

        return $next($request);
    }
}
