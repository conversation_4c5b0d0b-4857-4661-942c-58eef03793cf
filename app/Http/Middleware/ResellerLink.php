<?php

namespace App\Http\Middleware;

use App\Models\ResellerLink as ModelsResellerLink;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ResellerLink
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        try {
            $user = Auth::guard('sanctum')->user();
            if (!$user || strtolower($user->reference_object) == 'rsl_customers') {
                $reseller_link = ModelsResellerLink::where('id',$request->header('reseller-link-id'))->first();
                if (!$request->header('reseller-link-id') || !$reseller_link || $reseller_link->reseller->is_active != 1) {
                    return response()->json([
                            'error'   => true,
                            'status'  => 'Access denied!',
                            'message' => 'You are not allowed.',
                            'data'    => []
                        ],
                        403,
                        [],
                        JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
                    );
                }
            }
            return $next($request);
        } catch (\Exception $e) {
            return response()->json([
                'error'   => true,
                'status'  => 'Error',
                'message' => $e->getMessage().' Line : '.$e->getLine(),
                'data'    => []
            ],
            403,
            [],
            JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
        );
        }
    }
}
