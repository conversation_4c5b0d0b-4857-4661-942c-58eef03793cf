<?php

namespace App\Http\Middleware;

use App\Models\Reseller;
use App\Models\OrderReseller;
use App\Traits\ResponseAPI;
use Closure;

class ResellerOrder
{
    use ResponseAPI;
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $redirectToRoute
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse|null
     */
    public function handle($request, Closure $next)
    {    
        if($request->isMethod('get') && !isset($request->route()->parameters['order_no'])) return $this->sendError('Order no is required.', 200);
        if($request->isMethod('post') && !isset($request->order_no)) return $this->sendError('Order no is required.', 200);

        $reseller = Reseller::Where("reseller_id", "=", auth()->user()->reference_id)->first();
        
        $order_no = $request->isMethod('get') ? $request->route()->parameters['order_no'] : $request->order_no;

        $order = OrderReseller::where('order_no', $order_no)
                ->where('reseller_id', $reseller->id);

       if (!$order->exists()) {
            return $this->sendError('Data transaction not found.', 200);
        }
        

        return $next($request);
    }
}
