<?php

namespace App\Http\Middleware;

use App\Helpers\RestHelper;
use App\Models\Reseller;
use App\Models\ResellerRegistration;
use App\Models\User;
use App\Traits\ResponseAPI;
use Closure;

class ResellerRegister
{
    use ResponseAPI;
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $redirectToRoute
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse|null
     */
    public function handle($request, Closure $next)
    {
        $checkExistingUser = ResellerRegistration::where('email', $request->email)->where('status', '!=', ResellerRegistration::REGISTRATION_REJECTED)->first();
        if ($checkExistingUser) {
          switch ($checkExistingUser->status) {
            case ResellerRegistration::REGISTRATION_CREATED:
              return $this->sendError("This email Registration is being processed", 400);
              break;
            case ResellerRegistration::REGISTRATION_APPROVED:
              return $this->sendError("This email Registration is already approved", 400);
              break;
          }
        }
    
        $checkExistingUser = Reseller::where('email', $request->email)->first();
        if ($checkExistingUser) {
          return $this->sendError("Email entered already exist", 400);
        }

        return $next($request);
    }
}
