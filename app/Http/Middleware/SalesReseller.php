<?php

namespace App\Http\Middleware;

use Closure;

class SalesReseller
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $redirectToRoute
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse|null
     */
    public function handle($request, Closure $next)
    {
        // dd();
        if (!in_array($request->user()->reference_object, ['sales','finance']) || !in_array('Reseller',$request->user()->business_units()->pluck('name')->toArray())) {
            return response()->json([
                    'error'   => true,
                    'status'  => 'Access denied!',
                    'message' => 'Account not authorized to access',
                    'data'    => []
                ],
                403,
                [],
                JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
            );
        }

        return $next($request);
    }
}
