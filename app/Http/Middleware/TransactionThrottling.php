<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;

class TransactionThrottling
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Cache::forget('transaction-lock');
        if (Cache::has('transaction-lock')) {
            return response()->json([
                    'error'   => true,
                    'status'  => 'Access denied!',
                    'message' => 'Transaksi sedang berjalan, silahkan tunggu 10 detik.',
                    'data'    => []
                ],
                403,
                [],
                JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
            );
        }
        Cache::set('transaction-lock', true, now()->addSeconds(10));
        return $next($request);
    }
}
