<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Request;

class TransactionTimeout60
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if (Cache::has('transaction-timeout'.'-'.auth()->user()->customer->customer_id)) {
            return response()->json([
                    'error'   => true,
                    'status'  => 'Access denied!',
                    'message' => 'Transaksi sedang berjalan, silahkan tunggu 60 detik.',
                    'data'    => []
                ],
                403,
                [],
                JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
            );
        }
        Cache::set('transaction-timeout'.'-'.auth()->user()->customer->customer_id, true, now()->addSeconds(60));
        return $next($request);
    }
}
