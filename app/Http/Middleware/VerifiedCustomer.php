<?php

namespace App\Http\Middleware;

use Closure;
use Carbon\Carbon;
use App\Models\Invoice;
use App\Models\Customer;

class VerifiedCustomer
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $redirectToRoute
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse|null
     */
    public function handle($request, Closure $next)
    {
        if ($request->user()->reference_object != 'customer') {
            return response()->json(
                [
                    'error' => true,
                    'status' => 'Access denied!',
                    'message' => 'You are not allowed.',
                    'data' => []
                ],
                403,
                [],
                JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT
            );
        }

        if (!@$request->user()->customer->is_verified) {
            return response()->json(
                [
                    'error' => true,
                    'status' => 'Access denied!',
                    'message' => 'Your account has not been verified.',
                    'data' => []
                ],
                403,
                [],
                JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT
            );
        }

        $user = @$request->user();
        $topDays = Customer::where('customer_id', $user->reference_id)->first();

        if ($topDays->top != 'T001') {
            $invoices = Invoice::where('customer_id', $user->reference_id)
                ->where('status', 'BELUM DIBAYAR')
                ->whereDate('due_date', '<=', Carbon::now()->subDays(1)->toDateString())
                ->exists();
            if ($invoices) {
                if ($topDays->is_pending_payment == 0)
                    $topDays->is_pending_payment = 1;
            } else {
                if ($topDays->is_pending_payment == 1)
                    $topDays->is_pending_payment = 0;
            }

            $topDays->save();
        }

        return $next($request);
    }
}