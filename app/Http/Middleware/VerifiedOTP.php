<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class VerifiedOTP
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if ($request->user()->verified_otp_date == null) {
            return response()->json([
                    'error'   => true,
                    'status'  => 'Access denied!',
                    'message' => 'You are not allowed.',
                    'data'    => []
                ],
                403,
                [],
                JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
            );
        }
        return $next($request);
    }
}
