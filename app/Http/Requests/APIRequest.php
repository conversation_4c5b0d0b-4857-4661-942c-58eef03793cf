<?php

namespace App\Http\Requests;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

abstract class APIRequest extends FormRequest
{
    /**
     * If validator fails return the exception in json form
     * @param Validator $validator
     * @return array
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                    'error'   => true,
                    'status'  => '422 Validation Error(s)',
                    'message'  => $validator->errors(),
                    'data'    => []
                ],
                422,
                [],
                JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
            )
        );
    }

    protected function failedAuthorization()
    {
        throw new HttpResponseException(
            response()->json([
                    'error'   => true,
                    'status'  => 'Access denied!',
                    'message' => 'You are not allowed.',
                    'data'    => []
                ],
                403,
                [],
                JSO<PERSON>_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
            )
        );
    }

    abstract public function rules();
}
