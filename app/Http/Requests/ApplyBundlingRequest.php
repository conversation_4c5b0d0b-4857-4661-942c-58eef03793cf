<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class ApplyBundlingRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'order_no' => 'required|exists:rsl_order_headers,order_no',
            'items'=> 'present|array',
            'items.*.sequence' => 'required',
            'items.*.promotion_id' => 'required',
            'items.*.article' => 'required'
        ];
    }
}
