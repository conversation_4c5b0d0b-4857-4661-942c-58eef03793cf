<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class BulkCartRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'file' => ['required', 'string', 'regex:/\.xlsx?$/i'],
            'isChange' => 'required|boolean',
        ];
    }

    public function messages()
    {
        return [
            'file.regex' => "Format file tidak valid. Harap unggah file dengan format .XLSX. atau .XLS. ",
            'file.required' => "Tidak ada file untuk diunggah. Harap unggah file terlebih dahulu.",
        ];
    }
}