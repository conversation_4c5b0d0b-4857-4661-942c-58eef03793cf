<?php

namespace App\Http\Requests;

use App\Models\OrderReseller;
use App\Models\ResellerToken;
use App\Rules\OrderAuthorization;
use Illuminate\Foundation\Http\FormRequest;

class CancelOrderResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     $customer_id = ResellerToken::where('auth_device_token',$this->header('X-AUTH-DEVICE'))
    //                                 ->pluck('customer_id')->first();
        
    //     $cek_order = OrderReseller::where('order_no',$this->input('order_no'))
    //                                 ->where('customer_id',$customer_id)
    //                                 ->exists();
    //     return $cek_order;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'order_no' => ['required', new OrderAuthorization]
        ];
    }
}
