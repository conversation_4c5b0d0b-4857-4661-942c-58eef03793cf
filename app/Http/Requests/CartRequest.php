<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class CartRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'customer_id' => 'required|exists:rsl_cart,customer_id',
            'link_id' => 'required|exists:rsl_cart,link_id',
            'site' => 'required|exists:rsl_master_site,code'
        ];
    }

    public function messages()
  {
    return [            
      'customer_id.exists' => "Cart Data not found",
      'link_id.exists' => "Cart Data not found",
    ];
  }


}
