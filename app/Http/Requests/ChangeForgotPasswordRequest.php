<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class ChangeForgotPasswordRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'token' => 'required|string',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*?[A-Z])(?=.*?\d)(?=.*?[^A-Za-z0-9]).{8,}$/'
            ],
            'password_confirmation' => ['same:password']
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'password.regex' => 'Your password must be more than or equal 8 characters long, should contain at-least 1 Uppercase, 1 Numeric and 1 special character.',
        ];
    }
}
