<?php

namespace App\Http\Requests;

use App\Rules\MatchOldPassword;
use Illuminate\Validation\Rules\Password;
use Illuminate\Foundation\Http\FormRequest;

class ChangePasswordCustomerResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'current_password' => ['required', new MatchOldPassword],
            'new_password' => [
                'required',
                Password::min(8)->mixedCase()->numbers()->symbols(),
                'different:current_password'
            ],
            'new_password_confirmation' => ['same:new_password']
        ];
    }


    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'new_password.different' => 'Password Baru dan Password Lama Tidak Boleh Sama',
        ];
    }

    public function updateUserData()
    {
        $d = $this->all();
        return [
            'password' => bcrypt($d['new_password']),
            'is_change_password' => 1
        ];
    }
}
