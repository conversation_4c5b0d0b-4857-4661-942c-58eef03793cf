<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;
use App\Rules\MatchOldPassword;

class ChangePasswordRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'current_password' => ['required', new MatchOldPassword],
            'new_password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*?[A-Z])(?=.*?[0-9])(?=.*?[*&^%$#@!]).{8,}$/',
                'different:current_password'
            ],
            'new_password_confirmation' => ['same:new_password']
        ];
    }


    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'password.regex' => 'Your password must be more than or equal 8 characters long, should contain at-least 1 Uppercase, 1 Numeric and 1 special character.',
            'new_password.different' => 'Password Baru dan Password Lama T<PERSON>',

        ];
    }
}
