<?php

namespace App\Http\Requests;

use App\Models\OrderReseller;
use App\Models\PublicProduct;
use App\Models\ResellerToken;
use App\Models\MasterParameter;
use Illuminate\Validation\Rule;
use App\Models\ResellerPromotion;
use App\Models\ResellerArticleStock;
use App\Models\ResellerCartDetail;
use App\Models\ResellerLink;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class CheckoutResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'reseller_id' => ['required','exists:rsl_reseller_link,reseller_id'],
            'link_id' => ['required','exists:rsl_reseller_link,id'],
            'sub_total_amount' => ['required','numeric','min:0'],
            'discount_amount' => ['required','numeric','min:0'],
            'total_amount' => ['required','numeric','min:0'],
            'site' => ['required','exists:rsl_master_site,code'],
            'applied_discount' => ['sometimes','array'],
            'applied_discount.*.type' => ['required','in:promo,voucher,coupon'],
            'applied_discount.*.promo_id' => ['required','exists:rsl_promotions,id'],
            'applied_discount.*.amount' => ['required','numeric','min:0'],
            'items.*.article' => [
                'required','string',
                function($attribute,$value,$fail){
                    $article = PublicProduct::where('article',$value)->first();
                    
                    if (!$article) {
                        $fail('Article not found');
                    }

                    if ($article->price == null) {
                        $fail('Article price not found');
                    }
                }
            ],
            'items.*.qty' => [
                'required','numeric','min:0',
                function ($attribute, $value, $fail) {
                    $keys = explode('.',$attribute);
                    $index = $keys[1];
                    $article = $this->input('items.'.$index.'.article');
                    $product = ResellerArticleStock::where('article',$article)->where('location_code',$this->input('site'))->first();
                    if (!$product) {
                        // Product doesn't exist
                        $fail('The selected product is invalid.');
                    } elseif ($value > ($product->available_stock)) {
                        // Quantity exceeds available stock
                        $fail('The selected quantity exceeds the available stock.');
                    }
                }
            ],
            'items.*.unit_price' => ['required','numeric','min:0'],
            'items.*.line_amount' => ['required','numeric','min:0'],
            'items.*.discount_amount' => ['required','numeric','min:0'],
            'items.*.cart_detail_id' => ['required','string','exists:rsl_cart_detail,id'],
            'items.*.applied_discount' => ['sometimes','array'],
            'items.*.applied_discount.*.type' => ['required','in:promo,voucher,coupon'],
            'items.*.applied_discount.*.promo_id' => ['required','exists:rsl_promotions,id'],
            'items.*.applied_discount.*.amount' => ['required','numeric','min:0']
        ];
    }

    public function transformHeader(){
        $d = $this->all();
        $items = $this->input('items');
        $order_no = strtoupper('INVRS'.substr(uniqid(), -8));
        $user = Auth::guard('sanctum')->user();
        $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;
        $mp = MasterParameter::where('group_key','RESELLER_COMMISSION')->where('key','COMMISSION_PERCENTAGE')->first();
        $total_amount = 0;
        $link = ResellerLink::where('id',$d['link_id'])->first();
        foreach ($items as $data) {
            $cd = ResellerCartDetail::where('id',$data['cart_detail_id'])->first();
            $article = PublicProduct::where('article',$cd->article_id)->first();
            $total_amount += $article->price->amount*$cd->qty;
        }
        return [
            'order_no' => $order_no,
            'customer_id' => $customer_id,
            'link_id' => $link->id,
            'reseller_id' => $link->reseller->id,
            'discount_amount' => 0,
            'sub_total_amount' => $total_amount,
            'total_amount' => $total_amount,
            'pay_amount' => $total_amount,
            'commission_amount' => round($total_amount*($mp->value/100)),
            'order_status' => OrderReseller::ORDER_RESELLER_PENDING,
            'location_code' => $d['site']
        ];
    }

    public function transformDetail(){
        $d = $this->input('items');
        foreach ($d as $data) {
            $cd = ResellerCartDetail::where('id',$data['cart_detail_id'])->first();
            $article = PublicProduct::where('article',$cd->article_id)->first();
            $datas['detail'][] = [
                'article_id' => $article->article,
                'sku_code' => $article->sku_code_c,
                'product_name' => $article->product_name_c,
                'product_size' => $article->product_size_c,
                'product_variant' => $article->product_variant_c,
                'qty' => $cd->qty,
                'unit_price' => $article->price->amount,
                'line_amount' => $article->price->amount*$cd->qty,
                'discount_amount' => 0,
                'total_amount' => $article->price->amount*$cd->qty,
            ];
            $datas['cart_detail_id'][] =  $data['cart_detail_id'];
        }
        return $datas;
    }
}
