<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class CreateBannerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'required',
            //'thumbnail' => 'required',
            'thumbnail' => 'required',
            'url'=> 'required',
            'sequence_no'=> 'required',
            'start_period'=> 'required|date|after_or_equal:today',
            'end_period' => 'required|date|after_or_equal:start_period'
        ];
    }

}
