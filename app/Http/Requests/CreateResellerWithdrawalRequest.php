<?php

namespace App\Http\Requests;

use App\Models\Reseller;
use Illuminate\Foundation\Http\FormRequest;

class CreateResellerWithdrawalRequest extends APIRequest
{
    protected $id;

    public function all($keys = null)
    {
        $inputData = parent::all($keys);
        $inputData['account_name'] = $this->input('account_name');
        $inputData['account_no'] = $this->input('account_no');
        $inputData['amount'] = $this->input('amount');
        // $inputData['transfer_fee'] = $this->input('transfer_fee');
        $inputData['tax_amount'] = $this->input('tax_amount');
        $inputData['total'] = $this->input('total');
        $inputData['transfer_method'] = $this->input('transfer_method');

        return $inputData;
    }
    
    public function rules()
    {
        return [
            'account_name' => 'required|string',
            'account_no' => 'required|string',
            'amount' => 'required|numeric',
            // 'transfer_fee' => 'required|numeric',
            'tax_amount' => 'required|numeric',
            'total' => 'required|numeric',
            'transfer_method' => 'nullable|string',
        ];
    }
}
