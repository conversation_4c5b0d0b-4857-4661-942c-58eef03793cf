<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class CreateUserRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[A-Za-z\s]+$/'
            ],
            'username' => 'required|string|unique:user,username|max:255',
            'email' => [
                'required',
                'email',
                'max:255',
                'unique:user,email',
                'regex:/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/'
            ],
            'channels' => 'array|required',
            // 'channels.*' => 'required|exists:business_unit,id',
            'position'=>'nullable|string|max:255', 
            'area' => 'nullable|string|max:255',
            'roles_id' => 'nullable|string|exists:roles,id',
            'authorization_id' => 'nullable|string',
            'sales_id' => 'nullable|exists:sales,sales_id',
            'sap_user'=> 'nullable|string|max:255',
            'direct_to'=> 'nullable|string|max:255',
            'target' => 'nullable|numeric|min:0',
            'status' => 'required|string',
            'phone_no'=> 'nullable|regex:/^\d{1,20}$/',
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*?[A-Z])(?=.*?[0-9])(?=.*?[*&^%$#@!]).{8,}$/'
            ],
            'password_confirmation' => ['required', 'same:password']
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'password.regex' => 'Your password must be more than or equal 8 characters long, should contain at-least 1 Uppercase, 1 Numeric and 1 special character.',
        ];
    }
}