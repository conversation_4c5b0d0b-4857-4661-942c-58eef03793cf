<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class   CreateVARequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'reseller_id' => 'required|exists:rsl_reseller,reseller_id',
        ];
    }

    public function messages()
  {
    return [            
      'reseller_id.exists' => "Reseller not found",
    ];
  }


}
