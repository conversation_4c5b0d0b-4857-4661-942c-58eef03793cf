<?php

namespace App\Http\Requests;

use App\Helpers\RestHelper;
use App\Http\Requests\APIRequest;
use App\Models\Reseller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Models\ResellerRegistration;
use App\Helpers\FormatHelper;

class DeactivateCustomerResellerRequest extends APIRequest
{

    public function transform(){
        $d = $this->all();

        return [
            'is_active' => 0,
        ];
    }
    

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'is_active' => 'nullable',
        ];
    }
}
