<?php

namespace App\Http\Requests;

use App\Models\ResellerToken;
use Illuminate\Support\Facades\Auth;
use App\Models\ResellerCustomerShipment;
use Illuminate\Foundation\Http\FormRequest;

class DeleteShipmentCustResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'customer_shipment_id' => [
                'required',
                function ($attribute,$value,$fail)
                {
                    $cs = ResellerCustomerShipment::where('id',$value)->first();
                    $user = Auth::guard('sanctum')->user();
                    $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;

                    if (!$cs) {
                        $fail('Customer shipment not found');
                    } elseif ($cs->customer_id != $customer_id) {
                        $fail('You are not allowed to delete this shipment');
                    }
                }
            ]
        ];
    }
}
