<?php

namespace App\Http\Requests;

use App\Models\OrderReseller;
use Illuminate\Foundation\Http\FormRequest;

class DetailTransactionResellerRequest extends FormRequest
{
    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $inputData['order_no'] = $this->input('order_no');

        return $inputData;
    }

    public function rules()
    {
        return [
            'order_no' => 'required|string',
        ];
    }
}
