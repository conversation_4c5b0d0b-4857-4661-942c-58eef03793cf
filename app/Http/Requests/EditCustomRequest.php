<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class EditCustomRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'attachment_id' => 'nullable',
            'custom_id' => 'nullable',
            'image' => 'nullable|image|mimes:jpeg,png,jpg|max:512',
            'custom_text' => 'nullable',
            'custom_type' => 'nullable',
            'material' => 'nullable',
            'size' => 'nullable',
            'notes' => 'nullable'
        ];
    }

}
