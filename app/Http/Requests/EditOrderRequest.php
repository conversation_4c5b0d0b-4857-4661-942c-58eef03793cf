<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class EditOrderRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'orders' => 'array|nullable',
            'customs' => 'array|nullable',
            'dp' => 'nullable',
            'discount' => 'nullable',
            'dp_due_date' => 'nullable',
            'location_code' => 'nullable',
            'location_name' => 'nullable'
        ];
    }

}
