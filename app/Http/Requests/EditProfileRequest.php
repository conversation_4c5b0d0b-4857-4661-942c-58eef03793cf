<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;
use App\Models\Customer;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Validation\Rule;

class EditProfileRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        // $userId = auth()->user()->id;
        // $customerId = auth()->user()->customer->customer_id;
        return [
            'owner_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[A-Za-z\s]+$/'
            ],
            'email' => [
                'required',
                'email',
                'max:255',
                // 'unique:customers,email,' . $customerId . ',customer_id',
                'regex:/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/'
            ],
            'phone_number' => 'required|regex:/^\d{1,20}$/',
            'national_id' => [
                'nullable',
                'regex:/^\d{15,16}$/',
                // Rule::unique('customers', 'national_id')->ignore($customerId, 'customer_id'),
            ],
            'shipment_address' => 'required|string|max:1000',
            'shipment_province_code' => 'nullable|string|exists:rsl_master_address,region_code',
            'shipment_province' => 'required|string|exists:rsl_master_address,region_name',
            'shipment_city_code' => 'nullable|string|exists:rsl_master_address,city_code',
            'shipment_city' => 'required|string',
            'shipment_district_code' => 'nullable|string|exists:rsl_master_address,district_code',
            'shipment_district' => 'required|string|exists:rsl_master_address,district_name',
            'shipment_zip_code' => 'required|regex:/^\d{1,10}$/',
            'npwp' => [
                'required',
                'regex:/^\d{15,16}$/',
                // Rule::unique('customers', 'npwp')->ignore($customerId, 'customer_id'),
            ],
            'npwp_file' => 'nullable|string',
            'npwp_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[A-Za-z\s]+$/'
            ],
            'tax_type' => 'required|string|in:PPN,Non-PPN,Wapu',
            'tax_invoice' => 'nullable|string|in:02,03,04,07',
            'npwp_address' => 'required|string|max:1000',
            'npwp_province_code' => 'nullable|string|exists:rsl_master_address,region_code',
            'npwp_province' => 'required|string|exists:rsl_master_address,region_name',
            'npwp_city_code' => 'nullable|string|exists:rsl_master_address,city_code',
            'npwp_city' => 'required|string',
            'npwp_district_code' => 'nullable|string|exists:rsl_master_address,district_code',
            'npwp_district' => 'required|string|exists:rsl_master_address,district_name',
            'npwp_zip_code' => 'required|regex:/^\d{1,10}$/'
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $email = $this->email;
            $npwp = $this->npwp;
            $nationalId = $this->national_id;
            $customerId = auth()->user()->customer->customer_id;

            $customers = Customer::where(function ($query) use ($email, $npwp, $nationalId) {
                if ($email) {
                    $query->orWhereNotNull('email');
                }
                if ($npwp) {
                    $query->orWhereNotNull('npwp');
                }
                if ($nationalId) {
                    $query->orWhereNotNull('national_id');
                }
            })->get();

            if ($email) {
                $existingEmail = $customers->first(function ($customer) use ($email, $customerId) {
                    if (!$customer->email) return false;

                    try {
                        return Crypt::decryptString($customer->email) === $email && $customer->customer_id !== $customerId;
                    } catch (\Exception $e) {
                        return $customer->email === $email && $customer->customer_id !== $customerId;
                    }
                });

                if ($existingEmail) {
                    $validator->errors()->add('email', 'Email sudah terdaftar.');
                }
            }

            if ($npwp) {
                $existingNPWP = $customers->first(function ($customer) use ($npwp, $customerId) {
                    try {
                        return Crypt::decryptString($customer->npwp) === $npwp && $customer->customer_id !== $customerId;
                    } catch (\Exception $e) {
                        return $customer->npwp === $npwp && $customer->customer_id !== $customerId;
                    }
                });

                if ($existingNPWP) {
                    $validator->errors()->add('npwp', 'NPWP sudah terdaftar.');
                }
            }

            if ($nationalId) {
                $existingKTP = $customers->first(function ($customer) use ($nationalId, $customerId) {
                    try {
                        return Crypt::decryptString($customer->national_id) === $nationalId && $customer->customer_id !== $customerId;
                    } catch (\Exception $e) {
                        return $customer->national_id === $nationalId && $customer->customer_id !== $customerId;
                    }
                });

                if ($existingKTP) {
                    $validator->errors()->add('national_id', 'Nomor KTP sudah terdaftar.');
                }
            }
        });
    }
}