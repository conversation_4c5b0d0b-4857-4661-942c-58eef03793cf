<?php

namespace App\Http\Requests;

use App\Helpers\RestHelper;
use App\Http\Requests\APIRequest;
use App\Models\Reseller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Models\ResellerRegistration;
use App\Helpers\FormatHelper;
use App\Helpers\Danamon\Specification\TransferOverbooking;
use App\Models\MasterParameter;
use App\Helpers\Danamon\Specification\VAInquiry;
use App\Models\ResellerVA;
use App\Helpers\FileHelper;

class EditResellerRequest extends APIRequest
{
    use RestHelper;
    use FileHelper;

    public function transform($id){
        $d = $this->all();

        // $finfo = finfo_open(FILEINFO_MIME_TYPE);
        // $mimeIdCardType = finfo_file($finfo, $this->file('national_id_file'));
        // $mimeNPWPType = finfo_file($finfo, $this->file('npwp_file'));
        // finfo_close($finfo);

        // $allowedMimeTypes = array(
        // 'image/png',
        // 'image/jpeg',
        // 'image/jpg'
        // );

        // if (
        // !in_array($mimeIdCardType, $allowedMimeTypes)
        // || !in_array($mimeNPWPType, $allowedMimeTypes)
        // ) {
        //     return RestHelper::sendError('png, jpg, jpeg files only', 400);
        // }

        // //upload ktp to S3
        $is_double = $this->national_id_file ==  $this->npwp_file;
        $ktp_file_path = $this->fileTransfer($this->national_id_file,'ktp',!$is_double);
        if ($ktp_file_path['error'] == true) {
            $ktpFilepathResult = Reseller::where('reseller_id',$id)->first()->getOriginal('national_id_file');
        } else {
            $ktpFilepathResult = '/'.$ktp_file_path['filepath'];
        }

        // //upload NPWP to S3
        $npwp_file_path = $this->fileTransfer($this->npwp_file,'npwp');
        if ($npwp_file_path['error'] == true) {
            $npwpFilepathResult = Reseller::where('reseller_id',$id)->first()->getOriginal('npwp_file');
        } else {
            $npwpFilepathResult = '/'.$npwp_file_path['filepath'];
        }
        

        return [
            'phone_number' => FormatHelper::formatPhoneNo($d['phone_number']),
            'name' => $d['name'],
            'address' => $d['address'],
            'province_code' => $d['province_code'],
            'city_code' => $d['city_code'],
            'district_code' => $d['district_code'],
            'zip_code' => $d['zip_code'],
            'national_id' => $d['national_id'],
            'national_id_file' => $ktpFilepathResult,
            'npwp' => $d['npwp'],
            'npwp_file' => $npwpFilepathResult,
            'is_active' => Reseller::resellerStatusEnumMapping($d['status']),
        ];
    }

    
    public function transformBalanceInquiry($reseller,$id)
    {
        $rq = VAInquiry::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').$id;
        $rq["RequestTime"] = date("YmdHis");
        $rq["VirtualAccountNumber"] = ResellerVA::where('reseller_id',$reseller->id)->first()->virtual_account_no ?? '-';//tbd
        return $rq;
    }

    public function transformOverbooking($resellerId, $nominal)
    {
        $resellerBank = ResellerVA::where('reseller_id', $resellerId)->first();
        $operational = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','OPERATIONAL')->first()->value??'********';
        $operational_name = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','OPERATIONAL_NAME')->first()->value??'********';
        $rq = TransferOverbooking::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').uniqid('EXTCR').'OV';
        $rq["RequestTime"] = date("YmdHis");
        $rq["SourceAccountNumber"] = $resellerBank->virtual_account_no;
        $rq["SourceCardNumber"] = '';
        $rq["BeneficiaryAccountNumber"] = $operational;
        $rq["BeneficiaryName"] = $operational_name;
        $rq["Amount"] = str_replace(".","",$nominal);
        $rq["Description"] = '';
        $rq["TransactionDate"] =  date("Ymd");
        return $rq;
    }
    

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'phone_number' => 'required',
            'name' => 'required',
            'address' => 'required',
            'province_code' => 'nullable',
            'city_code' => 'nullable',
            'district_code' => 'nullable',
            'zip_code' => 'nullable',
            'national_id' => 'required',
            'national_id_file' => 'nullable',
            'npwp' => 'required',
            'npwp_file' => 'nullable',
            'status' => 'required',
        ];
    }
}
