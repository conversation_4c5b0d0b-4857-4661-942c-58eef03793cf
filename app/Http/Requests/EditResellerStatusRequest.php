<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;
use App\Models\Reseller;
use App\Helpers\Danamon\Specification\TransferOverbooking;
use App\Models\MasterParameter;
use App\Helpers\Danamon\Specification\VAInquiry;
use App\Models\ResellerVA;

class EditResellerStatusRequest extends APIRequest
{

    public function transform(){
        $d = $this->all();        

        return [
            'is_active' => Reseller::resellerStatusEnumMapping($d['status']),
        ];
    }

    public function transformBalanceInquiry($reseller,$id)
    {
        $rq = VAInquiry::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').$id;
        $rq["RequestTime"] = date("YmdHis");
        $rq["VirtualAccountNumber"] = ResellerVA::where('reseller_id',$reseller->id)->first()->virtual_account_no ?? '-';//tbd
        return $rq;
    }

    public function transformOverbooking($resellerId, $nominal)
    {
        $resellerBank = ResellerVA::where('reseller_id', $resellerId)->first();
        $operational = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','OPERATIONAL')->first()->value??'********';
        $operational_name = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','OPERATIONAL_NAME')->first()->value??'********';
        $rq = TransferOverbooking::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').uniqid('EXTCR').'OV';
        $rq["RequestTime"] = date("YmdHis");
        $rq["SourceAccountNumber"] = $resellerBank->virtual_account_no;
        $rq["SourceCardNumber"] = '';
        $rq["BeneficiaryAccountNumber"] = $operational;
        $rq["BeneficiaryName"] = $operational_name;
        $rq["Amount"] = str_replace(".","",$nominal);
        $rq["Description"] = '';
        $rq["TransactionDate"] =  date("Ymd");
        return $rq;

    }
    

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'status' => 'required'
        ];
    }
}
