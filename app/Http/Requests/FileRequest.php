<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class FileRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'module' => 'required|string',
            'file' => 'required|file'
        ];
    }

    public function messages()
  {
    return [            
      'file.max' => "File maxsize is 2MB",
    ];
  }


}
