<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class GenerateGuestTokenRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'customer_id' => 'required|string'
        ];
    }

    public function transform()
    {
        $d = $this->all();
        return [
            'customer_id' => $d['customer_id'],
            'auth_device_token' => hash('sha256',$d['customer_id'])
        ];
    }
}
