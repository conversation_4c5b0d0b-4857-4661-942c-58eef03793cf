<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class GenerateLinkRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'identifier' => 'required_if:type,STORE|string|unique:rsl_reseller_link,identifier',
            'type' => 'required|string|in:STORE,LIST,DETAIL',
            'metadata' => 'required_unless:type,STORE|string',

        ];
    }

    public function transform() 
	{
        $d = $this->all();
        if($d['type'] != 'STORE' && !$this->isFormatJson($d['metadata'])) return false;

        return [
            'reseller_id' => $this->user()->reference_id,
            'type' => $d['type'],
			'identifier' => $d['type'] == 'STORE' ? $d['identifier'] : $this->generateIdentifier($d['metadata']),
			'metadata' => $d['type'] == 'STORE' ? null : $d['metadata'],
		];
	}

    public function messages()
    {
        return [
            'type.in' => 'Wrong value on type parameter',
            'identifier.unique' => 'This identifier is not available',

        ];
    }

    function isFormatJson($string) {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
     }

     function generateIdentifier($string) {
        $secret = $this->user()->reference_id.date('c');
        return substr(hash_hmac('sha256', $string, $secret), 0, 8);
     }


}
