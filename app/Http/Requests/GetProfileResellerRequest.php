<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetProfileResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    public function prepareForValidation()
    {
        $this->merge([
            'reseller_id' => $this->route()->parameters['reseller_id']
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'reseller_id' => ['required','exists:rsl_reseller,id']
        ];
    }

    public function messages()
    {
        return [
            'reseller_id.exists' => 'Reseller with the given id not found'
        ];
    }
}
