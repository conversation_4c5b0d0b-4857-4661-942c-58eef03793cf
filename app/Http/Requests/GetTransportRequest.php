<?php

namespace App\Http\Requests;

use App\Rules\OrderAuthorization;
use Illuminate\Foundation\Http\FormRequest;

class GetTransportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'order_no' => ['required', new OrderAuthorization]
        ];
    }
}
