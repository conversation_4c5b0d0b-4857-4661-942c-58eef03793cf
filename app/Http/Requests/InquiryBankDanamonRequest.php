<?php

namespace App\Http\Requests;

use App\Helpers\Danamon\Specification\BankDanamonInquiry;
use App\Helpers\Danamon\Specification\BankInquiry;
use App\Http\Requests\APIRequest;
class InquiryBankDanamonRequest extends APIRequest
{
  
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'reseller_id' => 'required|exists:rsl_reseller,reseller_id',
            'bank_no'     => 'required'
        ];
    }

    public function transform()
    {
        $d = $this->all();
        $rq = BankDanamonInquiry::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').$d['reseller_id'];
        $rq["RequestTime"] = date("YmdHis");
        $rq["AccountNumber"] = $d['bank_no'];
        return $rq;
    }

    public function messages()
  {
    return [            
      'reseller_id.exists' => "Reseller not found",
    ];
  }


}
