<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;
use App\Helpers\Danamon\Specification\VAInquiry;
use App\Models\Reseller;
class InquiryVARequest extends APIRequest
{
  
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'reseller_id' => 'required|exists:rsl_reseller,reseller_id',
            'virtual_account' => 'required'
        ];
    }

    public function transform()
    {
        $d = $this->all();
        $rq = VAInquiry::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').$d['reseller_id'];
        $rq["RequestTime"] = date("YmdHis");
        $rq["VirtualAccountNumber"] = $d['virtual_account'];
        return $rq;
    }

    public function messages()
  {
    return [            
      'reseller_id.exists' => "Reseller not found",
    ];
  }


}
