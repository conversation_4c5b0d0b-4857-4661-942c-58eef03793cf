<?php

namespace App\Http\Requests;

use App\Helpers\Danamon\Specification\TopupTransfer;
use App\Helpers\Danamon\Specification\TransferOnline;
use App\Helpers\Danamon\Specification\TransferOverbooking;
use App\Helpers\Danamon\Specification\TransferRTGS;
use App\Helpers\Danamon\Specification\TransferSKN;
use App\Models\Commission;
use App\Models\MasterBank;
use App\Models\MasterParameter;
use App\Models\Reseller;
use App\Models\ResellerBank;
use App\Models\ResellerCommissionWithdrawal;
use App\Models\ResellerTransaction;
use App\Models\ResellerVA;
use Illuminate\Foundation\Http\FormRequest;

class InternalAdjustmentRequest extends APIRequest
{
    public function all($keys = null)
    {
        $inputData = parent::all($keys);
        $resellerId = Reseller::where('reseller_id',$this->route('id'))->first()->id;

        $adjustmentType = $this->input('adjustment_type');
        switch($adjustmentType){
            case 'adjustment':
                $adjustmentType = 'Adjustment';
                break;
            case 'bonus':
                $adjustmentType = 'Bonus';
                break;
            case 'refund':
                $adjustmentType = 'Refund';
                break;
        }
        $commissionType = $this->input('commission_type');
        if($commissionType == 'commission'){
            $commissionType = ResellerTransaction::TX_TYPE_COMMISSION;
        } else {
            $commissionType = ResellerTransaction::TX_TYPE_POTENTIAL_COMMISSION;
        }
        $orderNo = $this->input('order_no');
        $nominal = $this->input('nominal');
        $commission = $this->input('commission');
        // if($adjustmentType == 'Adjustment' || $adjustmentType == 'Refund'){
        //     $nominal = - $nominal;
        //     $commission = - $commission;
        // }
        $reason = $this->input('reason');
        $password = $this->input('password');

        $inputData['adjustment_type'] = $adjustmentType;
        $inputData['commission_type'] = $commissionType;
        $inputData['order_no'] = $orderNo;
        $inputData['nominal'] = $nominal;
        $inputData['commission'] = $commission;
        $inputData['reason'] = $reason;
        $inputData['password'] = $password;
        $inputData['reseller_id'] = $resellerId;

        return $inputData;
    }

    //approve transaksi

    public function updateBalance($commissionType, $commission, $nominal, $adjustmentType)
    {
        // if($adjustmentType == 'Adjustment' || $adjustmentType == 'Refund'){
        //     $nominal = - $nominal;
        //     // $commission = - $commission;
        // }
        if ($commissionType == 'Comission' || $adjustmentType == 'Bonus') {
            return [
                'commission_amount' => $commission->commission_amount + $nominal
            ];
        }else {
            return [
                'potential_amount' => $commission->potential_amount + $nominal
            ];
        }

    }

    public function createTxRecord($orderNo, $commissionType, $nominal, $reason, $adjustmentType)
    {
        $referenceName = null;
        // if($adjustmentType == 'Adjustment' || $adjustmentType == 'Refund'){
        //     $nominal = - $nominal;
        //     // $commission = - $commission;
        // }
        if($orderNo != null){
            $referenceName = 'order';
        }
        if($adjustmentType == 'Bonus'){
            $commissionType = 'Bonus';
            $reqNo = strtoupper('BS'.substr(uniqid(), -8));
        } else {
            $reqNo = strtoupper('ADJ'.substr(uniqid(), -8));
        }
        return [
            'reference_name' => $referenceName,
            'reference_id' => $orderNo?? null, 
            'type' => $commissionType,
            'status' => 'Success',
            'order_status' => $adjustmentType,
            'amount' => $nominal,
            'remarks' => $reason,
            'request_no' => $reqNo
        ];
    }


    public function createLedgerRecord($commission, $tx, $commissionType, $nominal, $id, $adjustmentType, $reason)
    {
        if($adjustmentType == 'Adjustment'|| $adjustmentType == 'Refund'){
            $type = 'Debit';
        }else{
            $type = 'Credit';
        }

        if($commissionType == ResellerTransaction::TX_TYPE_COMMISSION){
            $ledgerType = 'Commission';
        } else {
            $ledgerType = 'Potential';
        }
        
        return [
            'commission_id' => $commission->id,
            'transaction_id' => $tx->id, 
            'reseller_id' => $id,
            'type' => $type,
            'commission_type' => $ledgerType,
            'amount' => abs($nominal),
            'remarks' => $reason
        ];
    }

    public function transformOverbooking($resellerId, $nominal, $adjustmentType, $id, $reason = '')
    {
        $resellerBank = ResellerVA::where('reseller_id', $resellerId)->first();
        $operational = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','OPERATIONAL')->first()->value??'********';
        $operational_name = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','OPERATIONAL_NAME')->first()->value??'********';
        if($adjustmentType == 'Bonus'){
            $rq = TopupTransfer::rq();
            $rq["UserReferenceNumber"] = strtoupper(getenv('DANAMON_PARTNER_ID').uniqid('vatu'));
            $rq["RequestTime"] = date("YmdHis");
            $rq["VirtualAccountNumber"] = $resellerBank->virtual_account_no;
            $rq["Amount"] = strval($nominal);
            $rq["DebitedAccountNumber"] = $operational;
            $rq["PaymentType"] = "O";
            return $rq;
        } else {
            $rq = TransferOverbooking::rq();
            $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').uniqid('EXTCR').'OV';
            $rq["RequestTime"] = date("YmdHis");
            $rq["SourceAccountNumber"] = $resellerBank->virtual_account_no;
            $rq["SourceCardNumber"] = '';
            $rq["BeneficiaryAccountNumber"] = $operational;
            $rq["BeneficiaryName"] = $operational_name;
            $rq["Amount"] = strval(abs($nominal*100));
            $rq["Description"] = $reason;
            $rq["TransactionDate"] =  date("Ymd");
            return $rq;
        }

    }

    

    public function rules()
    {
        return [
            'adjustment_type' => 'required',
            'commission_type' => 'required',
            'order_no' => 'nullable',
            'nominal' => 'required|numeric|min:0',
            'commission' => 'required|numeric|min:0',
            'reason' => 'nullable',
            'password' => 'nullable',
            'reseller_id' => 'required'
        ];
    }
}
