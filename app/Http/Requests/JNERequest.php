<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;
use App\Rules\DecimalPlaces;

class JNERequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
  
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'from' => 'required|min:8|string',
            'thru' => 'required|min:8|string',
            'weight' => ['required', 'numeric', new DecimalPlaces(2), 'gte:0'],
            // 'weight' => ['required', 'numeric|between:0,99.99|gte:0'],
        ];
    }
    
    public function credentials()
    {

    }

    public function messages()
    {
      return [            
        'from.required' => 'From is required',
        'thru.required' => 'thru is required',
        'weight.required' => 'Weight is required',
      ];
    }

    public function transform()
    {
      $d = $this->all();
      return [
          'username' => getenv('JNE_USERNAME'),
          'api_key' => getenv('JNE_API_KEY'),
          'from' => $d['from'],
          'thru' => $d['thru'],
          'weight' => $d['weight'],
        ];
    }

}
