<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ListApplicantsHistoryRequest extends FormRequest
{
    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $orderBy = $this->query('order_by');
        switch ($orderBy) {
            case 'name':
                $orderBy = 'name';
                break;
            case 'registration_date':
                $orderBy = 'created_date';
                break;
            case 'verification_date':
                $orderBy = 'action_date';
                break;
            case 'user':
                $orderBy = 'action_by';
                break;
            default:
                $orderBy;
                break;
        }

        $inputData['order_by'] = $orderBy ?? 'action_date';
        $inputData['sort_value'] = $this->input('sort_value') ?? 'desc';
        $inputData['search'] = $this->input('search');
        $inputData['per_page'] = $this->input('per_page') ?? 15;
        $inputData['page'] = $this->input('page') ?? 1;

        return $inputData;
    }

    public function rules()
    {
        return [
            'order_by' => 'nullable',
            'sort_value' => 'nullable',
            'per_page' => 'nullable|numeric',
            'page' => 'nullable|numeric',
            'search' => 'nullable',
        ];
    }
}
