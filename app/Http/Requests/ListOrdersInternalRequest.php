<?php

namespace App\Http\Requests;

use App\Helpers\FormatHelper;
use App\Models\OrderReseller;
use App\Models\Reseller;
use Illuminate\Foundation\Http\FormRequest;

class ListOrdersInternalRequest extends FormRequest
{
    protected $id;

    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $transactionStatus = $this->input('order_status');
        switch ($transactionStatus) {
            case 'awaiting_payment':
                $statusQuery = [
            OrderReseller::ORDER_RESELLER_MENUNGGU_PEMBAYARAN
                ];
                break;
            case 'onprocess':
                $statusQuery = [
            OrderReseller::ORDER_RESELLER_DIPROSES
                ];
                break;
            case 'packaged':
                $statusQuery = [
            OrderReseller::ORDER_RESELLER_DIKEMAS
                ];
                break;
            case 'pending':
                $statusQuery = [
            OrderReseller::ORDER_RESELLER_PENDING
                ];
                break;
            case 'new':
                $statusQuery = [
            OrderReseller::ORDER_RESELLER_BARU
                ];
                break;
            case 'sent':
                $statusQuery = [
            OrderReseller::ORDER_RESELLER_DIKIRIM
                ];
                break;
            case 'received':
                $statusQuery = [
            OrderReseller::ORDER_RESELLER_DITERIMA
                ];
                break;
            case 'all':
                $statusQuery = null;
                break;
            case 'ongoing':
                $statusQuery = [
            OrderReseller::ORDER_RESELLER_PENDING,
            OrderReseller::ORDER_RESELLER_BARU,
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA
                ];
                break;
            case 'finished':
                $statusQuery = [
                    OrderReseller::ORDER_RESELLER_SELESAI
                        ];
                break;
            case 'canceled':
                $statusQuery = [
                    OrderReseller::ORDER_RESELLER_BATAL
                        ];
                break;
            default:
                $statusQuery = null;
                break;
        }


        $start = $this->input('date_from');
        $to = $this->input('date_to');

        $inputData['search'] = $this->input('search');
        $inputData['per_page'] = $this->input('per_page') ?? 15;
        $inputData['page'] = $this->input('page') ?? 1;
        $inputData['order_status'] = $statusQuery;
        $inputData['date_from'] = $start;
        $inputData['date_to'] = $to;
        $inputData['is_download'] = $this->input('is_download') ?? false;

        return $inputData;
    }

    public function rules()
    {
        return [
            'per_page' => 'nullable|numeric',
            'page' => 'nullable|numeric',
            'search' => 'nullable',
            'order_status' => 'nullable',
            'date_from' => 'nullable',
            'date_to' => 'nullable',
            'is_download' => 'nullable'
        ];
    }
}
