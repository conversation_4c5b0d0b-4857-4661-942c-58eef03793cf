<?php

namespace App\Http\Requests;

use App\Models\Reseller;
use Illuminate\Foundation\Http\FormRequest;

class ListResellersRequest extends FormRequest
{
    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $status = $this->input('reseller_status');
        switch ($status) {
            case 'all':
                $status = null;
                break;
            case 'active':
                $status = Reseller::RESELLER_ACTIVE;
                break;
            case 'freeze':
                $status = Reseller::RESELLER_REJECTED;
                break;
            case 'non-active':
                $status = Reseller::RESELLER_INACTIVE;
                break;
            default:
                $status = null;
                break;
        }

        $inputData['search'] = $this->input('search');
        $inputData['per_page'] = $this->input('per_page') ?? 15;
        $inputData['page'] = $this->input('page') ?? 1;
        $inputData['reseller_status'] = $status;

        return $inputData;
    }

    public function rules()
    {
        return [
            'per_page' => 'nullable|numeric',
            'page' => 'nullable|numeric',
            'search' => 'nullable',
            'reseller_status' => 'nullable',
        ];
    }
}
