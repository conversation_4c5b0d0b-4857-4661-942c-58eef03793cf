<?php

namespace App\Http\Requests;

use App\Models\OrderReseller;
use Illuminate\Foundation\Http\FormRequest;

class ListTransactionResellerRequest extends FormRequest
{
    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $inputData['start_date'] = $this->input('start_date');
        $inputData['end_date'] = $this->input('end_date');
        $inputData['per_page'] = $this->input('per_page') ?? 15;
        $inputData['page'] = $this->input('page') ?? 1;
        $inputData['order_status'] = $this->input('order_status');

        return $inputData;
    }

    public function rules()
    {
        return [
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date' => 'nullable|required_unless:start_date,null|date_format:Y-m-d',
            'per_page' => 'nullable|numeric',
            'page' => 'nullable|numeric',
            'search' => 'nullable',
            'order_status' => 'nullable',
        ];
    }
}
