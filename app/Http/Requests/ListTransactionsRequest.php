<?php

namespace App\Http\Requests;

use App\Helpers\FormatHelper;
use App\Models\OrderReseller;
use App\Models\Reseller;
use App\Models\ResellerTransaction;
use Illuminate\Foundation\Http\FormRequest;

class ListTransactionsRequest extends FormRequest
{
    protected $id;

    public function all($keys = null)
    {
        $this->id = $this->route('id');

        $resellerId = Reseller::where('reseller_id',$this->id)->first()->id;
        $inputData = parent::all($keys);

        $transactionStatus = $this->input('transaction_status');
        switch ($transactionStatus) {
            case 'all':
                $statusQuery = null;
                break;
            case 'cancel':
                $statusQuery = 'Batal';
                break;
            case 'waiting-payment':
                $statusQuery = 'Menunggu Pembayaran';
                break;
            case 'pending':
                $statusQuery = 'Pending';
                break;
            case 'new':
                $statusQuery = "Baru";
                break;
            case 'complete':
                $statusQuery = "Selesai";
                break;
            case 'refund':
                $statusQuery = "Refund";
                break;
            case 'adjustment':
                $statusQuery = 'Adjustment';
                break;
            case 'bonus':
                $statusQuery = 'Bonus';
                break;
            default:
                $statusQuery = null;
                break;
        }

        $commissionStatus = $this->input('commission_status');
        switch ($commissionStatus) {
            case 'all':
                $commissionQuery = null;
                break;
            case 'bonus':
                $commissionQuery = ResellerTransaction::TX_TYPE_BONUS;
                break;
            case 'potential_adjustment':
                $commissionQuery = "potential_adjustment";
                break;
            case 'potential':
                $commissionQuery = ResellerTransaction::TX_TYPE_POTENTIAL_COMMISSION;
                break;
            case 'commission':
                $commissionQuery = ResellerTransaction::TX_TYPE_COMMISSION;
                break;
            case 'commission_adjustment':
                $commissionQuery = "commission_adjustment";
                break;
            default:
                $commissionQuery = null;
                break;
        }

        $start = $this->input('date_from');
        $to = $this->input('date_to');

        $inputData['search'] = $this->input('search');
        $inputData['per_page'] = $this->input('per_page') ?? 15;
        $inputData['page'] = $this->input('page') ?? 1;
        $inputData['transaction_status'] = $statusQuery;
        $inputData['id'] = $resellerId;
        $inputData['date_from'] = $start;
        $inputData['date_to'] = $to;
        $inputData['commission_status'] = $commissionQuery;

        return $inputData;
    }

    public function rules()
    {
        return [
            'per_page' => 'nullable|numeric',
            'page' => 'nullable|numeric',
            'search' => 'nullable',
            'transaction_status' => 'nullable',
            'id' => 'nullable',
            'date_from' => 'nullable',
            'date_to' => 'nullable',
            'commission_status' => 'nullable',
        ];
    }
}
