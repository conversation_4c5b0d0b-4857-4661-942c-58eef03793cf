<?php

namespace App\Http\Requests;

use App\Helpers\FormatHelper;
use App\Models\OrderReseller;
use App\Models\Reseller;
use Illuminate\Foundation\Http\FormRequest;

class ListWithdrawalInternalRequest extends FormRequest
{
    protected $id;

    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $transactionStatus = $this->input('withdrawal_status');
        switch ($transactionStatus) {
            case 'all':
                $statusQuery = null;
                break;
            case 'waiting-approval':
                $statusQuery = "Menunggu Persetujuan";
                break;
            case 'approved':
                $statusQuery = "Disetujui";
                break;
            case 'rejected':
                $statusQuery = "Ditolak";
                break;
            case 'failed':
                $statusQuery = 'Gagal';
                break;
            case 'success':
                $statusQuery = 'Sukses';
                break;
            default:
                $statusQuery = null;
                break;
        }

        $start = $this->input('date_from');
        $to = $this->input('date_to');

        $inputData['search'] = $this->input('search');
        $inputData['per_page'] = $this->input('per_page') ?? 15;
        $inputData['page'] = $this->input('page') ?? 1;
        $inputData['withdrawal_status'] = $statusQuery;
        $inputData['date_from'] = $start;
        $inputData['date_to'] = $to;
        $inputData['is_download'] = $this->input('is_download') ?? false;

        return $inputData;
    }

    public function rules()
    {
        return [
            'per_page' => 'nullable|numeric',
            'page' => 'nullable|numeric',
            'search' => 'nullable',
            'withdrawal_status' => 'nullable',
            'date_from' => 'nullable',
            'date_to' => 'nullable',
            'is_download' => 'nullable'
        ];
    }
}
