<?php

namespace App\Http\Requests;

use App\Models\OrderReseller;
use Illuminate\Foundation\Http\FormRequest;

class ListWithdrawalRequest extends FormRequest
{
    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $inputData['start_date'] = $this->input('start_date');
        $inputData['end_date'] = $this->input('end_date');
        $inputData['per_page'] = $this->input('per_page') ?? 15;
        $inputData['page'] = $this->input('page') ?? 1;
        $inputData['status'] = $this->input('status');

        return $inputData;
    }

    public function rules()
    {
        return [
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date' => 'nullable|required_unless:start_date,null|date_format:Y-m-d|after:start_date',
            'per_page' => 'nullable|numeric',
            'page' => 'nullable|numeric',
            'status' => 'nullable|array',
        ];
    }
}
