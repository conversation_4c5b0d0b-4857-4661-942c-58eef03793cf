<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoginResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => 'required|email',
            'password' => 'required|string',
            'type' => 'required|in:customer,reseller'
        ];
    }

    public function credentials()
    {
        $d = $this->all();
        return [
            'username' => !empty($d['email']) ? $d['email'] : $d['username'],
            'password' => $d['password'],
            'reference_object' => $d['type'] == 'customer' ? 'rsl_customers' : 'reseller'
        ];
    }
}
