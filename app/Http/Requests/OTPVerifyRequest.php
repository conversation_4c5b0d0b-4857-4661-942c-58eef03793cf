<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class OTPVerifyRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return true;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => 'required|string',
            'phone' => 'required|string',
            'code'  => 'required'
        ];
    }

    public function transform() 
	{
        
        $d = $this->all();
        // dd(substr(explode('@',$d['email'])[0],0,20));
        return [
			'userid' => $d['phone'],
            /*
                1. Botika
                2. Email (CareOM)
                3. Mock 123456
            */
			'name' => getenv('OTP_MODE', '1') == '1' ? substr(explode('@',$d['email'])[0],0,20) : $d['email'],            
			'phone' => $d['phone'],
			'source' => 'CAREOM-Reseller',
            'code'   => $d['code']
		];
	}

}
