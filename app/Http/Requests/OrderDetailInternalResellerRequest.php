<?php

namespace App\Http\Requests;

use App\Models\OrderReseller;
use Illuminate\Foundation\Http\FormRequest;

class OrderDetailInternalResellerRequest extends FormRequest
{

    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $order = OrderReseller::where("order_no", $this->route('id'))->first();

        $inputData['order_id'] = $order->id;
        $inputData['per_page'] = $this->input('per_page') ?? 4;
        $inputData['page'] = $this->input('page') ?? 1;

        return $inputData;
    }

    public function rules()
    {
        return [
            'order_id' => 'required',
            'page' => 'nullable',
            'per_page'=> 'nullable'
        ];
    }
}
