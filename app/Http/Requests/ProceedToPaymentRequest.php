<?php

namespace App\Http\Requests;

use App\Models\OrderReseller;
use App\Models\ResellerToken;
use App\Models\ResellerCustomer;
use App\Rules\OrderAuthorization;
use Illuminate\Support\Facades\Auth;
use App\Models\ResellerCustomerShipment;
use Illuminate\Foundation\Http\FormRequest;

class ProceedToPaymentRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'order_no' => [
                'required',new OrderAuthorization,
                function($attribute,$value,$fail)
                {
                    $order = OrderReseller::where('order_no',$value)->first();
                    if ($order->transporter_id == null) {
                        $fail('Transporter is required');
                    }

                    if ((int)$order->pay_amount <= 10000) {
                        $fail('Pesanan tidak memenuhi minimum pembayaran');
                    }
                }
            ],
            'customer_shipment_id' => [
                'required',
                function($attribute,$value,$fail)
                {
                    $user = Auth::guard('sanctum')->user();
                    $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;
                    $customer_shipment = ResellerCustomerShipment::where('id',$value)
                                        ->where('customer_id',$customer_id)->where('is_active',1)
                                        ->first();

                    if (!$customer_shipment) {
                        $fail('Customer shipment not found');
                    }
                }
                ]
        ];
    }

    public function transformUpdateHeader()
    {
        $user = Auth::guard('sanctum')->user();
        $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;
        $order = OrderReseller::where('order_no',$this->input('order_no'))->first();
        $customer_shipment = ResellerCustomerShipment::where('id',$this->input('customer_shipment_id'))->first();
        $customer = ResellerCustomer::where('id',$customer_id)->first();
        

        return [
            'customer_name' => $customer != null && $customer->name != null ? $customer->name : $customer_shipment->name,
            'customer_phone_number' => $customer != null && $customer->phone_number != null ? $customer->phone_number : $customer_shipment->phone_number,
            'customer_email' => $customer != null && $customer->email != null ? $customer->email : $customer_shipment->email,
            'customer_shipment_id' => $this->input('customer_shipment_id'),
            'customer_shipment_name' => $customer_shipment->name,
            'customer_shipment_address' => $customer_shipment->address,
            'customer_shipment_region_name' => $customer_shipment->region_name,
            'customer_shipment_city_name' => $customer_shipment->city_name,
            'customer_shipment_district_name' => $customer_shipment->district_name,
            'customer_shipment_subdistrict_name' => $customer_shipment->subdistrict_name,
            'customer_shipment_phone_number' => $customer_shipment->phone_number,
            'customer_shipment_zip_code' => $customer_shipment->zip_code,
            'payment_method' => 'xendit',
        ];
    }
}
