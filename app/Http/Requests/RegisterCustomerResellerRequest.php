<?php

namespace App\Http\Requests;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Reseller;
use Illuminate\Support\Str;
use App\Helpers\FormatHelper;
use App\Models\MasterParameter;
use Illuminate\Validation\Rule;
use App\Models\ResellerMasterAddress;
use Illuminate\Validation\Rules\Password;
use Illuminate\Foundation\Http\FormRequest;

class RegisterCustomerResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    public function prepareForValidation()
    {
        $mp = MasterParameter::where('group_key','CHANNEL_CODE')->where('value','RESELLER')->first();
        $this->merge([
            'phone_number' => FormatHelper::formatPhoneNo($this->phone_number),
            'customer_shipments' => collect($this->input('customer_shipments'))->map(function ($shipment) {
                $shipment['phone_number'] = FormatHelper::formatPhoneNo($shipment['phone_number']);
                return $shipment;
            })->all(),
            'customer_id_con' => strtoupper($mp->key.$this->input('customer_id','').substr(Str::uuid(),-12))
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "full_name" => ["required","string", "regex:/^[A-Za-z0-9.,\s\-]+$/"],
            "phone_number" => ["required","numeric"],
            "email" => [
                "required",
                "email",
                function($attribute,$value,$fail)
                {
                    $u_c = User::where('email',$value)->where('reference_object','rsl_customers')->exists();
                    $user = User::where('email',$value)->where('reference_object','reseller')->exists();
                    $reseller = Reseller::where('email',$value)->exists();

                    if ($u_c == true) {
                        $fail('EMAIL SUDAH TERDAFTAR SEBAGAI CUSTOMER');
                    }
                    if ($user == true || $reseller == true) {
                        $fail('EMAIL ANDA SUDAH TERDAFTAR SEBAGAI EIGERPRENEUR');
                    }
                }
            ],
            "password" => ["required",Password::min(8)->mixedCase()->numbers()->symbols()],
            "password_confirmation" => ["required","same:password"],
            "customer_id" => ["required","string"],
            "customer_shipments.*.name" => ["required","string", "regex:/^[A-Za-z0-9.,\s\-]+$/"],
            "customer_shipments.*.phone_number" => ["required","numeric"],
            "customer_shipments.*.address" => ["required","string", "regex:/^[A-Za-z0-9.,\s\/\-]+$/"],

            // "customer_shipments.*.province_name" => ["required","string","exists:region,name"],
            // "customer_shipments.*.province_code" => ["required","string","exists:region,code"],
            // "customer_shipments.*.city_name" => ["required","string","exists:city,name"],
            // "customer_shipments.*.city_code" => ["required","string","exists:city,code"],
            // "customer_shipments.*.district_name" => ["required","string","exists:district,name"],
            // "customer_shipments.*.district_code" => ["required","string","exists:district,code"],
            // "customer_shipments.*.subdistrict_name" => ["required","string","exists:subdistrict,name"],
            // "customer_shipments.*.subdistrict_code" => ["required","string","exists:subdistrict,code"],
            // "customer_shipments.*.postal_code" => ["required","string","exists:subdistrict,postal_code"],

            "customer_shipments.*.province_name" => ["required","string","exists:rsl_master_address,region_name"],
            "customer_shipments.*.province_code" => ["required","string","exists:rsl_master_address,region_code"],
            "customer_shipments.*.city_name" => [
                "required","string",
                function($attribute,$value,$fail)
                {
                    $keys = explode('.',$attribute);
                    $index = $keys[1];
                    $city = ResellerMasterAddress::where('city_code',$this->input('customer_shipments.'.$index.'.city_code'))->first();
                    // dd(strtolower($city->city_name),strtolower($value),str_contains(strtolower($value),strtolower($city->city_name)));
                    if (!str_contains(strtolower($value),strtolower($city->city_name))) {
                        $fail('City name not found');
                    }
                }
            ],
            "customer_shipments.*.city_code" => ["required","string","exists:rsl_master_address,city_code"],
            "customer_shipments.*.district_name" => ["required","string","exists:rsl_master_address,district_name"],
            "customer_shipments.*.district_code" => ["required","string","exists:rsl_master_address,district_code"],
            "customer_shipments.*.subdistrict_name" => ["required","string","exists:rsl_master_address,sub_district_name"],
            "customer_shipments.*.subdistrict_code" => ["required","string","exists:rsl_master_address,sub_district_code"],
            "customer_shipments.*.postal_code" => ["required","string","exists:rsl_master_address,zip_code"],
            
            "customer_shipments.*.is_active" => ["required","boolean"],
        ];
    }

    public function transformUser()
    {
        $d = $this->all();
        return [
            'reference_id' => $d['customer_id_con'],
            'reference_object' => 'rsl_customers',
            'username' => $d['email'],
            'email' => $d['email'],
            'name' => $d['full_name'],
            'password' => bcrypt($d['password']),
            'is_active' => 0,
            'is_change_password' => 0,
            'created_by' => $d['full_name'],
        ];
    }

    public function transformCustomer()
    {
        $d = $this->all();
        return [
            'id' => $d['customer_id_con'],
            'name' => $d['full_name'],
            'phone_number' => $d['phone_number'],
            'email' => $d['email'],
            'is_active' => 0,
            'created_by' => $d['full_name']
        ];
    }

    public function transformCustomerShipment()
    {
        $header = $this->all();
        $datas = $this->input('customer_shipments');
        $d = [];
        foreach ($datas as $data) {
            $d[] = [
                'customer_id' => $header['customer_id_con'],
                'name' => $data['name'],
                'phone_number' => $data['phone_number'],
                'address' => $data['address'],
                'region_code' => $data['province_code'],
                'region_name' => $data['province_name'],
                'city_code' => $data['city_code'],
                'city_name' => $data['city_name'],
                'district_code' => $data['district_code'],
                'district_name' => $data['district_name'],
                'subdistrict_code' => $data['subdistrict_code'],
                'subdistrict_name' => $data['subdistrict_name'],
                'zip_code' => $data['postal_code'],
                'is_active' => $data['is_active'],
                'created_by' => $data['name']
            ];
        }
        return $d;
    }

    public function getActivationData()
    {
        $d = $this->all();
        return [
            'customer_id' => $d['customer_id_con'],
            'token' => bin2hex(random_bytes(16)),
            'expired_at' => Carbon::now()->addMinutes(5)->format('Y-m-d H:i:s')
        ];
    }

    public function messages(): array
    {
        return [
            'phone_number.required' => 'No. HP tidak boleh kosong.',
            'phone_number.numeric' => 'No. HP hanya boleh berupa angka.',
            'email.required' => 'Email tidak boleh kosong.',
            'email.email' => 'Format email salah.',
            'customer_shipments.*.address.required' => 'Alamat tidak boleh kosong.',
            'customer_shipments.*.address.regex' => 'Alamat tidak sesuai format. Pastikan hanya terdapat angka 0-9 dan minimal 12 huruf.',
        ];
    }
}
