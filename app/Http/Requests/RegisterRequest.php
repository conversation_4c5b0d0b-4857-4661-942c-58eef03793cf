<?php

namespace App\Http\Requests;

use App\Models\Customer;
use Illuminate\Support\Facades\Crypt;

class RegisterRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => [
                'required',
                'email',
                'max:255',
                // 'unique:user,email',
                'regex:/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/'
            ],
            'password' => [
                'required',
                'string',
                'min:8',
                'regex:/^(?=.*?[A-Z])(?=.*?[0-9])(?=.*?[*&^%$#@!]).{8,}$/'
            ],
            'password_confirmation' => ['required', 'same:password'],
            'owner_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[A-Za-z\s]+$/'
            ],
            'phone_number' => 'required|regex:/^\d{1,20}$/',
            'instance_name' => 'required|string|max:255',
            'shipment_address' => 'required|string|max:1000',
            'shipment_province_code' => 'nullable|string|exists:rsl_master_address,region_code',
            'shipment_province' => 'required|string|exists:rsl_master_address,region_name',
            'shipment_city_code' => 'nullable|string|exists:rsl_master_address,city_code',
            'shipment_city' => 'required|string',
            'shipment_district_code' => 'nullable|string|exists:rsl_master_address,district_code',
            'shipment_district' => 'required|string|exists:rsl_master_address,district_name',
            // 'shipment_subdistrict_code' => 'nullable|string|exists:rsl_master_address,subdistrict_name',
            // 'shipment_subdistrict' => 'required|string|exists:rsl_master_address,subdistrict_name',
            'shipment_zip_code' => 'required|regex:/^\d{1,10}$/',
            'npwp' => [
                'required',
                'regex:/^\d{15,16}$/',
                // 'unique:customers,npwp',
            ],
            // 'npwp_file' => 'required|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'npwp_file' => 'required|string',
            'tax_type' => 'required|string|in:PPN,Non-PPN,Wapu',
            'ktp' => [
                'nullable',
                'regex:/^\d{15,16}$/',
                // 'unique:customers,national_id',
            ],
            'ktp_file' => 'nullable|string',
            'tax_invoice' => 'nullable|string|in:02,03,04,07',
            'npwp_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[A-Za-z\s]+$/'
            ],
            'npwp_address' => 'required|string|max:1000',
            'npwp_province_code' => 'nullable|string|exists:rsl_master_address,region_code',
            'npwp_province' => 'required|string|exists:rsl_master_address,region_name',
            'npwp_city_code' => 'nullable|string|exists:rsl_master_address,city_code',
            'npwp_city' => 'required|string',
            'npwp_district_code' => 'nullable|string|exists:rsl_master_address,district_code',
            'npwp_district' => 'required|string|exists:rsl_master_address,district_name',
            'npwp_zip_code' => 'required|regex:/^\d{1,10}$/',
        ];
    }

    public function withValidator($validator) {
        $validator->after(function ($validator) {
            $npwp = $this->npwp;
            $ktp = $this->ktp;
            $email = $this->email;

            $customers = Customer::whereNotNull('email')
                ->orWhereNotNull('npwp')
                ->orWhereNotNull('national_id')
                ->get();

            if ($email) {
                $existingEmail = $customers->first(function ($customer) use ($email) {
                    if (!$customer->email) return false;

                    try {
                        return Crypt::decryptString($customer->email) === $email;
                    } catch (\Exception $e) {
                        return $customer->email === $email;
                    }
                });

                if ($existingEmail) {
                    $validator->errors()->add('email', 'Email sudah terdaftar.');
                }
            }

            if ($npwp) {
                $existingNPWP = $customers->first(function ($customer) use ($npwp) {
                    if (!$customer->npwp) return false;

                    try {
                        return Crypt::decryptString($customer->npwp) === $npwp;
                    } catch (\Exception $e) {
                        return $customer->npwp === $npwp;
                    }
                });

                if ($existingNPWP) {
                    $validator->errors()->add('npwp', 'NPWP sudah terdaftar.');
                }
            }
            
            if ($ktp) {
                $existingKTP = $customers->first(function ($customer) use ($ktp) {
                    if (!$customer->national_id) return false;

                    try {
                        return Crypt::decryptString($customer->national_id) === $ktp;
                    } catch (\Exception $e) {
                        return $customer->national_id === $ktp;
                    }
                });

                if ($existingKTP) {
                    $validator->errors()->add('ktp', 'KTP sudah terdaftar.');
                }
            }
        });
    }
}