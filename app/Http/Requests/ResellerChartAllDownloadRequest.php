<?php

namespace App\Http\Requests;

use App\Models\Reseller;
use Illuminate\Foundation\Http\FormRequest;

class ResellerChartAllDownloadRequest extends FormRequest
{
    public function all($keys = null)
    {
        $inputData = parent::all($keys);
        $inputData['date_from'] = $this->input('date_from');
        $inputData['date_to'] = $this->input('date_to');

        return $inputData;
    }
    public function rules()
    {
        return [
            'date_from' => 'nullable',
            'date_to' => 'nullable',
        ];
    }
}
