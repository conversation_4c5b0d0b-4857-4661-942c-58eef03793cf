<?php

namespace App\Http\Requests;

use App\Models\Reseller;
use Illuminate\Foundation\Http\FormRequest;

class ResellerChartAllRequest extends FormRequest
{
    protected $id;

    public function all($keys = null)
    {
        $inputData = parent::all($keys);
        $inputData['displayed_data'] = $this->input('displayed_data');
        $inputData['date_from'] = $this->input('date_from');
        $inputData['date_to'] = $this->input('date_to');

        return $inputData;
    }
    public function rules()
    {
        return [
            'displayed_data' => 'nullable',
            'date_from' => 'nullable',
            'date_to' => 'nullable',
        ];
    }
}
