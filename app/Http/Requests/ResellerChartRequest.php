<?php

namespace App\Http\Requests;

use App\Models\Reseller;
use Illuminate\Foundation\Http\FormRequest;

class ResellerChartRequest extends FormRequest
{
    protected $id;

    public function all($keys = null)
    {
        $this->id = $this->route('id');
        $resellerId = Reseller::where('reseller_id',$this->id)->first()->id;
        $data = $this->input('displayed_data');
        $inputData['id'] = $resellerId;
        $inputData['displayed_data'] = $data;

        return $inputData;
    }
    public function rules()
    {
        return [
            'displayed_data' => 'nullable',
            'id' => 'required'
        ];
    }
}
