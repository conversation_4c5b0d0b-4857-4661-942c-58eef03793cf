<?php

namespace App\Http\Requests;

use App\Models\Reseller;
use Illuminate\Foundation\Http\FormRequest;

class ResellerCommissionDetailRequest extends FormRequest
{
    protected $id;

    public function all($keys = null)
    {
        $data = $this->input('request_id');
        $inputData['request_id'] = $data;

        return $inputData;
    }
    public function rules()
    {
        return [
            'request_id' => 'required'
        ];
    }
}
