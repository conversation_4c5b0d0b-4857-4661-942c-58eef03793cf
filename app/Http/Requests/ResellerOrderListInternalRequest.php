<?php

namespace App\Http\Requests;

use App\Helpers\FormatHelper;
use App\Helpers\RestHelper;
use App\Models\OrderReseller;
use App\Models\Reseller;
use Illuminate\Foundation\Http\FormRequest;

class ResellerOrderListInternalRequest extends FormRequest
{
    protected $id;

    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $resellerId = Reseller::where("reseller_id", $this->route('id'))->first();

        $inputData['search'] = $this->input('search');
        $inputData['type'] = $this->input('type');
        $inputData['reseller_id'] = $resellerId->id;
        return $inputData;
    }

    public function rules()
    {
        return [
            'reseller_id' => 'required',
            'search' => 'nullable',
            'type' => 'nullable'
        ];
    }
}
