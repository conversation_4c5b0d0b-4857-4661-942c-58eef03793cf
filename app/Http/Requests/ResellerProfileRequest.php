<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Helpers\FormatHelper;

class ResellerProfileRequest extends APIRequest
{
    public function transform()
    {
        $d = $this->all();

        switch($d['type']) {
            case "profile":
                $data = [
                    'type' => $d['type'],
                    'name' => $d['name'],
                    'gender' => $d['gender'],
                    'date_of_birth' => $d['date_of_birth']
                ];
                break;
            case "password":
                $data = [
                    'type' => $d['type'],
                    'password' => Hash::make($d['password']),
                    'old_password' => $d['old_password']
                ];
            break;
            case "phone":
                $data = [
                    'type' => $d['type'],
                    'phone_number' => FormatHelper::formatPhoneNo($d['phone_number'])
                ];
            break;
            case "address":
                $data = [
                    'type' => $d['type'],
                    'address' => $d['address'],
                    'province_code' => $d['province_code']??'',
                    'city_code' => $d['city_code']??'',
                    'district_code' => $d['district_code']??'',
                    'zip_code' => $d['zip_code']
                ];
            break;
            case "bank":
                $data = [
                    'type' => $d['type'],
                    'bank_id' => $d['bank_id'],
                    'account_no' => $d['account_no'],
                    'account_name' => $d['account_name']
                ];
            break;
        }
        
        return $data;
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required_if:type,profile|string',
            'password' => 'required_if:type,password|string',
            'address' => 'required_if:type,address|string',
            'province_code' => 'nullable',
            'city_code' => 'nullable',
            'district_code' => 'nullable',
            'zip_code' => 'nullable',
            'bank_id' => 'required_if:type,bank|string',
            'account_no' => 'required_if:type,bank|string',
            'account_name' => 'required_if:type,bank|string',
        ];
    }
}