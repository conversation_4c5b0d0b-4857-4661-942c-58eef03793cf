<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Models\ResellerRegistration;
use App\Helpers\FormatHelper;
use App\Helpers\FileHelper;
use App\Helpers\RestHelper;

class ResellerRegisterRequest extends APIRequest
{
    use FileHelper;
    use RestHelper;
    public function transform() {
        $d = $this->all();

        $img_i = $d['file_identity'];
        $img_n = $d['file_npwp'];
        $image_type = ['jpg','gif','jpeg','png','swf','psd','bmp','tiff','tiff','jpc','jp2','jpx','jb2','swc','iff','wbmp','xbm','ico','webp'];
        $extension_i = pathinfo($img_i)['extension'] ?? '';
        $extension_n = pathinfo($img_n)['extension'] ?? '';

        $cek_image = in_array($extension_i, $image_type);
        $cek_image_n = in_array($extension_n, $image_type);
        
        if ( $cek_image == false
            && $cek_image_n == false
        ) {
            return $this->sendError('png, jpg, jpeg files only', 400);
        }

        $is_double = $img_i == $img_n;
        $img_ktp = $this->fileTransfer($img_i, 'ktp', !$is_double);
        if ($img_ktp['error'] == true) {
            return $this->sendError($img_ktp['message']);
        }

        $img_npwp = $this->fileTransfer($img_n, 'npwp');
        if ($img_npwp['error'] == true) {
            return $this->sendError($img_npwp['message']);
        }

        //upload ktp to S3
        // $fileNationalId = $this->file('file_identity');
        // $name = uniqid();
        // $content = file_get_contents($fileNationalId);
        // $fileName = $name . '.' . $fileNationalId->extension();
        $nameFileNationalId = "/".$img_ktp['filepath'];
        //AWS S3 ENV has to match with settings in filesystems.php
        // Storage::disk('s3')->put(substr($nameFileNationalId, 1), $content);

        //upload NPWP to S3
        // $fileNPWP = $this->file('file_npwp');
        // $name = uniqid();
        // $content = file_get_contents($fileNPWP);
        // $fileName = $name . '.' . $fileNPWP->extension();
        $nameFileNPWP = "/".$img_npwp['filepath'];
        // Storage::disk('s3')->put(substr($nameFileNPWP, 1), $content);

        // shuffle( 6 digits date(dmy) + 6 digits setelah 6 digit terdepan national id + 4 random int)
        $resellerId = str_shuffle(date('dmy') . substr($this->national_id, 6, 6) . mt_rand(1000, 9999));

        return [
            'reseller_id' => $resellerId,
            'email' => $d['email'],
            'phone_number' => FormatHelper::formatPhoneNo($d['phone_number']),
            'name' => $d['name'],
            'password' => Hash::make($d['password']),
            'gender' => $d['gender'],
            'date_of_birth' => $d['date_of_birth'],
            'address' => $d['address'],
            'province_code' => $d['province_code'],
            'city_code' => $d['city_code'],
            'district_code' => $d['district_code'],
            'zip_code' => $d['zip_code'],
            'national_id' => $d['national_id'],
            'national_id_file' => $nameFileNationalId,
            'npwp' => $d['npwp'],
            'npwp_file' => $nameFileNPWP,
            'status' => ResellerRegistration::REGISTRATION_CREATED,
        ];
    }
    

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'nullable',
            'body' => 'nullable',
            'file_identity' => 'required',
            'file_npwp' => 'required'
        ];
    }
    public function messages()
    {
      return [            
        'file_identity.required' => "gambar KTP tidak ditemukan/gagal upload, silahkan coba lagi",
        'file_npwp.required' => "gambar NPWP tidak ditemukan/gagal upload, silahkan coba lagi",
      ];
    }
  
}
