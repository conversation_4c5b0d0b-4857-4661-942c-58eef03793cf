<?php

namespace App\Http\Requests;

use App\Helpers\FormatHelper;
use App\Models\OrderReseller;
use App\Models\Reseller;
use Illuminate\Foundation\Http\FormRequest;

class ResellerSummaryDashboardRequest extends FormRequest
{
    protected $id;

    public function all($keys = null)
    {

        $start = $this->input('date_from');
        $to = $this->input('date_to');
        $inputData['date_from'] = $start;
        $inputData['date_to'] = $to;

        return $inputData;
    }

    public function rules()
    {
        return [
            'date_from' => 'nullable',
            'date_to' => 'nullable',
        ];
    }
}
