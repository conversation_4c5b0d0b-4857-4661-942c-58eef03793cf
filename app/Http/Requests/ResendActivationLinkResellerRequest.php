<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;

class ResendActivationLinkResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => [
                'required','email',
                // 'exists:rsl_customer_activation_link,customer_id'
                function ($attribute,$value,$fail) {
                    $user = User::where('email',$value)->where('reference_object','rsl_customers')
                                ->first();

                    if (!$user) {
                       $fail('User is not exists.');
                    } elseif ($user->is_active == 1) {
                        $fail('User is already active.');
                    }
                }
                ]
        ];
    }
}
