<?php

namespace App\Http\Requests;

use App\Models\User;
use App\Helpers\FormatHelper;
use App\Models\Reseller;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;
use Illuminate\Foundation\Http\FormRequest;

class Step1RegisterCustomerResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    public function prepareForValidation()
    {
        $this->merge([
            'phone_number' => FormatHelper::formatPhoneNo($this->phone_number)
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "full_name" => ["required","string"],
            "phone_number" => ["required","numeric"],
            "email" => [
                "required",
                "email",
                function($attribute,$value,$fail)
                {
                    $u_c = User::where('email',$value)->where('reference_object','rsl_customers')->exists();
                    $user = User::where('email',$value)->where('reference_object','reseller')->exists();
                    $reseller = Reseller::where('email',$value)->exists();

                    if ($u_c == true) {
                        $fail('EMAIL SUDAH TERDAFTAR SEBAGAI CUSTOMER');
                    }
                    if ($user == true || $reseller == true) {
                        $fail('EMAIL ANDA SUDAH TERDAFTAR SEBAGAI EIGERPRENEUR');
                    }
                }
            ],
            "password" => ["required",Password::min(8)->mixedCase()->numbers()->symbols()],
            "password_confirmation" => ["required","same:password"],
            "customer_id" => ["required","string"],
        ];
    }
}
