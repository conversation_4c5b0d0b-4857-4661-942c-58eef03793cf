<?php

namespace App\Http\Requests;

use App\Helpers\FormatHelper;
use App\Models\ResellerMasterAddress;
use Illuminate\Foundation\Http\FormRequest;

class Step2RegisterCustomerResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    public function prepareForValidation()
    {
        $this->merge([
            'phone_number' => FormatHelper::formatPhoneNo($this->phone_number)
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "name" => ["required","string"],
            "phone_number" => ["required","numeric"],
            "address" => ["required","string"],
            
            // "province_name" => ["required","string","exists:region,name"],
            // "province_code" => ["required","string","exists:region,code"],
            // "city_name" => ["required","string","exists:city,name"],
            // "city_code" => ["required","string","exists:city,code"],
            // "district_name" => ["required","string","exists:district,name"],
            // "district_code" => ["required","string","exists:district,code"],
            // "subdistrict_name" => ["required","string","exists:subdistrict,name"],
            // "subdistrict_code" => ["required","string","exists:subdistrict,code"],
            // "postal_code" => ["required","string","exists:subdistrict,postal_code"],
            
            "province_name" => ["required","string","exists:rsl_master_address,region_name"],
            "province_code" => ["required","string","exists:rsl_master_address,region_code"],
            "city_name" => [
                "required","string",
                function($attribute,$value,$fail)
                {
                    $city = ResellerMasterAddress::where('city_code',$this->input('city_code'))->first();
                    // dd(strtolower($city->city_name),strtolower($value),str_contains(strtolower($value),strtolower($city->city_name)));
                    if (!str_contains(strtolower($value),strtolower($city->city_name))) {
                        $fail('City name not found');
                    }
                }
            ],
            "city_code" => ["required","string","exists:rsl_master_address,city_code"],
            "district_name" => ["required","string","exists:rsl_master_address,district_name"],
            "district_code" => ["required","string","exists:rsl_master_address,district_code"],
            "subdistrict_name" => ["required","string","exists:rsl_master_address,sub_district_name"],
            "subdistrict_code" => ["required","string","exists:rsl_master_address,sub_district_code"],
            "postal_code" => ["required","string","exists:rsl_master_address,zip_code"],

            "is_active" => ["required","boolean"],
        ];
    }
}
