<?php

namespace App\Http\Requests;

use App\Models\ResellerArticleStock;
use Illuminate\Foundation\Http\FormRequest;

class StockCustomerResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'article' => ['required','exists:article,article'],
            'qty' => ['required','numeric', 'min:0']
        ];
    }
}
