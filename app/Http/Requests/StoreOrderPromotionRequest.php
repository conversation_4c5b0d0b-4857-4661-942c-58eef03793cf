<?php

namespace App\Http\Requests;

use App\Models\Coupon;
use App\Models\OrderReseller;
use App\Models\ResellerOrderPromotion;
use App\Models\Voucher;
use App\Rules\OrderAuthorization;
use Illuminate\Foundation\Http\FormRequest;

class StoreOrderPromotionRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'order_no' => ['required', new OrderAuthorization,
            function($attribute,$value,$fail){
                $order = OrderReseller::where('order_no',$this->input('order_no'))->first();

                if ($order->order_status != OrderReseller::ORDER_RESELLER_PENDING) {
                    $fail('Order status is not '.OrderReseller::ORDER_RESELLER_PENDING);
                }

                if ($order->total_amount <= 10000) {
                    $fail('Pesanan telah mencapai batas minimum pembelian!');
                }
            }
            ],
            'discount_type' => ['required','in:voucher,coupon'],
            'discount_id' => [
                'required',
                function($attribute,$value,$fail)
                {
                    $order = OrderReseller::where('order_no',$this->input('order_no'))->first();
                    $type = $this->input('discount_type');

                    if (strtolower($type) == 'coupon') {
                        $m = Coupon::where('id',$value)->first();
                        $ids = $order->items()->pluck('id')->toArray();
                        $ids[] = $order->id;
                        $exs = ResellerOrderPromotion::whereIn('reference_id',$ids)->where('discount_id',$value)->exists();
                        if ($exs) {
                            $fail('Coupon already applied');
                        }
                    }

                    if (strtolower($type) == 'voucher') {
                        $m = Voucher::where('id',$value)->active()->activeDate()->first();
                        $exs = ResellerOrderPromotion::where('reference_id',$order->id)->where('discount_id',$value)->exists();
                        if ($exs) {
                            $fail('Voucher already applied');
                        }
                        if (!$m ||  ($m->master->is_multiple == 1 && $m->remaining_amount <= 0) || ($m->master->is_multiple == 0 && $m->is_used == 1)) {
                            $fail('Voucher is unavailable');
                        }
                    }

                    if ($m == null) {
                        $fail($type.' is not found');
                    }
                }
            ]
        ];
    }
}
