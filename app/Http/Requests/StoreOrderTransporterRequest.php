<?php

namespace App\Http\Requests;

use App\Models\OrderReseller;
use App\Rules\OrderAuthorization;
use Illuminate\Foundation\Http\FormRequest;

class StoreOrderTransporterRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'order_no' => [
                'required',
                New OrderAuthorization,
                function($attribute,$value,$fail){
                    $order = OrderReseller::where('order_no',$value)->first();

                    if ($order->order_status != OrderReseller::ORDER_RESELLER_PENDING) {
                        $fail('Order status is not pending');
                    }
                }
            ],
            'transporter_id' => ['required','exists:rsl_transporters,transporter_id'],
            'shipment_method' => ['required'],
            'shipment_charges' => ['required','numeric','min:1']
        ];
    }
}
