<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;
use App\Models\Reseller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Helpers\FormatHelper;

class TopSellingItemsRequest extends APIRequest
{
    protected $id;
    public function transform()
    {
        $this->id = $this->route('id');
        $resellerId = Reseller::where('reseller_id',$this->id)->first()->id;
        if(!$resellerId){
            $this->sendError('reseller not found', 404);
        }
        $data['id'] = $resellerId;
        
        return $data;
    }
    
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'id' => 'nullable',
        ];
    }
}