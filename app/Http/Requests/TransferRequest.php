<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;
use Illuminate\Validation\Rule;
use App\Helpers\Danamon\DanamonConfig;
class   TransferRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
      $danamonAction = array_keys(DanamonConfig::resources());
        return [
            'module' => ['required', Rule::in($danamonAction)],
            'payload' => 'required'        
        ];
    }

    public function transform()
    {
        $d = $this->all()['payload'];
        $d["UserReferenceNumber"] = strtoupper(getenv('DANAMON_PARTNER_ID').$d['UserReferenceNumber']??uniqid('uatest'));
        $d["RequestTime"] = date("YmdHis");
        return $d;
    }

}
