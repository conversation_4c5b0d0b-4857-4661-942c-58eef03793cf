<?php

namespace App\Http\Requests;

use App\Helpers\Danamon\Specification\TopupTransfer;
use App\Http\Requests\APIRequest;
class TransferVAInquiryRequest extends APIRequest
{
  
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'va_number'     => 'required',
            'ref_num'       => 'required'
        ];
    }

    public function transform($operational = '********')
    {
        $d = $this->all();
        $rq = TopupTransfer::rq();
        $rq["UserReferenceNumber"] = $d['ref_num'];
        $rq["RequestTime"] = date("YmdHis");
        $rq["VirtualAccountNumber"] = $d['va_number'];
        $rq["DebitedAccountNumber"] = $operational;
        return $rq;
    }



}
