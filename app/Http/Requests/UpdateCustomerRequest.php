<?php

namespace App\Http\Requests;

use App\Models\Customer;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Validation\Rule;

class UpdateCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'sap_id' => 'nullable|string|max:10',
            'owner_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[A-Za-z\s]+$/'
            ],
            'password' => [
                'nullable',
                'string',
                'min:8',
                'regex:/^(?=.*?[A-Z])(?=.*?[0-9])(?=.*?[*&^%$#@!]).{8,}$/'
            ],
            'password_confirmation' => ['nullable', 'same:password'],
            'email' => [
                'required',
                'email',
                'max:255',
                // Rule::unique('customers', 'email')->ignore($this->route('id'), 'customer_id'),
                'regex:/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/'
            ],
            'phone_number' => 'required|regex:/^\d{1,20}$/',
            'instance_name' => 'required|string|max:255',
            'shipment_address' => 'required|string|max:1000',
            'shipment_province_code' => 'nullable|string|exists:rsl_master_address,region_code',
            'shipment_province' => 'required|string|exists:rsl_master_address,region_name',
            'shipment_city_code' => 'nullable|string|exists:rsl_master_address,city_code',
            'shipment_city' => 'required|string',
            'shipment_district_code' => 'nullable|string|exists:rsl_master_address,district_code',
            'shipment_district' => 'required|string|exists:rsl_master_address,district_name',
            // 'shipment_subdistrict_code' => 'nullable|string|exists:rsl_master_address,subdistrict_name',
            // 'shipment_subdistrict' => 'nullable|string|exists:rsl_master_address,subdistrict_name',
            'shipment_zip_code' => 'required|regex:/^\d{1,10}$/',
            'npwp' => [
                'required',
                'regex:/^\d{15,16}$/',
                // Rule::unique('customers', 'npwp')->ignore($this->route('id'), 'customer_id')
            ],
            'npwp_name' => [
                'required',
                'string',
                'max:255',
                'regex:/^[A-Za-z\s]+$/'
            ],
            // 'npwp_file' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:8192',
            'npwp_file' => 'nullable|string',
            'tax_type' => 'required|string|in:PPN,Non-PPN,Wapu',
            'npwp_address' => 'required|string|max:1000',
            'npwp_province_code' => 'nullable|string|exists:rsl_master_address,region_code',
            'npwp_province' => 'required|string|exists:rsl_master_address,region_name',
            'npwp_city_code' => 'nullable|string|exists:rsl_master_address,city_code',
            'npwp_city' => 'required|string',
            'npwp_district_code' => 'nullable|string|exists:rsl_master_address,district_code',
            'npwp_district' => 'required|string|exists:rsl_master_address,district_name',
            'npwp_zip_code' => ' required|regex:/^\d{1,10}$/',
            'tax_invoice' => 'nullable|string|in:02,03,04,07',
            'discount_percent' => 'required|numeric|min:0|max:100',
            'top_days' => 'required|string|in:Cash,7 Days,14 Days',
            'credit_limit' => 'required|numeric|min:0',
            // 'status' => 'nullable|string|in:Draft,Baru,Partially Approved,Revised,Rejected,Approved',
            // 'updated_by' => 'nullable|string'
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $email = $this->email;
            $npwp = $this->npwp;
            $currentId = $this->route('id');

            $customers = Customer::where(function ($query) use ($email, $npwp) {
                if ($email) {
                    $query->orWhereNotNull('email');
                }
                if ($npwp) {
                    $query->orWhereNotNull('npwp');
                }
            })->get();

            if ($email) {
                $existingEmail = $customers->first(function ($customer) use ($email, $currentId) {
                    if (!$customer->email) return false;

                    try {
                        return Crypt::decryptString($customer->email) === $email && $customer->customer_id !== $currentId;
                    } catch (\Exception $e) {
                        return $customer->email === $email && $customer->customer_id !== $currentId;
                    }
                });

                if ($existingEmail) {
                    $validator->errors()->add('email', 'Email sudah terdaftar.');
                }
            }

            if ($npwp) {
                $existingNPWP = $customers->first(function ($customer) use ($npwp, $currentId) {
                    if (!$customer->npwp) return false;

                    try {
                        return Crypt::decryptString($customer->npwp) === $npwp && $customer->customer_id !== $currentId;
                    } catch (\Exception $e) {
                        return $customer->npwp === $npwp && $customer->customer_id !== $currentId;
                    }
                });

                if ($existingNPWP) {
                    $validator->errors()->add('npwp', 'NPWP sudah terdaftar.');
                }
            }
        });
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'error' => true,
            'status' => 'fail',
            'message' => 'Validation failed',
            'errors' => $validator->errors(),
        ], 422));
    }
}
