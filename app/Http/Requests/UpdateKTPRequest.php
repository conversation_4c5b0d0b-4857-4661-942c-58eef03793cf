<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class UpdateKTPRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'nullable',
            'instance' => 'nullable',
            'email'=> 'nullable|email',
            'phone'=> 'nullable|numeric|digits_between:12,14',
            'address'=> 'nullable',
            'ktp' => 'nullable|numeric',
            'file' => 'nullable',

        ];
    }

    public function messages()
  {
    return [            
      'file.max' => "File maxsize is 2MB",
    ];
  }

}
