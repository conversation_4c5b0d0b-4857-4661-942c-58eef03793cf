<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class UpdateNPWPRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'=> 'nullable',
            'npwp'=> 'nullable',
            'address'=> 'nullable',
            'tax_invoice' => 'nullable',
            'file' => 'nullable',
            'tax_type' => 'nullable'

        ];
    }

}
