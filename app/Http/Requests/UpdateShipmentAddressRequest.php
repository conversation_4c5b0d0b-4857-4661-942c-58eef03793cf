<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class UpdateShipmentAddressRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'shipment_address' => 'required|string|max:1000',
            'shipment_province_code' => 'nullable|string|exists:rsl_master_address,region_code',
            'shipment_province' => 'required|string|exists:rsl_master_address,region_name',
            'shipment_city_code' => 'nullable|string|exists:rsl_master_address,city_code',
            'shipment_city' => 'required|string',
            'shipment_district_code' => 'nullable|string|exists:rsl_master_address,district_code',
            'shipment_district' => 'required|string|exists:rsl_master_address,district_name',
            // 'shipment_subdistrict_code' => 'nullable|string|exists:rsl_master_address,subdistrict_name',
            // 'shipment_subdistrict' => 'required|string|exists:rsl_master_address,subdistrict_name',
            'shipment_zip_code' => 'required|regex:/^\d{1,10}$/',
        ];
    }
}
