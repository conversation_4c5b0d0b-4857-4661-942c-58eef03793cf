<?php

namespace App\Http\Requests;

use App\Http\Requests\APIRequest;

class UpdateTransactionRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'customer_id'           => 'required|string|exists:customers,customer_id',
            'distribution_channel'  => 'required|string|in:W3,RE,RD',
            'customer_shipment_id'  => 'required|string',
            'article_items'         => 'required|array|min:1',
            'article_items.*.article_id'    => 'required|string|exists:article,article',
            'article_items.*.article_qty'   => 'required|integer|min:1',
            // 'subtotal'              => 'required|numeric|min:0',
            // 'final_price'           => 'required|numeric|min:0',
            // 'shipping_charge'       => 'required|numeric|min:0',
            'total_discount'        => 'required|numeric|min:0',
        ];

        return $rules;
    }
}