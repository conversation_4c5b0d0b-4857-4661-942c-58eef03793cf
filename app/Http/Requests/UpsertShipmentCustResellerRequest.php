<?php

namespace App\Http\Requests;

use App\Models\User;
use App\Helpers\FormatHelper;
use App\Models\ResellerToken;
use Illuminate\Support\Facades\Auth;
use App\Models\ResellerMasterAddress;
use App\Models\ResellerCustomerShipment;
use Illuminate\Foundation\Http\FormRequest;

class UpsertShipmentCustResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    public function prepareForValidation()
    {
        $this->merge([
            'customer_shipments' => collect($this->input('customer_shipments'))->map(function ($shipment) {
                $shipment['phone_number'] = FormatHelper::formatPhoneNo($shipment['phone_number']);
                return $shipment;
            })->all(),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "email" => [
                "sometimes","email",
                function($attribute,$value,$fail)
                {
                    $user = User::where('email',$value)->where('reference_object','rsl_customers')->exists();

                    if ($user == true) {
                        $fail('This field only used for guest user');
                    }
                }
            ],
            "customer_id" => [
                "required",
                function ($attribute,$value,$fail)
                {
                    $user = Auth::guard('sanctum')->user();
                    $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;

                    if (!$customer_id) {
                        $fail('Customer not found');
                    } elseif ($value != $customer_id) {
                        $fail('You are not allowed to create/update shipment');
                    }
                }
                ],
            "customer_shipments" => [
                "required","array",
                function ($attribute,$value,$fail)
                {
                    $user = Auth::guard('sanctum')->user();
                    $count_id = count(array_filter($this->input('customer_shipments'),function ($data){
                        return isset($data['id']);
                    }));

                    if (!$user && count($value) > 1) {
                        $fail('Customer is not allowed to make more than 1 customer shipment');
                    } 
                    
                    if ($user) {
                        $cs = ResellerCustomerShipment::where('customer_id',$user->reference_id)->get();
                        if (count($value)+count($cs)-$count_id > 10) {
                            $fail('Customer is not allowed to make more than 10 customer shipment');
                        }
                    }
                }
                ],
            "customer_shipments.*.id" => [
                "sometimes","string",
                function ($attribute,$value,$fail)
                {
                    $user = Auth::guard('sanctum')->user();
                    $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;
                    $cs = ResellerCustomerShipment::where('id',$value)->where('customer_id',$customer_id)->first();

                    if (!$cs) {
                        $fail('Customer shipment not found');
                    }
                }
                ],
            "customer_shipments.*.name" => ["required","string", "regex:/^[A-Za-z0-9.,\s\-]+$/"],
            "customer_shipments.*.phone_number" => ["required","numeric"],
            "customer_shipments.*.address" => ["required","string", "regex:/^[A-Za-z0-9.,\s\/\-]+$/"],

            // "customer_shipments.*.province_name" => ["required","string","exists:region,name"],
            // "customer_shipments.*.province_code" => ["required","string","exists:region,code"],
            // "customer_shipments.*.city_name" => ["required","string","exists:city,name"],
            // "customer_shipments.*.city_code" => ["required","string","exists:city,code"],
            // "customer_shipments.*.district_name" => ["required","string","exists:district,name"],
            // "customer_shipments.*.district_code" => ["required","string","exists:district,code"],
            // "customer_shipments.*.subdistrict_name" => ["required","string","exists:subdistrict,name"],
            // "customer_shipments.*.subdistrict_code" => ["required","string","exists:subdistrict,code"],
            // "customer_shipments.*.postal_code" => ["required","string","exists:subdistrict,postal_code"],

            "customer_shipments.*.province_name" => ["required","string","exists:rsl_master_address,region_name"],
            "customer_shipments.*.province_code" => ["required","string","exists:rsl_master_address,region_code"],
            "customer_shipments.*.city_name" => [
                "required","string",
                function($attribute,$value,$fail)
                {
                    $keys = explode('.',$attribute);
                    $index = $keys[1];
                    $city = ResellerMasterAddress::where('city_code',$this->input('customer_shipments.'.$index.'.city_code'))->first();
                    // dd(strtolower($city->city_name),strtolower($value),str_contains(strtolower($value),strtolower($city->city_name)));
                    if (!str_contains(strtolower($value),strtolower($city->city_name))) {
                        $fail('City name not found');
                    }
                }
            ],
            "customer_shipments.*.city_code" => ["required","string","exists:rsl_master_address,city_code"],
            "customer_shipments.*.district_name" => ["required","string","exists:rsl_master_address,district_name"],
            "customer_shipments.*.district_code" => ["required","string","exists:rsl_master_address,district_code"],
            "customer_shipments.*.subdistrict_name" => ["required","string","exists:rsl_master_address,sub_district_name"],
            "customer_shipments.*.subdistrict_code" => ["required","string","exists:rsl_master_address,sub_district_code"],
            "customer_shipments.*.postal_code" => ["required","string","exists:rsl_master_address,zip_code"],
            "customer_shipments.*.is_active" => ["required","boolean"],
        ];
    }

    public function transformCustomerShipment()
    {
        $user = Auth::guard('sanctum')->user();
        $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;
        $datas = $this->input('customer_shipments');
        $d = [];
        foreach ($datas as $data) {
            $d[] = [
                'id' => $data['id']??null,
                'customer_id' => $customer_id,
                'name' => $data['name'],
                'phone_number' => $data['phone_number'],
                'address' => $data['address'],
                'region_code' => $data['province_code'],
                'region_name' => $data['province_name'],
                'city_code' => $data['city_code'],
                'city_name' => $data['city_name'],
                'district_code' => $data['district_code'],
                'district_name' => $data['district_name'],
                'subdistrict_code' => $data['subdistrict_code'],
                'subdistrict_name' => $data['subdistrict_name'],
                'zip_code' => $data['postal_code'],
                'is_active' => $data['is_active'],
                'modified_by' => $user->name??'SYSTEM'
            ];
        }
        return $d;
    }

    public function transformCustomer()
    {
        $d = $this->all();
        
        if (!isset($d['email'])) {
            return [];
        }

        return [
            'id' => ResellerToken::customerID(request()->header('X-AUTH-DEVICE')),
            'email' => $d['email']
        ];
    }

    public function messages(): array
    {
        return [
            'customer_shipments.*.phone_number.required' => 'No. HP tidak boleh kosong.',
            'customer_shipments.*.phone_number.numeric' => 'No. HP hanya boleh berupa nomor.',
            'email.email' => 'Format email salah.',
            'customer_shipments.*.address.required' => 'Alamat tidak boleh kosong.',
            'customer_shipments.*.address.regex' => 'Alamat tidak sesuai format. Pastikan hanya terdapat angka 0-9 dan minimal 12 huruf.',
        ];
    }
}
