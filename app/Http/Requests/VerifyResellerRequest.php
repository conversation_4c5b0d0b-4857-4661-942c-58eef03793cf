<?php

namespace App\Http\Requests;

use App\Helpers\Danamon\Specification\CreateVADebit;
use App\Models\Reseller;
use App\Models\ResellerRegistration;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Cache;

class VerifyResellerRequest extends FormRequest
{
    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $inputData['action_notes'] = $this->input('action_notes');
        $inputData['type'] = $this->input('type');
        $inputData['reseller_id'] = $this->input('reseller_id');

        return $inputData;
    }

    public function transform($checkRegisteredById)
    {
        return [
            'reseller_id' => $checkRegisteredById->reseller_id,
            'name' => $checkRegisteredById->name,
            'gender' => $checkRegisteredById->gender,
            'date_of_birth' => $checkRegisteredById->date_of_birth,
            'phone_number' => $checkRegisteredById->phone_number,
            'email' => $checkRegisteredById->email,
            'address' => $checkRegisteredById->address,
            'province_code' => $checkRegisteredById->province_code,
            'city_code' => $checkRegisteredById->city_code,
            'district_code' => $checkRegisteredById->district_code,
            'zip_code' => $checkRegisteredById->zip_code,
            'national_id' => $checkRegisteredById->national_id,
            'national_id_file' => $checkRegisteredById->national_id_file,
            'npwp' => $checkRegisteredById->npwp,
            'npwp_file' => $checkRegisteredById->npwp_file,
            'is_active' => Reseller::RESELLER_ACTIVE,
        ];
    }

    public function transformUser($checkRegisteredById)
    {
        return [
            'reference_id' => $checkRegisteredById->reseller_id,
            'reference_object' => 'reseller',
            'username' => $checkRegisteredById->email,
            'email' => $checkRegisteredById->email,
            'name' => $checkRegisteredById->name,
            'password' => $checkRegisteredById->password,
            'is_change_password' => 0,
            'is_active' => RESELLER::RESELLER_ACTIVE,
        ];
    }

    public function transformStoreVA(Reseller $resellerNew, $dataArray)
    {
        return [
                "reseller_id" => $resellerNew->id,
                "reference_no" =>  $dataArray["UserReferenceNumber"],
                "bin_name" =>  $dataArray["BinName"],
                "virtual_account_no" =>  $dataArray["VirtualAccountNumber"],
                "virtual_account_name" => $dataArray["VirtualAccountName"],
                "name" => $resellerNew->name,
                "email" => $resellerNew->email,
                "phone_number" => $resellerNew->phone_number,
                "bin_no" => $dataArray["BinNumber"],
                "national_id" => $resellerNew->national_id,
                "expired_date" => $dataArray["ExpiredDate"],
                "status_code" => $dataArray["CodeStatus"],
                "status" => $dataArray["DescriptionStatus"],
                "currency" => $dataArray["Currency"]
              ];
    }

    public function createCommission(Reseller $resellerNew)
    {
        return [
            'reseller_id' => $resellerNew->id,
            'potential_amount' => 0,
            'commission_amount' => 0
        ];
    }

    public function MappingCreateVA(ResellerRegistration $param){
        $ph = strlen($param->phone_number) > 12 ? substr($param->phone_number, 0, 12) : str_pad($param->phone_number, 12, '0', STR_PAD_LEFT);
        return [
          "UserReferenceNumber" =>  getenv('DANAMON_PARTNER_ID').$param->reseller_id,
          "RequestTime" => date("YmdHis"),
          "VirtualAccountNumber"=> getenv('DANAMON_BIN_NO').$ph,
          "VirtualAccountName"=> preg_replace("/[^a-zA-Z]/", "", $param->name),
          "Currency"=> "IDR",
          "AuthorizedName"=>  preg_replace("/[^a-zA-Z]/", "", $param->name),
          "Email" => null,
          "Mobile" => null,
          "NIK"=> $param->national_id,
          "BinNumber"=> getenv('DANAMON_BIN_NO'),
          "ExpiredDate"=> date("c", strtotime("9999-12-31 23:59:59"))
        ];
      }

    public function rules()
    {
        return [
            'reseller_id' => 'required',
            'type' => 'required',
            'action_notes' => 'nullable'
        ];
    }
}
