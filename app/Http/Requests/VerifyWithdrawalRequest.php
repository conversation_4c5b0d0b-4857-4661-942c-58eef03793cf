<?php

namespace App\Http\Requests;

use App\Helpers\Danamon\Specification\BalanceInquiry;
use App\Helpers\Danamon\Specification\TransferOnline;
use App\Helpers\Danamon\Specification\TransferOverbooking;
use App\Helpers\Danamon\Specification\TransferRTGS;
use App\Helpers\Danamon\Specification\TransferSKN;
use App\Helpers\Danamon\Specification\VAInquiry;
use App\Models\Commission;
use App\Models\MasterBank;
use App\Models\MasterParameter;
use App\Models\Reseller;
use App\Models\ResellerBank;
use App\Models\ResellerCommissionWithdrawal;
use App\Models\ResellerTransaction;
use App\Models\ResellerVA;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;

class VerifyWithdrawalRequest extends FormRequest
{
    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $actionNotes = $this->input('action_notes');
        $type = $this->input('type');
        $requestId = $this->input('request_id');
        $password = $this->input('password');

        $inputData['action_notes'] = $actionNotes;
        $inputData['type'] = $type;
        $inputData['request_id'] = $requestId;
        $inputData['password'] = $password;

        return $inputData;
    }

    //approve transaksi
    public function transformBalanceInquiry($withdrawal,$id)
    {
        $rq = VAInquiry::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').$id;
        $rq["RequestTime"] = date("YmdHis");
        $rq["VirtualAccountNumber"] = ResellerVA::where('reseller_id',$withdrawal->reseller_id)->first()->virtual_account_no ?? '-';//tbd
        return $rq;
    }
    public function transformRTOL($withdrawal, $id)
    {
        $resellerBank = ResellerBank::where('reseller_id', $withdrawal->reseller_id)->first();
        $bank = MasterBank::where('id', $resellerBank->bank_id)->first();
        $resellerVA  = ResellerVA::where('reseller_id', $withdrawal->reseller_id)->first();

        $rq = TransferOnline::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').uniqid('EXTCR').'OL';
        $rq["RequestTime"] = date("YmdHis");
        $rq["SourceAccountNumber"] = $resellerVA->virtual_account_no;//tbd
        $rq["BeneficiaryAccountNumber"] = $resellerBank->account_no;
        $rq["BeneficiaryBankCode"] = $bank->bank_code;
        $rq["Amount"] = strval($withdrawal->total*100);
        $rq["Description"] = 'Transfer Komisi '.$resellerVA->virtual_account_no.date("YmdHis");
        return $rq;
    }

    public function transformRTGS($withdrawal, $id)
    {
        $resellerBank = ResellerBank::where('reseller_id', $withdrawal->reseller_id)->first();
        $resellerData = Reseller::where('id', $withdrawal->reseller_id)->first();
        $bank = MasterBank::where('id', $resellerBank->bank_id)->first();
        $resellerVA  = ResellerVA::where('reseller_id', $withdrawal->reseller_id)->first();

        $rq = TransferRTGS::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').uniqid('EXTCR').'R';
        $rq["RequestTime"] = date("YmdHis");
        $rq["SourceAccountNumber"] = $resellerVA->virtual_account_no;//tbd
        $rq["SourceCardNumber"] = '';//tbd
        $rq["BeneficiaryAccountNumber"] = $resellerBank->account_no;
        $rq["BeneficiaryName"] = $resellerBank->account_name;
;
        $rq["BeneficiaryAddress"] = '';
        $rq["BeneficiaryType"] = 1;
        $rq["BeneficiaryStatus"] = 1;
        $rq["BeneficiaryBICode"] = $bank->beneficiary_bi_code;
        $rq["BeneficiaryBranchCode"] = $bank->beneficiary_branch_code;
        $rq["BeneficiaryBankName"] = $bank->bank_name;
        $rq["Amount"] =  strval($withdrawal->total*100);
        $rq["Description"] = 'Transfer Komisi '.$resellerVA->virtual_account_no.date("YmdHis");
        $rq["TransactionDate"] = date("Ymd");
        return $rq;
    }

    public function transformSKN($withdrawal,$id)
    {
        $resellerBank = ResellerBank::where('reseller_id', $withdrawal->reseller_id)->first();
        $resellerData = Reseller::where('id', $withdrawal->reseller_id)->first();
        $bank = MasterBank::where('id', $resellerBank->bank_id)->first();
        $resellerVA  = ResellerVA::where('reseller_id', $withdrawal->reseller_id)->first();

        $rq = TransferRTGS::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').uniqid('EXTCR').'S';
        $rq["RequestTime"] = date("YmdHis");
        $rq["SourceAccountNumber"] = $resellerVA->virtual_account_no;//tbd
        $rq["SourceCardNumber"] = '';
        $rq["BeneficiaryAccountNumber"] = $resellerBank->account_no;
        $rq["BeneficiaryName"] = $resellerBank->account_name;
        $rq["BeneficiaryAddress"] = '';
        $rq["BeneficiaryType"] = '1';
        $rq["BeneficiaryStatus"] = '1';
        $rq["BeneficiaryBICode"] = $bank->beneficiary_bi_code;
        $rq["BeneficiaryBranchCode"] = $bank->beneficiary_branch_code;
        $rq["BeneficiaryBankName"] = $bank->bank_name;
        $rq["Amount"] =  strval($withdrawal->total*100);
        $rq["Description"] = 'Transfer Komisi '.$resellerVA->virtual_account_no.date("YmdHis");
        $rq["TransactionDate"] = date("Ymd");
        return $rq;
    }

    public function transformOverbooking($withdrawal, $id)
    {
        $resellerBank = ResellerBank::where('reseller_id', $withdrawal->reseller_id)->first();
        $resellerData = Reseller::where('id', $withdrawal->reseller_id)->first();
        $resellerVA  = ResellerVA::where('reseller_id', $withdrawal->reseller_id)->first();

        $rq = TransferOverbooking::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').uniqid('EXTCR').'OV';
        $rq["RequestTime"] = date("YmdHis");
        $rq["SourceAccountNumber"] = $resellerVA->virtual_account_no; //tbd
        $rq["SourceCardNumber"] = '';//tbd
        $rq["BeneficiaryAccountNumber"] = $resellerBank->account_no;
            $rq["BeneficiaryName"] = $resellerBank->account_name;
;
        $rq["Amount"] =  strval(abs($withdrawal->total*100));
        $rq["Description"] = 'Transfer Komisi '.$resellerVA->virtual_account_no.date("YmdHis");
        $rq["TransactionDate"] =  date("Ymd");
        return $rq;
    }

    public function transformOverbookingTax($withdrawal,$id)
    {
        $resellerBank = ResellerBank::where('reseller_id', $withdrawal->reseller_id)->first();
        $resellerVA  = ResellerVA::where('reseller_id', $withdrawal->reseller_id)->first();

        $rq = TransferOverbooking::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').uniqid('EXTCR').'OVT';
        $rq["RequestTime"] = date("YmdHis");
        $rq["SourceAccountNumber"] = $resellerVA->virtual_account_no; //tbd
        $rq["BeneficiaryAccountNumber"] = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','TAX')->first()->value;
        $rq["BeneficiaryName"] = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','TAX_NAME')->first()->value;;
        $rq["Amount"] =  strval(abs($withdrawal->tax_amount*100));
        $rq["Description"] = 'Pajak Komisi '.$resellerVA->virtual_account_no.date("YmdHis");
        $rq["TransactionDate"] =  date("Ymd");
        return $rq;
    }

    public function transformOverbookingTaxReversal($withdrawal,$id)
    {
        $resellerBank = ResellerBank::where('reseller_id', $withdrawal->reseller_id)->first();
        $resellerVA  = ResellerVA::where('reseller_id', $withdrawal->reseller_id)->first();
        
        $rq = TransferOverbooking::rq();
        $rq["UserReferenceNumber"] = getenv('DANAMON_PARTNER_ID').uniqid('EXTCR').'OVT';
        $rq["RequestTime"] = date("YmdHis");
        $rq["BeneficiaryAccountNumber"] = $resellerVA->virtual_account_no; //tbd
        $rq["SourceAccountNumber"] = MasterParameter::where('group_key','DANAMON_ACCOUNT_NO')->where('key','TAX')->first()->value;
        $rq["BeneficiaryName"] = $resellerVA->virtual_account_name;
        $rq["Amount"] =  strval(abs($withdrawal->tax_amount*100));
        $rq["Description"] = 'Pajak Komisi Reversal '.$resellerVA->virtual_account_no.date("YmdHis");
        $rq["TransactionDate"] =  date("Ymd");
        return $rq;
    }

    public function transformApprove()
    {
        return [
            'action_by' => auth()->user()->sales->sales_name,
            'action_type' => 'approved',
            'action_date' => now(),
            'payment_date' => now(), //placeholder
            'status' => ResellerCommissionWithdrawal::SUCCESS
        ];
    }

    public function updateBalance($commission, $withdrawal)
    {
        return [
            'commission_amount' => $commission->commission_amount - $withdrawal->amount
        ];
    }

    public function createTxRecord($withdrawal)
    {
        return [
            'reference_name' => "Withdrawal",
            'reference_id' => $withdrawal->request_id, 
            'type' => ResellerTransaction::TX_TYPE_COMMISSION,
            'status' => 'Success',
            'order_status' => 'Withdrawal',
            'amount' => $withdrawal -> amount,
            'remark' => ''
        ];
    }


    public function createLedgerRecord($withdrawal)
    {
        $commissionId = Commission::where('reseller_id', $withdrawal->reseller_id)->first()->id;
        $resellerData = Reseller::where('id', $withdrawal->reseller_id)->first();
        $tx = ResellerTransaction::where('reference_id', $withdrawal->request_id)->first();
        return [
            'commission_id' => $commissionId,
            'transaction_id' => $tx->id, 
            'reseller_id' => $resellerData->reseller_id,
            'type' => 'Debit',
            'commission_type' => 'Commission',
            'amount' => $withdrawal -> amount
        ];
    }

    //reject transaksi
    public function transformReject($actionNotes)
    {
        return [
            'action_by' => auth()->user()->sales->sales_name,
            'action_type' => 'rejected',
            'action_notes' => $actionNotes,
            'action_date' => date('Y-m-d H:i:s'),
            'status' => ResellerCommissionWithdrawal::REJECTED
        ];
    }

    public function rules()
    {
        return [
            'type' => 'required',
            'action_notes' => 'nullable',
            'request_id' => 'required',
            'password' => 'nullable'
        ];
    }
}
