<?php

namespace App\Http\Requests;

use App\Helpers\FormatHelper;
use App\Models\OrderReseller;
use App\Models\Reseller;
use Illuminate\Foundation\Http\FormRequest;

class WithdrawalDetailInternalRequest extends FormRequest
{
    protected $id;

    public function all($keys = null)
    {
        $inputData = parent::all($keys);

        $inputData['id'] = $this->route('id');

        return $inputData;
    }

    public function rules()
    {
        return [
            'id' => 'required|exists:rsl_commission_withdrawal,request_id'
        ];
    }

    public function messages()
    {
      return [            
        'id.exists' => "Detail not found",
      ];
    }
}
