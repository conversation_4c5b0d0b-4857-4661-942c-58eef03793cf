<?php

namespace App\Http\Requests;

use App\Rules\OrderAuthorization;
use Illuminate\Foundation\Http\FormRequest;

class getTransactionDetailCustResellerRequest extends APIRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    // public function authorize()
    // {
    //     return false;
    // }

    public function prepareForValidation()
    {
        // dd($this->route()->parameters['order_no']);
        $this->merge([
            'order_no' => $this->route()->parameters['order_no']
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'order_no' => ['required', new OrderAuthorization]
        ];
    }
}
