<?php

namespace App\Http\Resources;
use App\Models\City;
use App\Models\District;
use App\Models\Region;
use App\Models\ResellerCustomer;
use App\Models\ResellerMasterAddress;
use App\Models\ResellerRegistration;
use App\Models\Sales;
use App\Models\Subdistrict;
use App\Models\User;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ApplicantDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {

        $statusEnum = '';
        switch($this->status){
            case ResellerRegistration::REGISTRATION_CREATED:
                $statusEnum = 'Menunggu Verifikasi';
                break;
            case ResellerRegistration::REGISTRATION_APPROVED:
                $statusEnum = 'Diterima';
                break;
            case ResellerRegistration::REGISTRATION_REJECTED:
                $statusEnum = 'Ditolak';
                break;
        }

        $userName = Sales::where('sales_id',$this->action_by)->first()->sales_name ?? '-';
        // $province = Region::select('name')->where('code',$this->province_code)->first()->name ?? '-';
        // $city = City::select('name')->where('id',$this->city_code)->first()->name ?? '-';
        // $district = District::select('name')->where('code',$this->district_code)->first()->name ?? '-';
        // $zipCode = Subdistrict::select('postal_code')->where('code',$this->zip_code)->first()->postal_code ?? $this->zip_code;
        // $masterAddress = ResellerMasterAddress::get();
        $province = ResellerMasterAddress::where('region_code',$this->province_code)->first()->region_name?? '-';
        $city = ResellerMasterAddress::where('city_code',$this->city_code)->first()->city_name?? '-';
        $district = ResellerMasterAddress::where('district_code',$this->district_code)->first()->district_name?? '-';
        $zipCode = ResellerMasterAddress::where('sub_district_code',$this->zip_code)->first()->zip_code?? '-';
        $checkUser = User::where("username", $this->email)->where('reference_object','rsl_customers')->first();
        if($checkUser){
            $checkCustomer = ResellerCustomer::where('id', $checkUser->reference_id)->where('is_active',1)->first();
        } else {
            $checkCustomer = null;
        }


        return [
            'id' => $this->id,
            'reseller_id' => $this->reseller_id,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'name' => $this->name,
            'address' => $this->address.', '.$province.', '.$city.', '.$district.', '.$zipCode,
            'postal_code' => $zipCode,
            'national_id' => $this->national_id,
            'national_id_file' => env('S3_STREAM_URL').$this->national_id_file,
            'npwp' => $this->npwp,
            'npwp_file' => env('S3_STREAM_URL').$this->npwp_file,
            'status' => $statusEnum,
            'action_by' => $userName,
            'action_date' => $this->action_date,
            'action_notes' => $this->action_notes,
            'registration_date' => $this->created_date,
            'is_customer' => $checkCustomer? true : false
        ];
    }
}
