<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ApplicantsDashboardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public $new30d;
    public $newThisMonth;
    public $approved30d;
    public $approveThisMonth;
    public $reject30d;
    public $rejectThisMonth;

    public function __construct($data)
    {
        $this->new30d = $data['new30d'];
        $this->newThisMonth = $data['newThisMonth'];
        $this->approved30d = $data['approved30d'];
        $this->approveThisMonth = $data['approveThisMonth'];
        $this->reject30d = $data['reject30d'];
        $this->rejectThisMonth = $data['rejectThisMonth'];
    }
    public function toArray($request)
    {
        return [
            'new_register' => [
                'last_30_day' => abs($this->new30d),
                'negative' => $this->new30d < 0,
                'this_month' => $this->newThisMonth,
            ],
            'approved' => [
                'last_30_day' => abs($this->approved30d),
                'negative' => $this->new30d < 0,
                'this_month' => $this->approveThisMonth,
            ],
            'rejected' => [
                'last_30_day' => abs($this->reject30d),
                'negative' => $this->new30d < 0, 
                'this_month' => $this->rejectThisMonth,
            ],
        ];
    }
}
