<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;
class BannerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        
        return [
            'id' => $this->id,
            'title' => $this->title,
            'thumbnail' => filter_var($this->thumbnail, FILTER_VALIDATE_URL) ? $this->thumbnail : env('S3_STREAM_URL').$this->thumbnail,
            'url' => $this->url,
            'sequence_no' => $this->sequence_no,
            'start_period' => $this->start_period,
            'end_period' => $this->end_period,
            'status'=> ($this->sequence_no != null && Carbon::now()->between(Carbon::parse($this->start_period),Carbon::parse($this->end_period))) ? 'ACTIVE' : 'NON-ACTIVE'
        ];
    }
}
