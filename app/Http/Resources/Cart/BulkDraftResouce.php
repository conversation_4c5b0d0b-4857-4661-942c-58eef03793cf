<?php

namespace App\Http\Resources\Cart;

use Illuminate\Http\Resources\Json\JsonResource;

class BulkDraftResouce extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */

    private $tokoName;

    public function __construct($resource, $tokoName = null)
    {
        parent::__construct($resource);
        $this->tokoName = $tokoName;
    }
    public function toArray($request)
    {
        $price = $this->article_detail->article_price->sortByDesc('modified_date')->first()->amount ?? '0.00';
        return [
            'cart_id' => $this->cart_id,
            'bulk_draft_id' => $this->bulk_draft_id,
            'sku' => $this->article,
            'article_description' => $this->article_detail->article_description ?? "",
            'price' => $price,
            'flag' => $this->flag,
            'total_price' => number_format(($price * $this->qty), 2, ',', '.'),
            'qty' => $this->qty,
            'stock' => $this->stock,
            'stock_toko' => $this->stock_toko ?? 0,
            'nama_toko' => $this->tokoName,
            'size' => $this->article_detail->product_size_c ?? "",
            'color' => $this->value ?? "",
            'category' => $this->article_detail->lvl4_description ?? "",
        ];
    }
}