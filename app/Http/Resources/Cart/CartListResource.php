<?php

namespace App\Http\Resources\Cart;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\CartDetail;
use App\Models\OrderCustom;
use App\Models\Product;

class CartListResource extends JsonResource
{
    private $creditLimit;
    private $cartCollection;
    public function __construct($resource, $creditLimit = [])
    {
        parent::__construct($resource);
        $this->creditLimit = $creditLimit;

    }
    public function toArray($request)
    {
        if ($request->is_custom == 1) {
            $cartCollection = CartDetail::with(
                ['article_detail' => fn($q) 
                    => $q->with(['image_generic' => fn($q) 
                        => $q->onlyImage(), 'article_price' => fn($q) 
                            => $q->validPrice()])])
                                ->where('cart_id', $this->id)
                                ->groupBy('attachment_group_id')
                                ->when($request->has('is_available'), function ($query) use ($request) {
                                    if($request->is_available == 'tersedia'){
                                        return $query->where(function ($q) {
                                            $q->where('is_available', 1)
                                              ->orWhereNull('is_available');
                                        });
                                    }else{
                                        return $query->where('is_available', 0);
                                    }
                                })
                                ->where('is_custom',1)
                                ->desc('created_date')
                                ->pager($request);
        } else {
            $cartCollection = CartDetail::with(
                ['article_detail' => fn($q) 
                    => $q->with(['image_generic' => fn($q) 
                        => $q->onlyImage(), 'article_price' => fn($q) 
                            => $q->validPrice()])
                            ->groupBy('sku_code_c')])
                                ->where('cart_id', $this->id)
                                ->when($request->has('is_available'), function ($query) use ($request) {
                                    if($request->is_available == 'tersedia'){
                                        return $query->where(function ($q) {
                                            $q->where('is_available', 1)
                                              ->orWhereNull('is_available');
                                        });
                                    }else{
                                        return $query->where('is_available', 0);
                                    }
                                })
                                ->where('is_custom',0)
                                ->desc('created_date')
                                ->get();
                                // ;
                                
                                // ->pager($request);
        }
        $isAbleCheckout = count($cartCollection->where('is_available', 1)->where('selected', 1)) > 0 ? true : false;
        $cartData = collect(array_filter(DetailCartListResource::collection($cartCollection)->resolve(), fn($i)=>$i['sku']!=null))->paginate($request->per_page);
        return [
            "cart_id"=> (string)$this->id,
            "discount_percent"=> 0,
            "data_limit" => [$this->creditLimit],
            "data_list_cart" => [
                'total_data' => $cartData->total(),      // Total number of filtered items
                'size' => intval($cartData->perPage()),  // Items per page
                'active_page' => $cartData->currentPage(), // The current page being viewed
                'total_page' => $cartData->lastPage(),    // Total number of pages
                'cart_detail' => $cartData->items(),
                'isAbleCheckout' => $isAbleCheckout,
            ]
            ];
    }

}