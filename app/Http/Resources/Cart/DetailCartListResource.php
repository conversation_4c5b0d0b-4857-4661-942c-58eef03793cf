<?php

namespace App\Http\Resources\Cart;
use App\Models\Remark;
use App\Models\Product;
use App\Models\ProductSku;
use App\Helpers\RestHelper;
use App\Models\OrderCustom;
use App\Models\CartAttachment;
use App\Models\OrderCustomAttachment;
use App\Models\{Article, CartDetail};
use Illuminate\Http\Resources\Json\JsonResource;

class DetailCartListResource extends JsonResource
{
    public function toArray($request)
    {
        // $isChecked = RestHelper::fetchCheck($this->cart_id, $this->sku);

        if ($request->is_custom == 1 && $this->article_detail != null) {
            $cd = CartDetail::with('article_detail')
                ->where('is_custom', 1)->where('attachment_group_id', $this->attachment_group_id);
            $ocs = OrderCustom::where('reference_id', $this->cart_id)
                ->where('sku', $this->article_detail->sku_code_c)
                ->pluck('id')->toArray();
            $ocas = OrderCustomAttachment::whereIn('order_custom_id', $ocs)
                ->get();
            $cartAttachments = CartAttachment::where('attachment_group_id', $this->attachment_group_id)->get(['id', 'attachment_group_id', 'file_path', 'text', 'estimate_price']);
            $articles = Product::where('sku_code_c', $this->article_detail->sku_code_c)
                ->pluck('article')->toArray();
            $customs = [];
            foreach ($ocas as $oca) {
                $oc = OrderCustom::where('id', $oca->order_custom_id)->first();
                $cd_qty = $cd->where('cart_id', $this->cart_id)
                    ->whereIn('article', $articles)->sum('qty');
                $customs[] = [
                    'deskripsi_kustomisasi' => 'BIAYA KUSTOMISASI - ' . $oc->position_side . ' - ' . $oca->size . ' - ' . $oca->material,
                    'harga_satuan' => (int) $oca->custom_price,
                    'kuantiti' => (int) $cd_qty,
                    'total_harga' => (int) ($cd_qty * $oca->custom_price)
                ];
            }
        } else {
            $cd = CartDetail::with('article_detail')
                ->where('is_custom', 0);
            $customs = [];
        }

        $datas = $cd->findCart($this->cart_id)->related($this->article_detail->sku_code_c ?? '')->get();
        $stock = ProductSku::whereIn('sku_id', $datas->pluck('article'))
            ->get(['sku_id', 'stock'])
            ->pluck('stock', 'sku_id')
            ->toArray();
        $datas = $datas->map(function ($item) use ($stock) {
            $item->stock = isset($stock[$item->article]) ? $stock[$item->article] : 0; // Set the stock value, adjust if necessary
            return $item;
        });
        $remark = Remark::where('attachment_group_id', $this->attachment_group_id)->first();

        // $stock_cache = RestHelper::stockCache($datas->pluck('article')->toArray(), $request->user()->customer->customer_id);
        // $datas = RestHelper::addFieldStockMoq($stock_cache, $datas->toArray());
        // $datas = RestHelper::vArrayToObject($datas);

        $latestPrice = $this->article_detail ? $this->article_detail->article_price
            ->sortByDesc('modified_date') // Sort by latest modified_date
            ->first() : null;
        return [
            "tag" => $this->article_detail->product_tag ?? "",
            "serie" => $this->article_detail->lvl4_description ?? "",
            "category" => $this->article_detail->lvl4_description ?? "",
            "flag" => Article::getFlag($this->article) ?? "",
            "product_name" => $this->article_detail->product_name_c ?? "",
            "sku" => $this->article_detail->sku_code_c ?? "",
            "attachments_group_id" => $this->attachment_group_id ?? "",
            "cart_attachments" => $cartAttachments ?? null,
            "image_url" => !empty($this->article_detail->image_generic[0]->file_path) ? env('S3_STREAM_URL') . $this->article_detail->image_generic[0]->file_path : 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
            "price" => $latestPrice ? $latestPrice->amount : '0.00',
            "is_available" => $this->is_available == 1 || $this->is_available === null ? true : false,
            "selected" => $this->selected == 1 ? true : false,
            "cart_id" => $this->cart_id,
            "is_custom" => $request->is_custom ?? 0,
            "customs" => $customs,
            "remark" => $remark->remark ?? null,
            "items" => ItemsCartListResource::collection($datas)
        ];
    }

}