<?php

namespace App\Http\Resources\Cart;
use App\Models\Color;
use App\Models\Product;
use App\Helpers\RestHelper;
use App\Models\OrderCustom;
use App\Models\OrderCustomAttachment;
use Illuminate\Http\Resources\Json\JsonResource;

class ItemsCartListResource extends JsonResource
{
    public function getColor($product)
    {
        $color = Color::where('key', $product)->first();
        return $color ? $color->value : $product;
    }
    public function toArray($request)
    {
        // $stock = RestHelper::stockCache($this->article, $request->user()->customer->customer_id);
        $isChecked = RestHelper::fetchCheck($this->cart_id, $this->article);

        $product = Product::where('article', $this->article)->first();

        $ocs = OrderCustom::where('reference_id', $this->cart_id)
            ->where('sku', $product->sku_code_c)
            ->pluck('id')->toArray();

        $custom_price = OrderCustomAttachment::whereIn('order_custom_id', $ocs)
            ->sum('custom_price');

        return [
            "cart_detail_id" => $this->cart_detail_id ?? "",
            "article" => $this->article ?? "",
            "varian" => isset($product->product_variant_c) ? $this->getColor($product->product_variant_c) : 'N/A',
            "image_variant" => $product->image_variant_url,
            "size" => $product->product_size_c ?? 'N/A',
            "stock" => (int) $this->stock ?? 0,
            'stock_toko' => RestHelper::getCustomerStock($request->user()->customer->customer_id, $this->article),
            "qty" => $this->qty,
            "moq" => (int) $this->moq ?? 0,
            "custom_price" => (int) $custom_price ?? null,
            "selected" => $this->selected == 1 ? true : false,
            "preupdate" => true
        ];
    }

}