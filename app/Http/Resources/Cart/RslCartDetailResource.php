<?php

namespace App\Http\Resources\Cart;
use App\Models\Color;
use App\Helpers\RestHelper;
use App\Models\PublicProduct;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Helpers\Promotion\PromotionHelper as Promo;


class RslCartDetailResource extends JsonResource
{
    public function getColor($product){
        $color = Color::where('key', $product)->first();
        return $color ? $color->value : $product;
    }
    public function toArray($request)
    {
        $isChecked = RestHelper::fetchCheck($this->cart_id, $this->article_id);
        // $site = $request->input('site');

        // $article = PublicProduct::where('article',$this->article_id)
        //             ->withSum(['rsl_stock' => fn($q) =>
        //                 $q->where('location_code',$site)],
        //                 'available_stock')->first();
        $d = Promo::applyArticlePromotion($this->article_id, $this->article->price->amount??0);
        return [
            "cart_detail_id" => $this->id ?? "",
            "article" => $this->article_id ?? "",
            "sku_code_c" => $this->article->sku_code_c,
            "color" => $this->getColor($this->article->product_variant_c),
            'name' => $this->article->product_name_c,
            "size" => $this->article->product_size_c,
            "stock" => (int)$this->article->rsl_stock_sum_available_stock,
            "image_url" => !empty($this->article->mainImageVariant->file_path) ? env('S3_STREAM_URL').$this->article->mainImageVariant->file_path : 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
            "price" => $this->article->price->amount ?? '0.00',
            'discount' => $d['disc']??0,
            'discount_price' => $d['disc'] > 0 ? $this->article->price->amount - $d['disc'] : 0,
            'promotion' => $d['promotion'] ? collect($d['promotion'])->only(['name', 'action', 'discount_type'])->all() : null,         
            "qty" => $this->qty,
            "selected" => $isChecked,
        ];
    }

}
