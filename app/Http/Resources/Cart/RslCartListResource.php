<?php

namespace App\Http\Resources\Cart;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\ResellerCartDetail;

class RslCartListResource extends JsonResource
{
    private $creditLimit;
    private $cartCollection;
    public function __construct($resource)
    {
        parent::__construct($resource);

    }
    public function toArray($request)
    {
        $site = $request->input('site');
        $cartCollection = ResellerCartDetail::with(
            ['article' => fn($q) 
                => $q->with(['mainImageVariant','price'])->withSum(['rsl_stock' 
                    => function($q) use($site){$q->where('location_code',$site);}],'available_stock')])
                            ->where('cart_id', $this->id)
                            ->desc('created_date')
                            ->pager($request);
                            
        return [
            "cart_id"=> (string)$this->id??'-',
            'total_data' => $cartCollection->total(),
            'size' => intval($cartCollection->perPage()),
            'active_page' => $cartCollection->currentPage(),
            'total_page' => $cartCollection->lastPage(),
            'cart_detail' => array_filter(RslCartDetailResource::collection($cartCollection)->resolve()),

            ];
    }

}
