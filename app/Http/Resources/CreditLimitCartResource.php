<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\CreditLimit;
class CreditLimitCartResource extends JsonResource
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = Customer::class;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $cr = CreditLimit::where('customer_external_id', $this->customer_id)->first();
        return [
            'terpakai' => $cr->credit_limit_used ?? 0,
            'customer' => $this->customer_id ?? '0',
            'sisa' =>  $cr->credit_limit_remaining ?? 0,
            'currency' => $cr->credit_limit_currency ?? 'IDR',
            'plafond' => isset($cr->credit_limit) ? $cr->credit_limit : 0,
            'persentasi' => $cr->credit_limit_used_percentage ?? 0,
        ];
    }
}
