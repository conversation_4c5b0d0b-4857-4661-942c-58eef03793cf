<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\CreditLimit;
class CreditLimitResource extends JsonResource
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = Customer::class;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request, $total = null)
    {
        $cr = CreditLimit::where('customer_external_id', $this->customer_id)->first();
        $createdDate = @$this->orders()->orderBy('created_date', 'desc')->first();
        return [
            'credit_limit' => isset($cr->credit_limit) ? $cr->credit_limit : 0,
            // 'credit_limit_used' => isset($cr->credit_limit_used) ? $cr->credit_limit_used : 0,
            'credit_limit_used' => $cr->credit_limit_used ?? 0,
            'credit_limit_used_percentage' => isset($cr->credit_limit_used_percentage) ? $cr->credit_limit_used_percentage : 0,
            // 'credit_limit_remaining' => isset($cr->credit_limit_remaining) ? $cr->credit_limit_remaining : 0,
            'credit_limit_remaining' => $cr->credit_limit_remaining ?? 0,
            'currency' => isset($cr->credit_limit_currency) ? $cr->credit_limit_currency : 0,
            'avg_transaction' => $this->orders->avg('total_nett'),
            'total_transaction' => $this->orders->sum('total_nett'),
            'last_transaction' => $createdDate ?? '-' ? $createdDate->created_date ?? '-' : null,
            'last_update' => $createdDate ?? '-' ? $createdDate->created_date ?? '-' : null,
        ];
    }
}