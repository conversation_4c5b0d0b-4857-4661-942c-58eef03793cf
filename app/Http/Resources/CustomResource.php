<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;
class CustomResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        
        return [
            'product_group' => $this->product_group,
            'image_sequence' => $this->position_side,
            'coordinate' => $this->position_x,
            'position_y' => $this->position_y,
            'custom_type' => $this->custom_type,
            'dimension_width' => $this->dimension_width,
            'dimension_height' => $this->dimension_height,
        ];
    }
}
