<?php

namespace App\Http\Resources;

use App\Models\CreditLimit;
use Illuminate\Http\Resources\Json\JsonResource;


class CustomerInternalResource extends JsonResource
{
    protected bool $isMasked;

    public function __construct($resource, bool $isMasked)
    {
        parent::__construct($resource);
        $this->isMasked = $isMasked;
        $this->resource->setMasking($isMasked);
    }

    /**
     * Indicates if the resource's collection keys should be preserved.
     *
     * @var bool
     */
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $cr = CreditLimit::where('customer_external_id', $this->customer_id)->first();
        $createdDate = @$this->orders()->orderBy('created_date', 'desc')->first();
        $envValue = env('S3_STREAM_URL');

        return [
            'customer_id' => $this->customer_id,
            'sap_id' => $this->sap_id,
            'owner_name' => $this->owner_name,
            'instance_name' => $this->instance_name,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            // 'address' => $this->address,
            'distribution_channel' => $this->distribution_channel,
            'national_id' => $this->national_id,
            'npwp' => $this->npwp,
            'npwp_name' => $this->npwp_name,
            'tax_type' => $this->tax_type,
            'tax_invoice' => $this->tax_invoice ? "Ya - $this->tax_invoice" : 'Tidak',
            'npwp_province_code' => $this->npwp_province_code,
            'npwp_province' => $this->npwp_province,
            'npwp_city_code' => $this->npwp_city_code,
            'npwp_city' => $this->npwp_city,
            'npwp_district_code' => $this->npwp_district_code,
            'npwp_district' => $this->npwp_district,
            'npwp_zip_code' => $this->npwp_zip_code,
            'npwp_address' => $this->npwp_address,
            'npwp_file' => $envValue . $this->npwp_file,
            'top' => $this->top,
            'top_days' => $this->top_days,
            'discount_percent' => $this->discount_percent,
            'customer_type' => $this->customer_type,
            'areas' => $this->sales->regional ?? null,
            'sales' => $this->sales->sales_name ?? null,
            'is_active' => $this->is_active ? true : false,
            'is_verified' => $this->is_verified ? true : false,            
            'is_pending_payment' => $this->is_pending_payment ? true : false,
            'is_change_password' => @$this->user->is_change_password ? true : false ,
            'store_list' => CustomerShipmentResource::collection($this->shipments),
            'credit_limit' => isset($cr->credit_limit) ? $cr->credit_limit : 0,
            'credit_limit_used' => isset($cr->credit_limit_used) ? $cr->credit_limit_used : 0,
            'credit_limit_used_percentage' => isset($cr->credit_limit_used_percentage) ? $cr->credit_limit_used_percentage : 0,
            'credit_limit_remaining' => isset($cr->credit_limit_remaining) ? $cr->credit_limit_remaining : 0,
            'credit_limit_currency' => isset($cr->credit_limit_currency) ? $cr->credit_limit_currency : 0,
            'avg_transaction' => @$this->orders->avg('total_nett') ?: 0,
            'total_transaction' => @$this->orders->sum('total_nett') ?: 0,
            'last_transaction' => $createdDate ? $createdDate->created_date : null,             
            // 'last_login' => $createdDate ? $createdDate->created_date : now(),
            'status' => $this->status,
            'sumber' => $this->sumber,
            // 'updated_by' => $this->modified_by,                                                                      
        ];
    }
}

