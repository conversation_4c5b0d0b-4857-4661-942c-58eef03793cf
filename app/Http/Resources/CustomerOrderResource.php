<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CustomerOrderResource extends JsonResource
{
    const OrderStatusWait = 'Menunggu Konfirmasi';
    const OrderStatusPending = 'Pending';
    const OrderStatusOnHold = 'On Hold';
    const OrderStatusOnProcess = 'Diproses';
    const OrderStatusBaru = 'Baru';
    const OrderStatusGI = 'Siap Dikirim';
    const OrderStatusOnShipping = 'Dikirim';
    const OrderStatusDelivered = 'Diterima';
    const OrderStatusFinish = 'Selesai';
    const OrderStatusCancel = 'Batal';
    const OrderStatusPembayaran = 'Pembayaran';
    const OrderStatusSemua = 'Semua';
    const OrderStatusVerif = 'Menunggu Verifikasi';

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        if (strtolower($this->distribution_channel) == 'wholesales') {
            if (!in_array($this->order_status, [self::OrderStatusPending, self::OrderStatusOnHold, self::OrderStatusWait, self::OrderStatusBaru, self::OrderStatusCancel])) {
                if (isset($this->invoice) && $this->invoice->status === 'LUNAS') {
                  $order_status = $this->order_status;
                } else {    
                  $order_status = $this->order_status !== self::OrderStatusOnProcess ? self::OrderStatusPembayaran : $this->order_status;
                }
            } else {
                $order_status = $this->order_status;
            }

            return [
                'billing' => strval(@$this->invoice->invoice_no),
                'order' => $this->order_no,
                'store_name' => @$this->customer_shipment->name,
                'order_date' => $this->created_date,
                'do_date' => @$this->delivery_order->created_date,
                'total' => $this->invoice->nett_price ?? $this->total_nett,
                'order_status' => $order_status,
                'billing_status' => @$this->invoice->status??'-',
            ];
        }
        
        if (in_array(strtolower($this->distribution_channel), ['b2b', 'w3', 're', 'rd'])) {
            return [
                'billing' => $this->invoice_billing != null ? strval(@$this->invoice_billing->invoice_no) : strval(@$this->invoice_dp->invoice_no),
                'order' => $this->order_no,
                'store_name' => @$this->customer_shipment->name,
                'order_date' => $this->created_date,
                'do_date' => @$this->delivery_order->created_date,
                'total' => $this->invoice_billing != null ? $this->invoice_billing->nett_price : ($this->invoice_dp != null ? $this->invoice_dp->nett_price : $this->total_nett),
                'order_status' => $this->order_status,
                'billing_status' => $this->invoice_billing != null ? $this->invoice_billing->status : ($this->invoice_dp != null ? $this->invoice_dp->status : "-"),
            ];
        }
    }
}
