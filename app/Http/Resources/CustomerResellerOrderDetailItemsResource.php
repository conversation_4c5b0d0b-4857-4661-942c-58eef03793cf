<?php

namespace App\Http\Resources;

use App\Models\ResellerOrderPromotion;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResellerOrderDetailItemsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $ops = ResellerOrderPromotion::where('reference_id',$this->id)->whereIn('discount_type',['coupon','voucher'])->get();
        $rev_disc_amount = 0;
        if (!$ops->isEmpty()) {
            foreach ($ops as $op) {
                $rev_disc_amount += $op->amount;
            }
        }
        return [
            "id" => $this->id, 
            "order_header_id" => $this->order_header_id, 
            "sku_code" => $this->sku_code, 
            "article_id" => $this->article_id, 
            "image" => $this->mainImageVariant != null ? env('S3_STREAM_URL').$this->mainImageVariant->file_path : 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
            "product_name" => $this->product_name, 
            "product_variant" => $this->product_variant,
            "color" => $this->color_value,
            "product_size" => $this->product_size, 
            "qty" => $this->qty, 
            "additional_qty" => $this->additional_qty, 
            "unit_price" => $this->unit_price, 
            "base_uom" => $this->base_uom, 
            "line_amount" => $this->line_amount, 
            "discount_amount" => $this->discount_amount, 
            "total_amount" => $this->total_amount, 
            "promotion_type" => $this->promotions->promo??null,
            "remarks" => $this->remarks, 
            "created_date" => $this->created_date, 
            "created_by" => $this->created_by, 
            "modified_date" => $this->modified_date, 
            "modified_by" => $this->modified_by
        ];
    }
}
