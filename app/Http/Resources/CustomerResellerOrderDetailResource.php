<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use App\Models\Coupon;
use App\Models\Voucher;
use App\Models\OrderReseller;
use App\Models\MasterParameter;
use App\Models\ResellerOrderHeaders;
use App\Models\Promotions\Promotions;
use App\Models\ResellerOrderShipment;
use App\Models\ResellerOrderPromotion;
use App\Helpers\Promotion\PromotionHelper;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResellerOrderDetailResource extends JsonResource
{
    use PromotionHelper;
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $shipment = ResellerOrderShipment::where('order_header_id',$this->id)->groupBy('order_status')->latest()->get()
                    ->map(function($m){ 
                        return [
                            'order_status' => $m->order_status,
                            'created_date' => $m->created_date
                        ];
                    });

        $ids = $this->items()->pluck('id')->toArray();
        $ids[] = $this->id;
        // dd($ids);
        $promotions = ResellerOrderPromotion::whereIn('reference_id',$ids)->get();
        // dd($promotions);
        // $promo = CustomerResellerOrderDetailPromotionsResource::collection($promotions);
        $grouping_promo = [];
        foreach ($promotions as $p) {
            $discount_title = $p['disount_type'].' - '.$p['discount_id'];
            $amount = $p['amount'];

            if (!isset($grouping_promo[$discount_title])) {

                if (strtolower($p->discount_type) == 'voucher') {
                    $m = Voucher::where('id',$p->discount_id)->first();
                    $name = $m->master->name;
                    $category = $m->category;
                    $code = $m->code;
                }

                if (strtolower($p->discount_type) == 'coupon') {
                    $m = Coupon::where('id',$p->discount_id)->first();
            
                    $name = $m->name;
                    $category = $m->category;
                    $code = $m->coupon_code;
                }

                if (strtolower($p->discount_type) == 'promotion' || strtolower($p->discount_type) == 'promotions' || strtolower($p->discount_type) == 'bundlings') {
                    $m = Promotions::where('id',$p->discount_id)->first();
                    $name = $m->name;
                    $category = $m->action;
                    $code = '-';
                }

                $grouping_promo[$discount_title] = [
                    'id' => $p->id,
                    'reference_name' => $p->reference_name,
                    'discount_type' => $p->discount_type,
                    'discount_id' => $p->discount_id,
                    'code' => $code,
                    'amount' => 0,
                    'discount_name' => $name,
                    'discount_category' => $category,
                    'terms_and_conditions' => $m->description??'-',
                    'start_date' => !$m || $m->start_date == null ? null : Carbon::parse($m->start_date)->timezone('Asia/Jakarta')->format('Y-m-d H:i:s'),
                    'end_date' => !$m || $m->end_date == null ? null : Carbon::parse($m->end_date)->timezone('Asia/Jakarta')->format('Y-m-d H:i:s')
                ];
            }

            $grouping_promo[$discount_title]["amount"] += $amount;
        }
        $grouped_promo = array_values($grouping_promo);

        return [
            "id" => $this->id, 
            "order_no" => $this->order_no, 
            "ext_order_id" => $this->ext_order_id, 
            "invoice_no" => $this->invoice_no, 
            "order_date" => $this->order_date, 
            "completed_date" => $this->completed_date, 
            "reseller_id" => $this->reseller_id, 
            "link_id" => $this->link_id, 
            "customer_id" => $this->customer_id, 
            "customer_name" => $this->customer_name, 
            "customer_phone_number" => $this->customer_phone_number, 
            "customer_email" => $this->customer_email, 
            "customer_shipment_id" => $this->customer_shipment_id, 
            "customer_shipment_name" => $this->customer_shipment_name, 
            "customer_shipment_address" => $this->customer_shipment_address, 
            "customer_shipment_region_name" => $this->customer_shipment_region_name, 
            "customer_shipment_city_name" => $this->customer_shipment_city_name, 
            "customer_shipment_subdistrict_name" => $this->customer_shipment_subdistrict_name, 
            "customer_shipment_zip_code" => $this->customer_shipment_zip_code, 
            "shipment_method" => $this->shipment_method, 
            "transporter_id" => $this->transporter_id, 
            "shipment_charges" => $this->shipment_charges, 
            "handling_charges" => $this->handling_charges, 
            "sub_total_amount" => $this->sub_total_amount, 
            "discount_amount" => $this->discount_amount, 
            "total_amount" => $this->total_amount, 
            "pay_amount" => $this->pay_amount, 
            "payment_method" => $this->payment_method, 
            "payment_link" => $this->payment_link, 
            "payment_status" => $this->payment_status, 
            "payment_date" => $this->payment_date,
            "due_payment_date" => $this->due_payment_date, 
            "commission_amount" => $this->commission_amount, 
            "currency" => $this->currency, 
            "order_status" => $this->order_status, 
            "ext_order_status" => $this->ext_order_status, 
            "sla_date" => $this->sla_date, 
            "sla_status" => $this->sla_status, 
            "remarks" => $this->remarks, 
            "created_date" => Carbon::parse($this->created_date)->setTimezone('UTC')->format('c'), 
            "created_by" => $this->created_by, 
            "modified_date" => Carbon::parse($this->modified_date)->setTimezone('UTC')->format('c'), 
            "modified_by" => $this->modified_by,
            "minutes_pending" => MasterParameter::where('group_key','RESELLER_TIMER')->where('key','MINUTES_PENDING')->first()->value,
            "do_date" => @$shipment->where('order_status',OrderReseller::ORDER_RESELLER_DIPROSES)->first()['created_date'],
            "picked_date" => @$shipment->where('order_status',OrderReseller::ORDER_RESELLER_DIKEMAS)->first()['created_date'],
            "gi_date" => @$shipment->where('order_status',OrderReseller::ORDER_RESELLER_DIKIRIM)->first()['created_date'],
            "received_date" => @$shipment->where('order_status',OrderReseller::ORDER_RESELLER_DITERIMA)->first()['created_date'],
            "returned_date" => @$shipment->where('order_status',OrderReseller::ORDER_RESELLER_PENGEMBALIAN)->first()['created_date'],
            'awb_no' => ResellerOrderShipment::where('order_header_id',$this->id)->where('order_status',OrderReseller::ORDER_RESELLER_DIKEMAS)->first() == null ? null : ResellerOrderShipment::where('order_header_id',$this->id)->where('order_status',OrderReseller::ORDER_RESELLER_DIKEMAS)->first()->awb_no??null,
            "reseller" => DetailOrderResellerResource::make($this->reseller)??null,
            "customer_shipment" => $this->customer_shipment,
            "link_identifier" => $this->link,
            "items" => CustomerResellerOrderDetailItemsResource::collection($this->items),
            "promotions" => $grouped_promo,
            "item_bundlings" => CustomerResellerOrderDetailItemsResource::collection($this->whenLoaded('item_bundlings')),
            "available_bundlings" => $this->applyBundling(ResellerOrderHeaders::where('order_no', $this->order_no)->first())??[]

         ];
    }
}
