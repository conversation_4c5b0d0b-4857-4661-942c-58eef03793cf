<?php

namespace App\Http\Resources;

use App\Jobs\CreditLimitQueue;
use App\Models\{BankAccount,CustomerHistory};
use App\Models\CreditLimit;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\CustomerShipmentResource;

class CustomerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $cr = CreditLimit::where('customer_external_id', $this->customer_id)->first();        
        CreditLimitQueue::dispatch($this->customer_id);
        $envValue = env('S3_STREAM_URL');
        // $rejection_reason = CustomerHistory::where('customer_id', $this->customer_id)->orderBy('created_date','DESC')->first();
        return [
            'customer_id' => $this->customer_id,
            'owner_name' => $this->owner_name,
            'email' => $this->email,
            'is_verified' => $this->is_verified ? true : false,
            'phone_number' => $this->phone_number,
            'distribution_channel' => $this->distribution_channel,
            'npwp' => $this->npwp,
            'npwp_name' => $this->npwp_name,
            'npwp_address' => $this->npwp_address,
            'npwp_province_code' => $this->npwp_province_code,
            'npwp_province' => $this->npwp_province,
            'npwp_city_code' => $this->npwp_city_code,
            'npwp_city' => $this->npwp_city,
            'npwp_district_code' => $this->npwp_district_code,
            'npwp_district' => $this->npwp_district,
            'npwp_zip_code' => $this->npwp_zip_code,
            'npwp_file' => $envValue . $this->npwp_file,
            'tax_type' => $this->tax_type,
            'tax_invoice' => $this->tax_invoice ? "Ya - $this->tax_invoice" : 'Tidak',
            'national_id' => $this->national_id,
            'address' => $this->address,
            'top' => $this->top,
            'credit_limit' => isset($cr->credit_limit) ? $cr->credit_limit : 0,
            'credit_limit_used' => isset($cr->credit_limit_used) ? $cr->credit_limit_used : 0,
            'credit_limit_used_percentage' => isset($cr->credit_limit_used_percentage) ? $cr->credit_limit_used_percentage : 0,
            'credit_limit_remaining' => isset($cr->credit_limit_remaining) ? $cr->credit_limit_remaining : 0,
            'credit_limit_currency' => isset($cr->credit_limit_currency) ? $cr->credit_limit_currency : 0,
            'top_days' => $this->top_days,
            'customer_type' => $this->customer_type,
            'is_active' => $this->is_active ? true : false,
            'is_pending_payment' => $this->is_pending_payment ? true : false,
            'is_change_password' => $this->user->is_change_password ? true : false ,
            'store_list' => CustomerShipmentResource::collection($this->shipments),
            'va_list' => $this->va??[],
            'bank_list' => BankAccount::active()->get()->toArray(),
            'status' => $this->status,
            'remarks' => $this->remarks
        ];
    }
}
