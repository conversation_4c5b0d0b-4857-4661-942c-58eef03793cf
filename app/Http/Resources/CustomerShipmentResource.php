<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CustomerShipmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'customer_shipment_id' => (string)$this->customer_shipment_id,
            'name' => $this->name,
            'address' => $this->address,
            'province_code' => $this->province_code,
            'province' => $this->province,
            'city_code' => $this->city_code,            
            'city' => $this->city,
            'district_code' => $this->district_code,
            'district' => $this->district,
            'zip_code' => $this->zip_code,
            'is_primary' => $this->is_primary
        ];
    }
}
