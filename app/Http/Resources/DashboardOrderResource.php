<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class DashboardOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'order_date' => $this->created_date,
            'do_date' => $this->do_date,
            'order_no' => $this->order_no,
            'customer_name' => @$this->owner_name,
            'store_name' => $this->instance_name,
            'transaction' => $this->nett_price??$this->total_nett,
            'status' => $this->order_status                                                                                   
        ];
    }
}