<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class DownloadCSVResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // dd($this);
        return [
            'nama_akun' => $this->nama_akun,
            'nama_toko' => $this->nama_toko,
            'tanggal_pesan' => $this->tanggal_pesan,
            'status_pesanan' => $this->status_pesanan,
            'no_order' => $this->no_order,
            'ref_order' => $this->ref_order,
            'no_dn' => $this->no_dn,
            'no_billing' => $this->no_billing,
            'article' => $this->article,
            'article_description' => $this->article_description,
            'price' => $this->price,
            'qty_order' => $this->qty_order,
            'diskon' => $this->diskon,
            'item_product' => $this->item_product,
            'product_category' => $this->product_category,
            'nama_sales' => $this->nama_sales,
            'tanggal_gi' =>  $this->tanggal_gi,
            'tanggal_billing' => $this->tanggal_billing,
            'gross' => $this->gross,
            'nett' => $this->nett,
        ];
    }
}
