<?php

namespace App\Http\Resources;
use App\Models\City;
use App\Models\Reseller;
use App\Models\ResellerCustomer;
use App\Models\ResellerLinkHistory;
use App\Models\ResellerRegistration;
use App\Models\Sales;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class DownloadResellerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    { 

        // data diri
        $birthday = $this->date_of_birth ?? '2000-01-01';
        $age = Carbon::parse($birthday)->age;
        $city = City::select('name')->where('id',$this->city_code)->first()->name ?? '-';
        
        //performa
        $ids = [];
        foreach($this->link as $link){
            array_push($ids, $link->id);
        }
        $clicks = ResellerLinkHistory::whereIn('link_id',$ids)->count();

        //churn rate
        $lastMonth = Carbon::now()->subMonth()->month;
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;


        $lastMonthOrders = $this->orders->filter(function ($record) use ($lastMonth, $currentYear) {
            $recordDate = Carbon::parse($record['created_date']);
            return $recordDate->month == $lastMonth && $recordDate->year == $currentYear;
        });
        $thisMonthOrders = $this->orders->filter(function ($record) use ($currentMonth, $currentYear) {
            $recordDate = Carbon::parse($record['created_date']);
            return $recordDate->month == $currentMonth && $recordDate->year == $currentYear;
        });

        $customerIdsLastMonth = [];
        foreach($lastMonthOrders as $lastMonthOrder){
            array_push($customerIdsLastMonth, $lastMonthOrder->customer_id);
        }
        $customerIdsThisMonth = [];
        foreach($thisMonthOrders as $thisMonthOrder){
            array_push($customerIdsThisMonth, $thisMonthOrder->customer_id);
        }
        $customersLastMonth = ResellerCustomer::whereIn('id',$customerIdsLastMonth)->count();
        $customersThisMonth = ResellerCustomer::whereIn('id',$customerIdsThisMonth)->count();

        $difference = $customersLastMonth - $customersThisMonth;
        $difference = ($difference < 0) ? 0 : $difference;

        if($customersLastMonth == 0){
            $churnRate = 0;
        } else {
            $churnRate = $difference / $customersLastMonth * 100;
        }

        $status = $this->is_active;
        switch ($status) {
            case Reseller::RESELLER_ACTIVE:
                $status = 'active';
                break;
            case Reseller::RESELLER_REJECTED:
                $status = 'freeze';
                break;
            case Reseller::RESELLER_INACTIVE:
                $status = 'non-active';
                break;
            default:
                $status;
                break;
        }

        $regisData = ResellerRegistration::where('reseller_id',$this->reseller_id)->first();
        if($regisData){
            $regisDate = Carbon::parse($regisData->action_date)->format('Y-m-d');
        } else {
            $regisDate = '-';
        }

        return [
            'reseller_id' => (string)$this->reseller_id ?? '-',
            'name' => $this->name,
            'phone_number'=> (string)$this->phone_number,
            'email' => $this->email ?? 0,
            'gender' => $this->gender?? 'Pria',
            'birthday' => $birthday,
            'age' => $age,
            'city'=> $city,
            'address' => $this->address ?? '-',
            'current_commission' => $this->commissions->sum('commission_amount'),
            'activation_date' => $regisDate ?? '-',
            'last_transaction' => $this->orders->sortByDesc('created_date')->first()->created_date ?? '-',
            'total_transaction'=> $this->orders->sum('total_amount')?? 0,
            'total_commission' => $this->commissions->sum('commission_amount'),
            'total_commission_potential' => $this->commissions->sum('potential_amount'),
            'total_commission_withdrawn' => $this->withdrawals->sum('total'),
            'registration_date' => Carbon::parse($this->created_date)->format('Y-m-d'),
            'status'=> $status,
            'total_shares' => $this->link->count(),
            'total_clicks' => $clicks,
            'churn_risk' => number_format($churnRate, 2) ?? 0
        ];
    }
}
