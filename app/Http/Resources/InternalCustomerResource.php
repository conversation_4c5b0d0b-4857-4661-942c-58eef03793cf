<?php

namespace App\Http\Resources;

use App\Models\CustomerShipment;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class InternalCustomerResource extends JsonResource
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = CustomerShipment::class;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {

        $total = @$this->order_header()->whereIn('order_status', ['Baru', 'Diproses', 'Siap Dikirim', 'Pembayaran', 'Dikirim', 'Diterima'])->sum('total_nett');
        return [
            'id' => $this->customer_id,
            'customer_id' => $this->customer_id,
            'sap_id' => $this->sap_id,
            'type' => @$this->customer->top == 'T001' ? 'TOKO CASH' : 'TOKO TEMPO',
            'owner_name' => $this->customer->owner_name ?? '-',
            'store_name' => $this->name,
            'instance_name' => $this->customer->instance_name,
            'target' => (string)@$this->customer->sales->target_sales?:(string)0,
            'total' => (string)$total??(string)0,
            'credit_limit' => $this->credit_limit_used_percentage ?? 0,
            'sales_name' => @$this->customer->sales->sales_name?? '-',
            'status' => @$this->customer->status ?? '-',
            'registration_date' => Carbon::parse($this->customer->created_date)->format('Y-m-d') ?? '-'                                                             
        ];
    }
}