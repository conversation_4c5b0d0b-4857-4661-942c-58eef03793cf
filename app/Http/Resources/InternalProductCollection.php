<?php

namespace App\Http\Resources;

use App\Models\Product;
use Illuminate\Http\Resources\Json\ResourceCollection;

class InternalProductCollection extends ResourceCollection
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = InternalProductResource::class;
    
    public $isGeneric;

    public function __construct($resource, $isGeneric = false)
    {
        // Panggil parent constructor
        parent::__construct($resource);
        $this->isGeneric = $isGeneric;
    }

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $latest = Product::select('modified_date')
                    ->orderByDesc('modified_date')
                    ->first()->modified_date??date('Y-m-d H:i:s');
        return [
            'latest_update' => $latest,
            'total' => $this->total(),
            'showPage' => $this->perPage(),
            'active_page' => $this->currentPage(),
            'total_page' => $this->lastPage(),
            'data' => $this->collection->map(function ($item) {
                $resource = new InternalProductResource($item);
                $resource->isGeneric = $this->isGeneric;
                return $resource->toArray(request());
            }),
        ];
    }
}

