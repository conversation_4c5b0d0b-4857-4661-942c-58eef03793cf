<?php

namespace App\Http\Resources;

use App\Models\ResellerArticleStock;
use Illuminate\Http\Resources\Json\ResourceCollection;

class InternalProductResellerCollection extends ResourceCollection
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = InternalProductResellerResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    
    public function toArray($request)
    {
        $latest = ResellerArticleStock::select('modified_date')
                                    ->orderByDesc('modified_date')
                                    ->first()->modified_date??date('Y-m-d H:i:s');
        return [
            'total' => $this->total(),
            'showPage' => $this->perPage(),
            'latest_update' => $latest,
            'data' => $this->collection
        ];
    }
}
