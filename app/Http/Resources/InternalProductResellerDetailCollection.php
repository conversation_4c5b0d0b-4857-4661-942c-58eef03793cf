<?php

namespace App\Http\Resources;

use App\Models\Product;
use Illuminate\Http\Resources\Json\ResourceCollection;

class InternalProductResellerDetailCollection extends ResourceCollection
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = InternalProductResellerResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {

        return [
            'total' => $this->collection->count(),
            'showPage' => 1,
            'latest_update' => @$this->collection->sortBy([
                fn ($a, $b) => $b->modified_date <=> $a->modified_date
            ])->first()->modified_date,
            'data' => $this->collection
        ];
    }
}
