<?php

namespace App\Http\Resources;

use App\Models\Product;
use App\Models\ResellerMasterSite;
use Illuminate\Http\Resources\Json\JsonResource;

class InternalProductResellerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {   
        $params = \Route::current()->parameters();
        if ($this->available_stock == 0) {
            $itemStatus = 'kosong';
        } else if ($this->available_stock <= 3){
            $itemStatus = 'terbatas';
        } else {
            $itemStatus = 'tersedia';
        }

        $sumStock = $this->available_stock;

        $displayedStock = isset($params['sku']) ? $this->available_stock : $sumStock;

        return [
            'id' => $this->article,
            'sku' => $this->sku_code_c,
            'name' => isset($params['sku']) ? $this->article_description : $this->product_name_c,
            'stock' =>  $displayedStock <= 0 ? 0 : $displayedStock,
            'category' => !in_array(strtolower($this->lvl3_description), ['bags', 'footwear']) ? 'NON-BAGS' : strtoupper($this->lvl3_description),
            'modified_date' => $this->modified_date,
            'item_status' => $itemStatus,
            'site' => isset($params['sku']) ? $this->name : '-'
        ];
    }
}
