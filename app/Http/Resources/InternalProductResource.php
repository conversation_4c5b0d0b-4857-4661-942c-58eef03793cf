<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class InternalProductResource extends JsonResource
{
    public $isGeneric = false;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    { 
        $params = \Route::current()->parameters();
        $productSize = collect();
        $totalVariantAvailable = null;

        if ($this->isGeneric) {
            $productVariant = DB::table('article')
            ->where('sku_code_c', $this->sku_code_c)
            ->pluck('product_variant_c');

            $totalVariantAvailable = DB::table('article_skus')
            ->where('sku_code_c', $this->sku_code_c)
            ->where('stock', '!=', 0)
            ->count(); 
    
            $productSize = DB::table('article')
            ->where('sku_code_c', $this->sku_code_c)
            ->pluck('product_size_c')
            ->unique(); 
    
            $warna = DB::table('master_color')
                ->whereIn('key', $productVariant)
                ->where('is_active', 1)
                ->pluck('value');
        } else {
            $warna = DB::table('master_color')
            ->where('key', $this->product_variant_c)
            ->where('is_active', 1)
            ->pluck('value')
            ->first();
        }

        $data = [
            'id' => $this->article,
            'sku' => $this->sku_code_c,
            'name' => isset($params['sku']) ? $this->article_description : $this->product_name_c,
            'stock' => optional($this->skuStock)->stock ?? 0,
            'color' => $this->isGeneric
                ? ($warna->isNotEmpty() ? $warna->implode(', ') : $this->product_variant_c)
                : ($warna ?: $this->product_variant_c),
            'size' => $this->isGeneric ? $productSize->implode(', ') : $this->product_size_c,
            'category' => strtoupper($this->lvl4_description),
            'base_price' => (int) $this->price->amount,
            'modified_date' => $this->modified_date,
        ];
    
        if ($this->isGeneric) {
            $data['total_variant_available'] = $totalVariantAvailable;
        }
    
        return $data;
    }
}
