<?php

namespace App\Http\Resources;

use App\Models\Coupon;
use App\Models\Promotions\Promotions;
use App\Models\Voucher;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class InternalResellerOrderDetailPromotionsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $code = '-';
        if (strtolower($this->discount_type) == 'promotions'|| strtolower($this->discount_type) == 'bundlings') {
            $m = Promotions::where('id',$this->discount_id)->first();
            $m_name = 'name';
            $code = null;
        }

        if (strtolower($this->discount_type) == 'voucher') {
            $m = Voucher::where('id',$this->discount_id)->first();
            $m_name = 'category';
            $code = $m->code;
        }

        if (strtolower($this->discount_type) == 'coupon') {
            $m = Coupon::where('id',$this->discount_id)->first();
            $m_name = 'name';
            $code = $m->coupon_code;
        }
        return [
            'id' => $this->id,
            'reference_name' => $this->reference_name,
            'discount_type' => $this->discount_type,
            'code' => $code,
            'amount' => $this->amount,
            'discount_name' => $m->$m_name,
            'apply_date' => $this->created_date
        ];
    }
}
