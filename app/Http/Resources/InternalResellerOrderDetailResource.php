<?php

namespace App\Http\Resources;

use App\Models\MasterBank;
use App\Models\OrderItemReseller;
use App\Models\OrderReseller;
use App\Models\Reseller;
use App\Models\ResellerOrderPromotion;
use App\Models\ResellerOrderShipment;
use App\Models\ResellerShipments;
use App\Models\ResellerTransaction;
use App\Traits\ResponseAPI;
use Illuminate\Http\Resources\Json\JsonResource;

class InternalResellerOrderDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    protected $perPage;
    protected $page;

    public function __construct($resource, $perPage, $page)
    {
        parent::__construct($resource);
        $this->perPage = $perPage;
        $this->page = $page;
    }

    use ResponseAPI;
    public function toArray($request)
    {
        foreach ($this->items as $item) {
            $groupedItems = OrderItemReseller::where('order_header_id', $item->order_header_id)
                ->groupBy('sku_code')->get()->paginate($this->perPage);
        }
        $orderDetailItems = OrderDetailItemsResource::collection($groupedItems);

        $transactions = ResellerTransaction::where('reference_id', $this->order_no)->orderBy('created_date','desc')->get();
        $txNo = '-';
        $txDate = '-';
        $commissionStatus = $this->order_status != OrderReseller::ORDER_RESELLER_SELESAI ? 'Potensi Komisi' : 'Komisi' ;
        foreach ($transactions as $transaction) {
            $txNo = $transaction->reference_id;
            if($transaction->type === 'Invoice_Payment'){
                $txDate = $transaction->created_date->format('Y-m-d');
            }
        }
        
        // foreach($this->reseller->bankAccounts as $account) {
        //     $bankAccount = $account->account_no;
        //     $bankName = MasterBank::where('id',$account->bank_id)->first()->bank_name;
        // }

        $historyShipment = ResellerOrderShipment::where('order_header_id',$this->id);
        $shipmentsDate = $historyShipment->get();
        $shipments = $historyShipment->where('order_status','!=',OrderReseller::ORDER_RESELLER_DIPROSES)->groupBy('delivery_number')->get();
        $shipmentArray = [];
        foreach($shipments as $shipment){
            array_push($shipmentArray, $shipment->awb_no);
        }
        $deliveryNo = implode(', ', $shipmentArray);

        $ids = $this->items()->pluck('id')->toArray();
        $ids[] = $this->id;
        $promotions = ResellerOrderPromotion::whereIn('reference_id',$ids)->groupBy('discount_id')->get();

        $data = [
            'order_detail' => [
                'reseller_name' => $this->reseller->name,
                'order_no' => $this->order_no,
                'delivery_no' => $deliveryNo??"",
                'billing_no' => $this->invoice_no,
                'order_date' => $this->order_date,
                'order_status' => $this->order_status,
                'history_date' => [
                    'pesanan_dibuat_date' => $this->order_date ?? null,
                    'pesanan_diproses_date' => $this->payment_date ?? null,
                    'pesanan_dikemas_date' => $shipmentsDate->where('order_status',OrderReseller::ORDER_RESELLER_DIKEMAS)->first()->created_date ?? null,
                    'pesanan_dikirim_date' => $shipmentsDate->where('order_status',OrderReseller::ORDER_RESELLER_DIKIRIM)->first()->created_date ?? null,
                    'pesanan_diterima_date' => $shipmentsDate->where('order_status',OrderReseller::ORDER_RESELLER_DITERIMA)->first()->created_date ?? null,
                    'pesanan_selesai_date' => $this->order_status == OrderReseller::ORDER_RESELLER_SELESAI ? $this->completed_date : null
                ],
            ],
            'customer_detail' => [
                'customer_name' => $this->customer_name,
                'phone_number' => $this->customer_phone_number,
                'email' => $this->customer_email,
                'address' => $this->customer_shipment_address.', '
                .$this->customer_shipment_subdistrict_name.', '
                .$this->customer_shipment_city_name.', '
                .$this->customer_shipment_region_name.', '
                .$this->customer_shipment_zip_code
            ],
            "promo" => InternalResellerOrderDetailPromotionsResource::collection($promotions),
            'order_summary' => [
                'payment_status' => $this->payment_status,
                'total_price' => (double)$this->sub_total_amount,
                'shipment_charges' => (double)$this->shipment_charges,
                'discount' => (double)$this->discount_amount,
                'total' => (double)$this->pay_amount
            ],
            'commission_details' => [
                'reseller_id' => $this->reseller->reseller_id,
                'reseller_status' => Reseller::resellerStatusEnum($this->reseller->is_active),
                'reseller_name' => $this->reseller->name,
                'commissions' => (double)$this->commission_amount,
                'commission_status' => $commissionStatus
            ],
            "items" => $this->pagedResponse($orderDetailItems, $orderDetailItems->currentPage(), $this->perPage)
         ];


        $data['payment_details'] = null;
        if ($transactions->count() > 0) {
            $paymentDetails = [
                'transaction_no' => $txNo,
                'transaction_date' => $txDate,
                'channel' => $this->payment_method,
                'amount' => (double)$this->pay_amount,
                'status' => $this->payment_status
            ];
            $data['payment_details'] = $paymentDetails;
        }
    
        return $data;
    }
}
