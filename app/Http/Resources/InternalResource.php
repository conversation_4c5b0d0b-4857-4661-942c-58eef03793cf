<?php

namespace App\Http\Resources;

use App\Models\Internal;
use App\Models\SalesAssignment;
use Illuminate\Http\Resources\Json\JsonResource;

class InternalResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'sales_id' => $this->id,
            'sales_name' => $this->name,
            'email' => @$this->user->email ?: '-',
            'username' => @$this->user->username ?: '-',
            'channel' => @$this->user->business_units()->pluck('name')->toArray() ?: [],
            'position' => $this->position_name ?: '-',
            'area' => $this->regional,
            'role' => @$this->user->roles()->pluck('name')->toArray() ?: [],
            'authorization' => @$this->user->roles()->pluck('tier_level')->toArray() ?: [],
            'user_sap' => $this->sap_username ?: '-',
            'sales_id' => $this->external_id,
            'direct_to' => @$this->assign->sales_name ?: '-',
            'status' => Internal::STATUS,
            'customers' => $this->customers,
            'head' => SalesAssignment::with('direct_sales','direct_to.direct_sales')->where('sales_id',$this->sales_id)->first(),
            'child' => SalesAssignment::with('sales','child.sales')->where('sales_id',$this->sales_id)->first(),
            'target_sales' => $this->target
        ];
    }
}
