<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class InvoiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'invoice_no' => $this->invoice_no,
            'customer_id' => $this->customer_id,
            'delivery_order_no' => $this->delivery_order_no,
            'no_resi' => $this->no_resi,
            'company_name' => $this->company_name,
            'parameter_value' => $this->parameter_value,
            'billing_date' => $this->billing_date,
            'due_date' => $this->due_date,
            'po_no' => $this->po_no,
            'so_no' => $this->so_no,
            'vat_no' => $this->vat_no,
            'down_payment' => $this->down_payment,
            'due_payment' => $this->due_payment,
            'gross_price' => $this->gross_price,
            'nett_price' => $this->nett_price,
            'dpp' => $this->dpp,
            'tax_amount' => $this->tax_amount,
            'currency' => $this->currency,
            'uom' => $this->uom,
            'status' => $this->status,
            'signature' => $this->signature,
            'signature_job' => $this->signature_job,
            'invoice_file_path' => $this->invoice_file_path,
            'tax_invoice_file_name' => $this->tax_invoice_file_name,
            'tax_invoice_file_type' => $this->tax_invoice_file_type,
            'tax_invoice_file_path' => $this->tax_invoice_file_path,
            'created_date' => $this->created_date,
            'created_by' => $this->created_by,
            'modified_date' => $this->modified_date,
            'modified_by' => $this->modified_by
        ];
    }
}
