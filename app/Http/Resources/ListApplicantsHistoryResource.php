<?php

namespace App\Http\Resources;
use App\Models\ResellerRegistration;
use App\Models\Sales;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ListApplicantsHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    { 
        $userName = Sales::where('sales_id',$this->action_by)->first();
        return [
            'reseller_id' => $this->reseller_id,
            'registration_date' => $this->created_date,
            'name' => $this->name,
            'status'=> $this->status == ResellerRegistration::REGISTRATION_APPROVED ? 'DITERIMA' : 'DITOLAK',
            'verification_date'=> $this->action_date ?? '-',
            'user' => $userName->sales_name ?? '-',
            'phone_number' => $this->phone_number
        ];
    }
}
