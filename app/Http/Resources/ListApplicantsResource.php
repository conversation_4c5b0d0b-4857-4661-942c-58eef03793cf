<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ListApplicantsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    { 
        return [
            'reseller_id' => $this->reseller_id,
            'registration_date' => $this->created_date,
            'phone_number' => $this->phone_number,
            'name' => $this->name,
        ];
    }
}
