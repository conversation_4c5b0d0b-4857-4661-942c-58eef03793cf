<?php

namespace App\Http\Resources;
use App\Models\Reseller;
use App\Models\ResellerRegistration;
use App\Models\ResellerTransaction;
use App\Models\Sales;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ListFilteredTransactionsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function __construct($data)
    {
        $this->transaction_date = $data['transaction_date'];
        $this->order_no = $data['order_no'];
        $this->customer_name = $data['customer_name'];
        $this->total_transaction = $data['total_transaction'];
        $this->transaction_status = $data['transaction_status'];
        $this->total_commissions = $data['total_commissions'];
        $this->commission_status = $data['commission_status'];
    }

    public function toArray($request)
    { 
        $orderNo = $this->order_no;
        $customerName = $this->customer_name;
        if (empty($orderNo) || is_null($orderNo)) {
            $orderNo = '-';
        }
        if (empty($customerName) || is_null($customerName)) {
            $customerName = '-';
        }
        return [
            'transaction_date' => $this->transaction_date,
            'order_no' => $orderNo,
            'customer_name'=> $customerName,
            'total_transaction'=> (double)$this->total_transaction,
            'transaction_status' => $this->transaction_status,
            'total_commissions' => $this->total_commissions,
            'commission_status' => $this->commission_status
        ];
    }
}
