<?php

namespace App\Http\Resources;
use App\Models\ResellerOrderPromotion;
use App\Models\Coupon;
use App\Models\Voucher;
use App\Models\Promotions\Promotions;

use Illuminate\Http\Resources\Json\JsonResource;

class ListOrdersInternalDownloadResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */

    public function toArray($request)
    { 
        $ids = $this->items()->pluck('id')->toArray();
        $ids[] = $this->id;        
        $promotions = ResellerOrderPromotion::whereIn('reference_id', $ids)->get();

        $promo = [];
        $coupon = [];
        $voucher = [];
        foreach ($promotions as $p) {
            
            if (strtolower($p->discount_type) == 'voucher') {
                $m = Voucher::where('id',$p->discount_id)->first();
                $voucher[] = $m->code;
            }

            if (strtolower($p->discount_type) == 'coupon') {
                $m = Coupon::where('id',$p->discount_id)->first();
                $coupon[] = $m->coupon_code;
            }

            if (strtolower($p->discount_type) == 'promotions' 
            || strtolower($p->discount_type) == 'promotion' 
            || strtolower($p->discount_type) == 'bundlings') {
                $m = Promotions::where('id',$p->discount_id)->first();
                $promo[] = $m->name;
            }
        }
   
        return [
            'order_date' => $this->order_date,
            'order_no' => $this->order_no,
            'invoice_no' => $this->invoice_no,
            'ext_order_no' => $this->ext_order_no,
            'reseller_name' => $this->reseller->name ?? '-',
            'reseller_phone_number' => $this->reseller->phone_number ?? '-',
            'reseller_id' => $this->reseller->reseller_id ?? '-',
            'customer_name'=> $this->customer_name ?? '-',
            'customer_shipment_name'=> $this->customer_shipment_name ?? '-',
            'customer_shipment_phone_number'=> $this->customer_shipment_phone_number ?? '-',
            'customer_email'=> $this->customer_email ?? '-',
            'customer_shipment_address'=> $this->customer_shipment_address ?? '-',
            'customer_shipment_city_name'=> $this->customer_shipment_city_name ?? '-',
            'customer_shipment_zip_code'=> $this->customer_shipment_zip_code ?? '-',
            'sub_total_amount' => (double)$this->sub_total_amount,
            'shipment_charges' => (double)$this->shipment_charges,
            'handling_charges' => (double)$this->handling_charges,
            'discount_amount' => (double)$this->discount_amount,
            'total_amount' => (double)$this->total_amount,
            'pay_amount' => (double)$this->pay_amount,
            'commission_amount' => (double)$this->commission_amount,
            'promo'  => implode(",", array_unique($promo)) ?? '',
            'coupon' => implode(",", array_unique($coupon)) ?? '',
            'voucher' => implode(",", array_unique($voucher)) ?? '',
            'payment_method' => $this->payment_method,
            'shipment_method' => $this->shipment_method,
            'awb_no' => $this->order_shipment->awb_no ?? '-',
            'delivery_number' => $this->order_shipment->delivery_number  ?? '-',
            'order_status' => $this->order_status,
        ];
    }
}
