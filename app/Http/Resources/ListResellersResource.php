<?php

namespace App\Http\Resources;
use App\Models\Reseller;
use App\Models\ResellerRegistration;
use App\Models\Sales;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ListResellersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    { 
        $status = $this->is_active;
        switch ($status) {
            case Reseller::RESELLER_ACTIVE:
                $status = 'active';
                break;
            case Reseller::RESELLER_REJECTED:
                $status = 'freeze';
                break;
            case Reseller::RESELLER_INACTIVE:
                $status = 'non-active';
                break;
            default:
                $status;
                break;
        }
        return [
            'reseller_id' => $this->reseller_id,
            'name' => $this->name,
            'status'=> $status,
            'last_order'=> $this->newest_order?? '-',
            'commissions' => $this->commission_amount ?? 0,
            'phone_number' => $this->phone_number
        ];
    }
}
