<?php

namespace App\Http\Resources;

use App\Models\PublicProduct;
use Illuminate\Http\Resources\Json\JsonResource;

class ListTransactionCustomerResellerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $product = PublicProduct::where('article',$this->items[0]->article_id)->first();

        return [
            'created_date' => $this->created_date,
            'product_image' => $product->mainImageVariant != null ? env('S3_STREAM_URL').$product->mainImageVariant->file_path : 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
            'order_no' => $this->order_no,
            'order_qty' => $this->items_sum_qty,
            'order_status' => $this->order_status,
            'total_product' => $this->pay_amount
        ];
    }
}
