<?php

namespace App\Http\Resources;
use App\Models\OrderReseller;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ListTransactionResellerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // $file_path = array_reduce($this->items()->with('mainImageVariant')->whereHas('mainImageVariant')->get()->toArray(), fn($rs,$i) => $rs .= $i['main_image_variant']['file_path'] , '') ?? null;
        
        $items = $this->items->toArray();
        $file_path = $items[array_key_first($items)]["main_image_variant"]["file_path"] ?? null;
        return [
            'order_no' => $this->order_no,
            'total_amount' => $this->total_amount,
            'commission_amount' => $this->commission_amount,
            'order_status'=> $this->order_status,
            'product_name' => $items[array_key_first($items)]["product_name"],
            'product_image' => $file_path ? env('S3_STREAM_URL') . $file_path : 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
            'other_articles' => sizeof($this->items->toArray()) - 1,
            'order_date' => Carbon::parse($this->order_date??0)->timestamp??0
        ];
    }
}
