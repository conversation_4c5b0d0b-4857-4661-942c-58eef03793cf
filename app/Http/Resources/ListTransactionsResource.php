<?php

namespace App\Http\Resources;
use App\Models\Reseller;
use App\Models\ResellerRegistration;
use App\Models\ResellerTransaction;
use App\Models\Sales;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ListTransactionsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    { 

        //type
        $type = '';
        switch ($this->order_status) {
            case ResellerTransaction::TX_TYPE_WITHDRAWAL:
                $type = 'Komisi';
                break;
            case ResellerTransaction::TX_TYPE_BONUS:
                $type = 'Bonus';
                break;
            case ResellerTransaction::TX_TYPE_ADJUSTMENT:
                switch ($this->type) {
                    case ResellerTransaction::TX_TYPE_POTENTIAL_COMMISSION:
                        $type = 'Adjustment Potensi';
                        break;
                    case ResellerTransaction::TX_TYPE_COMMISSION:
                        $type = 'Adjustment Komisi';
                        break;
                    default:
                        $type = $this->type;
                        break;
                }
                break;
            default:
                if($this->order_status == 'Selesai'){
                    $type = 'Komisi';
                } else if($this->order_status == 'Refund') {
                    if($this->type == ResellerTransaction::TX_TYPE_POTENTIAL_COMMISSION){
                        $type = 'Potensi';
                    }
                    if($this->type == ResellerTransaction::TX_TYPE_COMMISSION){
                        $type = 'Komisi';
                    }
                } else {
                    $type = 'Potensi';
                }
                break;
        }
        

        //commission_amount
        $commission_amount = 0;
        if($this->commission_amount == null){
            $commission_amount = $this->order_commission;
        }
        if($this->order_commission == null){
            $commission_amount = $this->commission_amount;
        }

        $referenceId = $this->reference_id;
        $customerName = $this->customer_name;
        if (empty($referenceId) || is_null($referenceId)) {
            $referenceId = '-';
        }
        if (empty($customerName) || is_null($customerName)) {
            $customerName = '-';
        }

        return [
            'transaction_date' => $this->created_date,
            'order_no' => $referenceId,
            'customer_name'=> $customerName,
            'total_transaction'=> (double)$this->amount,
            'transaction_status' => $this->order_status,
            //'total_commissions' => (double)$commission_amount,
            'transaction_type' => ResellerTransaction::transformTypes($this->type),
            'request_no' => $this->request_no,
            'remark' => $this->remarks
        ];
    }
}
