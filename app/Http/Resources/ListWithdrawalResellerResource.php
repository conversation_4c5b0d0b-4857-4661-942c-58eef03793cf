<?php

namespace App\Http\Resources;
use App\Models\ResellerCommissionWithdrawal;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ListWithdrawalResellerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'request_id' => $this->request_id,
            'request_date' => $this->request_date,
            'status' => $this->status,
            'amount'=> $this->amount,
            'transfer_fee' => $this->transfer_fee,
            'tax_amount' => $this->tax_amount,
            'total' => $this->total
        ];
    }
}
