<?php

namespace App\Http\Resources;
use App\Models\Reseller;
use App\Models\ResellerRegistration;
use App\Models\ResellerTransaction;
use App\Models\Sales;
use App\Models\TaxMatrix;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
class ListWithdrawalsDownloadResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    { 
        $pointer_metadata = $this->tax_metadata ? json_decode($this->tax_metadata) : null;
        $currentTaxSetting = Arr::flatten(array_map(fn($i) => [
            "L$i Tax Withdraw Base" => $pointer_metadata == null && $i == 1 ?  (double)$this->amount : collect($pointer_metadata->tax_summary??[])->firstWhere('sequence', $i)->prorate_withdraw??'', 
            "L$i Tax Percentage" => $pointer_metadata == null && $i == 1 ?  "2.5%" : (collect($pointer_metadata->tax_summary??[])->firstWhere('sequence', $i) ? (collect($pointer_metadata->tax_template??[])->firstWhere('sequence', $i)->tax_percentage) * 100 . '%' ??'' : ''),
            "L$i Tax Amount" => $pointer_metadata == null && $i == 1 ?  (double)$this->tax_amount : collect($pointer_metadata->tax_summary??[])->firstWhere('sequence', $i)->tax_amount??''] ,collect(json_decode(TaxMatrix::getCurrentMetadata()->metadata)->ruleset)->sortBy('sequence')->pluck('sequence')->toArray()));
        return [
            'request_id' => $this->request_id,
            'reseller_id' => $this->reseller_id,
            'reseller_name' => $this->name,
            'ktp' => $this->national_id,
            'npwp'      => $this->npwp,
            'reseller_phone_number' => $this->phone_number,
            'reseller_email' => $this->email,
            'request_date' => $this->request_date,
            'action_date' => $this->action_date  ?? '-',
            'action_by' => $this->action_by  ?? '-',
            'withdraw_amount'=> (double)$this->amount,
            ...$currentTaxSetting,
            'transfer_fee'=> (double)$this->transfer_fee,
            'total'=> (double)$this->total,
            'payout_amount'=> (double)$this->payout_amount,
            'withdraw_status' => $this->status,
            'bank_name' => $this->bank_name?? '-',
            'account_no' => $this->account_no,
            'account_name' => $this->account_name,
            'action_notes' => $this->action_notes ?? '-'
        ];
    }
}
