<?php

namespace App\Http\Resources;
use App\Models\Reseller;
use App\Models\ResellerRegistration;
use App\Models\ResellerTransaction;
use App\Models\Sales;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ListWithdrawalsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    { 

        return [
            'request_date' => $this->request_date,
            'reference_no' => $this->request_id,
            'name'=> $this->name,
            'reseller_id' => $this->reseller_id,
            'withdraw_amount'=> (double)$this->amount,
            'withdraw_status' => $this->status,
            'reseller_status' => Reseller::resellerStatusEnum($this->reseller_status)
        ];
    }
}
