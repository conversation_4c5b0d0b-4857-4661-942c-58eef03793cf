<?php

namespace App\Http\Resources;

use App\Models\Product;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class MenuResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'menu' => $this->lvl2_description,
            'submenu' => SubMenuResource::collection(DB::table('article')
            ->select('lvl2_description','lvl3_description')
            ->where('lvl2_description','=',$this->lvl2_description)
                ->whereNotNull('lvl3_description')
            ->groupByRaw('lvl2_description , lvl3_description')
            ->get())
        ];
    }
}
