<?php

namespace App\Http\Resources;
use App\Models\Reseller;
use App\Models\ResellerRegistration;
use App\Models\Sales;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class OngoingOrdersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */

    public function toArray($request)
    { 
        return [
            'order_date' => $this->order_date,
            'order_no' => $this->order_no,
            'customer_name'=> $this->customer_name,
            'order_value' => $this->total_amount,
            'status' => $this->order_status
        ];
    }
}
