<?php

namespace App\Http\Resources;

use App\Models\OrderItemReseller;
use App\Models\Product;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderDetailItemsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $article = Product::with(['price', 'mainImageGeneric'])->where('article',$this->article_id)->first();
        $orderDetails = OrderItemReseller::where('order_header_id',$this->order_header_id)->where('sku_code',$article->sku_code_c)->get();
        $productItemsResource = OrderDetailProductItemsResource::collection($orderDetails);
        return [
            'sku_code_c'=>$article->sku_code_c,
            'product_name_c'=>$article->product_name_c,
            'image_url'=>$article->mainImageVariant != null ? env('S3_STREAM_URL').$article->mainImageVariant->file_path : 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
            'flag'=>$article->flag(),
            'base_price'=>(double)$article->price->amount,
            'sub_total'=>(double)$orderDetails->sum('total_amount'),
            'bundling_item' => $this->remarks != null ? true : false,
            'product_items'=>$productItemsResource,
        ];
    }
}
