<?php

namespace App\Http\Resources;

use App\Models\OrderItemReseller;
use App\Models\Product;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderDetailProductItemsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // JSON RESPONSE
        return [
            "article" => $this->article_id,
            "product_name" => $this->product_name,
            "product_variant" => $this->product_variant,
            "product_size" => $this->product_size,
            "qty" => $this->qty,
            "sub_total" => (double)$this->total_amount
        ];
    }
}
