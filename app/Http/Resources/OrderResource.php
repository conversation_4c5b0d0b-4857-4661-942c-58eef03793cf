<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'order_date' => $this->created_date,
            'order_no' => $this->order_no,
            'customer_name' => @$this->customer->owner_name,
            'store_name' => $this->location_name,
            'transaction' => $this->total_nett,
            'status' => $this->order_status                                                                                   
        ];
    }
}
