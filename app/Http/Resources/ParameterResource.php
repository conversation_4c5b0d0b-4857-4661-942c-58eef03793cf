<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ParameterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
//            'id' => $this->id,
                'type' => $this->group_key,
                'key' => $this->key,
                'value' => $this->value
//            'modified_date' => $this->modified_date,
//            'modified_by' => $this->modified_by,
//            'description' => $this->description,
//            'created_date' => $this->created_date,
//            'created_by'=> $this->created_by
        ];
    }
}
