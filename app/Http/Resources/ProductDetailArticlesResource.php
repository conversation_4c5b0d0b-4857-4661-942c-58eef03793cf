<?php

namespace App\Http\Resources;

use App\Models\Color;
use App\Models\ProductPrice;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Helpers\Promotion\PromotionHelper as Promo;

class ProductDetailArticlesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    protected $promotion;

    public function __construct($resource, $promotion)
    {
        parent::__construct($resource);
        $this->promotion = $promotion;
    }

    public function toArray($request)
    {
        // dd($this);
        // $price =  new ProductPriceResource(ProductPrice::where('sku_code_c',$this->sku_code_c)
        // ->where('valid_from', '<=', now()->format('Y-m-d'))
        // ->where('valid_to', '>=', now()->format('Y-m-d'))->orderBy('valid_from', 'desc')->first());
        // $price = $this->amount;
        // $d = Promo::applyArticlePromotion($this->article, $price->amount??0);
        $calculate = fn($price, $disc, $type) => $type == "Value" ? $disc : $price * $disc / 100;
        // $d = $this->name != null ?
        // ['disc' => $calculate($this->amount, $this->generic_discount ? $this->generic_discount : $this->article_discount??0,$this->discount_type??"Value"), 'promotion' => ['name' => $this->name, 'action' => $this->action, 'discount_type' => $this->discount_type]]
        // :
        // ['disc' => 0, 'promotion' => null];
        $index = array_search($this->article,array_column($this->promotion,'article_id'));
        $d = is_numeric($index) && array_key_exists($index, $this->promotion)?(array)$this->promotion[$index]:[
            'article_id' => $this->article,
            'is_single'  => 0,
            'is_tier'    => 0,
            'is_bogo'    => 0,
            'maximum_single' => '0'
        ];

        $d['discount_type'] = $d['maximum_single'] > 100 ? 'Value' : 'Percentage';
        $max_percentage = $d['maximum_single']??0;
        $discount_price = $d['discount_type'] == 'Value' ? 
        $this->amount - $d['maximum_single'] : 
        $this->amount - ($this->amount * ($max_percentage > 0 ? $max_percentage / 100 : 0));

        // dd($d);

        return [
            'article_id' => $this->article,
            'color' => $this->color_value,
            'size' => $this->product_size_c,
            'stock' => (int)$this->available_stock,
            'price' => $this->amount??0,
            // 'discount' => $d['disc']??0,
            // 'discount_price' => $d['disc'] > 0 ? $this->amount - $d['disc'] : $this->amount,
            // 'potential' => ($d['disc'] > 0 ? $this->amount - $d['disc'] : $this->amount) * env('RSL_COMMISSION_MULTIPLIER', 0.05),

            // 'promotion' => $d['promotion'] ? collect($d['promotion'])->only(['name', 'action', 'discount_type'])->all() : null,       
            'discount' => round($d['discount_type'] == 'Value' ? 
                $d['maximum_single'] : 
                $this->amount * ($max_percentage > 0 ? $max_percentage / 100 : 0)),
            'discount_price' => round($discount_price),
            'potential' => round($discount_price) * env('RSL_COMMISSION_MULTIPLIER',0.05),
            'promotion' => $d,  
            'image' => empty($this->v_file_path) ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL').$this->v_file_path
        ];
    }
}
