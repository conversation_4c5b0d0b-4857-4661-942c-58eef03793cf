<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;
use App\Models\ProductPrice;
use Illuminate\Support\Facades\DB;

class ProductDetailCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $flag = [];
        $date = now()->format('Y-m-d');
        if ($this[0]->transfer_date <= $date && $this[0]->expired_date >= $date) {
            array_push($flag, 'NEW');
        }
        if ($this[0]->is_customizable) {
            array_push($flag, 'CUSTOM');
        }

        if ($this[0]->lvl4_description){
            array_push($flag, $this[0]->lvl4_description);
        }

        return [
            'article' => $this[0]->article,
            'article_description' => $this[0]->article_description,
            'cross_site' => $this[0]->cross_site,
            'sku_code_c' => $this[0]->sku_code_c,
            'external_product_id' => $this[0]->external_product_id,
            'product_name_c' => $this[0]->product_name_c,
            'main_image' => $this[0]->main_image??'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
            'product_variant_c' => $this[0]->product_variant_c,
            'product_size_c' => $this[0]->product_size_c,
            'product_description' => $this[0]->product_description,
            'product_style' => $this[0]->product_style,
            'product_feature' => $this[0]->product_feature,
            'product_tag' => $flag,
            'product_type' => $this[0]->product_type,
            'product_gender' => $this[0]->product_gender,
            'product_status' => $this[0]->product_status,
            'product_category' => $this[0]->lvl4_description,
            'collection' => $this[0]->collection,
            'specification' => array(
                'dimension' => $this[0]->dimension,
                'activity' => $this[0]->product_activity,
                'weight' => $this[0]->weight,
                'uomweight'=>$this[0]->uom
            ),
            'product_price' => new ProductPriceResource(ProductPrice::where('sku_code_c',$this[0]->sku_code_c)
                ->where('valid_from', '<=', now()->format('Y-m-d'))
                ->where('valid_to', '>=', now()->format('Y-m-d'))
                ->orderBy('valid_from', 'desc')
                ->first()),
            'variant' => ProductDetailResource::collection(DB::table('article')
            ->select('product_variant_c','sku_code_c')
            ->where('sku_code_c','=',$this[0]->sku_code_c)
            ->groupByRaw('product_variant_c , sku_code_c')
            ->get())
        ];
    }

    public function getFlagAttribute()
    {
        $flag = [];
        $date = now()->format('Y-m-d');
        if ($this->transfer_date <= $date && $this->expired_date >= $date) {
            array_push($flag, 'NEW');
        }
        if ($this->is_customizable) {
            array_push($flag, 'CUSTOM');
        }
        array_push($flag, $this->lvl4_description);
        return $flag;
    }
}
