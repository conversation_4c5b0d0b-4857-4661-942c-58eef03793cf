<?php

namespace App\Http\Resources;

use App\Http\Resources\ProductImageResource;
use App\Http\Resources\ProductPriceResource;
use App\Http\Resources\ProductDetailResource;
use App\Models\{ProductPrice,Color};
use App\Models\CustomTag;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use App\Helpers\ApiClient;
use Illuminate\Support\Facades\Log;

class ProductDetailNewResource extends JsonResource
{

    private $limit = 12;
    protected $getStockURI;
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $ai = DB::table('image_generic')
        ->where('image_generic.sku_code_c', $this->sku_code_c)
        ->select('image_generic.sequence_no','image_generic.url','image_generic.file_path','image_generic.is_main_image')
        ->orderBy('image_generic.sequence_no', 'asc')
        ->get();

        $article_data = $ai->map(function($image, $key){
            return [
                'sequence_no' => $image->sequence_no ?? null,
                'url' => isset($image->file_path) && is_string($image->file_path) ? env('S3_STREAM_URL').$image->file_path??'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
                'is_main_image' => $image->is_main_image == 1 ? true : false,
                'article_no' => null,
                'variant_no' => $this->sku_code_c
            ];
        });

        $variantimg = DB::table('image_variant')
        ->where('image_variant.article', 'LIKE', $this->sku_code_c.'%')
        ->leftJoin('article as a', 'a.article', '=', 'image_variant.article')
        ->select('image_variant.sequence_no', 'image_variant.article', 'image_variant.is_main_image', 'image_variant.url', 'image_variant.file_path', 'image_variant.sequence_no', 'a.product_variant_c as color')
        ->orderBy('image_variant.sequence_no', 'asc')
        ->orderBy('image_variant.is_main_image', 'desc')
        ->get()
        ->map(function($image, $key){
            return [
                'sequence_no' => $image->sequence_no ?? null,
                'url' => isset($image->file_path) && is_string($image->file_path) ? env('S3_STREAM_URL').$image->file_path??'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
                'is_main_image' => $image->is_main_image == 1 ? true : false,
                'article_no' => $image->article ?? null,
                'color' => Color::getByKey($image->color) ?? null,
                'variant_no' => null
            ];
        });

        $customTags = CustomTag::where('product_group', $this->lvl4_description)->get();
        foreach ($customTags as &$item) {
            $item['coordinate'] = json_decode($item['coordinate'], true);
        }

        $img = current(array_filter($ai->toArray(), function($e) {return $e->is_main_image == 1;}));

        $mi = is_bool($img) ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL').$img->file_path??'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp';
        $det = $this->details_customer($this->article);
        return [
            'article' => $this->article,
            'article_description' => $this->article_description,
            'sku_code_c' => $this->sku_code_c,
            'product_name_c' => $this->product_name_c,
            'product_variant_c' => $this->product_variant_c,
            'product_size_c' => $this->product_size_c,
            'product_description' => $this->product_description,
            'product_style' => $this->product_style,
            'product_feature' => $this->product_feature,
            'product_tag' => $this->flag(),
            'product_type' => $this->product_type,
            'product_gender' => $this->product_gender,
            'product_material'=>$this->fabric_type,
            'product_status' => $this->product_status,
            'product_category' => $this->lvl4_description,
            'wholesales_published_date' => $this->wholesales_published_date,
            'b2b_published_date'=>$this->b2b_published_date,
            'main_image'=> $mi??'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
            'specification' => [
                'dimension' => $this->dimension,
                'activity' => $this->product_activity,
                'weight' => $this->weight,
                'uomweight'=>$this->uom,
                'material'=>$this->fabric_type,
                'is_custom_logo'=> in_array('CUSTOM', $this->flag()) ? true : false
            ],
            'media' => 
                $article_data,
            'variantmedia' => $variantimg,
            'product_price' => new ProductPriceResource(ProductPrice::where('sku_code_c',$this->sku_code_c)
            ->where('valid_from', '<=', now()->format('Y-m-d'))
            ->where('valid_to', '>=', now()->format('Y-m-d'))->orderBy('valid_from', 'desc')->first()),
            'variant' => ProductDetailResource::collection($this->details),
            'variantcustomer' => isset($det) ? $det : [] ,
            'custom_placement' => $customTags
            // 'media' => ProductImageResource::collection($this->media())
        ];
    }
}
