<?php

namespace App\Http\Resources;

use App\Http\Resources\ProductImageResource;
use App\Http\Resources\ProductPriceResource;
use App\Http\Resources\ProductDetailResource;
use App\Models\{ProductPrice,Color, PublicProduct};
use App\Models\CustomTag;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use App\Helpers\Promotion\PromotionHelper as Promo;
use Illuminate\Support\Facades\Log;

class ProductDetailPublicInternalResource extends JsonResource
{
    private $limit = 12;
    protected $getStockURI;
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $site = $request->query('site');

        $ai = DB::table('image_generic')
        ->where('image_generic.sku_code_c', $this->sku_code_c)
        ->select('image_generic.sequence_no','image_generic.url','image_generic.file_path','image_generic.is_main_image')
        ->orderBy('image_generic.sequence_no', 'asc')
        ->get();

        $article_data = $ai->map(function($image, $key){
            return [
                'sequence_no' => $image->sequence_no ?? null,
                'url' => @$image->file_path == null ? null : env('S3_STREAM_URL').$image->file_path,
                'is_main_image' => $image->is_main_image == 1 ? true : false,
                'article_no' => null,
                'variant_no' => $this->sku_code_c
            ];
        });

        $variantimg = DB::table('image_variant')
        ->where('image_variant.article', 'LIKE', $this->sku_code_c.'%')
        ->leftJoin('article as a', 'a.article', '=', 'image_variant.article')
        ->select('image_variant.sequence_no', 'image_variant.article', 'image_variant.is_main_image', 'image_variant.url', 'image_variant.file_path', 'image_variant.sequence_no', 'a.product_variant_c as color')
        ->orderBy('image_variant.sequence_no', 'asc')
        ->orderBy('image_variant.is_main_image', 'desc')
        ->get()
        ->map(function($image, $key){
            return [
                'sequence_no' => $image->sequence_no ?? null,
                'url' => @$image->file_path == null ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL').$image->file_path,
                'is_main_image' => $image->is_main_image == 1 ? true : false,
                'article_no' => $image->article ?? null,
                'color' => Color::getByKey($image->color) ?? null,
                'variant_no' => null
            ];
        });

        $colors = DB::table('article')->where('article.sku_code_c',$this->sku_code_c)
                    ->leftJoin('master_color','article.product_variant_c','=','master_color.key')
                    ->distinct()->pluck('value')->toArray();

        $sizes = DB::table('article')->where('article.sku_code_c',$this->sku_code_c)
                ->distinct()->pluck('product_size_c')->toArray();

        if ($site) {
            $articles = PublicProduct::where('sku_code_c',$this->sku_code_c)
                    ->withSum(['rsl_stock' => fn($q) =>
                        $q->where('location_code',$site)],
                        'available_stock')->get();
        } else {
            $articles = PublicProduct::where('sku_code_c',$this->sku_code_c)
                    ->withSum('rsl_stock','available_stock')->get();
        }

        $stock = array_reduce($articles->toArray(),function($stock,$item){
            $stock += $item['rsl_stock_sum_available_stock'];
            return $stock;
        });

        $randIsDiscount = rand(0,1);
        $price = @$this->price->amount ?? 0;

        $all_article = [];
        foreach ($articles as $item) {
            $all_article[] = $item->article;
        }
        $all_article = implode(',', $all_article);
        $query = (array) DB::select('call get_article_promotions_2(?)', array($all_article));

        $promotions = [];
        foreach ($articles as $i) {
            $promotions[] = new ProductDetailArticlesResource($i, $query);
        }

        $mi = $ai->isEmpty()
        ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp'
        : env('S3_STREAM_URL') . current(array_filter($ai->toArray(), function($e) {return $e->is_main_image == 1;}))->file_path;   
        
        $price =  new ProductPriceResource(ProductPrice::where('sku_code_c',$this->sku_code_c)
        ->where('valid_from', '<=', now()->format('Y-m-d'))
        ->where('valid_to', '>=', now()->format('Y-m-d'))->orderBy('valid_from', 'desc')->first());
        return [
            'data'=> [ 
            'article' => $this->article,
            'article_description' => $this->article_description,
            'sku_code_c' => $this->sku_code_c,
            'product_name_c' => $this->product_name_c,
            'product_variant_c' => $this->product_variant_c,
            'product_size_c' => $this->product_size_c,
            'product_description' => $this->product_description,
            'product_style' => $this->product_style,
            'product_feature' => $this->product_feature,
            'product_tag' => $this->flag(),
            'product_type' => $this->product_type,
            'product_gender' => $this->product_gender,
            'product_material'=>$this->fabric_type,
            'product_status' => $this->product_status,
            'product_category' => $this->lvl4_description,
            'lvl3_description' => $this->lvl3_description,
            'wholesales_published_date' => $this->wholesales_published_date,
            'b2b_published_date'=>$this->b2b_published_date,
            'colors' => $colors,
            'sizes' => $sizes,
            'stock' => (int)$stock,
            'discount_price' => Promo::applyArticlePromotion($this->article, $price->amount??0),
            'main_image'=> $mi,
            'specification' => [
                'dimension' => $this->dimension,
                'activity' => $this->product_activity,
                'weight' => $this->weight,
                'uomweight'=>$this->uom,
                'material'=>$this->fabric_type,
                'is_custom_logo'=> in_array('CUSTOM', $this->flag()) ? true : false
            ],
            'media' => 
                $article_data,
            'variantmedia' => $variantimg,
            'product_price' => $price,
            'variant' => ProductDetailResource::collection($this->details),
            'articles' => $promotions
            ]
        ];
    }
}
