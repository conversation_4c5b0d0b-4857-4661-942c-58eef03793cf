<?php

namespace App\Http\Resources;

use App\Http\Resources\ProductImageResource;
use App\Http\Resources\ProductPriceResource;
use App\Http\Resources\ProductDetailResource;
use App\Models\{ProductPrice,Color, PublicProduct};
use App\Models\CustomTag;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use App\Helpers\Promotion\PromotionHelper as Promo;
use Illuminate\Support\Facades\Log;

class ProductDetailPublicResource extends JsonResource
{
    private $limit = 12;
    protected $getStockURI;
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $site = $request->query('site', '0000');

        $ai = DB::table('image_generic')
        ->where('image_generic.sku_code_c', $this->sku_code_c)
        ->select('image_generic.sequence_no','image_generic.url','image_generic.file_path','image_generic.is_main_image')
        ->orderBy('image_generic.sequence_no', 'asc')
        ->get();

        $article_data = $ai->map(function($image, $key){
            return [
                'sequence_no' => $image->sequence_no ?? null,
                'url' => @$image->file_path == null ? null : env('S3_STREAM_URL').$image->file_path,
                'is_main_image' => $image->is_main_image == 1 ? true : false,
                'article_no' => null,
                'variant_no' => $this->sku_code_c
            ];
        });

        $variantimg = DB::table('image_variant')
        ->where('image_variant.article', 'LIKE', $this->sku_code_c.'%')
        ->leftJoin('article as a', 'a.article', '=', 'image_variant.article')
        ->select('image_variant.sequence_no', 'image_variant.article', 'image_variant.is_main_image', 'image_variant.url', 'image_variant.file_path', 'image_variant.sequence_no', 'a.product_variant_c as color')
        ->orderBy('image_variant.sequence_no', 'asc')
        ->orderBy('image_variant.is_main_image', 'desc')
        ->get()
        ->map(function($image, $key){
            return [
                'sequence_no' => $image->sequence_no ?? null,
                'url' => @$image->file_path == null ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL').$image->file_path,
                'is_main_image' => $image->is_main_image == 1 ? true : false,
                'article_no' => $image->article ?? null,
                'color' => Color::getByKey($image->color) ?? null,
                'variant_no' => null
            ];
        });

        // $colors = DB::table('view_reseller_article_detail as article')
        //             ->leftJoin('master_color as mc','article.product_variant_c','=','mc.key')
        //             ->where('article.sku_code_c',$this->sku_code_c)
        //             ->selectRaw('DISTINCT mc.value')->toArray();

        // $sizes = DB::table('view_reseller_article_detail as article')->where('article.sku_code_c',$this->sku_code_c)
        //         ->distinct()->pluck('product_size_c')->toArray();

        $queries[] = 'article.*';
        // $queries[] = 'view_reseller_active_promotions_detail.item_id';
        // $queries[] = 'view_reseller_active_promotions_detail.promotion_id';
        // $queries[] = 'view_reseller_active_promotions_detail.generic_discount';
        // $queries[] = 'view_reseller_active_promotions_detail.article_discount'; 
        // $queries[] = 'view_reseller_active_promotions_detail.generic_max_value'; 
        // $queries[] = 'view_reseller_active_promotions_detail.article_max_value'; 
        // $queries[] = 'view_reseller_active_promotions_detail.highest_promotion'; 
        // $queries[] = 'view_reseller_active_promotions_detail.name';
        // $queries[] = 'view_reseller_active_promotions_detail.action';
        // $queries[] = 'view_reseller_active_promotions_detail.discount_type';

        if ($site == '0000') {
            $queries[] = 'view_article_article_stock_all.available_stock';
            $articles = DB::table('view_reseller_article_detail AS article')->where('article.sku_code_c',$this->sku_code_c)
                        // ->leftJoin('view_reseller_active_promotions_detail', fn($join) => $join->on('article.article', '=', 'view_reseller_active_promotions_detail.item_id'))
                        ->leftJoin('view_article_article_stock_all', fn($join) => $join->on('article.article', '=', 'view_article_article_stock_all.article'))
                        ->select(...$queries)
                        ->get();
        } 

        if ($site != '0000') {
            $queries[] = 'view_article_article_stock_site.available_stock';
            $articles = DB::table('view_reseller_article_detail AS article')->where('article.sku_code_c',$this->sku_code_c)
                        // ->leftJoin('view_reseller_active_promotions_detail', fn($join) => $join->on('article.article', '=', 'view_reseller_active_promotions_detail.item_id'))
                        ->leftJoin('view_article_article_stock_site', fn($join) => $join->on('article.article', '=', 'view_article_article_stock_site.article')->where('view_article_article_stock_site.location_code', $site))
                        ->select(...$queries)
                        ->get();
        }

        $colors = array_values(array_unique($articles->pluck('color_value')->toArray()));
        $sizes = array_values(array_unique($articles->pluck('product_size_c')->toArray()));
        // dd($articles);

        $price = @$this->price->amount ?? 0;

        $mi = $ai->isEmpty()
        ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp'
        : env('S3_STREAM_URL') . current(array_filter($ai->toArray(), function($e) {return $e->is_main_image == 1;}))->file_path;   

        $calculate = fn($price, $disc, $type) => $type == "Value" ? $disc : $price * $disc / 100;
        // $d = Promo::applyArticlePromotion($this->article, $this->amount??0);
        // $d = $this->name != null ?
        // ['disc' => $calculate($this->amount, $this->generic_discount ? $this->generic_discount : $this->article_discount??0,$this->discount_type??"Value"), 'promotion' => ['name' => $this->name, 'action' => $this->action, 'discount_type' => $this->discount_type]]
        // :
        // ['disc' => 0, 'promotion' => null];
        $query = (array) DB::select('call get_sku_promotions(?)',array($this->sku_code_c??''));
        if(array_key_exists(0, $query)){
            $d = (array) $query[0];
        }
        else{
            $d = [
                'sku_code_c' => $this->sku_code_c,
                'is_single'  => 0,
                'is_tier'    => 0,
                'is_bogo'    => 0,
                'maximum_single' => '0'
            ];
        }
        $productPrice = ProductPrice::where('sku_code_c',$this->sku_code_c)
        ->where('valid_from', '<=', now()->format('Y-m-d'))
        ->where('valid_to', '>=', now()->format('Y-m-d'))->orderBy('valid_from', 'desc')->first();
        $price =  new ProductPriceResource($productPrice);
        // $productPrice = $this->amount;
        $d['discount_type'] = $d['maximum_single'] > 100 ? 'Value' : 'Percentage';
        $max_percentage = $d['maximum_single']??0;
        $discount_price = $d['discount_type'] == 'Value' ? 
        $productPrice->amount - $d['maximum_single'] : 
        $productPrice->amount - ($productPrice->amount * ($max_percentage > 0 ? $max_percentage / 100 : 0));

        // if(!$productPrice){
        //     $discountPrice = 0;
        // } else {
        //     $discountPrice = $price->amount - $d['disc']??0;
        // }

        $all_article = [];
        foreach ($articles as $item) {
            $all_article[] = $item->article;
        }
        $all_article = implode(',', $all_article);
        $query = (array) DB::select('call get_article_promotions_2(?)', array($all_article));
        $promotions = [];
        foreach ($articles as $i) {
            $promotions[] = new ProductDetailArticlesResource($i, $query);
        }

        $flag = [];
        $date = now()->format('Y-m-d');
        if ($this->transfer_date <= $date && $this->expired_date >= $date) {
            array_push($flag, 'BARU');
        }
        if($this->lvl4_description){
            array_push($flag, $this->lvl4_description);
        }

        return [
            'article' => $this->article,
            'article_description' => $this->article_description,
            'sku_code_c' => $this->sku_code_c,
            'product_name_c' => $this->product_name_c,
            'product_variant_c' => $this->product_variant_c,
            'product_size_c' => $this->product_size_c,
            'product_description' => $this->product_description,
            'product_style' => $this->product_style,
            'product_feature' => $this->product_feature,
            'product_tag' => $flag,
            'product_type' => $this->product_type,
            'product_gender' => $this->product_gender,
            'product_material'=>$this->fabric_type,
            'product_status' => $this->product_status??null,
            'product_category' => $this->lvl4_description,
            'lvl3_description' => $this->lvl3_description,
            'wholesales_published_date' => $this->wholesales_published_date,
            'b2b_published_date'=>$this->b2b_published_date,
            'colors' => $colors,
            'sizes' => $sizes,
            'stock' => $this->available_stock,
            'discount' => round($d['discount_type'] == 'Value' ? 
                $d['maximum_single'] : 
                $productPrice->amount * ($max_percentage > 0 ? $max_percentage / 100 : 0)),
            'discount_price' => round($discount_price),
            'potential' => round($discount_price) * env('RSL_COMMISSION_MULTIPLIER',0.05),
            'promotion' => $d,       
            'main_image'=> $mi,
            'specification' => [
                'dimension' => $this->dimension,
                'activity' => $this->product_activity,
                'weight' => $this->weight,
                'uomweight'=>$this->uom,
                'material'=>$this->fabric_type,
                'is_custom_logo'=> in_array('CUSTOM', $flag) ? true : false
            ],
            'media' => 
                $article_data,
            'variantmedia' => $variantimg,
            'product_price' => $price,
            'variant' => ProductDetailResource::collection($articles),
            'articles' => $promotions
        ];
    }
}
