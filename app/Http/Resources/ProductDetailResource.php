<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;

use App\Models\Product;
use App\Models\Color;
class ProductDetailResource extends JsonResource
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function getColor($product){
        $color = Color::where('key', $product)->first();
        return $color ? $color->value : $product;
    }
    public function toArray($request){
        if (empty($request->query('site'))) {
            return [
                'product_variant_c' => $this->getColor($this->product_variant_c),
                'cross_site' => $this->sku_code_c.'.'.$this->product_variant_c
            ];
        } else {
            return [
                'product_variant_c' => $this->color_value,
                'cross_site' => $this->sku_code_c.'.'.$this->product_variant_c
            ];
        }
    }
}
