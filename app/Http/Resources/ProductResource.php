<?php

namespace App\Http\Resources;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {

        // $article_data = DB::table('image_generic')
        //     ->where('image_generic.sku_code_c', $this->sku_code_c)
        //     ->where('image_generic.is_main_image', 1)
        //     ->select('image_generic.file_path')
        //     ->orderBy('image_generic.sequence_no', 'asc')
        //     ->first();
        $cart = $request->user()->customer->cart;
        return [
            'id' => $this->article,
            'image' => $this->image_generic_url,
            // 'image_variant' => $this->image_variant_url,
            'name' => $this->product_name_c,
            'sku' => $this->sku_code_c,
            'total_variant' => $this->total_variant,
            'price' => (int) @$this->price->amount,
            'stock' => @$this->skuStock->stock ?? 0,
            'flag' => $this->flag(null, $request),
            'cart' => [
                'count' => $cart ? $cart->detailByProduct($this->article) : 0
            ]
        ];
    }
}