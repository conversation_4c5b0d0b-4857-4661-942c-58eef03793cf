<?php

namespace App\Http\Resources;

use App\Models\ResellerArticleStock;
use App\Repositories\GetStokRepo;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Helpers\RestHelper;

class ProductVariantInternalResellerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    { 
        $article_data = DB::table('image_variant')
        ->where('image_variant.article', $this->article)
        ->select('image_variant.sequence_no','image_variant.url','image_variant.is_main_image')
        ->orderBy('image_variant.sequence_no', 'asc')
        ->orderBy('image_variant.is_main_image', 'desc')
        ->get()
        ->map(function($image, $key){
            return [
                'sequence_no' => $image->sequence_no ?? null,
                'url' => @$image->file_path == null ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL') . $image->file_path,
                'is_main_image' => $image->is_main_image == 1 ? true : false,
            ];
        });

        return [
            'article_id' => $this->article,
            'article_description' => $this->article_description,
            'product_size_c' => $this->product_size_c,
            'product_name' => $this->product_name_c,
            'product_images' => 
                $article_data,
            'article_category' => $this->article_category,
            'weight' => $this->weight,
            'dimension' => $this->dimension,
            "stock" => ResellerArticleStock::where('article',$this->article)->first()->available_stock ?? 0,
            'price' => $this->price->amount,
            'created_date' => $this->created_date,
            'created_by' => $this->created_by,
            'modified_date' => $this->modified_date,
            'modified_by' => $this->modified_by
      
        ];
    }

}
