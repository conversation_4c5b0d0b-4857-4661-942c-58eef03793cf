<?php

namespace App\Http\Resources;

use App\Repositories\GetStokRepo;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Helpers\RestHelper;

class ProductVariantResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // dd($this->article); 
        // $stock = RestHelper::stockCache($this->article, $request->user()->customer->customer_id);
        $article_data = DB::table('image_variant')
            ->where('image_variant.article', $this->article)
            ->select('image_variant.sequence_no', 'image_variant.url', 'image_variant.is_main_image')
            ->orderBy('image_variant.sequence_no', 'asc')
            ->orderBy('image_variant.is_main_image', 'desc')
            ->get()
            ->map(function ($image, $key) {
                return [
                    'sequence_no' => $image->sequence_no ?? null,
                    'url' => @$image->file_path == null ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL') . $image->file_path,
                    'is_main_image' => $image->is_main_image == 1 ? true : false,
                ];
            });

        $range = range(1, 500);

        $random = collect($range)->shuffle()->slice(0, 1);
        $cart = $request->user() ? $request->user()->customer->cart : [];

        return [
            'article_id' => $this->article,
            'article_description' => $this->article_description,
            'product_size_c' => $this->product_size_c,
            'product_name' => $this->product_name_c,
            'product_images' =>
                $article_data,
            'article_category' => $this->article_category ?? null,
            'weight' => $this->weight,
            'dimension' => $this->dimension,
            "stock" => (int) $this->skuStock->stock,
            // 'stock_toko' => RestHelper::getCustomerStock($request->user()->customer->customer_id, $this->article),
            'stock_toko' => 0,
            //            'stock' => $random[0],
            'price' => $this->price,
            'created_date' => $this->created_date,
            'created_by' => $this->created_by,
            'modified_date' => $this->modified_date,
            'modified_by' => $this->modified_by,
            "min_qty" => (int) $this->moq,
            'cart' => [
                'count' => $cart ? $cart->detailByArticle($this->article) : 0
            ]

        ];
    }

    private function getStock($data)
    {
        $details[] = [
            'source' => 'CAREOM',
            'destination' => 'STK',
            'article' => $data->article,
            // 'site' => '1200'
        ];


        $data = [
            "source" => "CAREOM",
            "destination" => "STK",
            "detail" => $details
        ];

        $getStokRepo = new GetStokRepo();
        $response = $getStokRepo->getStock($data, auth()->user()->customer->customer_id);
        $qty = 0;
        try {
            $qty = $getStokRepo->getQty($response);
        } catch (\Exception $e) {
            Log::warning('error get stock from SAP' . $e->getMessage());
        }
        return $qty;
    }

    private function get_Moq($data)
    {
        $details[] = [
            'source' => 'CAREOM',
            'destination' => 'STK',
            'article' => $data->article,
            // 'site' => '1200'
        ];


        $data = [
            "source" => "CAREOM",
            "destination" => "STK",
            "detail" => $details
        ];

        $getStokRepo = new GetStokRepo();
        $response = $getStokRepo->getStock($data, auth()->user()->customer->customer_id);
        $qty = 0;
        try {
            $moq = $getStokRepo->getMoq($response);
        } catch (\Exception $e) {
            Log::warning('error get stock from SAP' . $e->getMessage());
        }
        return $moq;
    }
}