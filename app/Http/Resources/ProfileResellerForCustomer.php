<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class ProfileResellerForCustomer extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $total_sold = array_reduce($this->orders->toArray(),function($hasil,$data){
            $hasil += $data['items_sum_qty'];
            return $hasil;
        });
        return [
            'id' => $this->id,
            'reseller_id' => $this->reseller_id,
            'name' => $this->name,
            'phone_number' => $this->phone_number,
            'created_date' => Carbon::parse($this->created_date)->timezone('Asia/Jakarta')->format('Y-m-d H:i:s'),
            'total_transaction' => $this->orders_count,
            'total_product_sold' => $total_sold
        ];
    }
}
