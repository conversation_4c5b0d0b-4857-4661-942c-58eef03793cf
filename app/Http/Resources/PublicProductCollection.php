<?php

namespace App\Http\Resources;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Resources\Json\ResourceCollection;

class PublicProductCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */

    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = PublicProductResource::class; 
    public function toArray($request)
    {
        $all_sku = [];
        foreach ($this as $item) {
            $all_sku[] = $item->sku_code_c;
        }
        $all_sku = implode(',', $all_sku);
        $query = (array) DB::select('call get_sku_promotions_2(?)', array($all_sku));
        $promotions = [];
        $count = count($query);

        for ($i = 0; $i < $count; $i++) {
            // $promoData = (array) $query[$i];
            $promotions[] = new PublicProductResource($this->collection[$i], $query);
        }

        return [
            'total' => $this->total(),
            'showPage' => $this->perPage(),
            'lastPage' => $this->currentPage() == $this->lastPage(),
            'data' => $promotions
        ];
    }
}
