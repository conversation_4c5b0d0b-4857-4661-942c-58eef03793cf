<?php

namespace App\Http\Resources;

use App\Models\PublicProduct;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Helpers\Promotion\PromotionHelper as Promo;

class PublicProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */

    protected $promotion;

    public function __construct($resource, $promotion)
    {
        parent::__construct($resource);
        $this->promotion = $promotion;
    }

    public static function flag($param, $od = null)
    {
        $flag = [];
        $date = now()->format('Y-m-d');
        if ($param->transfer_date <= $date && $param->expired_date >= $date) {
            array_push($flag, 'BARU');
        }
        if($param->lvl4_description){
            array_push($flag, $param->lvl4_description);
        }
        return $flag;
    }
    public function toArray($request)
    {
        // $stock = array_reduce($this->rsl_stock->toArray(),function($stock,$item){
        //     $stock += $item['available_stock'];
        //     return $stock;
        // });
        $calculate = fn($price, $disc, $type) => $type == "Value" ? $disc : $price * $disc / 100;
        
        /* Generasi pertama ambil promo */
        // $d = Promo::applyArticlePromotion($this->article, $this->amount??0);
        
        /* Generasi kedua ambil promo */
        // $d = $this->name??null != null ?
        // ['disc' => $calculate($this->amount, $this->generic_discount ? $this->generic_discount : $this->article_discount??0,$this->discount_type??"Value"), 'promotion' => ['name' => $this->name, 'action' => $this->action, 'discount_type' => $this->discount_type]]
        // :
        // ['disc' => 0, 'promotion' => null];

        /* Generasi ketiga ambil promo */
        // $d = [
        //     'maximum_tier_percentage' => $this->maximum_tier_percentage??0,
        //     'maximum_tier' => $this->maximum_tier??0,
        //     'maximum_single_percentage' => $this->maximum_single_percentage??0,
        //     'is_bogo'   => $this->is_bogo??0
        // ];
        // Log::info($this);
       
            $d = (array)$this->promotion[array_search($this->sku_code_c,array_column($this->promotion,'sku_code_c'))]??[
                'sku_code_c' => $this->sku_code_c,
                'is_single'  => 0,
                'is_tier'    => 0,
                'is_bogo'    => 0,
                'maximum_single' => '0'
            ];
        
        // dd($d);
        
        $d['discount_type'] = $d['maximum_single'] > 100 ? 'Value' : 'Percentage';
        $max_percentage = $d['maximum_single']??0;
        $discount_price = $d['discount_type'] == 'Value' ? 
        $this->amount - $d['maximum_single'] : 
        $this->amount - ($this->amount * ($max_percentage > 0 ? $max_percentage / 100 : 0));
        return [
            'id' => $this->article,
            'image' => !empty($this->file_path) ? env('S3_STREAM_URL') . $this->file_path : 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp',
            'name' => $this->product_name_c,
            'sku' => $this->sku_code_c,
            'price' => $this->amount,
            // 'discount' => $d['disc']??0,
            // 'discount_price' => $d['disc'] > 0 ? $this->amount - $d['disc'] : $this->amount,
            // 'promotion' => $d['promotion'] ? collect($d['promotion'])->only(['name', 'action', 'discount_type'])->all() : null,       
            // 'potential' => ($d['disc'] > 0 ? $this->amount - $d['disc'] : $this->amount) * env('RSL_COMMISSION_MULTIPLIER', 0.05),     
            'discount' => $d['discount_type'] == 'Value' ? 
                $d['maximum_single'] : 
                $this->amount * ($max_percentage > 0 ? $max_percentage / 100 : 0),
            'discount_price' => $discount_price ,
            'promotion' => $d,
            'potential' => $discount_price * env('RSL_COMMISSION_MULTIPLIER', 0.05),           
            'stock' => $this->available_stock,
            'flag' => self::flag($this),
            'published_date' => $this->reseller_published_date ?? null
        ];
    }
}
