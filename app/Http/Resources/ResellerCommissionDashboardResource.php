<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerCommissionDashboardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */

     public function __construct($data)
     {
         $this->requestValue = $data['requestValue'];
         $this->requestAmount = $data['requestAmount'];
         $this->processedValue = $data['processedValue'];
         $this->processedAmount = $data['processedAmount'];
         $this->paidValue = $data['paidValue'];
         $this->paidAmount = $data['paidAmount'];
     }
    public function toArray($request)
    {
        return [
            'total_requests' => [
                'value' => $this->requestValue,
                'amount' => $this->requestAmount
            ],
            'processed' => [
                'value' => $this->processedValue,
                'amount' => $this->processedAmount
            ],
            'paid' => [
                'value' => $this->paidValue,
                'amount' => $this->paidAmount
            ]
        ];
    }
}
