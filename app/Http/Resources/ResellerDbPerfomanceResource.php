<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerDbPerfomanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public $clickCurrent;
    public $clickPrev;
    public $clickIncrease;
    public $clickPercentage;
    public $transactionCurrent;
    public $transactionPrev;
    public $transactionIncrease;
    public $transactionPercentage;
    public $salesCurrent;
    public $salesPrev;
    public $salesIncrease;
    public $salesPercentage;
    public $commissionCurrent;
    public $commissionPrev;
    public $commissionIncrease;
    public $commissionPercentage;

    public function __construct($data)
    {
        $this->clickCurrent = $data['clickCurrent'];
        $this->clickPrev = $data['clickPrev'];
        $this->clickIncrease = $data['clickIncrease'];
        $this->clickPercentage = $data['clickPercentage'];
        $this->transactionCurrent = $data['transactionCurrent'];
        $this->transactionPrev = $data['transactionPrev'];
        $this->transactionIncrease = $data['transactionIncrease'];
        $this->transactionPercentage = $data['transactionPercentage'];
        $this->salesCurrent = $data['salesCurrent'];
        $this->salesPrev = $data['salesPrev'];
        $this->salesIncrease = $data['salesIncrease'];
        $this->salesPercentage = $data['salesPercentage'];
        $this->commissionCurrent = $data['commissionCurrent'];
        $this->commissionPrev = $data['commissionPrev'];
        $this->commissionIncrease = $data['commissionIncrease'];
        $this->commissionPercentage = $data['commissionPercentage'];
    }
    public function toArray($request)
    {
        return $response = [
            "click" => [
                "current_week" => $this->clickCurrent,
                "previous_week" => $this->clickPrev,
                "increase" => $this->clickIncrease,
                "percentage" => $this->clickPercentage
            ],
            "transaction" => [
                "current_week" => $this->transactionCurrent,
                "previous_week" => $this->transactionPrev,
                "increase" => $this->transactionIncrease,
                "percentage" => $this->transactionPercentage
            ],
            "sales" => [
                "current_week" => $this->salesCurrent,
                "previous_week" => $this->salesPrev,
                "increase" => $this->salesIncrease,
                "percentage" => $this->salesPercentage
            ],
            "commission" => [
                "current_week" => $this->commissionCurrent,
                "previous_week" => $this->commissionPrev,
                "increase" => $this->commissionIncrease,
                "percentage" => $this->commissionPercentage
            ]
        ];
    }
}
