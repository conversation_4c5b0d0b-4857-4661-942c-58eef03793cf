<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerIndexCommissionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public $commission_amount;
    public $potential_amount;
    public $sales;
    public $withdrawalHistory;

    public function __construct($data)
    {
        $this->commission_amount = $data['commission_amount'];
        $this->potential_amount = $data['potential_amount'];
        $this->sales = $data['sales'];
        $this->withdrawalHistory = $data['withdrawalHistory'];
    }
    public function toArray($request)
    {
        return [
            "commission_amount" => $this->commission_amount,
            "potential_amount" => $this->potential_amount,
            "sales" => $this->sales,
            "withdrawalHistory" => $this->withdrawalHistory,
        ];
    }
}
