<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerIndexTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public $commission_amount;
    public $potential_amount;
    public $newOrderCurrent;
    public $newOrderPrevious;
    public $newOrderPercentage;
    public $newOrderIncrease;
    public $completedOrderCurrent;
    public $completedOrderPrevious;
    public $completedOrderPercentage;
    public $completedOrderIncrease;

    public function __construct($data)
    {
        $this->commission_amount = $data['commission_amount'];
        $this->potential_amount = $data['potential_amount'];
        $this->newOrderCurrent = $data['newOrderCurrent'];
        $this->newOrderPrevious = $data['newOrderPrevious'];
        $this->newOrderPercentage = $data['newOrderPercentage'];
        $this->newOrderIncrease = $data['newOrderIncrease'];
        $this->completedOrderCurrent = $data['completedOrderCurrent'];
        $this->completedOrderPrevious = $data['completedOrderPrevious'];
        $this->completedOrderPercentage = $data['completedOrderPercentage'];
        $this->completedOrderIncrease = $data['completedOrderIncrease'];
    }
    public function toArray($request)
    {
        return [
            "commission_amount" => $this->commission_amount,
            "potential_amount" => $this->potential_amount,
            "new_order" => [
                "current_week" => $this->newOrderCurrent,
                "previous_week" => $this->newOrderPrevious,
                "increase" => $this->newOrderIncrease,
                "percentage" => $this->newOrderPercentage
            ],
            "completed_order" => [
                "current_week" => $this->completedOrderCurrent,
                "previous_week" => $this->completedOrderPrevious,
                "increase" => $this->completedOrderIncrease,
                "percentage" => $this->completedOrderPercentage
            ]
        ];
    }
}
