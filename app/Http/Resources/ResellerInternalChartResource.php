<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerInternalChartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public $monthResults;
    public $valueResults;
    public $startPeriod;
    public $endPeriod;
    public $avgValue;
    public $highestValue;
    public $highestMonth;
    public $lowestValue;
    public $lowestMonth;

    public function __construct($data)
    {
        $this->monthResults = $data['monthResults'];
        $this->valueResults = $data['valueResults'];
        $this->startPeriod = $data['startPeriod'];
        $this->endPeriod = $data['endPeriod'];
        $this->avgValue = $data['avgValue'];
        $this->highestValue = $data['highestValue'];
        $this->highestMonth = $data['highestMonth'];
        $this->lowestValue = $data['lowestValue'];
        $this->lowestMonth = $data['lowestMonth'];
    }
    public function toArray($request)
    {
        return [
            'chart' => [
                'month' => $this->monthResults,
                'value' => $this->valueResults,
                'start_period' => $this->startPeriod,
                'end_period' => $this->endPeriod,
            ],
            'average' => [
                'value' => $this->avgValue,
            ],
            'highest' => [
                'value' => $this->highestValue,
                'month' => $this->highestMonth,
            ],
            'lowest' => [
                'value' => $this->lowestValue,
                'month' => $this->lowestMonth,
            ],
        ];
    }
}
