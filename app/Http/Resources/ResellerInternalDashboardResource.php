<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerInternalDashboardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public $reseller30d;
    public $resellerThisMonth;
    public $order30d;
    public $orderThisMonth;
    public $totalOrder30d;
    public $totalOrderThisMonth;
    public $totalCommissions30d;
    public $totalCommisionsThisMonth;

    public function __construct($data)
    {
        $this->reseller30d = $data['reseller30d'];
        $this->resellerThisMonth = $data['resellerThisMonth'];
        $this->order30d = $data['order30d'];
        $this->orderThisMonth = $data['orderThisMonth'];
        $this->totalOrder30d = $data['totalOrder30d'];
        $this->totalOrderThisMonth = $data['totalOrderThisMonth'];
        $this->totalCommissions30d = $data['totalCommissions30d'];
        $this->totalCommisionsThisMonth = $data['totalCommisionsThisMonth'];
    }
    public function toArray($request)
    {
        return [
            'active_resellers' => [
                'last_30_day' => abs($this->reseller30d),
                'negative' => $this->reseller30d < 0,
                'this_month' => $this->resellerThisMonth,
            ],
            'transactions' => [
                'last_30_day' => abs($this->order30d),
                'negative' => $this->order30d < 0,
                'this_month' => $this->orderThisMonth,
            ],
            'transactions_total' => [
                'last_30_day' => abs($this->totalOrder30d).'%',
                'negative' => $this->totalOrder30d < 0,
                'this_month' => floor($this->totalOrderThisMonth),
            ],
            'commissions_total' => [
                'last_30_day' => abs($this->totalCommissions30d).'%',
                'negative' => $this->totalCommissions30d < 0,
                'this_month' => floor($this->totalCommisionsThisMonth),
            ],
        ];
    }
}
