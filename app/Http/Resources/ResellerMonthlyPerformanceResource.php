<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerMonthlyPerformanceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public $salesCurrent;
    public $salesPrev;
    public $salesIncrease;
    public $salesPercentage;
    public $soldCurrent;
    public $soldPrev;
    public $soldIncrease;
    public $soldPercentage;
    public $avgSalesCurrent;
    public $avgSalesPrev;
    public $avgSalesIncrease;
    public $avgSalesPercentage;
    public $avgSoldCurrent;
    public $avgSoldPrev;
    public $avgSoldIncrease;
    public $avgSoldPercentage;
    public $commissionCurrent;
    public $commissionPrev;
    public $commissionIncrease;
    public $commissionPercentage;

    public function __construct($data)
    {
        $this->salesCurrent = $data['salesCurrent'];
        $this->salesPrev = $data['salesPrev'];
        $this->salesIncrease = $data['salesIncrease'];
        $this->salesPercentage = $data['salesPercentage'];
        $this->soldCurrent = $data['soldCurrent'];
        $this->soldPrev = $data['soldPrev'];
        $this->soldIncrease = $data['soldIncrease'];
        $this->soldPercentage = $data['soldPercentage'];
        $this->avgSalesCurrent = $data['avgSalesCurrent'];
        $this->avgSalesPrev = $data['avgSalesPrev'];
        $this->avgSalesIncrease = $data['avgSalesIncrease'];
        $this->avgSalesPercentage = $data['avgSalesPercentage'];
        $this->avgSoldCurrent = $data['avgSoldCurrent'];
        $this->avgSoldPrev = $data['avgSoldPrev'];
        $this->avgSoldIncrease = $data['avgSoldIncrease'];
        $this->avgSoldPercentage = $data['avgSoldPercentage'];
        $this->commissionCurrent = $data['commissionCurrent'];
        $this->commissionPrev = $data['commissionPrev'];
        $this->commissionIncrease = $data['commissionIncrease'];
        $this->commissionPercentage = $data['commissionPercentage'];
    }
    public function toArray($request)
    {
        return $response = [
            "sales" => [
                "current_week" => $this->salesCurrent,
                "previous_week" => $this->salesPrev,
                "increase" => $this->salesIncrease,
                "percentage" => $this->salesPercentage
            ],
            "products_sold" => [
                "current_week" => $this->soldCurrent,
                "previous_week" => $this->soldPrev,
                "increase" => $this->soldIncrease,
                "percentage" => $this->soldPercentage
            ],
            "average_sales" => [
                "current_week" => $this->avgSalesCurrent,
                "previous_week" => $this->avgSalesPrev,
                "increase" => $this->avgSalesIncrease,
                "percentage" => $this->avgSalesPercentage
            ],
            "average_sold" => [
                "current_week" => $this->avgSoldCurrent,
                "previous_week" => $this->avgSoldPrev,
                "increase" => $this->avgSoldIncrease,
                "percentage" => $this->avgSoldPercentage
            ],
            "commission" => [
                "current_week" => $this->commissionCurrent,
                "previous_week" => $this->commissionPrev,
                "increase" => $this->commissionIncrease,
                "percentage" => $this->commissionPercentage
            ]
        ];
    }
}
