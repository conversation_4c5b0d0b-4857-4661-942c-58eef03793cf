<?php

namespace App\Http\Resources;
use App\Models\PublicProduct;

use Illuminate\Support\Facades\DB;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Helpers\Promotion\PromotionHelper as Promo;

class ResellerProductResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {  
        $article_data = DB::table('image_generic')
        ->where('image_generic.sku_code_c', $this->sku_code_c)
        ->where('image_generic.is_main_image', 1)
        ->select('image_generic.file_path')
        ->orderBy('image_generic.sequence_no', 'asc')
        ->first();
        // $cart = $request->user()->customer->cart;

        $articles = PublicProduct::where('sku_code_c',$this->sku_code_c)
                    ->withSum('rsl_stock','available_stock')->get();

        $stock = array_reduce($articles->toArray(),function($stock,$item){
            $stock += $item['rsl_stock_sum_available_stock'];
            return $stock;
        });

        $price = @$this->price->amount ?? 0;
        $d = Promo::applyArticlePromotion($this->article, $price??0);
        return [
            'id' => $this->article,
            'image' => @$article_data->file_path == null ? null : env('S3_STREAM_URL').$article_data->file_path,
            'name' => $this->product_name_c,
            'sku' => $this->sku_code_c,
            'price' => $price,
            'discount_price' => $d['disc'] > 0 ? $price - $d['disc'] : $price,
            'potential' => ($d['disc'] > 0 ? $price - $d['disc'] : $price) * env('RSL_COMMISSION_MULTIPLIER', 0.05),
            'promotion' => $d['promotion'] ? collect($d['promotion'])->only(['name', 'action', 'discount_type'])->all() : null,       
            'stock' => $stock,
            'flag' => $this->flag(),
            'cart' => [
                'count' => 0
                // $cart ? $cart->detailByProduct($this->article) : 0
            ]
        ];
    }
}
