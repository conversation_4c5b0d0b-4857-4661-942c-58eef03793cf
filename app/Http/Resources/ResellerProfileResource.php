<?php

namespace App\Http\Resources;
use App\Http\Controllers\Reseller\ResellerController;
use App\Models\City;
use App\Models\CommissionLedgers;
use App\Models\District;
use App\Models\OrderItem;
use App\Models\OrderItemReseller;
use App\Models\OrderReseller;
use App\Models\Region;
use App\Models\Reseller;
use App\Models\ResellerLinkHistory;
use App\Models\ResellerRegistration;
use App\Models\ResellerTransaction;
use App\Models\Sales;
use App\Models\Subdistrict;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\ResellerMasterAddress;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */

    public function toArray($request)
    { 
        $txQtySum = 0;
        // $province = Region::select('name')->where('code',$this->province_code)->first()->name ?? null;
        // $city = City::select('name')->where('id',$this->city_code)->first()->name ?? null;
        // $district = District::select('name')->where('code',$this->district_code)->first()->name ?? null;
        // $zipCode = Subdistrict::select('postal_code')->where('code',$this->zip_code)->first()->postal_code ?? $this->zip_code;
        $province = ResellerMasterAddress::where('region_code',$this->province_code)->first()->region_name?? '-';
        $city = ResellerMasterAddress::where('city_code',$this->city_code)->first()->city_name?? '-';
        $district = ResellerMasterAddress::where('district_code',$this->district_code)->first()->district_name?? '-';
        $zipCode = ResellerMasterAddress::where('sub_district_code',$this->zip_code)->first()->zip_code?? '-';

        //transaction Data
        foreach($this->orders as $order){
            $orderDetail =  OrderItemReseller::where('order_header_id',$order->id)->get();
            $txQtySum += $orderDetail->sum('qty');
        }
        if($this->orders->count() != 0){
            $avg = $this->orders->sum('pay_amount') / $this->orders->count();
        } else {
            $avg = 0;
        }

        $resellerId = $this->id;
        $id = $this->reseller_id;

        $latestTransaction = ResellerTransaction::
        leftJoin('rsl_order_headers', 'rsl_order_headers.order_no', '=', 'rsl_transactions.reference_id')
        ->leftJoin('rsl_commission_ledgers', 'rsl_commission_ledgers.transaction_id', '=', 'rsl_transactions.id')
        ->select(
        'rsl_transactions.created_date'
         )
        ->whereIn('rsl_transactions.type', [ResellerTransaction::TX_TYPE_COMMISSION,
        ResellerTransaction::TX_TYPE_POTENTIAL_COMMISSION, 
        ResellerTransaction::TX_TYPE_WITHDRAWAL, 
        ResellerTransaction::TX_TYPE_INVOICE_PAYMENT,
        ResellerTransaction::TX_TYPE_BONUS])
        ->where(function($query) use ($resellerId, $id) {
            $query->where('rsl_order_headers.reseller_id', $resellerId)
            ->orWhere('rsl_commission_ledgers.reseller_id', $id);
         })
         ->orderBy('rsl_transactions.created_date','desc')
         ->first();

        //performance data

         $rank_table = DB::table('view_reseller_ranking');
         $sum_reseller = $rank_table->count();
         $rank = $rank_table->where('id', $resellerId)->first();
        //average commis
        if($this->orders->where('order_status',OrderReseller::ORDER_RESELLER_SELESAI)->count() > 0){
            $avgCommis = $this->orders->where('order_status',OrderReseller::ORDER_RESELLER_SELESAI)->sum('commission_amount') /$this->orders->where('order_status',OrderReseller::ORDER_RESELLER_SELESAI)->count();
        }
        else{
            $avgCommis = 0;
        }

        $bonuses = [];
        $bonusTxs = ResellerTransaction::where('type',ResellerTransaction::TX_TYPE_BONUS)->get();
        foreach($bonusTxs as $bonus){
            array_push($bonuses, $bonus->id);
        }
        $bonusReceived = CommissionLedgers::whereIn('transaction_id',$bonuses)->where('reseller_id',$this->reseller_id)->sum('amount');

        return [
            'profile' => [
                'reseller_id' => $this->reseller_id,
                'name' => $this->name,
                'phone_number' => $this->phone_number,
                'email' => $this->email,
                'national_id' => $this->national_id,
                'national_id_file' => $this->national_id_file,
                'national_id_url' => env('S3_STREAM_URL').$this->national_id_file,
                'npwp' => $this->npwp,
                'npwp_file' => $this->npwp_file,
                'npwp_url' => env('S3_STREAM_URL').$this->npwp_file,
                'account_no' => $this->bankAccounts->first()->account_no?? '-',
                'address' => $this->address,
                'status' => Reseller::resellerStatusEnum($this->is_active),
                'province_code' => $this->province_code,
                'province' =>$province,
                'city_code' => $this->city_code,
                'city' =>$city,
                'district_code' => $this->district_code,
                'district' =>$district,
                'zip_code' => $zipCode,
                'zip_code_id' => $this->zip_code,
                'gender' => $this->gender,
                'date_of_birth' => $this->date_of_birth
            ],
            'commissions' => [
                'balance' => $this->commissions->sum('commission_amount'),
                'average' => round($avgCommis),
                'bonus' => (double)$bonusReceived,
                'potential_commission' => $this->commissions->sum('potential_amount')
            ],
            'transactions' => [
                'total' => $txQtySum,
                'average' => round($avg),
                'last' => $latestTransaction->created_date ?? '-'
            ],
            'performance' => [
                'rank' => $rank->rank??'-',
                'total_reseller' => $sum_reseller,
                'rating' => '-',
                'transaction_amount' => $this->orders->count(),
                'shared_link' => $this->link->count(),
                'clicks' => $rank->total??'-',
            ],
            'va' => $this->va
        ];
    }
}
