<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerRegisteredBankResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public $account_name;
    public $account_no;
    public $bank_name;
    public $bank_code;

    public function __construct($data)
    {
        $this->account_name = $data['account_name'] ?? null;
        $this->account_no = $data['account_no'] ?? null;
        $this->bank_name = $data['bank']['bank_name'] ?? null;
        $this->bank_code = $data['bank']['bank_code'] ?? null;
    }
    public function toArray($request)
    {
        return [
            'account_name' => $this->account_name,
            'bank_no' => $this->account_no,
            'bank_name' => $this->bank_name,
            'bank_code' => $this->bank_code
        ];
    }
}
