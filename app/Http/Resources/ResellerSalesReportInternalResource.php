<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerSalesReportInternalResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public $monthResult;
    public $transactionValue;
    public $numberOfTransactions;
    public $soldItems;
    public $topResellers;
    public $topItems;
    public $topClicks;
    public $topNumbers;
    public $topValue;


    public function __construct($data)
    {
        $this->monthResult = $data['monthResult'];
        $this->transactionValue = $data['transactionValue'];
        $this->numberOfTransactions = $data['numberOfTransactions'];
        $this->soldItems = $data['soldItems'];
        $this->topResellers = $data['topResellers'];
        $this->topItems = $data['topItems'];
        $this->topClicks = $data['topClicks'];
        $this->topNumbers = $data['topNumbers'];
        $this->topValue = $data['topValue'];
    }
    public function toArray($request)
    {
        return [
            'generated' => now()->format('Y-m-d h:i:s'),
            'transactionValue' => [
                'month' => $this->monthResult,
                'data' => $this->transactionValue,
            ],
            'numberOfTransactions' => [
                'month' => $this->monthResult,
                'data' => $this->numberOfTransactions,
            ],
            'soldItems' => [
                'month' => $this->monthResult,
                'data' => $this->soldItems,
            ],
            'top_resellers_overall' => $this->topResellers,
            'top_items' => $this->topItems,
            'top_clicks' => $this->topClicks,
            'top_amount' => $this->topNumbers,
            'top_sum_orders' => $this->topValue,
        ];
    }
}
