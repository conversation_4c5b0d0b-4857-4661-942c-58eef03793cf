<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerSalesReportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public $monthResult;
    public $transactionValue;
    public $numberOfTransactions;
    public $soldItems;
    public $commissions;

    public function __construct($data)
    {
        $this->monthResult = $data['monthResult'];
        $this->transactionValue = $data['transactionValue'];
        $this->numberOfTransactions = $data['numberOfTransactions'];
        $this->soldItems = $data['soldItems'];
        $this->commissions = $data['commissions'];
    }
    public function toArray($request)
    {
        return [
            'transactionValue' => [
                'month' => $this->monthResult,
                'data' => $this->transactionValue,
            ],
            'numberOfTransactions' => [
                'month' => $this->monthResult,
                'data' => $this->numberOfTransactions,
            ],
            'soldItems' => [
                'month' => $this->monthResult,
                'data' => $this->soldItems,
            ],
            'commissions' => [
                'month' => $this->monthResult,
                'data' => $this->commissions,
            ],
        ];
    }
}
