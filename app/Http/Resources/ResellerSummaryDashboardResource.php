<?php

namespace App\Http\Resources;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class ResellerSummaryDashboardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */

     public function __construct($data)
     {
         $this->items30d = $data['items30d'];
         $this->itemsThisMonth = $data['itemsThisMonth'];
         $this->order30d = $data['order30d'];
         $this->orderThisMonth = $data['orderThisMonth'];
         $this->totalOrder30d = $data['totalOrder30d'];
         $this->totalOrderThisMonth = $data['totalOrderThisMonth'];
         $this->avgOrder30d = $data['avgOrder30d'];
         $this->avgOrderThisMonth = $data['avgOrderThisMonth'];
     }
    public function toArray($request)
    {
        return [
            'sold_items' => [
                'last_30_day' => abs($this->items30d),
                'negative' => $this->items30d < 0,
                'this_month' => (int)$this->itemsThisMonth,
            ],
            'number_transaction' => [
                'last_30_day' => abs($this->order30d),
                'negative' => $this->order30d < 0,
                'this_month' => $this->orderThisMonth,
            ],
            'total_transaction' => [
                'last_30_day' => abs((double)round($this->totalOrder30d)).'%',
                'negative' => $this->totalOrder30d < 0,
                'this_month' => floor($this->totalOrderThisMonth),
            ],
            'average_transaction' => [
                'last_30_day' => abs((double)round($this->avgOrder30d)).'%',
                'negative' => $this->avgOrder30d < 0,
                'this_month' => floor($this->avgOrderThisMonth),
            ],
        ];
    }
}
