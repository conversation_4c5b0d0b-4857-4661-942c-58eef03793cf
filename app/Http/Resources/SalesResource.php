<?php

namespace App\Http\Resources;

use App\Models\Sales;
use App\Models\SalesAssignment;
use Illuminate\Http\Resources\Json\JsonResource;

class SalesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'sales_id' => $this->id,
            'sales_name' => $this->sales_name,
            'email' => @$this->user->email ?: '-',
            'username' => @$this->user->username ?: '-',
            'channel' => @$this->user->business_units()->whereIn('name', ['Wholesales', 'B2B'])->orderBy('name', 'desc')->pluck('name')->toArray() ?: [],
            'position' => $this->position_name ?: '-',
            'area' => $this->regional,
            'role' => @$this->user->roles()->pluck('name')->toArray() ?: [],
            'authorization' => @$this->user->roles()->pluck('tier_level')->toArray() ?: [],
            'user_sap' => $this->sap_username ?: '-',
            'sales_id' => $this->sales_id,
            'direct_to' => @$this->assign->sales_name ?: '-',
            'status' => Sales::STATUS,
            'customers' => $this->customers,
            'head' => SalesAssignment::with('direct_sales','direct_to.direct_sales')->where('sales_id',$this->sales_id)->first(),
            'child' => SalesAssignment::with('sales','child.sales')->where('sales_id',$this->sales_id)->first(),
            'target_sales' => $this->target_sales
        ];
    }
}
