<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class SalesSummaryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {   
        return [
            "total_sales" => intval($this[0]->total_sales),
            "transaction_totals" => intval($this[0]->transaction_totals),
            "transaction_amount" => intval($this[0]->transaction_amount),
            "transaction_average" => floatval($this[0]->transaction_average),

            "total_sales_diff" => intval($this[0]->total_sales_diff),
            "transaction_totals_diff" => intval($this[0]->transaction_totals_diff),
            "transaction_amount_diff" => intval($this[0]->transaction_amount_diff),
            "transaction_average_diff" => floatval($this[0]->transaction_average_diff),

            "total_sales_percentage" => max(min(floatval($this[0]->total_sales_change_percentage), 100), -100),
            "transaction_totals_percentage" => max(min(floatval($this[0]->transaction_totals_change_percentage), 100), -100),
            "transaction_amount_percentage" => max(min(floatval($this[0]->transaction_amount_change_percentage), 100), -100),
            "transaction_average_percentage" => max(min(floatval($this[0]->transaction_average_change_percentage), 100), -100),
        ];
    }
}