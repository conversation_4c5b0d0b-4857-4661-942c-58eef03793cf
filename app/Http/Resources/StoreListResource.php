<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\CustomerShipmentResource;

class StoreListResource extends JsonResource
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = Customer::class;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'owner_name' => $this->owner_name,
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'store_list' => CustomerShipmentResource::collection($this->shipments)
        ];
    }
}
