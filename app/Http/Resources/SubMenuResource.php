<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class SubMenuResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $items = DB::table('article')
            ->select('lvl4_description')
            ->where('lvl2_description','=',$this->lvl2_description)
            ->where('lvl3_description','=',$this->lvl3_description)
            ->groupByRaw('lvl4_description')
            ->get();
        $result = collect($items)->pluck('lvl4_description')->toArray();

        return [
            'name' => $this->lvl3_description,
            'menu_item' => $result
        ];
    }
}
