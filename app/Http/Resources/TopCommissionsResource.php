<?php

namespace App\Http\Resources;
use App\Models\Reseller;
use App\Models\ResellerRegistration;
use App\Models\Sales;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class TopCommissionsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */

    public function __construct($data)
    {
        $this->resellerId = $data['reseller_id'];
        $this->name = $data['name'];
        $this->status = $data['status'];
        $this->sumOrders = $data['commissions'];
        $this->position = $data['position'];
    }
    public function toArray($request)
    { 
        $status = $this->status;
        switch ($status) {
            case Reseller::RESELLER_ACTIVE:
                $status = 'active';
                break;
            case Reseller::RESELLER_REJECTED:
                $status = 'freeze';
                break;
            case Reseller::RESELLER_INACTIVE:
                $status = 'non-active';
                break;
            default:
                $status;
                break;
        }
        return [
            'reseller_id' => $this->resellerId ?? '-',
            'name' => $this->name,
            'status'=> $status,
            'commissions' => $this->sumOrders ?? 0,
            'position' => $this->position
        ];
    }
}
