<?php

namespace App\Http\Resources;
use App\Models\Reseller;
use App\Models\ResellerRegistration;
use App\Models\Sales;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

use Illuminate\Http\Resources\Json\JsonResource;

class TopSellingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public $sku;
    public $name;
    public $qty;
    public $position;

    public function __construct($data)
    {
        $this->sku = $data['sku'];
        $this->name = $data['name'];
        $this->qty = $data['qty'];
        $this->position = $data['position'];
    }
    public function toArray($request)
    { 
        return [
            'sku' => $this->sku,
            'name' => $this->name,
            'qty'=> $this->qty,
            'position' => $this->position
        ];
    }
}
