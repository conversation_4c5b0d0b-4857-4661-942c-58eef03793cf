<?php

namespace App\Http\Resources\User;
use Illuminate\Http\Resources\Json\JsonResource;
use PhpOffice\PhpSpreadsheet\Calculation\Category;

class NotificationResource extends JsonResource
{
    public function toArray($request)
    {
        return [
                    'id' => $this->id,
                    'date' => $this->created_date,
                    'title' => $this->name,
                    'message' => $this->message,
                    'url' => $this->module,
                    'category' => $this->category,
                    'channel' => $this->channel,
                    'level' => $this->level,
                    'is_read' => $this->is_read
                ];
    }
}
