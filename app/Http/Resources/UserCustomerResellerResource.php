<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserCustomerResellerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "user_id" => $this->user_id, 
            "reference_id" => $this->reference_id, 
            "reference_object" => $this->reference_object, 
            "username" => $this->username, 
            "email" => $this->email, 
            "name" => $this->name, 
            "is_change_password" => $this->is_change_password, 
            "remember_token" => $this->remember_token, 
            "expired_token" => $this->expired_token, 
            "is_active" => $this->is_active, 
            "created_date" => $this->created_date, 
            "created_by" => $this->created_by, 
            "modified_date" => $this->modified_date, 
            "modified_by" => $this->modified_by,
         ];
    }
}
