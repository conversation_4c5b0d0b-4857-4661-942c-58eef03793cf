<?php

namespace App\Http\Resources;

use App\Http\Resources\SalesResource;
use App\Models\SalesAssignment;
use App\Models\User;
use Illuminate\Http\Resources\Json\JsonResource;

class UserInternalDetailsResource extends JsonResource
{
    protected bool $isMasked = true;

    public function __construct($resource, bool $isMasked = true)
    {
        parent::__construct($resource);
        $this->isMasked = $isMasked;
        $this->resource->setMasking($isMasked);
    }

    /**
     * Indicates if the resource's collection keys should be preserved.
     *
     * @var bool
     */
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $sales = $this->sales;
        return [
            "id" => (string) $this->user_id,
            "username" => $this->username,
            "name" => $this->name,
            "email" => $this->email,
            "sap_username" => optional($sales)->sap_username,
            "target_sales" => optional($sales)->target_sales,
            "phone_number" => optional($sales)->phone_number,
            "channel" => $this->business_units->map(fn($item) => [
                'id' => (string) $item->id,
                'name' => $item->name,
                'pivot' => [
                    'user_id' => (string) $item->pivot->user_id,
                    'business_unit_id' => (string) $item->pivot->business_unit_id,
                    'tier_level' => $item->pivot->tier_level,
                ],
            ]),
            "position" => optional($sales)->position_name,
            "area" => optional($sales)->regional,
            "roles" => optional($this->roles->first(), function ($role) {
                return [
                    'id' => (string) $role->id,
                    'name' => $role->name,
                    'pivot' => [
                        'user_id' => (string) $role->pivot->user_id,
                        'roles_id' => (string) $role->pivot->roles_id,
                    ]
                ];
            }),
            "tier_level" => optional($this->business_units->first())->pivot->tier_level,
            "sales" => $sales ? [
                'sales_id' => $sales->sales_id,
                'sales_name' => $sales->sales_name
            ] : null,
            "direct_to" => optional($sales?->directTo?->direct_sales?->user)->username,
            // 'head' => optional($sales)->sales_id ? SalesAssignment::with(['direct_sales', 'direct_to.direct_sales'])->where('sales_id', $sales->sales_id)->first() : [],
            // 'child' => optional($sales)->sales_id ? SalesAssignment::with(['sales', 'child.sales'])->where('sales_id', $sales->sales_id)->first() : [],
            "status" => $this->is_active,
            "latest_update" => $this->modified_date ?? date('Y-m-d H:i:s')
        ];
    }
}