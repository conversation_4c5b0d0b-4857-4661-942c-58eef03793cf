<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserInternalListResource extends JsonResource
{
    /**
     * Indicates if the resource's collection keys should be preserved.
     *
     * @var bool
     */
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $convertedChannel = $this->business_units->map(function ($item) {
            return [
                'id' => (string) $item->id,
                'name' => $item->name,
                'pivot' => [
                    'user_id' => (string) $item->pivot->user_id,
                    'business_unit_id' => (string) $item->pivot->business_unit_id,
                    'tier_level' => $item->pivot->tier_level,
                ]
            ];
        });

        $roles = @$this->roles()->select('roles.id', 'name')->first();
        if ($roles) {
            $roles->id = strval($roles->id);
            $roles->pivot->user_id = strval($roles->pivot->user_id);
            $roles->pivot->roles_id = strval($roles->pivot->roles_id);
        }

        return [
            "id" => (string) $this->user_id,
            "username" => $this->username,
            "name" => $this->name,
            "channel" => $convertedChannel,
            "position" => optional($this->sales)->position_name,
            "roles" => $roles,
            "status" => $this->is_active
        ];
    }
}