<?php

namespace App\Http\Resources;

use App\Http\Resources\SalesResource;
use App\Models\SalesAssignment;
use App\Models\User;
use Illuminate\Http\Resources\Json\JsonResource;

class UserInternalResource extends JsonResource
{
    /**
     * Indicates if the resource's collection keys should be preserved.
     *
     * @var bool
     */
    public $preserveKeys = true;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        // convert channelIds to string
        $channel = @$this->business_units()->select('business_unit.id', 'name')->get();

        $convertedChannel = [];
    
        foreach ($channel as $item) {
            $convertedChannel[] = [
                'id' => strval($item->id),
                'name' => $item->name,
                'pivot' => [
                    'user_id' => strval($item->pivot->user_id),
                    'business_unit_id' => strval($item->pivot->business_unit_id),
                    'tier_level' => $item->pivot->tier_level,
                ]
            ];
        }

        //convert roles id to string
        $roles = @$this->roles()->select('roles.id', 'name')->first();
        if ($roles) {
            $roles->id = strval($roles->id);
            $roles->pivot->user_id = strval($roles->pivot->user_id);
            $roles->pivot->roles_id = strval($roles->pivot->roles_id);
        }

        $sapuser = $this->sales()->select('sap_username')->first();
        $sapusername = ($sapuser) ? $sapuser->sap_username : null;

        $target = $this->sales()->select('target_sales')->first();
        $targetsales = ($target) ? $target->target_sales : null;

        $phone = $this->sales()->select('phone_number')->first();
        $phoneNo = ($phone) ? $phone->phone_number : null;

        // $direct = isset($this->sales->assign->sales_id) ? User::where('reference_id',$this->sales->assign->sales_id)->first() : null;
        $directTo = optional($this->sales->directTo?->direct_sales?->user)->username;

        $sales_id = $this->sales()->select('sales_id')->first();

        $rte = $request->route()->getName();
        return [
            "id" => strval($this->user_id),
            "username" => $this->username,
            "name" => $this->name,
            "email" => $this->email,
            "sap_username" => $sapusername,
            "target_sales" => $targetsales,
            "phone_number" => $phoneNo,
            "channel" => $convertedChannel,
            "position" => $this->sales()->select('position_name')->first(),
            "area" => $this->sales()->select('regional')->first(),
            "roles" => $roles,
            "tier_level" => @$this->business_units()->first()->pivot->tier_level,
            "sales" => $this->sales()->select('sales_id','sales_name')->first(),
            "direct_to" => $directTo,
            'head' => $sales_id ? SalesAssignment::with('direct_sales','direct_to.direct_sales')->where('sales_id',$sales_id->sales_id)->first() : [],
            'child' => $sales_id ? SalesAssignment::with('sales','child.sales')->where('sales_id',$sales_id->sales_id)->first() : [],
            "status" => $this->is_active,
            // 'customers' => @$this->sales->customers
            @$this->mergeWhen(str_contains($rte, '.show'), [
                'customers' => $this->sales->customers
            ])
        ];
    }
}
