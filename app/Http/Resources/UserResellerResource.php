<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResellerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'user_id' => $this->user_id,
            'reference_id' => $this->reference_id,
            'reference_object' => $this->reference_object,
            'username' => $this->username,
            'store_name' => $this->reseller_store_name ? $this->reseller_store_name : null,
            'is_change_password' => $this->is_change_password ? true : false,
            'is_active' => $this->is_active ? true : false
        ];
    }
}
