<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'user_id' => $this->user_id,
            'reference_id' => $this->reference_id,
            'reference_object' => $this->reference_object,
            'username' => $this->username,
            'is_change_password' => $this->is_change_password ? true : false,
            'is_verified' => @$this->customer->is_verified ? true : false,
            'is_active' => @$this->customer->is_active ? true : false,
        ];
    }
}
