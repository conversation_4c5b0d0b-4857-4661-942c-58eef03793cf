<?php

namespace App\Http\Resources\WAB;
use Illuminate\Http\Resources\Json\JsonResource;
class TransactionListWABResource extends JsonResource
{
    public function toArray($request)
    {
        return [
                    'order_no' => $this->order_no,
                    'order_date' => $this->created_date,
                    'ref_order' => $this->external_order_no,
                    'nama_akun' => $this->customer_id === '0' ? 'Tidak ada' : $this->customer->owner_name ?? 'Tidak ada',
                    'location_code' => $this->location_code,
                    'total_transaksi' => $this->total_nett,
                    'status_pesanan' => strtoupper($this->order_status)
                ];
    }

}
