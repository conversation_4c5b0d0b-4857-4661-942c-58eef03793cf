<?php

namespace App\Http\Resources\WAB;
use Illuminate\Http\Resources\Json\JsonResource;
class TransactionListWholesalesResource extends JsonResource
{
    public function toArray($request)
    {
        return [
                    'order_no' => $this->order_no,
                    'transaction_date' => $this->created_date,
                    'store_type' => $this->external_order_no,
                    'ref_order' => $this->customer_id === '0' ? 'Tidak ada' : $this->customer->owner_name ?? 'Tidak ada',
                    'customer_name' => $this->location_code,
                    'store_name' => $this->total_nett,
                    'order_status' => strtoupper($this->order_status),
                    'sales_name'=>'',
                    'total_transaksi'=>'',
                ];
    }

}
