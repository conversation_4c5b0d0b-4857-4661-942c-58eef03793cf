<?php

namespace App\Http\Resources;
use App\Models\Reseller;
use App\Models\ResellerRegistration;
use App\Models\Sales;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use App\Models\{MasterParameter, TaxMatrix};
use App\Models\CommissionWithdrawal;

use Illuminate\Http\Resources\Json\JsonResource;

class WithdrawalDetailInternalResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */

    public function toArray($request)
    { 
        $resellerId = Reseller::where('id', $this->reseller_id)->first()->reseller_id;
        return [
            'reference_no' => $this->request_id,
            'request_date' => $this->request_date,
            'withdraw_status'=> $this->status,
            'reseller_id' => $resellerId,
            'bank_name' => $this->bank_name,
            'bank_account' => $this->account_no,
            'bank_account_name' => $this->account_name,
            'withdraw_amount' => (double)$this->amount,
            'tax' => (double)$this->tax_amount,
            'total_commission'=> (double)$this->total,
            'user' => $this->action_by,
            'confirmation_date' => $this->action_date ?? null, 
            'transfer_date' => $this->payment_date,
            'reason' => $this->action_notes,
            'transfer_fee' => (double)$this->transfer_fee,
            'metadata' => json_decode($this->tax_metadata)
        ];
    }
}
