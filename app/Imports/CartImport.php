<?php

namespace App\Imports;

use App\Models\Cart;
use App\Models\Product;
use App\Models\BulkDraft;
use App\Models\ProductSku;
use App\Helpers\RestHelper;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Validation\ValidationException;

class CartImport implements ToCollection
{

    use Importable;
    public $cartData;
    public $cart_header_id;
    public $totalArticle;
    private $custId;
    private $isChange;
    private $isImport;

    public function __construct($custId = null, $isChange = null, $isImport = false)
    {
        $this->custId = $custId;
        $this->isChange = $isChange;
        $this->isImport = $isImport;
    }

    private function validate($rows)
    {
        $validator = Validator::make($rows->toArray(), [
            '0.0' => ['required', 'regex:/^sku$/i'],
            '0.6' => ['required', 'regex:/^jumlah order$/i'],
            // '1.0' => ['required'],
            // '1.1' => ['required'],
        ], [
            '*.*.required' => 'Data Tidak Boleh Kosong.',
            '0.0.regex' => 'Format upload tidak valid!',
            '0.6.regex' => 'Format upload tidak valid!',
        ]);

        return $validator;
    }
    public function collection(Collection $rows)
    {
        $validator = $this->validate($rows);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        $actualRows = $rows->filter(function ($item) {
            return $item[0] != null;
        });


        if ($this->isImport) {
            $this->cart_header_id = count($actualRows);
            $data = [];
            if (count($actualRows) > 301) {
                \Log::info($actualRows);
                throw ValidationException::withMessages([
                    'message' => 'Batas maksimal baris yang diperbolehkan adalah 300 baris, silahkan lakukan upload ulang kembali.',
                ]);
            }
            foreach ($actualRows as $key => $row) {
                if ($key != 0 && $row[0] != '' && $row[6] != '') {
                    if (array_key_exists($row[0], $data)) {
                        $data[$row[0]] += $row[6];
                    } else {

                        $data[$row[0]] = $row[6];
                    }
                }
            }

            $this->totalArticle = count($data);
            // $data = array_filter($data, function ($value) {
            //     return $value != 0;
            // });

            $products = Product::whereIn('article', array_keys($data))->pluck('article')->toArray();
            $data = array_intersect_key($data, array_flip($products));
            if ($data) {
                RestHelper::syncStock(array_keys($data), $this->custId);
            }

            $newData = ProductSku::whereIn('sku_id', array_keys($data))->get(['stock', 'sku_id'])->toArray();
            $importedRows = array_filter($newData, function ($item) {
                return floatval($item['stock']) > 0;
            });

            $cart = Cart::firstOrCreate([
                'customer_id' => $this->custId
            ]);

            $this->cart_header_id = $cart->id;

            if ($this->isChange) {
                BulkDraft::where('cart_id', $this->cart_header_id)->delete();
            }

            $this->cartData = array_map(function ($item) use ($data) {
                return [
                    'cart_id' => $this->cart_header_id,
                    'article' => $item['sku_id'],
                    'bulk_draft_id' => (string) \Str::uuid(),
                    'qty' => $data[$item['sku_id']],
                    'modified_date' => now()->format('Y-m-d H:i:s'),
                    'stock' => $item['stock']
                ];
            }, $importedRows);

            \DB::beginTransaction();

            BulkDraft::upsert(
                $this->cartData,
                ['cart_id', 'article'],
                ['qty', 'stock']
            );

            \DB::commit();

        }
    }


}