<?php

namespace App\Imports;

use App\Models\Product;
use App\Models\ProductSku;
use App\Models\CustomerStock;
use App\Models\CustomerShipment;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\ToCollection;

class StockTokoImport implements ToCollection
{
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */

    public $result;

    private $custId;
    private $isChange;

    public $data_stock;
    public function __construct($custId = null, $isChange = null)
    {
        $this->custId = $custId;
        $this->isChange = $isChange;
    }
    public function collection(Collection $rows)
    {
        $data = [];
        foreach ($rows as $key => $row) {
            if ($key != 0 && $row[0] != '' && $row[6] != '') {
                if (array_key_exists($row[0], $data)) {
                    $data[$row[0]] += $row[6];
                } else {

                    $data[$row[0]] = $row[6];
                }
            }
        }

        $this->data_stock = $data;
        $data = array_filter($data, function ($value) {
            return $value != 0;
        });
        $products = Product::whereIn('article', array_keys($data));
        $data = array_intersect_key($data, array_flip($products->pluck('article')->toArray()));

        $importedRows = $products->get(['sku_code_c', 'article', 'product_name_c', 'product_variant_c', 'product_size_c'])->toArray();
        $tokoId = CustomerShipment::where('customer_id', $this->custId)->first();
        $customerStocks = CustomerStock::where('customer_shipment_id', $tokoId->customer_shipment_id);
        if ($customerStocks->exists()) {
            $customerStocks->delete();
        }
        if ($tokoId) {
            $this->result = array_map(function ($item) use ($data, $tokoId) {
                return [
                    'customer_id' => $this->custId,
                    'customer_shipment_id' => $tokoId->customer_shipment_id,
                    'sku_code' => $item['sku_code_c'],
                    'article_id' => $item['article'],
                    'product_name' => $item['product_name_c'],
                    'product_variant' => $item['product_variant_c'],
                    'product_size' => $item['product_size_c'],
                    'qty' => $data[$item['article']],
                ];
            }, $importedRows);
        }


        \DB::beginTransaction();

        $chunks = array_chunk($this->result, 100);

        foreach ($chunks as $chunk) {
            CustomerStock::upsert(
                $this->result,
                ['customer_shipment_id', 'article_id'],
                ['sku_code', 'product_name', 'product_variant', 'product_size', 'qty']
            );
        }
        \DB::commit();

    }
}