<?php

namespace App\Interfaces;

interface OTPInterface
{
    /**
     * Bulk insert data article products to database
     *
     * @method  POST api/rsl
     * @access  public
     */
    public function requestOTP(array $data);

        /**
     * Bulk insert data article products to database
     *
     * @method  POST api/rsl
     * @access  public
     */
    public function verifyOTP(array $data);
}
