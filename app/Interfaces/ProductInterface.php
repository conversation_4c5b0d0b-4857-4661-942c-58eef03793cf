<?php

namespace App\Interfaces;

use App\Models\Product;

interface ProductInterface
{
    /**
     * Get data products
     *
     * @method  GET api/products
     * @access  public
     */
    public function getData(Product $product, $req,  $withStock = true, $isGeneric = false);

    /**
     * Get detail products
     *
     * @method  GET api/products/{$sku}
     * @access  public
     */
    public function getDetail($sku);

    /**
     * Get variant products
     *
     * @method  GET api/products/variant/{$id}
     * @access  public
     */
    public function getDetailVariant($id);

    /**
     * Get data filter list
     *
     * @method  GET api/product-filter-list/*
     * @access  public
     */
    public function getFilter(array $req);

    /**
     * Get data filter list
     *
     * @method  GET api/header/menus/*
     * @access  public
     */
    public function getMenus($req, $cid);

    /**
     * Get data detail public
     *
     * @method  GET /api/rsl/public/product/detail/{sku}?type=reseller
     * @access  public
     */
    public function getDetailPublic($sku);

    public function getDetailNew($sku, $customer_type);

    public function getDetailNewInternal($sku);

    public function getDetailVariantNew($sku, $color, $customer_type);

    public function getDetailVariantNewCustomer($sku, $color, $customer_type, $customer_id);
}
