<?php

namespace App\Interfaces;

interface ResellerInterface
{
    /**
     * Handle a login system
     *
     * @method  POST api/rsl/register
     * @access  public
     */
    public function registerAsReseller($request);

    /**
     * update reseller data
     *
     * @method  PUT api/rsl/verify-reseller
     * @access  public
     */
    public function verifyReseller($request, $id);

    /**
     * validate if phone number registered or not
     *
     * @method  GET api/rsl/check-phonenumber
     * @access  public
     */
    public function validateExistingPhoneNumber($request, $id);

    /**
     * validate if phone number registered or not
     *
     * @method  GET api/rsl/faq
     * @access  public
     */
    public function faq($request, $id);

    /**
     * validate if phone number registered or not
     *
     * @method  GET api/rsl/terms-and-conditions
     * @access  public
     */
    public function termsAndConditions($request, $id);
    
    /** 
     * validate if email registered or not
     *
     * @method  GET api/rsl/check-email
     * @access  public
     */
    public function validateExistingEmail($request, $id);
}
