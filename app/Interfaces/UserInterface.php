<?php

namespace App\Interfaces;

interface UserInterface
{
    /**
     * Handle a login system
     *
     * @method  POST api/login
     * @access  public
     */
    public function login($request);

    /**
     * Customer initial change password
     *
     * @method  POST api/initial-change-password
     * @access  public
     */
    public function initChangePassword($request);

    /**
     * Get user store list
     *
     * @method  GET api/get-store-list
     * @access  public
     */
    public function storeList($customer);

    /**
     * Handle a activation user
     *
     * @method  GET api/user-activation
     * @access  public
     */
    public function activation($user);

    /**
     * Get user profile
     *
     * @method  GET api/profile
     * @access  public
     */
    public function getProfile($customer);

    /**
     * Handle user to change the password
     *
     * @method  POST api/change-password
     * @access  public
     */
    public function changePassword($customer);

    /**
     * Handle user to get their credit limit
     *
     * @method  GET api/credit-limit
     * @access  public
     */
    public function getCreditLimit($customerId);

    /**
     * update user data
     *
     * @method  PATCH api/edit-profile
     * @access  public
     */
    public function updateProfile($request);

    /**
     * add shipment address
     *
     * @method  POST api/add-address
     * @access  public
     */
    public function addShipmentAddress($request);

    /**
     * edit shipment address
     *
     * @method  PUT api/edit-address
     * @access  public
     */
    public function editShipmentAddress($id, $request);

    /**
     * change shipment address
     *
     * @method  PATCH api/change-address/{id}
     * @access  public
     */
    public function changeShipmentAddress($id, $request);

    /**
     * delete shipment address
     *
     * @method  DELETE api/delete-address/{id}
     * @access  public
     */
    public function deleteShipmentAddress($id, $request);
}
