<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Services\LoggerIntegration;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Helpers\DanamonHelper;

class BankQueue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private $dn_helper;
    private $rq, $action, $log;

    //retry per n-second
    public $backoff = 60;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request, $action)
    {
        $this->onConnection('redis');
        $this->onQueue('eventqueue');
        $this->dn_helper = new DanamonHelper;
        $this->rq = $request;
        $this->action = $action;
        $this->log = [
            'reference_no'=> '',
            'module' => 'Danamon Queue',
            'name' => $action,
            'type'=> 'dispatch',
            'status' => '',
            'description' => ''
        ];

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{

            $this->dn_helper->doAction($this->action, $this->rq);
           
        }
        catch(\Exception $e){
            $this->log['status'] = "failed";
            $this->log['description'] = $e->getMessage();
            (new LoggerIntegration())->InsertLogger($this->log);
        } 

        

    }
}
