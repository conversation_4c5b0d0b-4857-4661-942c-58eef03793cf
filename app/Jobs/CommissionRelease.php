<?php

namespace App\Jobs;

use App\Helpers\Danamon\Specification\TopupTransfer;
use App\Models\ResellerVA;
use App\Services\LoggerIntegration;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Helpers\DanamonHelper;

class CommissionRelease implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private $dn_helper;
    private $reseller_id, $amt, $log;

    //retry per n-second
    public $backoff = 60;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($rsl, $amt)
    {
        $this->onConnection('redis');
        $this->onQueue('eventqueue');
        $this->dn_helper = new DanamonHelper;
        $this->reseller_id = $rsl;
        $this->amt = $amt;
        $this->log = [
            'reference_no'=> '',
            'module' => 'Danamon Queue',
            'name' => 'Commission Release',
            'type'=> 'Inbound',
            'status' => '',
            'description' => ''
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{

            $this->dn_helper->topupTransfer($this->transform());
           
        }
        catch(\Exception $e){
            $this->log['status'] = "failed";
            $this->log['description'] = $e->getMessage();
            (new LoggerIntegration())->InsertLogger($this->log);
        } 

    }

    public function transform()
    {
        $d = ResellerVA::where('reseller_id', $this->reseller_id)->first()->virtual_account_no??'********';
        $rq = TopupTransfer::rq();
        $rq["UserReferenceNumber"] = strtoupper(getenv('DANAMON_PARTNER_ID').uniqid('vatu'));
        $rq["RequestTime"] = date("YmdHis");
        $rq["VirtualAccountNumber"] = $d;
        $rq["Amount"] = strval($this->amt);
        $rq["DebitedAccountNumber"] = $this->dn_helper->getOperationalAccount();
        $rq["PaymentType"] = "O";
        return $rq;
    }
}
