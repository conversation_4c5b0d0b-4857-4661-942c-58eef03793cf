<?php

namespace App\Jobs;

use App\Events\RCEvent;
use Exception;
use App\Models\Reseller;
use App\Models\TaxMatrix;
use App\Models\MasterBank;
use App\Models\ResellerVA;
use App\Helpers\RestHelper;
use App\Models\ResellerBank;
use Illuminate\Bus\Queueable;
use App\Helpers\DanamonHelper;
use App\Models\MasterParameter;
use App\Models\UserNotification;
use App\Models\ResellerCommission;
use Illuminate\Support\Facades\DB;
use App\Services\LoggerIntegration;
use Illuminate\Support\Facades\Log;
use App\Models\CommissionWithdrawal;
use App\Repositories\GetSocialsRepo;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\ResellerCommissionWithdrawal;
use App\Helpers\Danamon\Specification\TopupTransfer;
use App\Models\User;

class CreateCommissionWithdrawal implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, RestHelper;
    private $dn_helper;
    private $validatedData, $user_id, $log;

    //retry per n-second
    public $backoff = 60;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($validatedData,$user_id)
    {
        $this->onConnection('redis');
        $this->onQueue('createcwqueue');
        // $this->dn_helper = new DanamonHelper;
        $this->validatedData = $validatedData;
        $this->user_id = $user_id;
        $this->log = [
            'reference_no'=> '',
            'module' => 'Careom Queue',
            'name' => 'Create Commission Withdrawal',
            'type'=> 'dispatch',
            'status' => '',
            'description' => ''
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            $user = User::where('user_id',$this->user_id)->first();
            $validatedData = $this->validatedData;
            $accountName = $validatedData['account_name'];
            $accountNo = $validatedData['account_no'];
            $amount = $validatedData['amount'];
            // $transfer_fee = $validatedData['transfer_fee'];
            $taxAmount = $validatedData['tax_amount'];
            $total = $validatedData['total'];
            $transferMethod = $validatedData['transfer_method'] ?? "Overbooking";
            $mp = MasterParameter::where('group_key','COMMISSION_WITHDRAWAL_FEE')
                                            ->where('key',$transferMethod)->first();
            $transfer_fee = (int)$mp->value;

            $reseller = Reseller::Where("reseller_id", "=", $user->reference_id)->first();

            #Current Saldo
            $checkAmount = ResellerCommission::Where("reseller_id", $reseller->id)->first();

            $resellerBank = ResellerBank::where('reseller_id', $reseller->id)->first();
                $bank = MasterBank::where('id', $resellerBank->bank_id)->first();
            #Current Pending Request
            $checkPendingRequest = ResellerCommissionWithdrawal::Where("reseller_id", $reseller->id)
                ->Where("status", ResellerCommissionWithdrawal::WAITING_FOR_APPROVAL)->first();

            if ($checkPendingRequest) {
                throw new Exception("There is a pending request of your commission waiting for approval. RequestID: " . $checkPendingRequest->request_id);
                // return $this->sendError("There is a pending request of your commission waiting for approval. RequestID: " . $checkPendingRequest->request_id, 200);
            }

            if ($total < 10000 && $transferMethod == CommissionWithdrawal::RTOL) {
                throw new Exception("Minimal Pengajuan Rp 10.000 setelah dipotong Biaya transfer & Pajak");
                // return $this->sendError("Minimal Pengajuan Rp 10.000 setelah dipotong Biaya transfer & Pajak", 200);
            }

            $requestId = "CR" . date("Ymd") . mt_rand(1000, 9999);
            $extRequestId = "EXT" . $requestId;
            $e_insert = null;
            try {
                $taxCalculation = TaxMatrix::calculateTax(TaxMatrix::getCurrentMetadata(), $amount, CommissionWithdrawal::getLeverageTax($reseller));
                $data = [
                    'request_date' => date("YmdHis"),
                    'request_id' => $requestId,
                    'ext_request_id' => $extRequestId,
                    'reseller_id' => $reseller->id,
                    'bank_name' => $bank->bank_name??'',
                    'account_name' => $accountName,
                    'tax_reference' => TaxMatrix::getCurrentMetadata()->id??null,
                    'tax_metadata'=> json_encode($taxCalculation),
                    'account_no' => $accountNo,
                    'status' => ResellerCommissionWithdrawal::WAITING_FOR_APPROVAL,
                    'amount' => $amount,
                    'transfer_fee' => $transfer_fee,
                    'tax_amount' => $taxCalculation['tax_total']??0,
                    'payout_amount' =>  $taxCalculation['nett_total'],
                    'total' =>  $taxCalculation['nett_total'] - $transfer_fee,
                    'transfer_method' => $transferMethod
                ];
                ResellerCommissionWithdrawal::create($data);
            } catch (\Exception $e) {
                // return $this->sendError("Sorry system can't process your withdrawal request, " . $e->getMessage(), 500);
                $e_insert = "Sorry system can't process your withdrawal request, " . $e->getMessage();
            }

            if (!empty($e_insert)) {
                throw new Exception($e_insert);
            }

            $notificationMesssage = 'Selamat pengajuan komisi Anda dengan nomor request ' . $requestId . '. telah dikirimkan ke nomor rekening terpilih!';
            $this->notifStore($user->reference_id, 'Pengajuan Penarikan Komisi Menunggu Persetujuan', 'reseller-commission-withdrawal', $notificationMesssage, $requestId, UserNotification::RESELLER_COMMISSION);

            $notificationMesssageInternal = 'Reseller atas nama ' . $reseller->name . ' mengajukan penarikan komisi dengan nomor ' . $requestId . ' memerlukan persetujuan Anda. Klik pesan ini untuk melihat detail.';
            $salesAll = MasterParameter::where('group_key', 'SALES_NOTIF')->where('key', 'RESELLER')->first();
            $this->notifStore($salesAll->value, 'Pengajuan Penarikan Komisi Baru', 'reseller-commission-withdrawal-internal', $notificationMesssageInternal, $requestId, UserNotification::RESELLER_COMMISSION);

            $socmed = new GetSocialsRepo();
            $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
            $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
            $twitter = $socmed->getSocialMediaParameters('TWITTER');
            $linkedin = $socmed->getSocialMediaParameters('LINKEDIN');

            $bank = ResellerBank::With("bank")
                ->Where("reseller_id", "=", $reseller->id)->first();

            $param = [
                'data' => [
                'type' => 'Reseller',
                'reseller_id' => $reseller->reseller_id,
                'name' => $reseller->name,
                'phone_no' => $reseller->phone_number,
                'email' => $reseller->email,
                'ktp' => $reseller->national_id,
                'npwp' => $reseller->npwp,
                'address' => $reseller->address,

                'request_id' => $requestId,
                'request_date' => date("d-m-Y"),
                'status' => ResellerCommissionWithdrawal::WAITING_FOR_APPROVAL,
                'bank_name' => $bank->bank->bank_name,
                'account_no' => $accountNo,
                'account_name' => $accountName,
                'transfer_method' => $transferMethod,
                'metadata' => $taxCalculation,
                'amount' => $amount,
                'transfer_fee' => $transfer_fee,
                'tax_amount' => $taxCalculation['tax_total']??0,
                'payout_amount' =>  $taxCalculation['nett_total'],
                'total' =>  $taxCalculation['nett_total'] - $transfer_fee,

                'facebook' => $facebook,
                'twitter' => $twitter,
                'instagram' => $instagram,
                'linkedin' => $linkedin,
                ]
            ];
            // Send Mail to reseller
            MailSender::dispatch($reseller->email, json_encode($param), 'mail_reseller_withdrawal');

            $internal = DB::table('user as u')
                ->leftJoin('sales as s', 's.sales_id', '=', 'u.reference_id')
                ->leftJoin('user_matrix as um', 'um.user_id', '=', 'u.user_id')
                ->leftJoin('business_unit as bu', 'bu.id', '=', 'um.business_unit_id')
                ->Select('u.email')
                ->Where('bu.name', 'Reseller')
                ->whereIn('um.tier_level',[4,5])
                ->get()
                ->toArray();

            $param['data']['type'] = 'Internal';
            #Send Mail to internal
            foreach ($internal as $admin) {
                MailSender::dispatch($admin->email, json_encode($param), 'mail_reseller_withdrawal');
            }
            $resp = [
                'status' => 'success',
                'data' => $data
            ];
            event(new RCEvent($this->user_id,$resp,'reseller_commission_withdrawal.created'));
            // return $this->sendSuccess("Commission withdrawal created. Request ID: " . $requestId);
           
        }
        catch(\Exception $e){
            $this->log['status'] = "failed";
            $this->log['description'] = [
                'message' => $e->getMessage(),
                'data' => $this->validatedData
            ];
            (new LoggerIntegration())->InsertLogger($this->log);
            event(new RCEvent($this->user_id,[
                'status' => 'failed',
                'message' => $e->getMessage()
            ],'reseller_commission_withdrawal.created'));

        } 

    }
}
