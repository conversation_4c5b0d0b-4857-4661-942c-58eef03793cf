<?php

namespace App\Jobs;

use App\Http\Resources\CreditLimitResource;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Helpers\CreditLimit;
use App\Models\Customer;
use App\Events\CreditLimitSynced;
use App\Events\GenericEvent;
use Illuminate\Support\Facades\Log;

class CreditLimitQueue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $cust_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($cust_id)
    {
        $this->onConnection('redis');
        $this->onQueue('rqueue');
        $this->cust_id = $cust_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $getLimit = new CreditLimit([
                [
                    'destination' => 'PLF',
                    'customer' => $this->cust_id
                ]
            ]);
            $st = $getLimit->syncQueue();
            // \Log::info($st);
            if ($st == "available") {
                event(new CreditLimitSynced($this->cust_id, new CreditLimitResource(Customer::find($this->cust_id))));
            } else {
                event(new CreditLimitSynced($this->cust_id, new CreditLimitResource(Customer::find($this->cust_id))));
            }
        } catch (\Exception $e) {
            event(new GenericEvent($this->cust_id, ['error' => $e->getMessage()], 'credit.limit'));

        }




    }
}