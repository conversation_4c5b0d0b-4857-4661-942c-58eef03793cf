<?php

namespace App\Jobs;

use App\Mail\MailActivate;
use App\Mail\MailUpdate;
use App\Mail\MailVerification;
use App\Mail\SendMail;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class MailSender implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private $email;
    private $data;
    private $type;
    private $token;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($email, $data, $type, $token = null)
    {
        $this->onConnection('redis');
        $this->onQueue('rqueue');
        $this->email = $email;
        $this->data = $data;
        $this->type = $type;
        $this->token = $token;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        switch ($this->type) {
            case 'mail_verification':
                Mail::to($this->email)->send(new MailVerification($this->data, $this->token));
                break;

            case 'mail_update':
                Mail::to($this->email)->send(new MailUpdate($this->data));
                break;

            case 'mail_activate':
                Mail::to($this->email)->send(new MailActivate($this->data));
                break;

            default:
                Mail::to($this->email)->send(new SendMail($this->data));
                break;
        }
    }
}