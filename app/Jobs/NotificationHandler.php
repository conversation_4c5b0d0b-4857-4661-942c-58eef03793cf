<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Bus\Batchable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Helpers\RestHelper;
use Illuminate\Support\Facades\Log;
use App\Models\QueueLog;
use Illuminate\Support\Facades\Queue;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Queue\Events\JobExceptionOccurred;

Queue::after(function (JobProcessed $event) {
    QueueLog::create([
        'uuid' => $event->job->uuid(),
        'job_id' => $event->job->getJobId(),
        'queue_name' => $event->job->getName(),
        'queue_resolve_name' => $event->job->resolveName(),
        'connection_name' => $event->connectionName,
        'isFailed' => $event->job->hasFailed() == true ? 1 : 0,
        'isReleased' => $event->job->isReleased() == true ? 1 : 0,
        'isDeleted' => $event->job->isDeleted() == true ? 1 : 0,
        'payload'=> json_encode($event->job->payload()),
        'exceptions' => null,
    ]);

    

});

Queue::failing(function (JobFailed $event) {
    QueueLog::create([
        'uuid' => $event->job->uuid(),
        'job_id' => $event->job->getJobId(),
        'queue_name' => $event->job->getName(),
        'queue_resolve_name' => $event->job->resolveName(),
        'connection_name' => $event->connectionName,
        'isFailed' => $event->job->hasFailed() == true ? 1 : 0,
        'isReleased' => $event->job->isReleased() == true ? 1 : 0,
        'isDeleted' => $event->job->isDeleted() == true ? 1 : 0,
        'payload'=> json_encode($event->job->payload()),
        'exceptions' => $event->exception,
    ]);
});


Queue::exceptionOccurred(function (JobExceptionOccurred $event) {
    QueueLog::create([
        'uuid' => $event->job->uuid(),
        'job_id' => $event->job->getJobId(),
        'queue_name' => $event->job->getName(),
        'queue_resolve_name' => $event->job->resolveName(),
        'connection_name' => $event->connectionName,
        'isFailed' => $event->job->hasFailed() == true ? 1 : 0,
        'isReleased' => $event->job->isReleased() == true ? 1 : 0,
        'isDeleted' => $event->job->isDeleted() == true ? 1 : 0,
        'payload'=> json_encode($event->job->payload()),
        'exceptions' => $event->exception,
    ]);
});

class NotificationHandler implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private $cust_id;
    private $title;
    private $type;
    private $content;
    private $key;
    private $category;
    private $channel;
    private $level;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->onConnection('redis');
        $this->onQueue('rqueue');
        $this->cust_id = $cust_id;
        $this->title = $title;
        $this->type = $type;
        $this->content = $content;
        $this->key = $key;
        $this->category = $category ?? null;
        $this->channel = $channel ?? null;
        $this->level = $level ?? null;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            RestHelper::notifStoreQueue($this->cust_id, $this->title, $this->type, $this->content, $this->key, $this->category, $this->channel, $this->level);
        }
        catch(\Exception $e){
            Log::channel('stderr')->info("Notification Failed cust_id : $this->cust_id");

        }

    }
}
