<?php

namespace App\Jobs;

use App\Models\User;
use App\Mail\MailForgot;
use App\Models\Customer;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Auth\Notifications\ResetPassword;

class QueuedPasswordResetJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;
    protected $customer_channel;
    protected $token;
    protected $data;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(User $user, $customer_channel, $token, $data)
    {
        //the user property passed to the constructor through the job dispatch method
        $this->onConnection('redis');
        $this->onQueue('rqueue');
        $this->user = $user;
        $this->customer_channel = $customer_channel;
        $this->token = $token;
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //This queued job sends
        //Illuminate\Auth\Notifications\ResetPassword notification
        //to the user by triggering the notification
        Mail::to($this->user->email)->send(new MailForgot($this->user, $this->customer_channel, $this->token, $this->data));
        // $this->user->notify(new ResetPassword($this->token));
    }
}