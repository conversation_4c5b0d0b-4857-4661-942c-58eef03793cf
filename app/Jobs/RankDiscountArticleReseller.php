<?php

namespace App\Jobs;

use App\Models\QueueLog;
use App\Models\PublicProduct;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Console\Commands\RankArticleReseller;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Queue\Events\JobExceptionOccurred;
use App\Helpers\Promotion\PromotionHelper as Promo;
use App\Models\ResellerArticleFilter;

Queue::after(function (JobProcessed $event) {
    QueueLog::create([
        'uuid' => $event->job->uuid(),
        'job_id' => $event->job->getJobId(),
        'queue_name' => $event->job->getName(),
        'queue_resolve_name' => $event->job->resolveName(),
        'connection_name' => $event->connectionName,
        'isFailed' => $event->job->hasFailed() == true ? 1 : 0,
        'isReleased' => $event->job->isReleased() == true ? 1 : 0,
        'isDeleted' => $event->job->isDeleted() == true ? 1 : 0,
        'payload'=> json_encode($event->job->payload()),
        'exceptions' => null,
    ]);

    

});

Queue::failing(function (JobFailed $event) {
    QueueLog::create([
        'uuid' => $event->job->uuid(),
        'job_id' => $event->job->getJobId(),
        'queue_name' => $event->job->getName(),
        'queue_resolve_name' => $event->job->resolveName(),
        'connection_name' => $event->connectionName,
        'isFailed' => $event->job->hasFailed() == true ? 1 : 0,
        'isReleased' => $event->job->isReleased() == true ? 1 : 0,
        'isDeleted' => $event->job->isDeleted() == true ? 1 : 0,
        'payload'=> json_encode($event->job->payload()),
        'exceptions' => $event->exception,
    ]);
});


Queue::exceptionOccurred(function (JobExceptionOccurred $event) {
    QueueLog::create([
        'uuid' => $event->job->uuid(),
        'job_id' => $event->job->getJobId(),
        'queue_name' => $event->job->getName(),
        'queue_resolve_name' => $event->job->resolveName(),
        'connection_name' => $event->connectionName,
        'isFailed' => $event->job->hasFailed() == true ? 1 : 0,
        'isReleased' => $event->job->isReleased() == true ? 1 : 0,
        'isDeleted' => $event->job->isDeleted() == true ? 1 : 0,
        'payload'=> json_encode($event->job->payload()),
        'exceptions' => $event->exception,
    ]);
});

class RankDiscountArticleReseller implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $articles;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $articles)
    {
        $this->onConnection('redis');
        $this->onQueue('rqueue');
        $this->articles = $articles;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $ad = [];
            foreach ($this->articles as $d) {
                $a = PublicProduct::where('article',$d['article'])->first();
                if ($a != null) {
                    $discount_amount = $a->price == null ? 0 : Promo::applyArticlePromotion($a->article,$a->price->amount)['disc'];
                    $discount_percentage = $a->price == null ? 0 : ($discount_amount/$a->price->amount)*100;
                    $ra = ResellerArticleFilter::where('article',$a->article)->first();
                    if ($ra != null) {
                        $ra->discount_percentage = $discount_percentage;
                        $ra->save();
                    } else {
                        ResellerArticleFilter::create([
                            'article' => $a->article,
                            'discount_percentage' => $discount_percentage
                        ]);
                    }
                }

            }

            Log::info('Rank article discount success');

        } catch(\Exception $e){
            $error =  [
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ];
            Log::channel('stderr')->info("Rank discount article reseller error : ",$error);
        }
    }
}
