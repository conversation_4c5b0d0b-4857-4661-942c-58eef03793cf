<?php

namespace App\Jobs;

use App\Models\QueueLog;
use App\Models\PublicProduct;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Queue;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Console\Commands\RankArticleReseller;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Queue\Events\JobExceptionOccurred;
use App\Helpers\Promotion\PromotionHelper as Promo;
use App\Models\ResellerArticleStock;

Queue::after(function (JobProcessed $event) {
    QueueLog::create([
        'uuid' => $event->job->uuid(),
        'job_id' => $event->job->getJobId(),
        'queue_name' => $event->job->getName(),
        'queue_resolve_name' => $event->job->resolveName(),
        'connection_name' => $event->connectionName,
        'isFailed' => $event->job->hasFailed() == true ? 1 : 0,
        'isReleased' => $event->job->isReleased() == true ? 1 : 0,
        'isDeleted' => $event->job->isDeleted() == true ? 1 : 0,
        'payload'=> json_encode($event->job->payload()),
        'exceptions' => null,
    ]);

    

});

Queue::failing(function (JobFailed $event) {
    QueueLog::create([
        'uuid' => $event->job->uuid(),
        'job_id' => $event->job->getJobId(),
        'queue_name' => $event->job->getName(),
        'queue_resolve_name' => $event->job->resolveName(),
        'connection_name' => $event->connectionName,
        'isFailed' => $event->job->hasFailed() == true ? 1 : 0,
        'isReleased' => $event->job->isReleased() == true ? 1 : 0,
        'isDeleted' => $event->job->isDeleted() == true ? 1 : 0,
        'payload'=> json_encode($event->job->payload()),
        'exceptions' => $event->exception,
    ]);
});


Queue::exceptionOccurred(function (JobExceptionOccurred $event) {
    QueueLog::create([
        'uuid' => $event->job->uuid(),
        'job_id' => $event->job->getJobId(),
        'queue_name' => $event->job->getName(),
        'queue_resolve_name' => $event->job->resolveName(),
        'connection_name' => $event->connectionName,
        'isFailed' => $event->job->hasFailed() == true ? 1 : 0,
        'isReleased' => $event->job->isReleased() == true ? 1 : 0,
        'isDeleted' => $event->job->isDeleted() == true ? 1 : 0,
        'payload'=> json_encode($event->job->payload()),
        'exceptions' => $event->exception,
    ]);
});

class ResellerArticleSorter implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $articles, $site;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($site = null)
    {
        $this->site = $site;
        $this->onConnection('redis');
        $this->onQueue('rqueue');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $name_cache = $this->site == null ? 'reseller_article_list' : "reseller_article_list_".$this->site;
            if(Cache::has('article_lock')) return;
            if(!Cache::has($name_cache) || abs(Cache::get($name_cache.'_last_update') - date('ymdhis')) > 21600){
                Cache::set('article_lock', 1, now()->addMinutes(2));
                $article_list = PublicProduct::with('mainImageGeneric')->select('article','sku_code_c')->where('is_reseller', 1)->groupBy('sku_code_c')->get()->toArray();
                $get_stock = fn($sku, $site) => $site != null ?  ResellerArticleStock::where('article', 'LIKE', "%$sku%")->where('location_code', $site)->sum('available_stock') :  ResellerArticleStock::where('article', 'LIKE', "%$sku%")->sum('available_stock');
                $stock_mapper = array_map(fn($i) => ['article' => $i['article'], 'stock' => (int)$get_stock($i['sku_code_c'], $this->site), 'image' => $i['main_image_generic']['file_path']??null],$article_list);
                $collection = collect($stock_mapper)->sortBy([
                    ['stock', 'desc'],
                    ['image', 'desc'],
                ]);
                // $hydrate = $collection->map(function (array $lines) {
                //     return PublicProduct::where('article',$lines['article'])->first();
                // });
    
                $whereIn =  $collection->pluck('article')->values()->all();
                // $hydrate = PublicProduct::whereIn('article', $whereIn)
                // ->orderByRaw("field(article,".implode(',',$whereIn).")")->get();
                Cache::forever($name_cache, $whereIn);
                Cache::forever($name_cache."_last_update", date('ymdhis'));
                Log::info("Reseller article sorter success Site: {$this->site}");
                cache::forget('article_lock');
            }
          

        } catch(\Exception $e){
            $error =  [
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'message' => $e->getMessage(),
            ];
            cache::forget('article_lock');
            Log::channel('stderr')->info("Reseller article sorter error Site: {$this->site} : ",$error);
        }
    }
}
