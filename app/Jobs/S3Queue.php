<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\LoggerIntegration;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class S3Queue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $module, $url, $filename, $ext, $log;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($module, $url, $filename, $ext)
    {
        $this->onConnection('redis');
        $this->onQueue('rqueue');
        $this->url = $url;
        $this->module = $module;
        $this->filename = $filename;
        $this->ext = $ext;
        $this->log = [
            'reference_no'=> $filename,
            'module' => 'Module Custom Fadil',
            'name' => 'Airtable to S3',
            'type'=> 'Inbound',
            'status' => 'queueing',
            'description' => $url
        ];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // $originalName = $file->getClientOriginalName();
        $contents = file_get_contents($this->url);
        $filename = $this->filename;
        $file_path = '/'.$this->module.'/'.$filename;
        Storage::disk('s3')->put(substr($file_path,1), $contents);
        Log::info($filename);
        $this->log['status'] = 'success';
        (new LoggerIntegration())->InsertLogger($this->log);
    }

    public function failed(\Exception $exception): void
    {
        $this->log['status'] = 'failed';
        Log::info('failed');
        (new LoggerIntegration())->InsertLogger($this->log);

    }
}
