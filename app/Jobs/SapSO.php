<?php

namespace App\Jobs;

use App\Http\Resources\CreditLimitResource;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Helpers\CreditLimit;
use App\Models\Customer;
use App\Events\CreditLimitSynced;

class SapSO implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private $tipe, $param, $cust_id;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($tipe, $param, $cust_id)
    {
        $this->tipe = $tipe;
        $this->param = $param;
        $this->cust_id = $cust_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        if($this->tipe === 'get_credit_limit'){
            $getLimit = new CreditLimit([
                [
                    'destination' => 'PLF',
                    'customer' => $this->cust_id
                ]
            ]);
            $getLimit->sync();
            event(new CreditLimitSynced($this->cust_id, new CreditLimitResource(Customer::find($this->cust_id))));

        }

    }
}
