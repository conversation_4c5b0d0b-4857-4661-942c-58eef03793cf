<?php

namespace App\Jobs;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\CartDetail;
use App\Models\ProductSku;
use App\Helpers\RestHelper;
use App\Models\OrderCustom;
use App\Events\GenericEvent;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Carbon;
use App\Repositories\GetStokRepo;
use Illuminate\Support\Facades\DB;
use App\Services\LoggerIntegration;
use App\Models\OrderCustomAttachment;
use Illuminate\Queue\SerializesModels;

use Illuminate\Queue\InteractsWithQueue;
use App\Repositories\CreateSimulateSORepo;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\{Log, Cache};


class SimulateSOQueue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, RestHelper;

    private $errordata = [];
    private $cust_id;
    private $order_no;
    private $status;
    private $log;
    private $isCustom;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($cust_id, $order_no, $status = "s", $isCustom = false)
    {
        $this->onConnection('redis');
        $this->onQueue('rqueue');
        $this->cust_id = $cust_id;
        $this->order_no = $order_no;
        $this->status = $status;
        $this->isCustom = $isCustom;
        $this->log = [
            'reference_no' => '',
            'module' => 'SAP Stock',
            'name' => 'SO Adjustment Stock',
            'type' => 'Inbound',
            'status' => '',
            'description' => ''
        ];

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $input = Order::where('order_no', $this->order_no)->first();
            if ($this->status == "s") {
                if ($this->isCustom == true) {
                    $this->createSimulateSO($this->status, $this->order_no, $this->cust_id, 0, $this->isCustom);
                } else {
                    $this->createSimulateSO($this->status, $this->order_no, $this->cust_id);
                }
                // event(new GenericEvent($this->cust_id, [
                //     'total_tax' => $input['total_tax'],
                //     'total' => $input['total'],
                //     'total_discount' => $input['total_discount'],
                //     'total_nett' => $input['total_nett'],
                //     'nett_before_tax' => $input['nett_before_tax']
                // ], 'simulateSO.new'));
                Log::channel('stderr')->info($this->cust_id);
                Log::channel('stderr')->info([
                    'data' => [
                        'total_tax' => $input['total_tax'],
                        'total' => $input['total'],
                        'total_discount' => $input['total_discount'],
                        'total_nett' => $input['total_nett'],
                        'nett_before_tax' => $input['nett_before_tax']
                    ]
                ]);
            } else {
                $article_map = array_map(function ($b) {
                    return [
                        'source' => 'CAREOM',
                        'destination' => 'STK',
                        'article' => $b,
                    ];
                }, OrderItem::where('order_no', $this->order_no)->pluck('article_id')->toArray() ?? []);

                $rs = $this->getStock($article_map);
                if ($rs == true) {
                    $this->notifStore($this->cust_id, 'Revisi Order', 'order', "Terdapat Revisi order untuk {$this->order_no} silahkan lakukan konfirmasi ulang", $this->order_no, 'Transaksi', $input->distribution_channel, 'info');
                }
            }

        } catch (\Exception $e) {
            Log::channel('stderr')->info($this->cust_id);
            Log::channel('stderr')->info($e->getMessage());
            Log::channel('stderr')->info($e->getFile());
            Log::channel('stderr')->info($e->getLine());
            event(new GenericEvent($this->cust_id, ['success' => false, 'message' => 'SAP Down'], 'simulateSO.new'));
        }
    }

    public function getStock($article)
    {


        $data = [
            "source" => "CAREOM",
            "destination" => "STK",
            "detail" => $article
        ];

        $getStokRepo = new GetStokRepo();

        try {
            $resp = $getStokRepo->getStock($data, $this->cust_id);
            $new_resp = $getStokRepo->getData($resp);
            $this->log['status'] = "success";
            $this->log['description'] = json_encode($new_resp['data']);
            foreach ($new_resp['data'] as $it) {
                if ((int) $it['qty'] < 1) {
                    $cr = OrderItem::where('order_no', $this->order_no)->where('article_id', $it['article'])->first();
                    if ($cr) {
                        $rq = new \stdClass;
                        $rq->article = $it['article'];
                        $rq->qty = $cr->qty;
                        $rq->is_custom = 0;
                        $this->cartStore($this->cust_id, $rq);
                        // $dt = OrderItem::where('order_no', $this->order_no)->where('article_id', $it['article'])->delete();
                    }
                } else {
                    $dt = OrderItem::where('order_no', $this->order_no)->where('article_id', $it['article'])->first();
                    if ($dt) {
                        if ($dt->qty > (int) $it['qty']) {
                            $dt->issued_qty = (int) $it['qty'] ?? $dt->qty;
                            $dt->sub_total = (int) $it['qty'] ?? $dt->qty * $dt->price;
                            $dt->save();
                        }
                    }
                }

                $this->log['reference_no'] .= $it['article'] . ", ";
                ProductSku::where('sku_id', $it['article'])->update(['stock' => (int) $it['qty']]);
                if ($it['qty'] == 0) {
                    CartDetail::where('article', $it['article'])->update('is_available', 0);
                }
                Cache::tags('article_stock')->put($it['article'], ['article' => $it['article'], 'qty' => (int) $it['qty'], 'moq' => $it['moq']]);
            }
            (new LoggerIntegration())->InsertLogger($this->log);

            return true;

        } catch (\Exception $e) {

            $this->log['status'] = "failed";
            $this->log['description'] = $e->getMessage();
            (new LoggerIntegration())->InsertLogger($this->log);
            Log::info("Batching Article stock failed");
            return false;
        }

    }


    public function createSimulateSO($type, $order_no, $customer_id, $customer_shipment_id = 0, $isCustom = false)
    {
        // $orders = $orderItems;
        // if($orderItems==""){
        $orders = OrderItem::leftJoin('article', 'order_detail.article_id', '=', 'article.article')
            ->select('order_detail.*', 'article.sku_code_c', 'article.product_name_c')
            ->where('order_detail.order_no', $order_no)->orderBy('order_detail.order_detail_id', 'desc')
            ->get();
        $harga_custom = 0;

        if (count($orders) <= 0) {
            $orders = OrderCustom::leftJoin('article', 'order_custom.article_id', '=', 'article.article')
                ->select('order_custom.*', 'article.sku_code_c', 'article.product_name_c')
                ->where('order_custom.reference_id', $order_no)->orderBy('order_custom.id', 'desc');
            $attachments = OrderCustomAttachment::whereIn('order_custom_id', $orders->pluck('attachment_group_id')->toArray())->sum('custom_price');
            $groupedOrders = $orders->groupBy('attachment_group_id');

            // 2. Fetch all relevant attachments
            $attachments = OrderCustomAttachment::whereIn(
                'order_custom_id',
                $groupedOrders->keys()->toArray()
            )->get()->keyBy('order_custom_id');

            // 3. Calculate the total custom price
            $harga_custom = 0;

            foreach ($groupedOrders as $groupId => $groupOrders) {
                $qtySum = $groupOrders->sum('qty');

                $customPrice = optional($attachments->get($groupId))->custom_price ?? 0;

                $harga_custom += $customPrice * $qtySum;
            }
            $orders = $orders->get();

        }

        $sales = DB::table('order_header as oh')
            ->join('customer_sales as cs', 'cs.customer_id', '=', 'oh.customer_id')
            ->join('sales', 'sales.sales_id', '=', 'cs.sales_id')
            ->select('sales.sap_username')
            ->where('oh.order_no', $order_no)
            ->first();
        if ($sales) {
            if ($sales->sap_username !== null) {
                $sap_username = env('ARTICLE_STOCK_SAPUSER');
            } elseif ($sales->sap_username == null) {
                $sap_username = env('ARTICLE_STOCK_SAPUSER');
            }
        } else {
            $sap_username = env('ARTICLE_STOCK_SAPUSER');
        }


        $custId = $customer_id;
        $client = new CreateSimulateSORepo();
        //Log::channel('stderr')->info('[ITEMS REQUEST KE SAP]',$item);
        $soDetail = [];

        $date = Carbon::now();
        $yymmdd = $date->format('Ymd');
        $no = 1;
        foreach ($orders as $item) {
            $so = [
                'destination' => 'SLO',
                //'flag' => $type,
                'flag' => $type,
                'itmnumber' => sprintf("%02d", $no),
                'article' => $item->article_id,
                'targetqty' => STRVAL($item->issued_qty && $item->issued_qty > 0 ? $item->issued_qty : $item->qty),
                'uom' => 'PC',

            ];
            array_push($soDetail, $so);
            $no = $no + 1;
        }
        // dd($customer_shipment_id ? str_pad($customer_shipment_id, 10, '0', STR_PAD_LEFT) : strval($custId));die();
        $soDetailSet = $soDetail;
        $soPartnerSet = ($type == "i" && $custId != $customer_shipment_id) ? [
            [
                'destination' => 'SLO',
                'partnrole' => 'SH',
                'partnnumb' => strval($custId)
            ],
            [
                'destination' => 'SLO',
                'partnrole' => 'SH',
                'partnnumb' => strval($customer_shipment_id)
            ],
        ] :
            [
                [
                    'destination' => 'SLO',
                    'partnrole' => 'SH',
                    'partnnumb' => strval($custId)
                ]
            ];
        $soReturnSet = [];

        $data = [
            "source" => "CAREOM",
            "destination" => "SLO",
            "flag" => $type,
            "doctype" => "ZESD",
            "salesorg" => "1000",
            "distrchan" => "W1",
            "division" => "00",
            "reqdate" => $yymmdd,
            "externalno" => $order_no,
            "salesorder" => "",
            "soDetailSet" => $soDetailSet,
            "soPartnerSet" => $soPartnerSet,
            "soReturnSet" => $soReturnSet,
            "sap_username" => $sap_username,
        ];

        $result = [];
        //    try{
        $response = $client->createSO($data);
        //    }
        //    catch(Exception $e){
        //     $data['soPartnerSet'][0]['partnnumb'] = strval($custId);
        //     $response = $client->createSO($data);
        //    }
        $result = $client->getData($response);
        if ($result) {
            if ($type == "i") {
                // $res = $client->getData($response);
                $this->updateTableOrderCreateSO($result, $order_no);
            }
            //simulate
            if ($type == "s") {
                // $res = $client->getDataWhole($response);
                $this->updateTableOrder($result, $order_no, $isCustom, $harga_custom);
            }
        }
        // dd($result);
        // try {
        //     $response = $client->createSO($data);
        //  //   $statusCode = $response->getStatusCode();
        //     $result = $client->getData($response);

        //     // Log::channel('stderr')->info('[RESPONSE SAP]',$result);
        //     Log::channel('stderr')->info($result);

        //     if($result){
        //         if($type=="i"){
        //             $this->updateTableOrderCreateSO($result, $order_no);
        //         }
        //         //simulate
        //         if($type=="s"){
        //             $this->updateTableOrder($result, $order_no); 
        //         }
        //     }
        // } catch (\Exception $e) {
        //         Log::error($e->getMessage());
        //         // return error response
        //     // return $data;

        // }

        return $result;

    }

    public function updateTableOrder($input, $order_no, $isCustom = false, $harga_custom = 0)
    {
        // dd($input);die();
        //$order = OrderHeader::find($id);
        $order = Order::where('order_no', $order_no)->first();

        if (!$order) {
            // return response()->json(['error' => 'Order not found'], 404);
            Log::info("Order not found when update simulate SO " . $order_no);
        }


        if (isset($input['tax'])) {
            $order->total_tax = $input['tax'] != null || $input['tax'] != "" ? $input['tax'] : $order->getRawOriginal('total_tax');
            $order->total_discount = $input['discount'] != null || $input['discount'] != "" ? $input['discount'] : $order->getRawOriginal('total_discount');
            if ($isCustom == true) {
                $order->total = $input['gross'] != null || $input['gross'] != "" ? ((int) $input['gross'] + (int) $harga_custom) : $order->getRawOriginal('total');
                $order->total_nett = $input['net'] != null || $input['net'] != "" ? ((int) $input['net'] + (int) $harga_custom) : $order->getRawOriginal('total_nett');
            } else {
                $order->total = $input['gross'] != null || $input['gross'] != "" ? $input['gross'] : $order->getRawOriginal('total');
                $order->total_nett = $input['net'] != null || $input['net'] != "" ? $input['net'] : $order->getRawOriginal('total_nett');
            }
            $order->nett_before_tax = $input['netbtax'] != null || $input['netbtax'] != "" ? $input['netbtax'] : $order->getRawOriginal('nett_before_tax');
            $order->save();
        }


        return response()->json(['message' => 'Order updated successfully']);
    }

    public function updateTableOrderCreateSO($data, $order_no)
    {
        //$order = OrderHeader::find($id);
        // $jsonData = json_decode($data, true);
        // Log::info("Order not found when update Create SO ". $data);
        Log::info('DATA UPDATE SO:');
        Log::info($data);
        $this->errordata = $data;
        $vbeln = ltrim($data['vbeln'], '0');
        $order = Order::where('order_no', $order_no)->first();
        if (!$order) {
            // return response()->json(['error' => 'Order not found'], 404);
            Log::info("Order not found when update Create SO " . $data);
        }

        if (array_key_exists('discount', $data) && array_key_exists('gross', $data)) {
            $order->total = str_replace(".", "", $data['gross']);
            $order->total_discount = str_replace(".", "", $data['discount']);
        }

        if (array_key_exists('detail', $data)) {
            if (!empty($data['detail'])) {
                foreach ($data['detail'] as $i) {
                    if ($i['qty'] < 1) {
                        OrderItem::where(['order_no' => $order_no, 'article_id' => $i['article']])->delete();
                    } else {
                        $dt = OrderItem::where(['order_no' => $order_no, 'article_id' => $i['article']])->first();
                        if ($dt) {
                            $dt->issued_qty = $i['qty'] ?? $dt->qty;
                            $dt->sub_total = $i['qty'] ?? $dt->qty * $dt->price;
                            $dt->save();
                        }
                    }

                }
            }
        }


        $order->sales_order_no = $vbeln;
        // $order->order_status = self::OrderStatusOnHold;
        $order->save();

        //return response()->json(['message' => 'Order updated successfully']);
    }
}