<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Events\GenericEvent;
use App\Models\Order;
use App\Models\OrderItem;
use App\Repositories\CreateSimulateSORepo;
use App\Events\simulateSONew;
use App\http\Controllers\TransactionController;
use App\simulateSONew as DevSimulateSONew;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Carbon;


class SimulateSOQueueDebug implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $errordata = [];
    private $cust_id;
    private $order_no, $disc_value;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($cust_id, $order_no, $disc_value)
    {
        $this->onConnection('redis');
        $this->onQueue('rqueue');
        $this->cust_id = (string)$cust_id;
        $this->order_no = (string)$order_no;
        $this->disc_value = (string)$disc_value;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $input = Order::where('order_no', $this->order_no)->first();
            event(new GenericEvent($this->cust_id, [
                'total_tax' => $input['total_tax'],
                'total' => $input['total'],
                'total_discount' => $this->disc_value,
                'total_nett' => $input['total_nett'],
                'nett_before_tax' => $input['nett_before_tax']
                ], 'simulateSO.new'));

        } 
        catch (\Exception $e) {
            event(new GenericEvent($this->cust_id, ['success' => false, 'message' => 'SAP Down'], 'simulateSO.new'));
        }
    }

}
