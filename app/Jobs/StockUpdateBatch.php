<?php

namespace App\Jobs;

use App\Models\QueueLog;
use App\Services\LoggerIntegration;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Events\GenericEvent;
use App\Models\Product;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Repositories\GetStokRepo;
use Illuminate\Support\Facades\Queue;

Queue::failing(function (JobFailed $event) {
    QueueLog::create([
        'uuid' => $event->job->uuid(),
        'job_id' => $event->job->getJobId(),
        'queue_name' => $event->job->getName(),
        'queue_resolve_name' => $event->job->resolveName(),
        'connection_name' => $event->connectionName,
        'isFailed' => $event->job->hasFailed() == true ? 1 : 0,
        'isReleased' => $event->job->isReleased() == true ? 1 : 0,
        'isDeleted' => $event->job->isDeleted() == true ? 1 : 0,
        'payload'=> json_encode($event->job->payload()),
        'exceptions' => $event->exception->getMessage()??'Error Stock Caching',
    ]);
});
class StockUpdateBatch implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $errordata = [];
    private $article;
    private $user_id;
    private $log;
    
    private $customer_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($article, $customer_id = null)
    {
        $this->onConnection('redis');
        $this->onQueue('productqueue');
        $this->article = $article;
        $this->customer_id = $customer_id;
        $this->log = [
            'reference_no'=> '',
            'module' => 'SAP Stock',
            'name' => 'Article Batch Caching',
            'type'=> 'Inbound',
            'status' => '',
            'description' => ''
        ];

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->getStock();

    }

    public function getStock()
    {
       
   
        $data = [
                "source"=> "CAREOM",
                "destination" => "STK",
                "detail" => $this->article
            ];

        $getStokRepo = new GetStokRepo();
        
        try{
            if(!Cache::has('SAP-Access-Cache')){

            $resp = $getStokRepo->getStock($data, $this->customer_id);
            $new_resp = $getStokRepo->getData($resp);
            $this->log['status'] = "success";
            $this->log['description'] = json_encode($new_resp['data']);
            event(new GenericEvent($this->customer_id, count($new_resp['data']) > 0 ? $new_resp['data'] : array_map(fn($i)=> [
                "article"=> $i['article'],
                "qty"=> "0",
                "moq" => "0"
            ],$this->article), 'stock.update.batch'));
                foreach($new_resp['data'] as $it){
                $this->log['reference_no'] .= $it['article'].", ";
                Log::info("caching article : " . $it['article']);
                Cache::tags('article_stock')->put($it['article'],   ['article'=> $it['article'], 'qty' => (int)$it['qty'], 'moq' => $it['moq']]);
            }
        }
            // (new LoggerIntegration())->InsertLogger($this->log);

                 
        } catch (\Exception $e) {
            event(new GenericEvent($this->customer_id, array_map(fn($i)=> [
                "article"=> $i['article'],
                "qty"=> "0",
                "moq" => "0"
            ],$this->article), 'stock.update.batch'));
            Cache::set('SAP-Access-Cache', "not-available", now()->addMinutes(10));
            $this->log['status'] = "failed";
            $this->log['description'] = $e->getMessage();
            (new LoggerIntegration())->InsertLogger($this->log);
            Log::info("Batching Article stock failed");
        }
    
    }

}
