<?php

namespace App\Jobs;

use App\Models\QueueLog;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\Events\JobFailed;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Events\GenericEvent;
use App\Models\Product;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Repositories\GetStokRepo;
use App\Services\LoggerIntegration;
use Illuminate\Support\Facades\Queue;

Queue::failing(function (JobFailed $event) {
    QueueLog::create([
        'uuid' => $event->job->uuid(),
        'job_id' => $event->job->getJobId(),
        'queue_name' => $event->job->getName(),
        'queue_resolve_name' => $event->job->resolveName(),
        'connection_name' => $event->connectionName,
        'isFailed' => $event->job->hasFailed() == true ? 1 : 0,
        'isReleased' => $event->job->isReleased() == true ? 1 : 0,
        'isDeleted' => $event->job->isDeleted() == true ? 1 : 0,
        'payload'=> json_encode($event->job->payload()),
        'exceptions' => $event->exception->getMessage()??'Error Stock Caching',
    ]);
});


class StockUpdateQueue implements ShouldQueue, ShouldBeUnique
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $errordata = [];
    private $article = [];
    public $uniqueFor = 30;

    private $customer_id;
    private $log;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($article = [], $customer_id)
    {
        $this->onConnection('redis');
        $this->onQueue('productqueue');
        $this->article = $article;
        $this->customer_id = $customer_id;
        $this->log = [
            'reference_no'=> '',
            'module' => 'SAP Stock',
            'name' => '',
            'type'=> 'Inbound',
            'status' => '',
            'description' => ''
        ];

    }

    public function uniqueId(): string
    {
        return hash('sha256', implode(',',$this->article) . $this->customer_id??'-');
    }

    // public function uniqueId(): string
    // {
    //     return $this->article;
    // }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $fetch = $this->getStock();
        // Log::info('stock update queue fetch',$fetch);
        foreach($fetch as $data){
            Cache::tags('article_stock')->put($data['article'], $data);
            if($this->customer_id != null){
                event(new GenericEvent($this->customer_id, $data, 'stock.update'));
            }
        }
     
    }

    public function getDefaultResponse(){
        $cache = Cache::tags('article_stock');
        $rs = [];
        foreach ($this->article as $dat){
            $rs[] = $cache->has($dat) ? $cache->get($dat) :  [
                "article"=> $dat,
                "qty"=> "0",
                "moq" => "1"
            ];
        }
        return $rs;
    }

    public function getStock()
    {
        $this->log['reference_no'] = implode(',',$this->article);
        $this->log['name'] = "Article caching";

        $article_map = array_map(function ($b) {
            return [
              'source' => 'CAREOM',
              'destination' => 'STK',
              'article' => $b
            ];
        }, $this->article);
   

        $data = [
                "source"=> "CAREOM",
                "destination" => "STK",
                "detail" => $article_map
            ];

        $getStokRepo = new GetStokRepo();
        // $response = $getStokRepo->getStock($data, $this->customer_id);
        // $qty = 0;
        // $moq = 0;
        
        try{
            if(!Cache::has('SAP-Access-Cache')){
                $resp = $getStokRepo->getStock($data, $this->customer_id);
                $new_resp = $getStokRepo->getData($resp);

                // $qty = $getStokRepo->getQty($response);
                // $moq = $getStokRepo->getMoq($response);
                $this->log['status'] = "success";
                $this->log['description'] = json_encode($new_resp['data']);
                // (new LoggerIntegration())->InsertLogger($this->log);

                foreach($new_resp['data'] as $it){
                    $this->log['reference_no'] .= $it['article'].", ";
                    Log::info("caching article : " . $it['article']);
                    Cache::tags('article_stock')->put($it['article'],   ['article'=> $it['article'], 'qty' => (int)$it['qty']??0, 'moq' => $it['moq']??'1']);
                }
                return count($new_resp['data']) > 0 ? 
                array_map(fn($i)=>[
                    "article" => $i['article'],
                    "qty" => (int)$i['qty']??0,
                    "moq" => $i['moq']??'1'
                ],$new_resp['data'])
                : $this->getDefaultResponse();
            }
            else {
               return $this->getDefaultResponse();
            }

        } catch (\Exception $e) {

            $this->log['status'] = "failed";
            Cache::set('SAP-Access-Cache', "not-available", now()->addMinutes(10));
            $this->log['description'] = $e->getMessage();
            (new LoggerIntegration())->InsertLogger($this->log);
            Log::info("Batching Article stock failed");
            return $this->getDefaultResponse();
        }
    
    }

}
