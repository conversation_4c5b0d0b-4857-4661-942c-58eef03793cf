<?php

namespace App\Jobs;

use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Events\GenericEvent;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class StockUploadQueue implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $request, $csv, $bn;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request, $csv, $batch_number)
    {
        $this->onConnection('redis');
        $this->onQueue('rqueue');
        $this->request = $request;
        $this->csv = $csv;
        $this->bn = $batch_number;

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            
            $cus_id = $this->request['customer_id'];
            $email = $this->request['email'];
            $tokoId = $this->request['toko_id'];
            $date = date('Y-m-d H:i:s');

            foreach($this->csv as $item){
                Log::info($item[0] ?? 'SKU Undefined');
                Log::info($item[1] ?? 'Stok Undefined');
                $cust_stock = DB::table('customer_stock')
                ->where('customer_id','=',$cus_id)
                ->where('article_id','=',$item[0])
                ->first();

            if ($cust_stock == null) {
                $article = DB::table('article')
                    ->where('article','=',$item[0])
                    ->first();

                if ($article == null) {
                    DB::rollBack();
                    $this->fail(new \Exception('Article not found for SKU : ' . $item[0]));
                }

                DB::table('customer_stock')
                    ->where('customer_id','=',$cus_id)
                    ->insert([
                        'customer_id' => $cus_id,
                        'sku_code' => $article->sku_code_c,
                        'article_id' => $item[0],
                        'product_name' => $article->product_name_c,
                        'product_variant' => $article->product_variant_c,
                        'product_size' => $article->product_size_c,
                        'qty' => $item[1],
                        'created_date' => $date,
                        'created_by' => $email
                    ]);

                Log::info('success insert new article');
            }
            Log::info('update stok :');
            Log::info($item);
            DB::table('customer_stock')
                ->where('customer_id','=',$cus_id)
                ->where('article_id','=',$item[0])
                ->update([
                    'customer_shipment_id' => $tokoId,
                    'qty' => $item[1],
                    'modified_date' => $date,
                    'modified_by' => $email
                ]);

            DB::commit();
            }
            event(new GenericEvent($cus_id, ['error' => false, 'message' => ((int)$this->bn + count($this->csv)).' stock processed', 'batch_number' => $this->bn], 'stock.upload'));

        }
        catch(\Exception $e){
            DB::rollBack();
            event(new GenericEvent($cus_id, ['error' => true, 'message' => $e->getMessage(), 'batch_number' => $this->bn], 'stock.upload'));

        }

    }

    
}

