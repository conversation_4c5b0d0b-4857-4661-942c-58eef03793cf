<?php

namespace App\Jobs;

use App\Helpers\RestHelper;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class SyncStockJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $article;
    protected $sku;
    public function __construct($article, $sku = null)
    {
        $this->onConnection('redis');
        $this->onQueue('rqueue');
        $this->article = $article;
        $this->sku = $sku;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        RestHelper::syncStock($this->article, null, $this->sku);
    }
}