<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class MailActivate extends Mailable
{
    use Queueable, SerializesModels;
    public $data;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($data)
    {
        $this->data = json_decode($data, true);
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(env('MAIL_FROM_ADDRESS'), env('MAIL_FROM_NAME'))
            ->subject('<PERSON><PERSON><PERSON> B2B Anda Sudah Aktif')
            ->view('email_activate')
            ->with(
                [
                    'name' => $this->data['name'],
                    'facebook' => $this->data['facebook'],
                    'twitter' => $this->data['twitter'],
                    'instagram' => $this->data['instagram'],
                    'tiktok' => $this->data['tiktok'],
                    'youtube' => $this->data['youtube']
                ]
            );
    }
}
