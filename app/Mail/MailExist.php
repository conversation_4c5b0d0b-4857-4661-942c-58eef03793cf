<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class MailExist extends Mailable
{
    use Queueable, SerializesModels;
    public $name;
    public $custid;
    public $email;
    public $token;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($customers)
    {
        //
        $this->name = $customers->owner_name;
        $this->email = $customers->email;
        $this->custid = $customers->customer_id;
        $this->token = $customers->token;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(env('MAIL_FROM_ADDRESS'), env('MAIL_FROM_NAME'))
            ->subject('AKTIVASI AKUN')
            ->view('email_exist')
            ->with(
                [
                    'custid' => $this->custid,
                    'email' => $this->email,
                    'name' => $this->name,
                    'token' => $this->token,
                ]
            );
    }
}
