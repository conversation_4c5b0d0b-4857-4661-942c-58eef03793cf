<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class MailForgot extends Mailable
{
    use Queueable, SerializesModels;
    public $name;
    public $reference_id;
    public $email;
    public $reference_object;
    public $customer_channel;
    public $token;
    public $data;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($user, $customer_channel, $token, $data)
    {
        $this->name = $user->name;
        $this->email = $user->email;
        $this->reference_id = $user->reference_id;
        $this->reference_object = $user->reference_object;
        $this->customer_channel = $customer_channel;
        $this->token = $token;
        $this->data = $data;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(env('MAIL_FROM_ADDRESS'), $this->customer_channel === 'B2B' ? 'Eiger-B2B' : 'Eiger-Wholesales')
            ->subject('RESET KATA SANDI')
            ->view('forgot_password')
            ->with(
                [
                    'reference_id' => $this->reference_id,
                    'email' => $this->email,
                    'name' => $this->name,
                    'reference_object' => $this->reference_object,
                    'distribution_channel' => $this->customer_channel,
                    'token' => $this->token,
                    'facebook' => $this->data['facebook'],
                    'twitter' => $this->data['twitter'],
                    'instagram' => $this->data['instagram'],
                    'tiktok' => $this->data['tiktok'],
                    'youtube' => $this->data['youtube']
                ]
            );
    }
}