<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class MailRegistration extends Mailable
{
    use Queueable, SerializesModels;
    public $email;
    public $name;
    public $phone;
    public $npwp;
    public $address;
    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        //
        $this->email = $request->email;
        $this->name = $request->owner_name;
        $this->phone = $request->phone_number;
        $this->npwp = $request->npwp;
        $this->address = $request->npwp_address;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(env('MAIL_FROM_ADDRESS'), env('MAIL_FROM_NAME'))
            ->subject('AKUN EIGER B2B ANDA SUDAH AKTIF')
            ->view('email_reg')
            ->with(
                [
                    'nama' => $this->name,
                    'email' => $this->email,
                    'email' => $this->phone,
                    'email' => $this->npwp,
                    'address' => $this->address,
                ]
            );
    }
}
