<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class MailVerification extends Mailable
{
    use Queueable, SerializesModels;
    public $data;
    public $token;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($data, $token)
    {
        $this->data = $data;
        $this->token = $token;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(env('MAIL_FROM_ADDRESS'), 'Eiger-B2B')
            ->subject('Verifikasi Akun B2B Eigerindo')
            ->view('email_verification')
            ->with(
                [
                    'token' => $this->token,
                    'name' => $this->data['name'],
                    'email' => $this->data['email'],
                    'facebook' => $this->data['facebook'],
                    'twitter' => $this->data['twitter'],
                    'instagram' => $this->data['instagram'],
                    'tiktok' => $this->data['tiktok'],
                    'youtube' => $this->data['youtube']
                ]
            );
    }
}
