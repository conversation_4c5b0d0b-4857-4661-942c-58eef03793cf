<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Article extends FilterableModel
{
    use HasFactory;
    public $table = "article";

    public function scopeFetch($q, $customer_id)
    {
        return $q->whereHas('cart_detail.cart', function ($query) use ($customer_id) {
            $query->where('customer_id', $customer_id)
                ->desc('cart_detail.created_date');
        });
    }



    // public function cart_detail_sku(){
    //     return $this->where('')
    // }

    public function image_generic()
    {
        return $this->hasMany(ImageGeneric::class, 'sku_code_c', 'sku_code_c');
    }

    public static function getFlag($article)
    {
        $flag = [];
        $date = now()->format('Y-m-d');
        $articleData = self::where('article', $article)->first();
        if (!empty($articleData)) {
            if ($articleData->transfer_date <= $date && $articleData->expired_date >= $date) {
                array_push($flag, 'NEW');
            }
            if ($articleData->is_custom_logo || $articleData->is_custom_size) {
                array_push($flag, 'CUSTOM');
            }
            array_push($flag, $articleData->lvl4_description);
        }
        return $flag;
    }

    public function cart_detail()
    {
        return $this->belongsTo(CartDetail::class, 'article', 'article');
    }

    public function flag()
    {
        return $this->belongsTo(Product::class, 'article', 'article');
    }

    public function sku_stock()
    {
        return $this->hasOne(ProductSku::class, 'sku_id', 'article');
    }


    public function article_price()
    {
        return $this->hasMany(ArticlePrice::class, 'sku_code_c', 'sku_code_c');
    }
}