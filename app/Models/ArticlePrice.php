<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class ArticlePrice extends FilterableModel
{
    use HasFactory;
    public $table = "article_price";
    protected $keyType = 'string';
    public $incrementing = false;

    public function scopeValidPrice($query)
    {
        $currentDate = now()->format('Y-m-d');
        return $query->where('valid_from', '<=', $currentDate)
            ->where('valid_to', '>=', $currentDate)
            ->desc('valid_from');
    }

}