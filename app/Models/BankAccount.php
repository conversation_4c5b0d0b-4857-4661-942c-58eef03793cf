<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BankAccount extends Model
{
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';
    protected $primaryKey = 'company_bank_id';
    protected $table = 'company_bank';

    protected $fillable = [
        'company_id',
        'bank_name',
        'bank_account_name',
        'bank_account_no',
        'created_by',
        'modified_by',
    ];
    
    public function scopeActive($query){
        return $query->where('is_active', 1);
    }
}
