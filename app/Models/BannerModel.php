<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\FilterableModel;

class BannerModel extends FilterableModel
{
    use HasFactory;
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $table = 'banner';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'title',
        'thumbnail',
        'url',
        'sequence_no',
        'start_period',
        'end_period',
    ];

    protected $validator = [
        'title' => 'required',
        'thumbnail' => 'required',
        'url' => 'nullable',
        'sequence_no' => 'nullable',
        'start_period' => 'required',
        'end_period' => 'required',
    ];
}
