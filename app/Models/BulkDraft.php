<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BulkDraft extends Model
{
    use HasFactory;

    protected $table = 'bulk_draft';
    protected $guarded = [];

    public function article_detail()
    {
        return $this->hasOne(Article::class, 'article', 'article');
    }

    public function cart()
    {
        return $this->belongsTo(Cart::class, 'cart_id', 'id');
    }

    public function scopeArticle($query, $search = null, $color = null, $size = null, $sku = null)
    {
        $query->join('article', 'bulk_draft.article', '=', 'article.article')
            ->join('master_color', 'article.product_variant_c', '=', 'master_color.key')
            ->with([
                'article_detail.article_price' => function ($query) {
                    $query->validPrice(); // Applies your custom scope or condition
                }
            ])
            ->select(
                'bulk_draft.*',
                'master_color.value',
            );
        if ($search != '') {
            $query->where(function ($q) use ($search) {
                $q->whereRaw('LOWER(article.article_description) LIKE ?', ['%' . strtolower($search) . '%'])
                    ->orWhere('article.article', 'like', "%{$search}%");
            });
        }


        if ($color) {
            $query->orderBy('master_color.value', $color);
        }

        if ($size) {
            $query->orderBy('article.product_size_c', $size);
        }

        if ($sku) {
            $query->orderBy('article.article_description', $sku);
        }

        return $query;
    }

}