<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Cart extends FilterableModel
{
    use HasFactory;
    public $table = "cart";
    protected $keyType = 'string';
    public $incrementing = false;
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';
    public $serie;
    public $product_name;
    public $sku;
    public $min_qty;
    public $is_available;
    public $total_sub_qty;
    public $image_url;

    public $price;
    public $total_sub_price;
    public $detailItems;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by',
    ];

    public function details()
    {
        return $this->hasMany(CartDetail::class);
    }

    public function customers()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'customer_id');
    }

    public function cart_detail()
    {
        return $this->hasMany(CartDetail::class, 'cart_id', 'id');
    }

    // public function article_fetch($custId)
    // {
    //     return Article::fetch($custId)->groupBy('sku_code_c');
    // }

    public static function validate($customer_id)
    {
        if (!$cartExist = self::select('id')->where('customer_id', $customer_id)->first())
            return "Data Cart not found";
        if (!CartDetail::select('cart_id')->where('cart_id', $cartExist->id)->first())
            return "Data Cart successfully";
        return true;
    }


    public function scopeFetch($q, $customer_id, $is_available = null)
    {
        return $q->where('customer_id', $customer_id)
            ->has('cart_detail.article_detail')
            ->with([
                'customers',
                'cart_detail' => function ($cartDetailQuery) use ($is_available) {
                    $cartDetailQuery->when($is_available != '', function ($query) use ($is_available) {
                        if ($is_available == 'tersedia') {
                            return $query->where(function ($q) {
                                $q->where('is_available', 1)
                                    ->orWhereNull('is_available');
                            });
                        } else {
                            return $query->where('is_available', 0);
                        }
                    });
                },
                'cart_detail.article_detail' => function ($order_detail) {
                    $order_detail
                        ->with([
                            'image_generic' => function ($query) {
                                $query->onlyImage();
                            },
                            'article_price' => function ($query) {
                                $query->validPrice();
                            }
                        ], 'flag')
                        ->desc('created_date');
                    // ->group('sku_code_c');
                }
            ]);

    }

    public static function exist($customer_id)
    {
        return self::select('id')->where('customer_id', $customer_id)->first();
    }

    public function detailByProduct($articleId)
    {
        $article = substr($articleId, 0, -3);
        $details = $this->details()
            ->where('article', 'like', $article . '%')
            ->get();

        $collection = collect($details);
        return $collection->sum('qty');
    }

    public function detailByArticle($articleId)
    {
        $details = $this->details()
            ->where('article', $articleId)
            ->get();

        $collection = collect($details);
        return $collection->sum('qty');
    }


    public function items()
    {
        return $this->hasMany(CartItem::class);
    }
}



class CartResponseList extends Model
{
    public function items()
    {
        return $this->hasMany(CartItem::class);
    }


}

class Article_price extends FilterableModel
{
    use HasFactory;
    public $table = "article_price";
    protected $keyType = 'string';
    public $incrementing = false;

    public function scopeValidPrice($query)
    {
        $currentDate = now()->format('Y-m-d');
        return $query->where('valid_from', '<=', $currentDate)
            ->where('valid_to', '>=', $currentDate)
            ->desc('valid_from');
    }

}


class Article extends FilterableModel
{
    use HasFactory;
    public $table = "article";

    public function scopeFetch($q, $customer_id)
    {
        return $q->whereHas('cart_detail.cart', function ($query) use ($customer_id) {
            $query->where('customer_id', $customer_id)
                ->desc('cart_detail.created_date');
        });
    }

    // public function cart_detail_sku(){
    //     return $this->where('')
    // }

    public function image_generic()
    {
        return $this->hasMany(ImageGeneric::class, 'sku_code_c', 'sku_code_c');
    }

    public static function getFlag($article)
    {
        $flag = [];
        $date = now()->format('Y-m-d');
        $articleData = self::where('article', $article)->first();
        if (!empty($articleData)) {
            if ($articleData->transfer_date <= $date && $articleData->expired_date >= $date) {
                array_push($flag, 'NEW');
            }
            if ($articleData->is_custom_logo || $articleData->is_custom_size) {
                array_push($flag, 'CUSTOM');
            }
            array_push($flag, $articleData->lvl4_description);
        }
        return $flag;
    }

    public function cart_detail()
    {
        return $this->belongsTo(CartDetail::class, 'article', 'article');
    }

    public function flag()
    {
        return $this->belongsTo(Product::class, 'article', 'article');
    }

    public function article_price()
    {
        return $this->hasMany(Article_price::class, 'sku_code_c', 'sku_code_c');
    }

}

class CartItem extends Model
{
    use HasFactory;

    public function cart()
    {
        return $this->belongsTo(Cart::class);
    }
}