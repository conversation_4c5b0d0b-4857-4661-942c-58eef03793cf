<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CartCustom extends Model
{
    use HasFactory;

    public $table = "cart_custom";
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $fillable = [
        'cart_detail_id',
        'article',
        'new_article',
        'image_path',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by',
    ];
}
