<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class CartDetail extends FilterableModel
{
    use HasFactory;
    protected $primaryKey = 'cart_detail_id';
    public $table = "cart_detail";
    public $timestamps = false;
    public $incrementing = false;
    protected $keyType = 'string';
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';


    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'cart_id',
        'article',
        'new_article',
        'is_custom',
        'qty',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by',
    ];

    
    /**
     * Get the user that owns the cart_detail
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
  

     public function cart()
     {
         return $this->belongsTo(Cart::class, 'cart_id', 'id');
     }
     
    public function cart_custom()
    {
        return $this->hasOne(CartCustom::class,'cart_detail_id');
    }
    public function article_detail()
    {
        return $this->hasOne(Article::class, 'article', 'article');
    }

    public function scopeRelated($query, $param){
        return $query->where('article', 'like', "$param%");
    }

    public function scopeFindCart($query, $param){
        return $query->where('cart_id', $param);
    }

    public static function scopeCustomer($param){
        return self::whereHas('cart', fn($q) => $q->where('customer_id', $param))->get();
    }

    public static function exist($cart_id){
        return self::select('cart_id')->where('cart_id',$cart_id)->first();
    }
}