<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\FilterableModel;

class City extends FilterableModel
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $table = 'city';
    protected $keyType = 'string';

    protected $fillable = [
        'code',
        'region_code',
        'name'
    ];
}
