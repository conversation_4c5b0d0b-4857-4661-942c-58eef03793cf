<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Color extends Model
{
    // use HasFactory;

    // const CREATED_AT = 'created_date';
    // const UPDATED_AT = 'modified_date';

    protected $table = 'master_color';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'key',
        'value',
        'created_by',
        'modified_date',
        'modified_by',
    ];

    public static function getByKey($code)
    {
        if ($color = self::where('key', $code)->first()) {
            return $color->value;
        }

        return $code;
    }
}
