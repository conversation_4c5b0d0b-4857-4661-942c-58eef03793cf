<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CommissionLedgers extends Model
{
    use HasFactory;
    
    public $table = "rsl_commission_ledgers";
    
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'commission_id',
        'transaction_id',
        'reseller_id',
        'type',
        'commission_type',
        'amount',
        'remarks'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime',
    ];

}
