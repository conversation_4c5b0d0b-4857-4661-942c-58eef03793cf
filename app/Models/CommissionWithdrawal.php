<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class CommissionWithdrawal extends Model
{
    use HasFactory;

    const WAITING_FOR_APPROVAL = 'Menunggu Persetujuan';
    const APPROVED = 'Disetujui';
    const REJECTED = 'Ditolak';
    const FAILED = 'Gagal';
    const SUCCESS = 'Sukses';
    const RTOL = 'RTOL';
    const RTGS = 'RTGS';
    const OVERBOOKING = 'Overbooking';
    const SKN = 'SKN';
    public $table = "rsl_commission_withdrawal";
    
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'reseller_id',
        'request_id',
        'ext_request_id',
        'request_date',
        'bank_name',
        'account_name',
        'account_no',
        'payment_date',
        'tax_reference',
        'tax_metadata',
        'status',
        'amount',
        'additional_amount',
        'tax_amount',
        'total',
        'action_by',
        'action_type',
        'action_notes'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime',
    ];

    public function rsl_bank()
    {
        return $this->hasOne(ResellerBank::class, 'account_no', 'account_no');
    }

    
    public static function getLeverageTax(Reseller $reseller)
    {
        return $reseller->withdrawals()->whereYear('created_date', Carbon::now()->year)->whereIn('status', [self::APPROVED, self::SUCCESS])->sum('amount');
    }

}
