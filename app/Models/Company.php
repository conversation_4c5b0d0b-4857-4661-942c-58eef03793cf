<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = "company";
    protected $keyType = 'string';
    public $incrementing = false;

    protected $primaryKey = 'company_id';

    protected $fillable = [
        'company_id',
        'company_name',
        'company_address',
        'company_description',
        'npwp',
        'npwp_city',
        'npwpaddress',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by'
    ];
}
