<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyBank extends Model
{
    use HasFactory;

    public $table = 'company_bank';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'company_bank_id',
        'company_id',
        'bank_name',
        'bank_account_name',
        'bank_account_no',
        'is_active',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by'
    ];
}
