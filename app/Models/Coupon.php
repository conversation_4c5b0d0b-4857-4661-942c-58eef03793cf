<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coupon extends Model
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = "rsl_coupons";

    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'id_code',
        'coupon_code',
        'name',
        'description',
        'amount',
        'remarks',
        'mix_promotion',
        'max_use_count',
        'max_per_user',
        'start_date',
        'end_date',
        'sunday_enabled',
        'sunday_start_at',
        'sunday_end_at',
        'monday_enabled',
        'monday_start_at',
        'monday_end_at',
        'tuesday_enabled',
        'tuesday_start_at',
        'tuesday_end_at',
        'wednesday_enabled',
        'wednesday_start_at',
        'wednesday_end_at',
        'thursday_enabled',
        'thursday_start_at',
        'thursday_end_at',
        'friday_enabled',
        'friday_start_at',
        'friday_end_at',
        'saturday_enabled',
        'saturday_start_at',
        'saturday_end_at',
        'type',
        'category',
        'condition_type',
        'discount_type',
        'status',
        'used_count',
        'created_by',
        'modified_by'
    ];

    public function scopeActive($q)
    {
        return $q->where('status', 'active');
    }

    public function scopeActiveDate($q)
    {
        return $q->where('start_date', '<=', now()->format('Y-m-d'))
            ->where('end_date', '>=', now()->format('Y-m-d'));
    }

    public function scopeActiveDay($q, $d, $s, $e)
    {
        return $q->where(function($q)use($d,$s,$e){
            $q->where(function($q) use($d,$s,$e){
                $q->where($d,1)
                ->where($s, '<=', now()->format('H:i:s'))
                ->where($e, '>=', now()->format('H:i:s'));
            })
            ->orWhere(function($q){
                $q->where('sunday_enabled',0)
                ->where('monday_enabled',0)
                ->where('tuesday_enabled',0)
                ->where('wednesday_enabled',0)
                ->where('thursday_enabled',0)
                ->where('friday_enabled',0)
                ->where('saturday_enabled',0);
            });
        });
        // return $q->where($d, 1);
    }

    public function scopeActiveDayDate($q, $s, $e)
    {
        return $q->where($s, '<=', now()->format('H:i:s'))
            ->where($e, '>=', now()->format('H:i:s'));
    }

    public function articles()
    {
        return $this->hasMany(CouponArticle::class,'coupon_id','id');
    }

    public function applied_orders()
    {
        return $this->hasMany(ResellerOrderPromotion::class,'discount_id','id');
    }
}
