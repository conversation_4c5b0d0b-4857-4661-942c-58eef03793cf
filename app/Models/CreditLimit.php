<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CreditLimit extends Model
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = 'credit_limit';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'customer_external_id',
        'credit_limit',
        'credit_limit_used',
        'credit_limit_remaining',
        'credit_limit_used_percentage',
        'currency',
        'created_by',
        'modified_by',
    ];
}
