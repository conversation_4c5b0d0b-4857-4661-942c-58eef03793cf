<?php

namespace App\Models;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Customer extends Model
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    static $wholesalesCode = ['Z1', 'W1'];
    static $b2bCode = ['Z4', 'W3'];
    static $wholesalesChan = ['WHOLESALES'];
    static $b2bChan = ['B2B'];

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'customer_id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'owner_name',
        'email',
        'is_verified',
        'phone_number',
        'instance_name',
        'national_id',
        'national_id_file',
        'distribution_channel',
        'npwp',
        'npwp_file',
        'tax_invoice',
        'npwp_province_code',
        'npwp_province',
        'npwp_city_code',
        'npwp_city',
        'npwp_district_code',
        'npwp_district',
        'npwp_zip_code',
        'npwp_address',
        'customer_type',
        'top',
        'top_days',
        'discount_percent',
        'customer_type',
        'is_active',
        'is_pending_payment',
        'created_by',
        'modified_by',
        'is_rejected',
        'tax_type',
        'zip_code_id',
        'status',
        'sumber'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime',
    ];


    public $isMasked = true;


    protected $unMaskedRoles = ['Admin', 'Senior Sales', 'Sales'];

    protected static function booted()
    {
        static::saving(function ($customer) {
            $fields = ['phone_number', 'email', 'npwp', 'national_id'];

            foreach ($fields as $field) {
                $value = $customer->$field;

                try {
                    Crypt::decryptString($value);
                    // Already encrypted, skip
                } catch (\Exception $e) {
                    // Not encrypted, encrypt it
                    $customer->$field = Crypt::encryptString($value);
                }
            }
        });
    }

    public function user()
    {
        return $this->hasOne(User::class, 'reference_id', 'customer_id');
    }

    /**
     * Get all of the shipments for the Customer
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function shipments()
    {
        return $this->hasMany(CustomerShipment::class, 'customer_id', 'customer_id');
    }

    public function creditLimit()
    {
        return $this->hasOne(CreditLimit::class, 'customer_external_id', 'customer_id');
    }

    public function cart()
    {
        return $this->hasOne(Cart::class, 'customer_id', 'customer_id');
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'customer_id', 'customer_id');
    }

    public function va()
    {
        return $this->hasMany(VirtualAccount::class, 'customer_id', 'customer_id');
    }

    public function history_reject()
    {
        return $this->hasMany(CustomerHistory::class, 'customer_id', 'customer_id');
    }

    public function customer_sales()
    {
        return $this->belongsToMany(Sales::class, 'customer_sales', 'customer_id', 'sales_id');
    }

    public function customer_sales_model()
    {
        return $this->belongsTo(CustomerSales::class, 'customer_id', 'customer_id');
    }

    public function getSalesAttribute()
    {
        return $this->customer_sales->first();
    }

    public function getStatusAttribute()
    {
        if ($this->distribution_channel === 'Wholesales') {
            if ($this->is_rejected) {
                return 'Ditolak';
            } elseif ($this->is_pending_payment) {
                return 'Dibekukan';
            } elseif ($this->is_active) {
                return 'Aktif';
            } else {
                return 'Non Aktif';
            }
        } else {
            return $this->attributes['status'];
        }
    }


    public function setMasking(bool $masked)
    {
        $this->isMasked = $masked;
        return $this;
    }

    public function getPhoneNumberAttribute($value)
    {
        try {
            $value = Crypt::decryptString($value);
        } catch (\Exception $e) {

        }

        if (!$this->isMasked) {
            return $value;
        }

        if (auth()->user() && auth()->user()->reference_id != $this->customer_id && !in_array(auth()->user()->roles[0]->name, $this->unMaskedRoles) == true) {
            return strlen($value) > 4
                ? substr($value, 0, -4) . str_repeat('*', min(4, strlen($value)))
                : str_repeat('*', strlen($value));
        }

        return $value;
    }

    public function getNpwpAttribute($value)
    {

        try {
            $value = Crypt::decryptString($value);
        } catch (\Exception $e) {

        }

        if (!$this->isMasked) {
            return $value;
        }

        if (auth()->user() && auth()->user()->reference_id != $this->customer_id && !in_array(auth()->user()->roles[0]->name, $this->unMaskedRoles) == true) {
            return strlen($value) > 4
                ? substr($value, 0, -4) . str_repeat('*', min(4, strlen($value)))
                : str_repeat('*', strlen($value));
        }

        return $value;
    }

    public function getNationalIdAttribute($value)
    {
        try {
            $value = Crypt::decryptString($value);
        } catch (\Exception $e) {

        }

        if (!$this->isMasked) {
            return $value;
        }

        if (auth()->user() && auth()->user()->reference_id != $this->customer_id && !in_array(auth()->user()->roles[0]->name, $this->unMaskedRoles) == true) {
            return strlen($value) > 4
                ? substr($value, 0, -4) . str_repeat('*', min(4, strlen($value)))
                : str_repeat('*', strlen($value));
        }

        return $value;
    }

    public function getEmailAttribute($value)
    {
        try {
            $value = Crypt::decryptString($value);
        } catch (\Exception $e) {

        }

        if (!$this->isMasked) {
            return $value;
        }

        if (auth()->user() && auth()->user()->reference_id != $this->customer_id && !in_array(auth()->user()->roles[0]->name, $this->unMaskedRoles) == true) {
            $parts = explode('@', $value);
            if (count($parts) === 2) {
                $username = $parts[0];
                $domain = $parts[1];
                $maskedUsername = strlen($username) > 3
                    ? substr($username, 0, 1) . str_repeat('*', strlen($username) - 2) . substr($username, -1)
                    : str_repeat('*', strlen($username));
                return $maskedUsername . '@' . $domain;
            }
        }

        return $value;
    }

    public function scopeChannel($q, $type)
    {
        return $q->where('distribution_channel', strtoupper($type));
    }
}