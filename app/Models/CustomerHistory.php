<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerHistory extends Model
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    public $incrementing = false;
    public $table = "customers_history";

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'customer_id',
        'owner_name',
        'instance_name',
        'email',
        'address',
        'phone_number',
        'distribution_channel',
        'national_id',
        'national_id_file',
        'npwp',
        'npwp_city',
        'npwp_address',
        'tax_type',
        'tax_invoice',
        'top',
        'top_days',
        'discount_percent',
        'customer_type',
        'status',
        'is_active',
        'is_blocked',
        'is_pending_payment',
        'registered_sap_at',
        'created_by',
        'modified_by',
        'rejection_reason'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime',
    ];

}
