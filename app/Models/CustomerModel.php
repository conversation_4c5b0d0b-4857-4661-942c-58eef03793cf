<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerSales extends Model
{
    use HasFactory;
    public $table = "customer_sales";
    protected $keyType = 'string';
    public $incrementing = false;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'customer_id' ,
        'sales_id',
        'created_by',
        'created_date',
        'modified_by',
        'modified_date',
    ];
}