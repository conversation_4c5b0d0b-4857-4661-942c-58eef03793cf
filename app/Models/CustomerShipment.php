<?php

namespace App\Models;

use App\Models\Customer;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CustomerShipment extends Model
{
    use HasFactory;
    protected $keyType = 'string';
    public $incrementing = false;  
    public $table = "customer_shipment";
    protected $primaryKey = 'customer_shipment_id';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $fillable = [
        'customer_shipment_id',
        'customer_id',
        'name',
        'address',
        'city',
        'province',
        'district',
        'subdistrict',
        'zip_code',
        'shipment_type',
        'phone_number',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by'
    ];

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }

    public function credit_limit()
    {
        return $this->belongsTo(CreditLimit::class, 'customer_external_id');
    }

    public function order_header()
    {
        return $this->hasMany(Order::class, 'customer_shipment_id', 'customer_shipment_id');
    }
}
