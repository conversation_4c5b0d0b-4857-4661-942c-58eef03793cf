<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerStock extends Model
{
    use HasFactory;

    protected $table = "customer_stock";
    protected $keyType = 'string';
    public $incrementing = false;

    protected $guarded = [];
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public function customer_shipment()
    {
        return $this->belongsTo(CustomerShipment::class, 'customer_shipment_id', 'customer_shipment_id');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'customer_id');
    }
}