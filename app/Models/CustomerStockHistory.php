<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerStockHistory extends Model
{
    use HasFactory;

    protected $table = "customer_stock_history";
    protected $keyType = 'string';
    public $incrementing = false;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public function customer_stock()
    {
        return $this->belongsTo(CustomerStock::class,'customer_stock_id','id');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'customer_id');
    }

    public function customer_shipment()
    {
        return $this->belongsTo(CustomerShipment::class, 'customer_shipment_id','customer_shipment_id');
    }
}
