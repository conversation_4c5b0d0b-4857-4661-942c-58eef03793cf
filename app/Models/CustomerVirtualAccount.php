<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerVirtualAccount extends Model
{
    use HasFactory;

    public $table = 'customer_virtual_account';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'id',
        'customer_id',
        'bank_name',
        'virtual_account_no ',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by'
    ];
}
