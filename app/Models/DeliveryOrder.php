<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeliveryOrder extends Model
{
    use HasFactory;
    public $table = 'delivery_order';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The attributes that are mass assignable. 
     *
     * @var array
     */
    protected $fillable = [
        'delivery_order_no', 
        'sales_order_no',
        'customer_id',
        'customer_shipment_id',
        'good_issue_no',
        'good_issue_date',
        'route',
        'delivery_date',
        'delivery_type',
        'total',
        'parent_location',
        'location_type',
        'destination',
        'source',
        'remark',
        'status',
        'timestamp',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by'
    ];

    public function items(){
        return $this->hasMany(DeliveryOrderDetail::class, 'delivery_order_no', 'delivery_order_no');
    }

    public function customer_shipment()
    {
        return $this->belongsTo(CustomerShipment::class, 'customer_shipment_id');
    }
}