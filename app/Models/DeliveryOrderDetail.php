<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeliveryOrderDetail extends Model
{
    use HasFactory;
    public $table = "delivery_order_detail";
    protected $keyType = 'string';
    public $incrementing = false;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'delivery_order_no',
        'article',
        'sequence_no',
        'product_name',
        'product_variant',
        'product_size',
        'is_new_arrival',
        'uom',
        'qty',
        'issued_qty',
        'currency',
        'status',
        'merchandise_category',
        'location',
        'sub_total',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by'
    ];

    public function product()
    {
        return $this->belongsTo(Product::class, 'article', 'article');
    }

    public function delivery_order(){
        return $this->belongsTo(DeliveryOrder::class, 'delivery_order_no', 'delivery_order_no');
    }
}