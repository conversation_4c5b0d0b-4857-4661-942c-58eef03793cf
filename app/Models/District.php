<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\FilterableModel;

class District extends FilterableModel
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $table = 'district';
    protected $keyType = 'string';

    protected $fillable = [
        'code',
        'city_id',
        'name'
    ];
}
