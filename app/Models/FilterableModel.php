<?php

namespace App\Models;
use App\Helpers\QueryMapping;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Carbon\Carbon;
use App\Helpers\RedisBuilder;
class FilterableModel extends Model
{
    /*
        Extensi Model untuk mendukung Filter dari Request Get & POST (POST onprogress)
        step untuk menggunakan ini:
        1. Membuat Model
        2. Extends Model yang dibuat menggunakan class ini
        ex: class Cart Extends FilterableModel
        3. di Controller pakai format ini untuk memanggil:

            $request_collection = $request->collect();
            Model::filterHelper('<jenis_filter>', $request_collection);

        Step kerja ini membaca pada file App/Helpers/QueryMapping.php dan mencocokan query parameter yang ada di get dengan 
        mengambil jenis_filter yang dipanggil ke dalam scope FilterHelper
    */
    public function valueModifier($text, $type){
        return $type == 'date' ? Carbon::parse($text)->format('Y-m-d H:i:s') : $text;
    }

    public function validatorMapper($fields, $computed = []){
        return array_map(fn(String $i)=> [
            $i => array_key_exists($i, $computed) 
            ?  $computed[$i]
            : 'nullable'
        ], $fields);
    }

    public function likeModifier($text, $type){
        return $type == 'LIKE' ? '%'.$text.'%' : $text;
    }

    public function whereModifier($model, $key, $query, $operation, $value){
        switch($query) {
            case 'where':
                return $model->where($key, $operation, $value);
            case 'whereIn':
                return $model->whereIn($key, $value);
            case 'whereNotIn':
                return $model->whereNotIn($key, $value);
            case 'whereDate':
                return $model->whereDate($key, $operation, $value);
        }
    }

    public function doSearch($model, $key, $value, $mapping_array){
        $mapObj = (object) $mapping_array[$key];
        $value = $this->likeModifier($this->valueModifier($value, $mapObj->type), $mapObj->operation);
        if($mapObj->relation == ''){
           return $model->when($value, function ($q) use ($value, $mapObj){
                    $this->whereModifier($q,$mapObj->field_name,$mapObj->query, $mapObj->operation, $value);
            });
        }
        else{
            return $model->when($value, function ($q) use ($value, $mapObj){
                $q->whereHas($mapObj->relation, function ($q) use ($value, $mapObj){
                    $this->whereModifier($q,$mapObj->field_name,$mapObj->query, $mapObj->operation, $value);
                });
        });
        }
    }

    public function scopeDesc($query, $attr){
        return $query->orderBy($attr, 'DESC');
    }
    public function scopeGroup($query, $attr){
        return $query->groupBy($attr);
    }
    public function scopeAsc($query, $attr){
        return $query->orderBy($attr, 'ASC');
    }

    public function scopePager($query, $param){
        $param = $param->collect();
        return $query->paginate($param['per_page']??12,['*'], 'page',$param['page']??1);
    }


    public function scopeFilterHelper($models, $mapping_name, $query) {
        $map_model = array_filter(QueryMapping::$resources, fn ($m) => $m['name'] == $mapping_name);
       
        if(count($map_model) === 0)
        {
            return $models;
        }
       
        foreach($map_model as $m){
            foreach($query as $key => $value){
                if(Arr::exists($m['parameter'], $key)){
                     if($query[$key] !== null && $query[$key] !== ''){
                         $models = $this->doSearch($models, $key, $value, $m['parameter']);
                     }
                 
                 }
             }
        }
        
    return $models;

    }

    public static function getQueryWithBindings($query): string
        {
           return hash('sha256',str_replace(' ', '',vsprintf(str_replace('?', '%s', $query->toSql()), collect($query->getBindings())->map(function ($binding) {
                $binding = addslashes($binding);
                return is_numeric($binding) ? $binding : "'{$binding}'";
            })->toArray())));
        }

        public function newEloquentBuilder($query): RedisBuilder
        {
            return new RedisBuilder($query);
        }

    public function getCreatedDateAttribute($value)
    {
        return Carbon::parse($value)->timezone('Asia/Jakarta')->format('Y-m-d H:i:s');
    }

    public function getModifiedDateAttribute($value)
    {
        return Carbon::parse($value)->timezone('Asia/Jakarta')->format('Y-m-d H:i:s');
    }

}
