<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ImageGeneric extends Model
{
    use HasFactory;

    protected $table = "image_generic";
    protected $keyType = 'string';
    public $incrementing = false;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public function scopeOnlyImage($query){
        return $query->where('is_main_image',1);
    }
}
