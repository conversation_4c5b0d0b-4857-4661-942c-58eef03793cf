<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = 'invoice';

    protected $primaryKey = 'invoice_no';

    protected $fillable = [
        'invoice_no',
        'customer_id',
        'delivery_order_no',
        'billing_date',
        'due_date',
        'order_no',
        'sales_order_no',
        'vat_no',
        'down_payment',
        'due_payment',
        'gross_price',
        'nett_price',
        'dpp',
        'tax_amount',
        'currency',
        'uom',
        'status',
        'signature',
        'signature_job',
        'invoice_file_path',
        'tax_invoice_file_name',
        'tax_invoice_file_type',
        'tax_invoice_file_path',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by'
    ];

    public function detail(){
        return $this->hasMany(InvoiceDetail::class, 'invoice_no','invoice_no');
    }

    public function order(){
        return $this->belongsTo(Order::class,'order_no','order_no');
    }

    public function customer(){
        return $this->belongsTo(Customer::class,'customer_id','customer_id');
    }

    public function sales(){
        return $this->hasOneThrough(Sales::class,Order::class,'sales_id','sales_id','order_no','order_no');
    }

    public function deliveryOrder(){
        return $this->hasOne(DeliveryOrder::class, 'delivery_order_no', 'delivery_order_no');
    }
}
