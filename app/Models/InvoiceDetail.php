<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InvoiceDetail extends Model
{
    use HasFactory;
    public $table = "invoice_detail";
    protected $keyType = 'string';
    public $incrementing = false;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $fillable = [
        'invoice_detail_id',
        'invoice_no',
        'reference_do ',
        'reference_so ',
        'article ',
        'sequence_no ',
        'characteristic_value ',
        'product_name ',
        'product_variant ',
        'product_size ',
        'article_description ',
        'qty ',
        'uom ',
        'price',
        'discount_percent',
        'gross_price',
        'nett_price',
        'currency ',
        'created_date',
        'created_by ',
        'modified_date',
        'modified_by'
    ];

    public function invoice(){
        return $this->belongsTo(Invoice::class);
    }

    public function product(){
        return $this->belongsTo(Product::class,'article','article');
    }

}
