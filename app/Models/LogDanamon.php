<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LogDanamon extends Model
{
    public $timestamps = false;
    protected $table = 'rsl_danamon_log';
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $keyType = 'string';
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $fillable = [
        'danamon_reference',
        'careom_reference',
        'from',
        'to',
        'amount',
        'remarks',
        'success',
        'created_by',
        'modified_by'
    ];

}
