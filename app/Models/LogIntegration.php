<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LogIntegration extends Model
{
    public $timestamps = false;
    protected $table = 'log_integration';
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $keyType = 'string';
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $fillable = [
        'reference_no',
        'module',
        'name',
        'type',
        'status',
        'description',
        'created_by',
        'modified_by'
    ];

    public static $validator = [
        'reference_no' => 'nullable|max:255',
        'module' => 'nullable|max:255',
        'name' => 'required|max:255',
        'type' => 'nullable|max:255',
        'status' => 'nullable|max:20',
        'description' => 'required',
        'created_by' => 'nullable',
        'modified_by' => 'nullable',
    ];
}
