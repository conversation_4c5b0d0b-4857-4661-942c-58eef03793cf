<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MasterParameter extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'master_parameter';
    protected $keyType = 'string';
    public $incrementing = false;
    public $timestamps = false;

    protected $fillable = [
        'group_key',
        'key',
        'value',
        'description',
        'created_by',
        'modified_by'
    ];

    public static $validator = [
        'group_key' => 'nullable|max:255',
        'key' => 'nullable|max:255',
        'value' => 'nullable|max:255',
        'description' => 'nullable',
        'created_by' => 'nullable',
        'modified_by' => 'nullable',
    ];

    public static function byGroup($groupKey)
    {
        return self::where('group_key', $groupKey)->get();
    }

    public static function CrossError()
    {
        return self::where('group_key','CREATE_ORDER_ERROR')->where('key','CROSS_CATEGORY')->first()->value ?? '<PERSON><PERSON>, pesanan <PERSON>a tidak dapat diproses, karena perlu adanya pemisahan kategori untuk bags, non bags, dan footwear dalam 1 pesanan. Silakan lakukan pemesanan ulang dengan kategori yang telah ditentukan.';
    }


    public static function WAB()
    {
        return self::where('group_key','CHANNEL_CODE')->where('value','WAB')->first()->key ?? 'C14';
    }

    public static function Wholesales()
    {
        return self::where('group_key','CHANNEL_CODE')->where('value','WHOLESALES')->first()->key ?? 'W1';
    }

    public static function byKey($key, $singleRow = true)
    {
        $query = self::where('key', $key);
        if ($singleRow == true) {
            return $query->first();
        }
        return $query->get();
    }
}