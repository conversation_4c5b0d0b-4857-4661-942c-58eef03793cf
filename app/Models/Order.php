<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;
use App\Models\Customer;
class Order extends FilterableModel
{
    use HasFactory;

    public $table = "order_header";
    protected $primaryKey = 'order_no';
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    const OrderStatusWait = 'Menunggu Konfirmasi';
    const OrderStatusPending = 'Pending';
    const OrderStatusOnHold = 'On Hold';
    const OrderStatusOnProcess = 'Diproses';
    const OrderStatusBaru = 'Baru';
    const OrderStatusGI = 'Siap Dikirim';
    const OrderStatusOnShipping = 'Dikirim';
    const OrderStatusDelivered = 'Diterima';
    const OrderStatusFinish = 'Selesai';
    const OrderStatusCancel = 'Batal';
    const OrderStatusPembayaran = 'Pembayaran';
    const OrderStatusSemua = 'Semua';
    const TokoCash = 'Cash';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

    protected $casts = [
        'order_no' => 'string'
    ];

    protected $fillable = [
        'order_no',
        'customer_id',
        'customer_shipment_id',
        'order_group_id',
        //'sales_order_no',
        'sales_id',
        'order_status',
        'total',
        'sub_total',
        'total_discount',
        'nett_before_tax',
        'total_tax',
        'total_nett',
        // 'shipping_date',
        // 'shipping_method',
        'completed_date',
        // 'sales_organization',
        'distribution_channel',
        // 'division',
        // 'verified_date',
        // 'verified_by',
        'created_by',
        'created_date',
        'modified_by',
        'modified_date',
        'sales_name',
        'bill_to',
        'bill_to_address',
        'bill_to_phone_number',
        'bill_to_email',
        'ship_to',
        'ship_to_address',
        'ship_to_phone_number',
        'dp_percentage',
        'currency',
        'dp_amount',
        'dp_due_date',
        'payment_file',
        'location_code',
        'location_name',
        'shipping_charges'
    ];

    public static function channel($param)
    {
        return self::where('distribution_channel', $param);
    }

    public static function validateConfirm($param, $customer_id)
    {
        if (!isset($param['order_no']) && !isset($param['customer_shipment_id']))
            return 'order_no/customer_shipment_id tidak ada';
        $order = self::with('customer')->where('order_no', $param['order_no']);
        $fetch = $order->first();
        if (!$fetch)
            return "Order " . $param['order_no'] . " tidak ditemukan!";
        if ($fetch->customer === null)
            return "Customer tidak ditemukan";
        if ($fetch->customer->customer_id != $customer_id)
            return "Anda tidak dapat Konfirmasi pesanan orang lain";
        if ($fetch->order_status == self::OrderStatusBaru && ($fetch->sales_order_no != null || $fetch->sales_order_no != '')) {
            return "Sorry this order is already confirm";
        }

        return true;
    }

    public function items()
    {
        return $this->hasMany(OrderItem::class, 'order_no', 'order_no');
    }
    public function custom()
    {
        return $this->hasMany(OrderCustom::class, 'reference_id', 'order_no');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'customer_id');
    }

    public function customer_sales()
    {
        return $this->belongsTo(CustomerSales::class, 'customer_id', 'customer_id');
    }

    public function customer_shipment()
    {
        return $this->belongsTo(CustomerShipment::class, 'customer_shipment_id', 'customer_shipment_id');
    }

    public function scopeWholesales($query)
    {
        return $query->whereIn('distribution_channel', [MasterParameter::Wholesales(), 'WHOLESALES']);
    }

    public function ScopeWab($query)
    {
        return $query->whereIn('distribution_channel', [MasterParameter::Wholesales(), 'WAB']);
    }

    public function invoice()
    {
        return $this->hasOne(Invoice::class, 'sales_order_no', 'sales_order_no');
    }

    public function invoice_billing()
    {
        return $this->hasOne(Invoice::class, 'sales_order_no', 'sales_order_no')
            ->where('invoice_type', 'BILLING');
    }

    public function invoice_dp()
    {
        return $this->hasOne(Invoice::class, 'sales_order_no', 'sales_order_no')
            ->where('invoice_type', 'DOWN PAYMENT');
    }

    public function delivery_order()
    {
        return $this->hasOne(DeliveryOrder::class, 'sales_order_no', 'sales_order_no');
    }

    public static function updateStatus($order_no, $status)
    {
        $order = self::where('order_no', $order_no);

        if (!$order->first())
            return false;
        $order->update(['order_status', $status]);
        return true;
    }
}