<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderApproval extends Model
{
    use HasFactory;

    public $table = "order_approval";

    public $incrementing = false;
    protected $keyType = 'string';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $fillable = [
        'order_no',
        'status',
        'action_by',
        'action_date',
        'created_by',
        'created_date',
        'modified_by',
        'modified_date'
    ];

    public function order()
    {
        return $this->belongsTo(Order::class,'order_no','order_no');
    }
}
