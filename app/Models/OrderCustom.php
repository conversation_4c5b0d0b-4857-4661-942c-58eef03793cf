<?php

namespace App\Models;

use App\Models\OrderCustomAttachment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OrderCustom extends Model
{
    use HasFactory;
    public $table = "order_custom";
    protected $keyType = 'string';
    public $incrementing = false;
    protected $primaryKey = 'id';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'sku',
        'article_id',
        'reference_name',
        'reference_id',
        'custom_price',
        'position_side',
        'generated_file_path',
        'created_by',
        'created_date',
        'modified_by',
        'modified_date'
    ];


    public function attachment()
    {
        return $this->hasMany(OrderCustomAttachment::class, 'order_custom_id', 'id');
    }

    public function attachmentSku()
    {
        return $this->hasMany(OrderCustomAttachment::class, 'order_custom_id', 'attachment_group_id');
    }
    
    public function article()
    {
        return $this->belongsTo(Article::class, 'sku', 'sku_code_c');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'sku', 'sku_code_c');
    }

}