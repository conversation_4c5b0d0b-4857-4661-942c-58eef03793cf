<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderCustomAttachment extends Model
{
    use HasFactory;
    public $table = "order_custom_attachment";
    protected $keyType = 'string';
    public $incrementing = false;
    protected $primaryKey = 'id';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'order_custom_id',
        'custom_type',
        'custom_price',
        'position_x',
        'position_y',
        'dimension_width',
        'dimension_height',
        'file_path',
        'material',
        'custom_text',
        'size',
        'notes',
        'created_by',
        'created_date',
        'modified_by',
        'modified_date'
    ];

    // public function OrderCustom()
    // {
    //     return $this->belongsTo(OrderCustom::class, 'order_custom_id');
    // }
}