<?php

namespace App\Models;

use App\Models\Article;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OrderItem extends Model
{
    use HasFactory;
    public $table = "order_detail";
    protected $keyType = 'string';
    public $incrementing = false;
    protected $primaryKey = 'order_detail_id';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'order_detail_id',
        'order_no',
        'article_id',
        'product_name',
        'product_size',
        'is_new_arrival',
        'price',
        'sub_total',
        'qty',
        'primary_discount',
        'additional_discount',
        'total',
        'created_by',
        'created_date',
        'modified_by',
        'modified_date',
        'is_available',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_no');
    }

    public function scopeAvailable($q, $is_available = true)
    {
        if ($is_available == false) {
            return $q->whereNotNull('issued_qty')->whereColumn('qty', '>', 'issued_qty');
        }

        return $q;
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'article_id', 'article');
    }

    public function article()
    {
        return $this->belongsTo(Article::class, 'article_id', 'article');
    }

}