<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItemReseller extends Model
{
    use HasFactory;
    public $table = "rsl_order_details";
    protected $keyType = 'string';
    public $incrementing = false;
    protected $primaryKey = 'id';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'order_header_id',
        'sku_code',
        'article_id',
        'qty',
        'additional_qty',
        'unit_price',
        'base_uom', 
        'line_amount',
        // 'promotion_id',
        // 'discount_code',
        'discount_amount',
        'total_amount',
        'remarks',
        'product_name',
        'product_size',
        'product_variant',
    ];

    public function orderReseller()
    {
        return $this->belongsTo(OrderReseller::class, 'order_header_id','id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'article_id', 'article');
    }

    public function color()
    {
        return $this->belongsTo(Color::class,'product_variant','key');
    }

    public function getColorValueAttribute()
    {
        return $this->color ? $this->color->value : null;
    }

    public function mainImageVariant()
    {
        return $this->hasOne(ImageVariant::class, 'article', 'article_id')
                    ->where('file_path','!=','')
                    ->where('is_main_image',1);
    }

    public function promotions(){
        return $this->hasOne(ResellerPromotion::class,'reference_id', 'id')->where('discount_type', 'promotions')->orderByDesc('amount');
    }
    
    public function promotion(){
        return $this->hasOne(ResellerOrderPromotion::class,'reference_id', 'id')->where('discount_type', 'promotions')->orderByDesc('amount');
    }
}