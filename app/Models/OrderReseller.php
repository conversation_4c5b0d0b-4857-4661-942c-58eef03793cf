<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;
use App\Models\Customer;
use App\Models\OrderItemReseller;
use Carbon\Carbon;
use App\Helpers\Promotion\PromotionHelper;
class OrderReseller extends FilterableModel
{
    use HasFactory;
    
    public $table = "rsl_order_headers";
    protected $primaryKey = 'order_no';
    protected $keyType = 'string';
    public $incrementing = false;
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    const ORDER_RESELLER_PENDING = 'Pending';
    const ORDER_RESELLER_BARU = 'Baru';
    const ORDER_RESELLER_BATAL = 'Batal';
    const ORDER_RESELLER_DIPROSES = 'Diproses';
    const ORDER_RESELLER_DIKEMAS = 'Dikemas';
    const ORDER_RESELLER_DIKIRIM = 'Dikirim';
    const ORDER_RESELLER_DITERIMA = 'Diterima';
    const ORDER_RESELLER_SELESAI = 'Selesai';
    const ORDER_RESELLER_PENGEMBALIAN = 'Pengembalian';
    const ORDER_RESELLER_REFUND = 'Refund';
    const ORDER_RESELLER_MENUNGGU_PEMBAYARAN = 'Menunggu Pembayaran';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

    protected $casts = [
        'order_no' => 'string',
        // 'created_date' => 'datetime:Y-m-d H:i:s',
        // 'modified_date' => 'datetime:Y-m-d H:i:s'
    ];
    
    protected $fillable = [
        'order_no',
        'ext_order_id',
        'ext_order_no',
        'reseller_id',
        'link_id',
        'invoice_no',
        'order_date',
        'customer_id',
        'customer_name',
        'customer_phone_number',
        'customer_email',
        'customer_shipment_id',
        'customer_shipment_name',
        'customer_shipment_address',
        'customer_shipment_region_name',
        'customer_shipment_city_name',
        'customer_shipment_district_name',
        'customer_shipment_subdistrict_name',
        'customer_shipment_phone_number',
        'customer_shipment_zip_code',
        'shipment_method',
        'shipment_code',
        'shipment_charges',
        'handling_charges',
        'promotion_id',
        'voucher_id',
        'discount_code',
        'discount_type',
        'discount_amount',
        'total_amount',
        'sub_total_amount',
        'pay_amount',
        'commission_amount',
        'payment_method',
        'payment_link',
        'payment_status',
        'payment_date',
        'currency',
        'order_status',
        'sla_date',
        'sla_status',
        'shipping_date',
        'est_shipping_date',
        'delivery_date',
        'est_delivery_date',
        'remarks',
        'location_code',
        'due_payment_date',
        'modified_date',
        'modified_by',
        'created_date',
        'created_by',
        'completed_date'
    ];

    public function all_items()
    {
        return $this->hasMany(OrderItemReseller::class,'order_header_id','id')->with('promotion.promo');
    }

    public function items()
    {
        return $this->hasMany(OrderItemReseller::class,'order_header_id','id')->with('promotion.promo')->where('remarks', null);
    }

    public function item_bundlings(){
    return $this->hasMany(OrderItemReseller::class,'order_header_id', 'id')->with('promotion.promo')->whereNotNull('remarks');
    }
    public function promotion(){
        return $this->hasMany(ResellerPromotion::class,'reference_id', 'id')->where(['reference_name' => 'headers', 'discount_type' => 'promotions']);
    }

    public static function totalOrderItemQty(OrderReseller $m) {
        return $m->items()->sum('qty');
    }
    public static function totalLineAmount(OrderReseller $m) {
        return $m->items()->sum('line_amount');
    }
    public static function totalLineAmountIn(OrderReseller $m, $whereIn = []) {
        return $m->items()->whereIn('article_id',$whereIn)->sum('line_amount');
    }
    public static function totalGrossLineAmount(OrderReseller $m) {
        return $m->items()->sum('total_amount');
    }

    public function scopeOngoing($query){
        return $query->whereIn('order_status', [
            OrderReseller::ORDER_RESELLER_BARU, 
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA,
            OrderReseller::ORDER_RESELLER_SELESAI
        ]);
    }

    public function scopeEligible($query){
        return $query->whereIn('order_status', [
            OrderReseller::ORDER_RESELLER_BARU, 
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA
        ]);
    }

    public function scopeCurrentMonth($query){
        return $query->whereBetween('created_date', 
        [
            Carbon::now()->startOfMonth(), 
            Carbon::now()->endOfMonth()
        ]);
    }

    public function scopeNewOrder($query, $id, $startWeek, $endWeek)
    {
        return $query->Where('reseller_id', '=', $id)
        ->whereBetween('order_date', [$startWeek, $endWeek])
        ->whereIn('order_status', [
            OrderReseller::ORDER_RESELLER_BARU, 
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA
         ])->count("order_no");
    }

    public function scopeCompletedOrder($query, $id, $startWeek, $endWeek)
    {
        return $query->Where('reseller_id', '=', $id)
        ->whereBetween('order_date', [$startWeek, $endWeek])
        ->where('order_status', OrderReseller::ORDER_RESELLER_SELESAI)->count("order_no");
    }
    public function scopeTransactionCount($query, $id, $month, $year)
    {
        return $query->Where("reseller_id", "=", $id)->whereMonth("created_date", $month)
        ->whereYear("created_date", $year)
        ->whereIn("order_status", [
            OrderReseller::ORDER_RESELLER_BARU, 
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA,
            OrderReseller::ORDER_RESELLER_SELESAI            
            ])->count("order_no");
        }

    public function scopeSalesSumTotalAmount($query, $id, $month, $year)
    {
        return $query->Where("reseller_id", "=", $id)->whereMonth("created_date", $month)
        ->whereYear("created_date", $year)
        ->whereIn("order_status", [
            OrderReseller::ORDER_RESELLER_BARU, 
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA,
            OrderReseller::ORDER_RESELLER_SELESAI
         ])
        ->sum("total_amount");
    }

    public function scopeSalesMonthlyPerformance($query, $id, $month, $year)
    {
        return $query->Where("reseller_id", "=", $id)
        ->whereMonth("rsl_order_headers.created_date", $month)
        ->whereYear("rsl_order_headers.created_date", $year)
        ->whereIn("order_status", [
            OrderReseller::ORDER_RESELLER_BARU, 
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA,
            OrderReseller::ORDER_RESELLER_SELESAI
        ]);
    }
    
    public function scopeProductSold($query, $id, $month, $year)
    {
        return $query->Where("reseller_id", "=", $id)
        ->leftJoin('rsl_order_details', 'rsl_order_details.order_header_id', '=', 'rsl_order_headers.id')
        ->whereMonth("rsl_order_headers.created_date", $month)
        ->whereYear("rsl_order_headers.created_date", $year)
        ->whereIn("rsl_order_headers.order_status", [
            OrderReseller::ORDER_RESELLER_BARU, 
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA,
            OrderReseller::ORDER_RESELLER_SELESAI
         ]);
    }

    public function scopecommissionSumAmount($query, $id, $month, $year)
    {
        return $query->Where("reseller_id", "=", $id)->whereMonth("created_date", $month)
        ->whereYear("created_date", $year)
        ->whereIn("order_status", [
            OrderReseller::ORDER_RESELLER_BARU, 
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA,
            OrderReseller::ORDER_RESELLER_SELESAI
         ])
        ->sum("commission_amount");
    }

    public function reseller()
    {
        return $this->belongsTo(Reseller::class,'reseller_id','id');
    }
    public function detail(){
        return $this->hasMany(OrderItemReseller::class,'order_header_id', 'id')->with('promotion.promo')->where('remarks', null);
    }

    public function customer_shipment()
    {
        return $this->belongsTo(ResellerCustomerShipment::class,'customer_shipment_id','id');
    }

    public function order_shipment()
    {
        return $this->belongsTo(ResellerOrderShipment::class,'id','order_header_id');
    }

    public function link()
    {
        return $this->belongsTo(ResellerLink::class,'link_id','id');
    }

    public function promotions(){
        return $this->hasMany(ResellerPromotion::class,'reference_id', 'id');
    }
}