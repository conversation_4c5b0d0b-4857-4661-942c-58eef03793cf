<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Position extends Model
{
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $table = 'position';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'code',
        'name',
        'level',
        'created_by',
        'modified_by',
    ];

    public function authority()
    {
        return $this->hasOne(Authority::class, 'level');
    }
}
