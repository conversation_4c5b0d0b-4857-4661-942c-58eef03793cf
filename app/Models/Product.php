<?php

namespace App\Models;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $table = "article";

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'article';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        // 'article_category',
        'article',
        'article_description',
        'sku_code_c',
        'product_name_c',
        'product_variant_c',
        'product_size_c',
        'product_description',
        'product_style',
        'product_feature',
        'product_tag',
        'product_type',
        'product_gender',
        'product_activity',
        'product_status',
        'fabric_type',
        'lvl2_description',
        'lvl3_description',
        'lvl4_description',
        'season',
        'is_wholesales',
        'is_b2b',
        'deletion_flag',
        'weight',
        'dimension',
        'min_qty',
        'transfer_date',
        'expired_date',
        'created_date',
        'created_by',
        // 'selling_type',
        'modified_date',
        'modified_by',
        'uom',
        'is_custom_logo'
    ];

    public function skuStock()
    {
        return $this->hasOne(ProductSku::class, 'sku_id', 'article');
    }
    public function StockGeneric()
    {
        return $this->hasMany(ProductSku::class, 'sku_code_c', 'sku_code_c');
    }

    public function price()
    {
        return $this->hasOne(ProductPrice::class, 'sku_code_c', 'sku_code_c')
            ->where('valid_from', '<=', now()->format('Y-m-d'))
            ->where('valid_to', '>=', now()->format('Y-m-d'))
            // ->orderBy('valid_from', 'desc')
            ->orderBy('modified_date', 'desc');
        ;
    }

    public function mainImageGeneric()
    {
        return $this->hasOne(ImageGeneric::class, 'sku_code_c', 'sku_code_c')
            ->where('file_path', '!=', '')
            ->where('is_main_image', 1);
    }

    public function mainImageVariant()
    {
        return $this->hasOne(ImageVariant::class, 'article', 'article')
            ->where('file_path', '!=', '')
            ->where('is_main_image', 1);
    }

    public function getImageGenericUrlAttribute()
    {
        return optional($this->mainImageGeneric)->file_path
            ? env('S3_STREAM_URL') . $this->mainImageGeneric->file_path
            : "https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp";
    }

    public function getImageVariantUrlAttribute()
    {
        return optional($this->mainImageVariant)->file_path 
            ? env('S3_STREAM_URL') . $this->mainImageVariant->file_path 
            : "https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp";
    }

    public function thumbnail()
    {
        return $this->hasOne(ProductImage::class, 'article', 'article')->whereIsThumbnail(1);
    }

    public function exclusion()
    {
        return $this->hasMany(ProductExclusion::class, 'article_id', 'article')
            ->where('valid_from', '<=', now()->format('Y-m-d'))
            ->where('valid_to', '>=', now()->format('Y-m-d'));
    }

    public function details()
    {
        $customerType = @Auth::user()->customer->customer_type;
        return $this->hasMany(Product::class, 'sku_code_c', 'sku_code_c')->exclude($customerType);
    }

    public function details_customer($param)
    {
        $customerId = @Auth::user()->customer->customer_id;
        return CustomerStock::where('article_id', 'LIKE', "$param%")->where('customer_id', $customerId)->get()->toArray();
    }

    public function flag($od = null, $req = null)
    {
        $flag = [];

        if ($req || !Auth::check()) {
            $req = new Request();
            $fakeUser = new \stdClass();
            $fakeUser->customer = new \stdClass();
            $fakeUser->reference_object = 'customer';
            $fakeUser->customer->distribution_channel = 'B2B';
            $fakeUser->customer->cart = null;
            $req->setUserResolver(function () use ($fakeUser) {
                return $fakeUser;
            });
            if (($req->user()->customer != null and $req->user()->customer->distribution_channel == 'B2B') or ($od?->order != null and $od->order->distribution_channel == 'B2B')) {
                $flag_custom_master = MasterParameter::where('group_key', 'B2B_CUSTOM')->pluck('value');
                if (in_array($this->lvl3_description, $flag_custom_master->toArray())) {
                    array_push($flag, 'CUSTOM');
                }
            }
        } else {
            if ((auth()->user()->customer != null and auth()->user()->customer->distribution_channel == 'B2B') or ($od?->order != null and $od->order->distribution_channel == 'B2B')) {
                $flag_custom_master = MasterParameter::where('group_key', 'B2B_CUSTOM')->pluck('value');
                if (in_array($this->lvl3_description, $flag_custom_master->toArray())) {
                    array_push($flag, 'CUSTOM');
                }
            }
        }

        $date = now()->format('Y-m-d');
        if ($this->transfer_date <= $date && $this->expired_date >= $date) {
            array_push($flag, 'BARU');
        }
        array_push($flag, $this->lvl4_description);
        return $flag;
    }

    public function scopeExclude($q, $customerType)
    {
        return $q->whereDoesntHave('exclusion', function ($query) use ($customerType) {
            $query->where('customer_type', $customerType);
        })->where(function ($query) use ($customerType) {
            if (in_array($customerType, \App\Models\Customer::$wholesalesCode)) {
                $query->where('is_wholesales', 1);
            } elseif (in_array($customerType, \App\Models\Customer::$b2bCode)) {
                $query->where('is_b2b', 1);
            }
        });
    }

    public function scopeCustomerType($q, $customerType)
    {
        return $q->where(function ($query) use ($customerType) {
            if ($customerType == \App\Models\Customer::$wholesalesCode) {
                $query->where('is_wholesales', 1);
            } elseif ($customerType == \App\Models\Customer::$b2bCode) {
                $query->where('is_b2b', 1);
            }
        });
    }

    public function scopeIsWholesales()
    {
        return $this->where('is_wholesales', 1)->where('wholesales_published_date', '<=', now()->format('Y-m-d'));
    }

    public function scopeIsB2B()
    {
        return $this->where('is_b2b', 1)->where('b2b_published_date', '<=', now()->format('Y-m-d'));
    }

    public static function filter($query, $request)
    {
        if ($category = $request->get('category')) {
            if ($category == 'non-bags') {
                $query = $query->whereNotIn('lvl3_description', ['bags', 'footwear']);
            } else {
                $query = $query->where('lvl3_description', $category);
            }
        }
        if ($subCategoryId = $request->get('subcategory')) {
            $query = $query->where('lvl4_description', $subCategoryId);
        }
        if ($activityId = $request->get('activity')) {
            $query = $query->where('lvl2_description', $activityId);
        }
        if ($color = $request->get('color')) {
            $query = $query->where('product_variant_c', $color);
        }

        return $query;
    }

    public static function getSubCategories($request)
    {
        $subCategories = self::isWholesales()->select('lvl4_description')->whereNotNull('lvl4_description')->where('lvl4_description', '!=', '')->where('lvl4_description', '!=', '-');
        return self::filter($subCategories, $request)->orderBy('lvl4_description')->groupBy('lvl4_description')->pluck('lvl4_description')->toArray();
    }

    public static function getActivities($request)
    {
        // $activities = self::isWholesales()->select('lvl2_description')->whereNotNull('lvl2_description')->where('lvl2_description', '!=', '')->where('lvl2_description', '!=', '-');
        // return self::filter($activities, $request)->orderBy('lvl2_description')->groupBy('lvl2_description')->pluck('lvl2_description')->toArray();
        $activities = self::isWholesales()->select('product_activity')->whereNotNull('product_activity')->where('product_activity', '!=', '')->where('product_activity', '!=', '-');
        return self::filter($activities, $request)->orderBy('product_activity')->groupBy('product_activity')->pluck('product_activity')->toArray();
    }

    public static function getColors($request)
    {
        $color = self::isWholesales()->select('product_variant_c');
        return self::filter($color, $request)->orderBy('product_variant_c')->groupBy('product_variant_c')->pluck('product_variant_c')->toArray();
    }

    public static function getSizes($request)
    {
        $sizes = self::isWholesales()->select('product_size_c')->whereNotNull('product_size_c')->where('product_size_c', '!=', '')->where('product_size_c', '!=', '-');
        return self::filter($sizes, $request)->orderBy('product_size_c')->groupBy('product_size_c')->pluck('product_size_c')->toArray();
    }

    // public function media()
    // {
    //     return $this->join('article_image', 'article.article', '=', 'article_image.article')
    //                 ->where('article_image.article', 'like', $this->sku_code_c.'%')
    //                 ->orderBy('sequence_no')
    //                 ->get();
    // }

    public function getTotalVariantAttribute()
    {
        return self::where('sku_code_c', $this->sku_code_c)->count();
    }
}