<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Proforma extends Model
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATE_AT = 'modified_date';

    public $table = 'proforma_invoice';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'sales_order_no',
        'sales_order_date',
        'due_date',
        'po_no',
        'customer_external_id',
        'customer_name',
        'customer_address',
        'customer_npwp',
        'down_payment',
        'down_payment_percentage',
        'gross_price',
        'nett_price',
        'dpp',
        'tax',
        'currency',
        'signature',
        'signature_job',
        'signature_date',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by'
    ];

    public function detail()
    {
        return $this->hasMany(ProformaDetail::class, 'proforma_invoice_id', 'id');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'customer_external_id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'sales_order_no', 'sales_order_no');
    }
}