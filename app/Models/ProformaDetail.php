<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProformaDetail extends Model
{
    use HasFactory;
    public $table = 'proforma_invoice_detail';
    protected $keyType = 'string';
    public $incrementing = false;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $fillable = [
        'id',
        'proforma_invoice_id',
        'sequence_no',
        'article',
        'sku_code',
        'product_name',
        'product_variant',
        'qty',
        'currency',
        'price',
        'gross_price',
        'nett_price',
        'discount',
        'created_date',
        'created_by',
        'modified_date',
        'modified_by'
    ];

    public function proformaInvoice() {
        return $this -> belongsTo(Proforma::class);
    }
    
    public function product(){
        return $this->belongsTo(Product::class,'article','article');
    }
}
