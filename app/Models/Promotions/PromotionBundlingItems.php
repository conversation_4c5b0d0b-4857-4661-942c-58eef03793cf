<?php

namespace App\Models\Promotions;

use App\Models\Product;
use App\Models\ResellerArticleStock;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;
class PromotionBundlingItems extends FilterableModel
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = "rsl_promotion_bundling_items";
    public $incrementing = false;

    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'promotion_id',
        'promotion_bundling_id',
        'item_type',
        'item_id',
        'created_by',
        'modified_by'
    ];
    
    public static $validator = [
        'ext_id'        => 'required|string|unique:rsl_promotions|distinct',
        'promotion_id' => 'required|numeric|exists:rsl_promotions,ext_id',
        'minimum_value' => 'nullable|numeric',
        'sequence' => 'nullable|numeric'
    ];

    public function bundlings()
    {
        return $this->belongsTo(PromotionBundlings::class, 'id', 'promotion_bundling_id');
    }

    public function article(){
        return $this->belongsTo(Product::class, 'item_id', 'article');
    }
    public function stock(){
        return $this->belongsTo(ResellerArticleStock::class, 'item_id', 'article');
    }

    public function promotions()
    {
        return $this->belongsTo(Promotions::class, 'id', 'promotion_id');
    }
}
