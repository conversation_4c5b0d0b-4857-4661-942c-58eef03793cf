<?php

namespace App\Models\Promotions;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;
class PromotionBundlings extends FilterableModel
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = "rsl_promotion_bundlings";
    public $incrementing = false;

    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'promotion_id',
        'sequence',
        'minimum_value',
        'created_by',
        'modified_by'
    ];
    
    public static $validator = [
        'ext_id'        => 'required|string|unique:rsl_promotions|distinct',
        'promotion_id' => 'required|numeric|exists:rsl_promotions,ext_id',
        'minimum_value' => 'nullable|numeric',
        'sequence' => 'nullable|numeric'
    ];

    public function items()
    {
        return $this->hasMany(PromotionBundlingItems::class, 'promotion_bundling_id')->with(['article'  => function ($query) {
            $query->select('article','article_description','sku_code_c', 'product_name_c','product_size_c','product_variant_c')->with(['price' => fn ($q) => $q->select('sku_code_c','amount'), 'mainImageVariant']);
        }, 'stock']);
    }

    public function promotions()
    {
        return $this->belongsTo(Promotions::class, 'id', 'promotion_id');
    }
}
