<?php

namespace App\Models\Promotions;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;
class PromotionExceptionItems extends FilterableModel
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = "rsl_promotion_exception_items";
    public $incrementing = false;

    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'promotion_id',
        'item_type',
        'item_id',
        'created_by',
        'modified_by'
    ];

    public static $validator = [
        'ext_id'        => 'required|string|unique:rsl_promotions|distinct',
        'promotion_id' => 'required|numeric|exists:rsl_promotions,ext_id',
        'item_type' => 'nullable|string',
        'item_id' => 'nullable|string',
        'created_at' => 'nullable|date_format:Y-m-d H:i:s',
        'updated_at' => 'nullable|date_format:Y-m-d H:i:s',
    ];
    
    public function promotions()
    {
        return $this->belongsTo(Promotions::class, 'promotion_id', 'id');
    }
    public function scopeItem($q, $d){
        return $q->where('item_id',$d);
    }
}
