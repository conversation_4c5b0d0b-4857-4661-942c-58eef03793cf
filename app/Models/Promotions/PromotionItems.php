<?php

namespace App\Models\Promotions;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;
class PromotionItems extends FilterableModel
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = "rsl_promotion_items";
    public $incrementing = false;

    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'promotion_id',
        'item_type',
        'item_id',
        'discount_amount',
        'max_value',
        'deleted_at',
        'deleted_by',
        'created_by',
        'modified_by'
    ];

    public static $validator = [
        'ext_id' => 'required|string|unique:rsl_promotions|distinct',
        'promotion_id' => 'required|numeric|exists:rsl_promotions,ext_id',
        'item_type' => 'nullable|string',
        'item_id' => 'nullable|string',
        'discount_amount' => 'required|regex:/[\d]{2},[\d]{2}/',
        'max_value' => 'nullable|numeric',
        'created_at' => 'nullable|date_format:Y-m-d H:i:s',
        'updated_at' => 'nullable|date_format:Y-m-d H:i:s'
    ];

    protected $hidden = ['created_date, modified_date', 'deleted_at', 'modified_by', 'created_by', 'deleted_by', 'id', 'id','promotion_id'];

    public function promotions()
    {
        return $this->belongsTo(Promotions::class, 'promotion_id', 'id');
    }

    public function scopeItem($q, $d){
        return $q->where('item_id',$d);
    }
}
