<?php

namespace App\Models\Promotions;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;
class PromotionParameters extends FilterableModel
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = "rsl_promotion_parameters";
    public $incrementing = false;

    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'promotion_id',
        'type',
        'statement',
        'value',
        'edc_id',
        'card_type_id',
        'prefix',
        'gender',
        'payment_type_id',
        'deleted_at',
        'deleted_by',
        'created_by',
        'modified_by'
    ];

    public static $validator = [
        'ext_id'        => 'required|string|unique:rsl_promotions|distinct',
        'promotion_id' => 'required|numeric|exists:rsl_promotions,ext_id',
        'type' => 'nullable|string',
        'statement' => 'nullable|string',
        'value' => 'nullable|numeric',
        'edc_id' => 'nullable|numeric',
        'card_type_id' => 'nullable|numeric',
        'prefix' => 'nullable|string',
        'gender' => 'nullable|string',
        'payment_type_id' => 'nullable|numeric',
    ];

    public function promotions()
    {
        return $this->belongsTo(Promotions::class, 'id', 'promotion_id');
    }
}
