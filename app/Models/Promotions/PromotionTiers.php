<?php

namespace App\Models\Promotions;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;
class PromotionTiers extends FilterableModel
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = "rsl_promotion_tiers";
    public $incrementing = false;

    protected $primaryKey = 'id';

    protected $fillable = [
        'id',
        'promotion_id',
        'sequence_no',
        'minimum_qty',
        'discount_amount',
        'maximum_value',
        'n_customer',
        'created_by',
        'modified_by'
    ];
    
    public static $validator = [
        'ext_id'        => 'required|string|unique:rsl_promotions|distinct',
        'promotion_id'          => 'required|numeric|exists:rsl_promotions,ext_id',
        'sequence'   => 'nullable|numeric',
        'mininum_qty'        => 'nullable|numeric',
        'discount_amount'        => 'required|regex:/[\d]{2},[\d]{2}/',
        'maximum_value'        => 'nullable|numeric',
        'n_customer'        => 'nullable|numeric'
    ];

    public function promotions()
    {
        return $this->belongsTo(Promotions::class, 'id', 'promotion_id');
    }
}
