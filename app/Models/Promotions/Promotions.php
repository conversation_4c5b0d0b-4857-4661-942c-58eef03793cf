<?php

namespace App\Models\Promotions;

use App\Models\ResellerPromotion;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;

class Promotions extends FilterableModel
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = "rsl_promotions";
    public $incrementing = false;

    protected $primaryKey = 'id';

    protected $hidden = ['
    created_date, 
    modified_date', 
    'deleted_at', 
    'modified_by', 
    'created_by', 
    'deleted_by', 
    'boolean',
    'loadshare_discount_eiger',
    'loadshare_discount_partner',
    'sunday_enabled',
    'sunday_start_at',
    'sunday_end_at',
    'monday_enabled',
    'monday_start_at',
    'monday_end_at',
    'tuesday_enabled',
    'tuesday_start_at',
    'tuesday_end_at',
    'wednesday_enabled',
    'wednesday_start_at',
    'wednesday_end_at',
    'thursday_enabled',
    'thursday_start_at',
    'thursday_end_at',
    'friday_enabled',
    'friday_start_at',
    'friday_end_at',
    'saturday_enabled',
    'saturday_start_at',
    'saturday_end_at',
    'is_loadshare',
    'coupon_tnc',
    'coupon_generated_multiple',
    'is_homepage',
    'schema',
    'partner',
    'customer_group_id',
    'start_date',
    'end_date',
];


    protected $fillable = [
        'id',
        'name',
        'description',
        'customer_group_id',
        'status',
        'action',
        'start_date',
        'end_date',
        'discount_type',
        'item_discount',
        'item_bundling',
        'item_exception',
        'discount_amount',
        'point_amount',
        'is_all_location',
        'is_include_new_store',
        'based_on',
        'coupon_expired_days',
        'coupon_expired_at',
        'first_n_customers',
        'boolean',
        'loadshare_discount_eiger',
        'loadshare_discount_partner',
        'sunday_enabled',
        'sunday_start_at',
        'sunday_end_at',
        'monday_enabled',
        'monday_start_at',
        'monday_end_at',
        'tuesday_enabled',
        'tuesday_start_at',
        'tuesday_end_at',
        'wednesday_enabled',
        'wednesday_start_at',
        'wednesday_end_at',
        'thursday_enabled',
        'thursday_start_at',
        'thursday_end_at',
        'friday_enabled',
        'friday_start_at',
        'friday_end_at',
        'saturday_enabled',
        'saturday_start_at',
        'saturday_end_at',
        'is_loadshare',
        'coupon_tnc',
        'coupon_generated_multiple',
        'is_homepage',
        'schema',
        'partner',
        'max_value',
        'discount_mechanism',
        'is_multiple_qty',
        'multiple_amount',
        'max_qty',
        'deleted_at',
        'deleted_by',
        'created_by',
        'modified_by'
    ];

    public static $validator = [
        'ext_id' => 'required|string|unique:rsl_promotions|distinct',
        'name' => 'required|string',
        'description' => 'required|string',
        'status' => 'nullable|string|in:active',
        'action' => 'required|string|in:Single Discount,Tier Discount,Bogo Discount',
        'start_date' => 'required|date',
        'end_date' => 'required|date|after_or_equal:start_date',
        'discount_type' => 'required|string|in:value,percentage',
        'item_discount' => 'required|string|in:article,brand,brand_teritory,sku_group,product_category,product_group',
        'item_bundling' => 'required|string:in:article,brand,sku_group',
        'item_exception' => 'required|string|in:article,brand,brand_teritory,sku_group,product_category,product_group',
        'discount_amount' => 'required|regex:/[\d]{2},[\d]{2}/',
        'based_on' => 'required|string|in:bill,gross'
    ];

    public function scopeActive($q)
    {
        return $q->where('status', 'active');
        // return $q;
    }

    public function scopeActiveDate($q)
    {
        return $q->where('start_date', '<=', now()->format('Y-m-d'))
            ->where('end_date', '>=', now()->format('Y-m-d'));
    }

    public function scopeActiveDay($q, $d)
    {
        return $q->where($d, 1)->orWhere($d, null);
    }

    public function scopeActiveDayDate($q, $s, $e)
    {
        return $q->where($s, '<=', now()->format('H:i:s'))->orWhere($s, null)
            ->where($e, '>=', now()->format('H:i:s'))->orWhere($e, null);
    }

    public function scopeArticle($q, $d)
    {
        return $q->where($d, 1);
    }

    public static function totalUsage($q)
    {
        return $q->usage->pluck('items.order')->unique('order_no')->count();  
        // return $q->usage()->sortBy('reference_id')->count();
    }

    public function usage() 
    {
        return $this->hasMany(ResellerPromotion::class, 'discount_id', 'id')->with('items.order')->where('rsl_order_promotions.discount_type', 'promotions')->groupBy('reference_id');
    }


    public function items()
    {
        return $this->hasMany(PromotionItems::class, 'promotion_id');
    }

    public function parameters()
    {
        return $this->hasMany(PromotionParameters::class, 'promotion_id');
    }

    public function bundlings()
    {
        return $this->hasMany(PromotionBundlings::class, 'promotion_id');
    }

    public function tiers()
    {
        return $this->hasMany(PromotionTiers::class, 'promotion_id');
    }

    public function exception_items()
    {
        return $this->hasMany(PromotionExceptionItems::class, 'promotion_id');
    }
}
