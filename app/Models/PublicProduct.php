<?php

namespace App\Models;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PublicProduct extends Model
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $table = "article";

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'article';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */

    public function price()
    {
        return $this->hasOne(ProductPrice::class, 'sku_code_c', 'sku_code_c')
                    ->where('valid_from', '<=', now()->format('Y-m-d'))
                    ->where('valid_to', '>=', now()->format('Y-m-d'))
                    ->latest();
    }

    public function mainImageGeneric()
    {
        return $this->hasOne(ImageGeneric::class, 'sku_code_c', 'sku_code_c')
                    ->where('file_path','!=','')
                    ->where('is_main_image',1);
    }
    public function mainImageVariant()
    {
        return $this->hasOne(ImageVariant::class, 'article', 'article')
                    ->where('file_path','!=','')
                    ->where('is_main_image',1);
    }

    public function thumbnail()
    {
        return $this->hasOne(ProductImage::class, 'article', 'article')->whereIsThumbnail(1);
    }

    public function exclusion()
    {
        return $this->hasMany(ProductExclusion::class, 'article_id', 'article')
                 ->where('valid_from', '<=', now()->format('Y-m-d'))
                 ->where('valid_to', '>=', now()->format('Y-m-d'));
    }

    public function details()
    {
        return $this->hasMany(Product::class, 'sku_code_c', 'sku_code_c');
    }


    public function flag($od = null)
    {
        $flag = [];
        $date = now()->format('Y-m-d');
        if ($this->transfer_date <= $date && $this->expired_date >= $date) {
            array_push($flag, 'BARU');
        }
        if($this->lvl4_description){
            array_push($flag, $this->lvl4_description);
        }
        return $flag;
    }


    public static function filter($query, $request)
    {
        if ($category = $request->get('category')) {
            if ($category == 'non-bags') {
                $query = $query->whereNotIn('lvl3_description', ['bags', 'footwear']);
            } else {
                $query = $query->where('lvl3_description', $category);
            }
        }
        if ($subCategoryId = $request->get('subcategory')) {
            $query = $query->where('lvl4_description', $subCategoryId);
        }
        if ($activityId = $request->get('activity')) {
            $query = $query->where('lvl2_description', $activityId);
        }
        if ($color = $request->get('color')) {
            $query = $query->where('product_variant_c', $color);
        }

        return $query;
    }

    public static function getSubCategories($request)
    {
        $subCategories = self::isWholesales()->select('lvl4_description')->whereNotNull('lvl4_description')->where('lvl4_description', '!=', '')->where('lvl4_description', '!=', '-');
        return self::filter($subCategories, $request)->orderBy('lvl4_description')->groupBy('lvl4_description')->pluck('lvl4_description')->toArray();
    }

    public static function getActivities($request)
    {
        $activities = self::isWholesales()->select('lvl2_description')->whereNotNull('lvl2_description')->where('lvl2_description', '!=', '')->where('lvl2_description', '!=', '-');
        return self::filter($activities, $request)->orderBy('lvl2_description')->groupBy('lvl2_description')->pluck('lvl2_description')->toArray();
    }

    public static function getColors($request)
    {
        $color = self::isWholesales()->select('product_variant_c');
        return self::filter($color, $request)->orderBy('product_variant_c')->groupBy('product_variant_c')->pluck('product_variant_c')->toArray();
    }

    public static function getSizes($request)
    {
        $sizes = self::isWholesales()->select('product_size_c')->whereNotNull('product_size_c')->where('product_size_c', '!=', '')->where('product_size_c', '!=', '-');
        return self::filter($sizes, $request)->orderBy('product_size_c')->groupBy('product_size_c')->pluck('product_size_c')->toArray();
    }
    
    public function color()
    {
        return $this->belongsTo(Color::class,'product_variant_c','key');
    }

    public function rsl_stock()
    {
        return $this->hasMany(ResellerArticleStock::class,'article','article');
    }

    public function getColorValueAttribute()
    {
        return $this->color ? $this->color->value : null;
    }
}
