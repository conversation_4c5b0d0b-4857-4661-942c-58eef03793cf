<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\FilterableModel;

class Region extends FilterableModel
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $table = 'region';
    protected $keyType = 'string';

    protected $fillable = [
        'code',
        'country_code',
        'name',
        'island',
        'id',
        'country_id'
    ];
}
