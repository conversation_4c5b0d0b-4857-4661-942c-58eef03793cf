<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class   Reseller extends Model
{
    use HasFactory;
    
    public $table = "rsl_reseller";

    const RESELLER_ACTIVE = 1;
    const RESELLER_INACTIVE = 0;
    const RESELLER_REJECTED = 9;
    
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'reseller_id',
        'name',
        'gender',
        'date_of_birth',
        'phone_number',
        'email',
        'address',
        'province_code',
        'city_code',
        'district_code',
        'zip_code',
        'national_id',
        'national_id_file',
        'npwp',
        'npwp_file',
        'is_active',
        'created_by',
        'modified_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'reference_id', 'customer_id');
    }

    public function scopeFetch($q, $args)
    {
        return $q->where('reseller_id', $args);
    }

    public function link()
    {
        return $this->hasMany(ResellerLink::class,'reseller_id','id');
    }

    public function orders()
    {
        return $this->hasMany(OrderReseller::class,'reseller_id','id');
    }

    public function commissions()
    {
        return $this->hasMany(Commission::class,'reseller_id','id');
    }
    
    public function ledgers()
    {
        return $this->hasMany(CommissionLedgers::class,'reseller_id','id');
    }
    public function withdrawals()
    {
        return $this->hasMany(CommissionWithdrawal::class,'reseller_id','id');
    }
    public function bankAccounts()
    {
        return $this->hasMany(ResellerBank::class,'reseller_id','id');
    }
    public function va()
    {
        return $this->hasOne(ResellerVA::class,'reseller_id','id');
    }

    public static function resellerStatusEnum($request){
        switch ($request) {
            case self::RESELLER_ACTIVE:
                $request = 'active';
                break;
            case self::RESELLER_REJECTED:
                $request = 'freeze';
                break;
            case self::RESELLER_INACTIVE:
                $request = 'non-active';
                break;
            default:
                $request;
                break;
        }

        return $request;
    }

    public static function resellerStatusEnumMapping($request){
        switch ($request) {
            case 'active':
                $request = self::RESELLER_ACTIVE;
                break;
            case 'freeze':
                $request = self::RESELLER_REJECTED;
                break;
            case 'non_active':
                $request = self::RESELLER_INACTIVE;
                break;
            default:
                $request;
                break;
        }

        return $request;
    }

    public static function resellerAddress(Reseller $reseller){
        $province = Region::select('name')->where('code',$reseller->province_code)->first()->name ?? '-';
        $city = City::select('name')->where('id',$reseller->city_code)->first()->name ?? '-';
        $district = District::select('name')->where('code',$reseller->district_code)->first()->name ?? '-';
        $zipCode = Subdistrict::select('postal_code')->where('code',$reseller->zip_code)->first()->postal_code ?? $reseller->zip_code;
        $address = $reseller->address.', '.$district.', '.$city.', '.$province.', '.$zipCode;

        return $address;
    }

}
