<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerArticleStock extends Model
{
    use HasFactory;

    protected $table = 'rsl_article_stock';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $casts = [
        'created_date' => 'datetime:Y-m-d H:i:s',
        'modified_date' => 'datetime:Y-m-d H:i:s'
    ];

    protected $fillable = [
        'article',
        'location_code',
        'stock',
        'booked_stock',
        'available_stock',
        'created_by',
        'modified_by'
    ];
}
