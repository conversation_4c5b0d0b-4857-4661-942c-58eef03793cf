<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerBank extends Model
{
    use HasFactory;

    protected $table = 'rsl_reseller_bank';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $primaryKey = 'id';

    public $incrementing = false;

    protected $keyType = 'string';

    protected $fillable = [
        'reseller_id',
        'bank_id',
        'account_name',
        'account_no',
        'is_active',
        'created_by',
        'modified_by'
    ];

    public function bank()
    {
        return $this->hasOne(MasterBank::class, 'id', 'bank_id');
    }

}
