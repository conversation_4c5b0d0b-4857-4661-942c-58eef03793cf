<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerCart extends FilterableModel
{
    use HasFactory;
    
    public $table = "rsl_cart";
    
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'link_id',
        'customer_id',
        'created_by',
        'modified_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime',
    ];

    public function cart_detail()
    {
        return $this->hasMany(ResellerCartDetail::class, 'cart_id', 'id');
    }

    public function customers()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'customer_id');
    }


    public function scopeFetch($q, $customer_id, $link_id){
        return $q->where(['customer_id'=> $customer_id, 'link_id' => $link_id]);
                //  ->with(['customers', 'cart_detail.article' => function($order_detail) {
                //     $order_detail
                //     ->with(['image_generic' => function($query) {
                //         $query->onlyImage();
                //    },
                //     'article_price' => function($query) {
                //         $query->validPrice();
                //    }], 'flag')
                //     ->desc('created_date')
                //     ->group('sku_code_c');
                // }]);
                
    }
}
