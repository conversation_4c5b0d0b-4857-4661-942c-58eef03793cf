<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerCartDetail extends FilterableModel
{
    use HasFactory;
    
    public $table = "rsl_cart_detail";
    
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'cart_id',
        'article_id',
        'qty',
        'created_by',
        'modified_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime',
    ];
    public function article()
    {
        return $this->hasOne(PublicProduct::class, 'article', 'article_id');
    }
    public function article_detail()
    {
        return $this->hasOne(Article::class, 'article', 'article_id');
    }
}
