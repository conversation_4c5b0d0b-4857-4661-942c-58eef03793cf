<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerCommission extends Model
{
    use HasFactory;

    protected $table = 'rsl_commission';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $primaryKey = 'id';

    public $incrementing = false;

    protected $keyType = 'string';
    
    protected $fillable = [
        'reseller_id',
        'potential_amount',
        'commission_amount',
        'created_by',
        'modified_by'
    ];

}
