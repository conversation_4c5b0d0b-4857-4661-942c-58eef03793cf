<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerCommissionLedger extends Model
{
    use HasFactory;

    protected $table = 'rsl_commission_ledgers';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $primaryKey = 'id';

    public $incrementing = false;

    protected $keyType = 'string';
    
    protected $fillable = [
        'commission_id',
        'transaction_id',
        'reseller_id',
        'type',
        'commission_type',
        'amount',
        'remarks',
        'created_by',
        'modified_by'
    ];
}
