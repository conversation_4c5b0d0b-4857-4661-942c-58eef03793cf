<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerCommissionWithdrawal extends Model
{
    use HasFactory;

    protected $table = 'rsl_commission_withdrawal';

    const WAITING_FOR_APPROVAL = 'Menunggu Persetujuan';
    const APPROVED = 'Disetujui';
    const REJECTED = 'Ditolak';
    const FAILED = 'Gagal';
    const SUCCESS = 'Sukses';
    
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $primaryKey = 'id';

    public $incrementing = false;

    protected $keyType = 'string';
    
    protected $fillable = [
        'reseller_id',
        'request_id',
        'ext_request_id',
        'bank_name',
        'request_date',
        'account_name',
        'account_no',
        'payment_date',
        'tax_reference',
        'tax_metadata',
        'status',
        'transfer_method',
        'amount',
        'transfer_fee',
        'tax_amount',
        'total',
        'payout_amount',
        'action_by',
        'action_type',
        'action_notes',
        'action_date',
        'created_by',
        'modified_by'
    ];

    public function rsl_bank()
    {
        return $this->hasOne(ResellerBank::class, 'reseller_id', 'reseller_id');
    }

}
