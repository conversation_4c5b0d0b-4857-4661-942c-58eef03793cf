<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerCustomer extends Model
{
    use HasFactory;

    protected $table = 'rsl_customers';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $primaryKey = 'id';

    public $incrementing = false;

    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'name',
        'phone_number',
        'email',
        'is_active',
        'created_by',
        'modified_by'
    ];

    public function scopeRegistered($q)
    {
        return $q->where('name','!=','')->where('phone_number','!=','');
    }

    public function customer_shipment()
    {
        return $this->hasMany(ResellerCustomerShipment::class,'customer_id','id');
    }
}
