<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerCustomerActivation extends Model
{
    use HasFactory;

    protected $table = 'rsl_customer_activation_link';
    
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $primaryKey = 'id';

    public $incrementing = false;

    protected $keyType = 'string';

    protected $fillable = [
        'customer_id',
        'token',
        'expired_at',
        'activation_at'
    ];

}
