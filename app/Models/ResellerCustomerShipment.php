<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerCustomerShipment extends Model
{
    use HasFactory;

    protected $table = 'rsl_customer_shipments';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $primaryKey = 'id';

    public $incrementing = false;

    protected $keyType = 'string';

    protected $fillable = [
        'customer_id',
        'name',
        'email',
        'phone_number',
        'address',
        'region_code',
        'region_name',
        'city_code',
        'city_name',
        'district_code',
        'district_name',
        'subdistrict_code',
        'subdistrict_name',
        'zip_code',
        'is_active',
        'created_by',
        'modified_by'
    ];
}
