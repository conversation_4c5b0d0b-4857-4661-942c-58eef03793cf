<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerDanamonToken extends Model
{
    use HasFactory;
    
    public $table = "rsl_danamon_token";

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'token',
        'expired_date'
    ];
    public $timestamps = false;
}
