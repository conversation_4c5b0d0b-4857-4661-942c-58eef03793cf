<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;

class ResellerJNELocation extends FilterableModel
{
    use HasFactory;

    public $timestamps = false;
    protected $table = 'rsl_location_jne';
    protected $keyType = 'string';

    protected $primaryKey = null;
    public $incrementing = false;


    protected $fillable = [
        'code',
        'province',
        'city',
        'district',
        'sub_district',
        'zip    '
    ];
}
