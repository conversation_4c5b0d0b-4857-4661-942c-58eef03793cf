<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerLink extends Model
{
    use HasFactory;
    
    public $table = "rsl_reseller_link";
    
    const TYPE_STORE  = "STORE";
    const TYPE_LIST   = "LIST";
    const TYPE_DETAIL = "DETAIL";

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'reseller_id',
        'type',
        'identifier',
        'metadata',
        'created_by',
        'modified_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime',
    ];

    public function reseller()
    {
        return $this->hasOne(Reseller::class, 'id', 'reseller_id');
    }

    public function history()
    {
        return $this->hasMany(ResellerLinkHistory::class,'link_id','id');
    }

}
