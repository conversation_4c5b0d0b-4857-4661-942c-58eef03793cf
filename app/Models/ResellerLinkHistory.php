<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerLinkHistory extends Model
{
    use HasFactory;
    
    public $table = "rsl_reseller_link_history";
    
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'link_id',
        'created_by',
        'modified_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime',
    ];

    public function reseller_link()
    {
        return $this->hasMany(ResellerLink::class, 'id', 'link_id');
    }

    public function scopeMonthPeriod($query,$carbon_date)
    {
        return $query->whereYear('created_date',$carbon_date->year)
                    ->whereMonth('created_date',$carbon_date->month);
    }

    public function scopeLinkClickCount($query, $links, $month, $year)
    {
        return $query->whereIn("link_id", $links)->whereMonth("created_date", $month)
        ->whereYear("created_date", $year)
        ->count();
    }
}
