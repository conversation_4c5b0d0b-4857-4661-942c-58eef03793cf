<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerMasterSite extends Model
{
    use HasFactory;

    public $table = "rsl_master_site";
    protected $keyType = 'string';
    public $incrementing = false;
    protected $primaryKey = 'code';

    public function ma()
    {
        return $this->belongsTo(MasterAddress::class,'zip_code','zip_code');
    }
}
