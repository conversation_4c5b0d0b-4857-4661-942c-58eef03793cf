<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class ResellerOrderDetails extends FilterableModel
{
    use HasFactory;

    public $table = "rsl_order_details";
    
    protected $fillable = [
            "id",
            'order_header_id',
            'sku_code',
            'article_id',
            'product_name',
            'product_variant',
            'product_size',
            'qty',
            'additional_qty',
            'unit_price',
            'base_uom',
            'line_amount',
            'discount_amount',
            'total_amount',
            'remarks',
            'created_by',
            'modified_by'
        ];
        
    protected $primaryKey = 'id';
    protected $keyType = 'string';
    public $incrementing = false;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public function order(){
        return $this->belongsTo(ResellerOrderHeaders::class,'order_header_id', 'id');
    }
    public function promotion(){
        return $this->hasOne(ResellerPromotion::class,'reference_id', 'id')->where('discount_type', 'promotions');
    }
    public function bundlings(){
        return $this->hasMany(ResellerPromotion::class,'reference_id', 'id')->where('discount_type', 'bundlings');
    }
    public function mainImageVariant()
    {
        return $this->hasOne(ImageVariant::class, 'article', 'article_id')
                    ->where('file_path','!=','')
                    ->where('is_main_image',1);
    }
}
