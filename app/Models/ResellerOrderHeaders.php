<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class ResellerOrderHeaders extends FilterableModel
{
    use HasFactory;

    public $table = "rsl_order_headers";
    
    protected $fillable = [
                        'order_no',
                        'ext_order_id',
                        'invoice_no',
                        'order_date',
                        'completed_date',
                        'reseller_id',
                        'link_id',
                        'customer_id',
                        'customer_name',
                        'customer_phone_number',
                        'customer_email',
                        'customer_shipment_id',
                        'customer_shipment_name',
                        'customer_shipment_address',
                        'customer_shipment_region_name',
                        'customer_shipment_city_name',
                        'customer_shipment_subdistrict_name',
                        'customer_shipment_zip_code',
                        'shipment_method',
                        'transporter_id',
                        'shipment_charges',
                        'handling_charges',
                        'sub_total_amount',
                        'discount_amount',
                        'total_amount',
                        'pay_amount',
                        'payment_method',
                        'payment_link',
                        'payment_status',
                        'payment_date',
                        'due_payment_date',
                        'commission_amount',
                        'currency',
                        'order_status',
                        'ext_order_status',
                        'sla_date',
                        'sla_status',
                        'remarks',
                        'created_by',
                        'modified_by'
                    ];
                    
    protected $keyType = 'string';
    public $incrementing = false;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    const ORDER_RESELLER_PENDING = 'Pending';
    const ORDER_RESELLER_BARU = 'Baru';
    const ORDER_RESELLER_BATAL = 'Batal';
    const ORDER_RESELLER_DIPROSES = 'Diproses';
    const ORDER_RESELLER_DIKEMAS = 'Dikemas';
    const ORDER_RESELLER_DIKIRIM = 'Dikirim';
    const ORDER_RESELLER_DITERIMA = 'Diterima';
    const ORDER_RESELLER_SELESAI = 'Selesai';
    const ORDER_RESELLER_PENGEMBALIAN = 'Pengembalian';
    const ORDER_RESELLER_MENUNGGU_PEMBAYARAN = 'Menunggu Pembayaran';

    public function detail(){
        return $this->hasMany(ResellerOrderDetails::class,'order_header_id', 'id')->with('promotion.promo')->where('remarks', null);
    }
    public function detail_bundling(){
        return $this->hasMany(ResellerOrderDetails::class,'order_header_id', 'id')->with('promotion.promo')->whereNotNull('remarks');
    }
    public function promotion(){
        return $this->hasMany(ResellerPromotion::class,'reference_id', 'id')->where(['reference_name' => 'headers', 'discount_type' => 'promotions']);
    }

    public function bundlings(){
        return $this->hasMany(ResellerPromotion::class,'reference_id', 'id')->where(['reference_name' => 'headers', 'discount_type' => 'bundlings']);
    }

    public static function totalOrderItemQty(ResellerOrderHeaders $m) {
        return $m->detail()->sum('qty');
    }
    public static function totalOrderItemQtyIn(ResellerOrderHeaders $m, $whereIn = []) {
        return $m->detail()->whereIn('article_id',$whereIn)->sum('qty');
    }
    public static function totalLineAmount(ResellerOrderHeaders $m) {
        return $m->detail()->sum('line_amount');
    }
    public static function totalLineAmountIn(ResellerOrderHeaders $m, $whereIn = []) {
        return $m->detail()->whereIn('article_id',$whereIn)->sum('line_amount');
    }
    public static function totalGrossLineAmount(ResellerOrderHeaders $m) {
        return $m->detail()->sum('total_amount');
    }
    public static function adjust(ResellerOrderHeaders $m)
    {
        $gross = $m->detail()->sum('total_amount');
        $nett = $m->detail()->sum('line_amount');
        $disc = $m->detail()->sum('discount_amount');
        return $m->update(['sub_total_amount' => $nett, 
        'total_amount' => $gross,
    'discount_amount' => $disc,
    'pay_amount' => $gross + $m->shipment_charges??0]);

    }
}
