<?php

namespace App\Models;

use App\Models\Promotions\Promotions;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerOrderPromotion extends Model
{
    use HasFactory;

    use HasFactory;
    
    public $table = "rsl_order_promotions";

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    public $incrementing = false;
    
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $fillable = [
        'reference_name',
        'reference_id',
        'discount_type',
        'discount_id',
        'amount',
        'bundling_code',
        'created_by',
        'modified_by'
    ];
    
    public function promotion(){
        return $this->hasOne(Promotions::class,'id', 'discount_id');
    }
    public function voucher(){
        return $this->hasOne(Voucher::class,'id', 'discount_id');
    }
    public function coupon(){
        return $this->hasOne(Coupon::class,'id', 'discount_id');
    }
    public function header(){
        return $this->belongsTo(ResellerOrderHeaders::class,'reference_id','id');
    }
    public function detail(){
        return $this->belongsTo(ResellerOrderDetails::class,'reference_id','id');
    }

    public function promo(){
        return $this->belongsTo(Promotions::class,'discount_id', 'id');
    }
}
