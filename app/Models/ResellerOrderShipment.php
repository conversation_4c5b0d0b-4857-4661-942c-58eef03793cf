<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerOrderShipment extends FilterableModel
{
    use HasFactory;

    public $table = "rsl_order_shipments";
    
    const PICKED = 'Picked';
    const PARTIAL_PICKED = 'Partial Picked';
    const PACKED = 'Packed';
    const PARTIAL_PACKED = 'Partial Packed';
    const PARTIAL_SHIPPED = 'Partial Shipped';
    const SHIPPED_COMPLETE = 'Shipped Complete';
    const ARRIVED = 'Arrived';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $fillable = [
        'order_header_id',
        'location_code',
        'transporter_id',
        'awb_no',
        'delivery_number',
        'shipment_status',
        'order_status',
        'modified_date',
    ];
}
