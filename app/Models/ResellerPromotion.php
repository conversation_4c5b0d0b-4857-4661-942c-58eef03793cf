<?php

namespace App\Models;

use App\Models\Promotions\Promotions;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerPromotion extends FilterableModel
{
    
    public $table = "rsl_order_promotions";
    
    protected $fillable = [
        'reference_name',
        'reference_id',
        'discount_type',
        'discount_id',
        'amount',
        'bundling_code',
        'created_by',
        'modified_by',
        ];
        
    protected $keyType = 'string';
    public $incrementing = false;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';
    protected static function boot()
    {
        parent::boot();
        static::updated(function ($model) {
            // $model->order->adjust($model->order);
            LogIntegration::create([
            'reference_no' => $model->order->order_no,
            'module' => 'Promotion Engine',
            'name' => "Promotion Apply {$model->order->order_no}",
            'type' => 'Internal',
            'status' => 'success',
            'description' => json_encode($model),
            'created_by' => 'SYSTEM',
            'updated_by' => 'SYSTEM']);
        });
    }
    use HasFactory;

    public function order(){
        return $this->belongsTo(ResellerOrderHeaders::class,'reference_id', 'id');
    }

    public function promo(){
        return $this->belongsTo(Promotions::class,'discount_id', 'id');
    }
    public function coupon(){
        return $this->belongsTo(Coupon::class,'discount_id', 'id');
    }
    public function voucher(){
        return $this->belongsTo(Voucher::class,'discount_id', 'id');
    }
    public function items(){
        return $this->belongsTo(ResellerOrderDetails::class,'reference_id','id');
    }
}
