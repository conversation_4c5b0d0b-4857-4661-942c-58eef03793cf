<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerRegistration extends Model
{
    use HasFactory;
    
    public $table = "rsl_registration";

    const REGISTRATION_CREATED = 0;
    const REGISTRATION_APPROVED = 1;
    const REGISTRATION_REJECTED = 9;
    
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'reseller_id',
        'email',
        'name',
        'gender',
        'date_of_birth',
        'phone_number',
        'password',
        'address',
        'province_code',
        'city_code',
        'district_code',
        'zip_code',
        'national_id',
        'national_id_file',
        'npwp',
        'npwp_file',
        'status',
        'action_by',
        'action_notes',
        'created_by',
        'modified_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime',
    ];

    public function user()
    {
        return $this->hasOne(User::class, 'reference_id', 'customer_id');
    }

}
