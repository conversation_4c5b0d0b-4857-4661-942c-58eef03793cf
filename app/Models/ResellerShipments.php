<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;

class ResellerShipments extends FilterableModel
{
    use HasFactory;

    public $timestamps = false;
    protected $table = 'rsl_shipments_mapping';
    protected $keyType = 'string';
    public $incrementing = false;


    protected $fillable = [
        'service_code',
        'service_name'
    ];
}
