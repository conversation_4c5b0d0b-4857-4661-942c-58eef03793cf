<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerToken extends Model
{
    use HasFactory;

    public $table = "rsl_token";

    public $primaryKey = 'id';

    public $incrementing = false;

    protected $keyType = 'string';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $fillable = [
        'customer_id',
        'auth_device_token',
        'created_by',
        'modified_by'
    ];

    public function scopeCustomerID($query,$token)
    {
        return $query->where('auth_device_token',$token)->pluck('customer_id')->first();
    }
}
