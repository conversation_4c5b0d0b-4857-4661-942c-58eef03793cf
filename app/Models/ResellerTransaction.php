<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerTransaction extends Model
{
    use HasFactory;

    protected $table = 'rsl_transactions';

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';
    
    const TX_TYPE_COMMISSION = 'Comission';
    const TX_TYPE_POTENTIAL_COMMISSION = 'Potential_Commission';
    const TX_TYPE_WITHDRAWAL = 'Withdrawal';
    const TX_TYPE_INVOICE_PAYMENT = 'Invoice_Payment';
    const TX_TYPE_BONUS = 'Bonus';
    const TX_TYPE_DISCOUNT = 'Discount';
    const TX_TYPE_SHIPMENT_CHARGE = 'Shipment_Charge';
    const TX_TYPE_HANDLING_CHARGE = 'Handling_Charge';
    const TX_TYPE_ADJUSTMENT = 'Adjustment';

    const TX_TYPE_REVERSAL = 'Reversal';


    protected $primaryKey = 'id';

    public $incrementing = false;

    protected $keyType = 'string';
    
    protected $fillable = [
        'reference_name',
        'reference_id',
        'request_no',
        'discount_type',
        'discount_id',
        'type',
        'status',
        'order_status',
        'amount',
        'remarks',
        'created_by',
        'modified_by'
    ];

    public static function transformTypes($param){
        switch($param){
            case self::TX_TYPE_INVOICE_PAYMENT:
                $new  = 'Pembayaran';
                break;
            case self::TX_TYPE_COMMISSION:
                $new  = 'Komisi';
                break;
            case self::TX_TYPE_POTENTIAL_COMMISSION:
                $new  = 'Potensi Komisi';
                break;
            case self::TX_TYPE_HANDLING_CHARGE:
                $new  = 'Biaya Penanganan';
                break;
            case self::TX_TYPE_DISCOUNT:
                $new  = 'Diskon';
                break;
            case self::TX_TYPE_SHIPMENT_CHARGE:
                $new  = 'Biaya Pengiriman';
                break;
            case self::TX_TYPE_HANDLING_CHARGE:
                $new  = 'Biaya Penanganan';
                break;
            default:
                $new  = str_replace('_', ' ', $param);;
                break;
        }

        return $new;
    }
}
