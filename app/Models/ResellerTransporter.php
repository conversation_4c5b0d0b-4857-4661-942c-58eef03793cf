<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\FilterableModel;

class ResellerTransporter extends FilterableModel
{
    use HasFactory;

    public $timestamps = false;
    protected $table = 'rsl_transporters';
    protected $keyType = 'string';

    protected $primaryKey = 'id';
    public $incrementing = false;


    protected $fillable = [
        'transporter_id',
        'name',
        'service',
        'service_code'
    ];
}
