<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ResellerVA extends Model
{
    use HasFactory;
    
    public $table = "rsl_reseller_va";
    
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'reseller_id',
        'reference_no',
        'bin_no',
        'bin_name',
        'virtual_account_no',
        'virtual_account_name',
        'currency',
        'name',
        'email',
        'phone_number',
        'national_id',
        'status_code',
        'status',
        'expired_date',
        'created_date',
        'created_by',
        'modified_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'modified_date' => 'datetime',
    ];

    
    public function scopeFetch($q, $args)
    {
        return $q->where('reseller_id', $args);
    }
}
