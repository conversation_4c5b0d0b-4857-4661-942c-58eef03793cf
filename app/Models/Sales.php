<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Sales extends Model
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';
    const STATUS = 'Aktif';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'sales_id';
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sales_id',
        'position_id',
        'position_name',
        'sales_name',
        'regional',
        'target_sales',
        'phone_number',
        'sap_username',
        'created_by',
        'modified_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime',
    ];

    protected $hidden = [
        'id','created_date','created_by','modified_date','modified_by'
    ];

    public function position()
    {
        return $this->belongsTo(UserMatrix::class, );
    }

    public function assignment()
    {
        return $this->belongsToMany(Sales::class, 'sales_assignment', 'sales_id', 'sales_direct_to_id');
    }

    public function directTo()
    {
        return $this->hasOne(SalesAssignment::class, 'sales_id');
    }

    public function assignedSales()
    {
        return $this->hasMany(SalesAssignment::class, 'sales_direct_to_id');
    }

    public function user()
    {
        return $this->hasOne(User::class, 'reference_id', 'sales_id')->where('reference_object', 'sales');
    }

    public function getChannelAttribute()
    {
        $channel = [];
        if ($roles = $this->user->roles) {
            foreach ($roles->business_unit as $business) {
                $channel[] = $business->name;
            }
        }

        return implode(', ', $channel);
    }

    public function getAssignAttribute()
    {
        return $this->assignment()->first();
    }

    public function getAssignfullAttribute()
    {
        return $this->assignment()->get();
    }
    
    public function customer_sales()
    {
        return $this->belongsToMany(Customer::class, 'customer_sales', 'sales_id', 'customer_id');
    }

    public function getCustomersAttribute()
    {
        $data = [];
        $i = 0;
        $customers = $this->customer_sales()->with('customer')->select('customers.customer_id', 'owner_name')->pluck('owner_name', 'customer_id')->toArray();
        foreach ($customers as $id => $name) {
            $data[$i]['id_cust'] = $id;
            $data[$i]['name_cust'] = $name;
            $i++;
        }

        return $data;
    }

    public function child()
    {
        return $this->hasMany(SalesAssignment::class, 'sales_direct_to_id');
    }
}
