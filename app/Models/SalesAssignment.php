<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SalesAssignment extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sales_assignment';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $hidden = [
        'id','created_date','created_by','modified_date','modified_by'
    ];

        /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'sales_id',
        'sales_direct_to_id ',
        'created_by',
        'modified_by',
    ];

    public function direct_to()
    {
        return $this->belongsTo(SalesAssignment::class, 'sales_direct_to_id', 'sales_id')->with('direct_to.sales');
    }

    public function sales()
    {
        return $this->belongsTo(Sales::class, 'sales_id');
    }

    public function direct_sales()
    {
        return $this->belongsTo(Sales::class, 'sales_direct_to_id');
    }


    public function child()
    {
        return $this->hasMany(SalesAssignment::class, 'sales_direct_to_id', 'sales_id')->with('child.sales');
    }

    public function childRecursive()
    {
        return $this->child()->with('child.sales');
    }
}
