<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\FilterableModel;

class Subdistrict extends FilterableModel
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $table = 'subdistrict';
    protected $keyType = 'string';

    protected $fillable = [
        'code',
        'district_code',
        'name',
        'postal_code'
    ];
}
