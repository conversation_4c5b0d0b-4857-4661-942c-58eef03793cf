<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TaxMatrix extends Model
{
    use HasFactory;
    protected $primaryKey = 'id';
    protected $table = 'rsl_income_tax_matrix';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'metadata',
        'remarks',
        'is_active',
        'valid_from',
        'valid_to'
    ];

    public static function getResponse(){
        return [
            "income_total" => 0,
            "withdraw_total" => 0,
            "tax_total" => 0,
            "tax_template" => [
                        [
                            "to" => ">",
                            "from" => 0,
                            "sequence" => 1,
                            "tax_percentage" => 2.5
                        ]
                ],
            "tax_summary" => [
                [
                    "sequence" => 1,
                    'prorate_percentage' => 0,
                    "tax_amount" => 0
                ]
            ]
        ];
    }

    public static function getCurrentMetadata(){

        return self::where('valid_from', '<=', now()->format('Y-m-d'))
                    ->where('valid_to', '>=', now()->format('Y-m-d'))
                    ->first();
        // return self::where('is_active', 1)->first();
    }
    public static function getCurrentMetadataId(){
        self::getCurrentMetadata()->id??null;
    }
    public static function  calculateTaxProrate(TaxMatrix $tax, $amount = 0, $accumulation = 0)
    {
        $metadata = collect(json_decode($tax->metadata)->ruleset)->sortBy('sequence');
        $rs_tax = [];

        $is_next = fn($amt, $accumulation) => $accumulation > $amt;  
        
        $total_accumulation = $accumulation+$amount;
        $tax_total = 0;
        $base_accumulation = $total_accumulation;
        foreach($metadata as $dat){
            $amt = $dat->to;
            if($amt != '>'){
                    // $prorate = $amt / ($accumulation+$amount == 0 ? $amt : abs($accumulation+$amount));
                    $prorate = ($total_accumulation - $amt) < 0 ? ($total_accumulation / $base_accumulation) : ($amt / $base_accumulation);
                    $prorate_wd = $amount * $prorate;
                    array_push($rs_tax,[
                        'sequence'                => $dat->sequence,
                        'current_accumulation'    => $total_accumulation,
                        'accumulation_deduction'  => $amt,
                        'accumulation_left'       => $total_accumulation - $amt,
                        'prorate_percentage'      => $prorate,
                        'prorate_withdraw'        => round($prorate_wd,0,PHP_ROUND_HALF_UP),
                        'tax_amount'              => round($prorate_wd * $dat->tax_percentage,0,PHP_ROUND_HALF_UP)
                    ]);

                    $tax_total += $prorate_wd * $dat->tax_percentage;
                    if(!$is_next($amt, $total_accumulation)) break;
                    $total_accumulation-= $amt;
            }
            else{
                    $prorate = $total_accumulation / $base_accumulation;
                    $prorate_wd = $amount * $prorate;
                    array_push($rs_tax,[
                        'sequence' => $dat->sequence,
                        'current_accumulation'    => $total_accumulation,
                        'accumulation_deduction'  => $total_accumulation,
                        'accumulation_left'       => $total_accumulation - $total_accumulation,
                        'prorate_percentage' => $prorate,
                        'prorate_withdraw'   => round($prorate_wd,0,PHP_ROUND_HALF_UP),
                        'tax_amount'         => round($prorate_wd * $dat->tax_percentage,0,PHP_ROUND_HALF_UP)
                    ]);
                    $tax_total += $prorate_wd * $dat->tax_percentage;
                    break;
            }
        }

        $response = [
            "income_total" => (double)$accumulation,
            "withdraw_total" => (double)$amount,
            "tax_total" => round($tax_total,0,PHP_ROUND_HALF_UP),
            "nett_total" => $amount - round($tax_total,0,PHP_ROUND_HALF_UP),
            "tax_template" => $metadata,
            "tax_summary" => $rs_tax
        ];

        
        
        return $response;

        
    }
    public static function  calculateTax_v1(TaxMatrix $tax, $amount = 0, $accumulation = 0)
    {
        $metadata = collect(json_decode($tax->metadata)->ruleset)->sortBy('sequence');
        $rs_tax = [];

        $is_next = fn($amt, $accumulation) => $accumulation > $amt;  
        
        $total_accumulation = $amount;
        $current_step = $accumulation;
        $tax_total = 0;
        $base_accumulation = $total_accumulation;
        foreach($metadata as $dat){
            $amt = $dat->to;
            if($amt != '>'){
                    if($amt - $current_step < 0){
                        array_push($rs_tax,[
                            'sequence'                => $dat->sequence,
                            'current_accumulation'    => $current_step,
                            'accumulation_deduction'  => $amt,
                            'accumulation_left'       => $current_step - $amt,
                            'prorate_percentage'      => 0,
                            'prorate_withdraw'        => 0,
                            'tax_amount'              => 0
                        ]);
                        $current_step-= $amt;
                        continue;


                    }
                    $diff = $amt - $current_step;
                    if($diff < $total_accumulation){
                        $amt = $diff;
                    }
                    // $prorate = $amt / ($accumulation+$amount == 0 ? $amt : abs($accumulation+$amount));
                    $prorate = ($total_accumulation - $amt) < 0 ? ($total_accumulation / $base_accumulation) : ($amt / $base_accumulation);
                    $prorate_wd = $amount * $prorate;
                    array_push($rs_tax,[
                        'sequence'                => $dat->sequence,
                        'current_accumulation'    => $total_accumulation,
                        'accumulation_deduction'  => $amt,
                        'accumulation_left'       => $total_accumulation - $amt,
                        'prorate_percentage'      => $prorate,
                        'prorate_withdraw'        => round($prorate_wd,0,PHP_ROUND_HALF_UP),
                        'tax_amount'              => round($prorate_wd * $dat->tax_percentage,0,PHP_ROUND_HALF_UP)
                    ]);

                    $tax_total += $prorate_wd * $dat->tax_percentage;
                    if(!$is_next($amt, $total_accumulation)) break;
                    $total_accumulation-= $amt;
            }
            else{
                    $prorate = $total_accumulation / $base_accumulation;
                    $prorate_wd = $amount * $prorate;
                    array_push($rs_tax,[
                        'sequence' => $dat->sequence,
                        'current_accumulation'    => $total_accumulation,
                        'accumulation_deduction'  => $total_accumulation,
                        'accumulation_left'       => $total_accumulation - $total_accumulation,
                        'prorate_percentage' => $prorate,
                        'prorate_withdraw'   => round($prorate_wd,0,PHP_ROUND_HALF_UP),
                        'tax_amount'         => round($prorate_wd * $dat->tax_percentage,0,PHP_ROUND_HALF_UP)
                    ]);
                    $tax_total += $prorate_wd * $dat->tax_percentage;
                    break;
            }
        }

        $response = [
            "income_total" => (double)$accumulation,
            "withdraw_total" => (double)$amount,
            "tax_total" => round($tax_total,0,PHP_ROUND_HALF_UP),
            "nett_total" => $amount - round($tax_total,0,PHP_ROUND_HALF_UP),
            "tax_template" => $metadata,
            "tax_summary" => $rs_tax
        ];

        
        
        return $response;

        
    }
    public static function  calculateTax(TaxMatrix $tax, $amount = 0, $accumulation = 0)
    {
        $metadata = collect(json_decode($tax->metadata)->ruleset)->sortBy('sequence');
        $rs_tax = [];

        $tax_total = 0;
        $deduction = 0;
        $base_accumulation = $amount;
        $utang = $accumulation;
        foreach($metadata as $dat){
            $amt = $dat->to;
            if($amt != '>'){
                    if((int)$accumulation > $amt){
                        array_push($rs_tax,[
                            'sequence'                => $dat->sequence,
                            'current_accumulation'    => $amount,
                            'accumulation_deduction'  => 0,
                            'accumulation_left'       => $amount,
                            'prorate_percentage'      => 0,
                            'prorate_withdraw'        => 0,
                            'tax_amount'              => 0,
                            'remark' => 'condition_0'

                        ]);
                        $utang-= $dat->to - $dat->from;
                        continue;
                    }
                    if($amount > $amt){
                        $max_reduction = $utang > 0 ? $dat->to - $dat->from - $utang : $dat->to - $dat->from;
                        $deduction = $max_reduction;
                        $prorate = $deduction / $base_accumulation;
                        array_push($rs_tax,[
                            'sequence'                => $dat->sequence,
                            'current_accumulation'    => $amount,
                            'accumulation_deduction'  => $deduction,
                            'accumulation_left'       => $amount - $deduction,
                            'prorate_percentage'      => $prorate,
                            'prorate_withdraw'        => round($deduction,0,PHP_ROUND_HALF_UP),
                            'tax_amount'              => round($deduction * $dat->tax_percentage,0,PHP_ROUND_HALF_UP),
                            'remark' => 'condition_a'
                        ]);
                        $tax_total += round($deduction * $dat->tax_percentage,0,PHP_ROUND_HALF_UP);
                        $amount-=$deduction;
                        $utang -= $deduction;
                        continue;
                    }
                    else{
                        if($amount - $dat->from > 0)
                        {
                            $max_reduction = $utang > 0 ? $dat->to - $dat->from - $utang : $dat->to - $dat->from;
                            $deduction = $amount > $max_reduction ? $max_reduction : $amount;
                            $prorate = $dat->from == 0 ? 1 : $deduction / $base_accumulation;
                            array_push($rs_tax,[
                                'sequence'                => $dat->sequence,
                                'current_accumulation'    => $amount,
                                'accumulation_deduction'  => $deduction,
                                'accumulation_left'       => $amount - $deduction,
                                'prorate_percentage'      => $prorate,
                                'prorate_withdraw'        => round($deduction,0,PHP_ROUND_HALF_UP),
                                'tax_amount'              => round(($deduction) * $dat->tax_percentage,0,PHP_ROUND_HALF_UP),
                                'remark' => 'condition_b'

                            ]);
                            $tax_total += round(($dat->from == 0 ? $amount : $deduction) * $dat->tax_percentage,0,PHP_ROUND_HALF_UP);
                            $utang-=$deduction;
                            $amount-=$deduction;
                            if($amount <= 0) break;
                        }
                        else
                        {
                            $max_reduction = $utang > 0 ? $dat->to - $dat->from - $utang : $dat->to - $dat->from;
                            $deduction = $amount > $max_reduction ? $max_reduction : $amount;
                            $prorate = $deduction / $base_accumulation;
                            array_push($rs_tax,[
                                'sequence'                => $dat->sequence,
                                'current_accumulation'    => $amount,
                                'accumulation_deduction'  => $deduction,
                                'accumulation_left'       => $amount - $deduction,
                                'prorate_percentage'      => $prorate,
                                'prorate_withdraw'        => round($deduction,0,PHP_ROUND_HALF_UP),
                                'tax_amount'              => round($deduction * $dat->tax_percentage,0,PHP_ROUND_HALF_UP),
                                'remark' => 'condition_c'
                            ]);
                            $tax_total += round($deduction * $dat->tax_percentage,0,PHP_ROUND_HALF_UP);
                            $amount-=$deduction;
                            $utang -= $deduction;
                            if($amount <= 0) break;
                        }
                    }
                
                   
            }
            else{
                $deduction = $amount;
                $prorate = $deduction / $base_accumulation;
                array_push($rs_tax,[
                    'sequence'                => $dat->sequence,
                    'current_accumulation'    => $amount,
                    'accumulation_deduction'  => $amount,
                    'accumulation_left'       => $amount - $deduction,
                    'prorate_percentage'      => $prorate,
                    'prorate_withdraw'        => round($deduction,0,PHP_ROUND_HALF_UP),
                    'tax_amount'              => round($deduction * $dat->tax_percentage,0,PHP_ROUND_HALF_UP),
                    'remark' => 'condition_d'

                ]);
                $tax_total += round($deduction * $dat->tax_percentage,0,PHP_ROUND_HALF_UP);
                $amount-=$deduction;
                break;
            }
        }

        $response = [
            "income_total" => (double)$accumulation,
            "withdraw_total" => (double)$base_accumulation,
            "tax_total" => round($tax_total,0,PHP_ROUND_HALF_UP),
            "nett_total" => $base_accumulation - round($tax_total,0,PHP_ROUND_HALF_UP),
            "tax_template" => $metadata,
            "tax_summary" => $rs_tax
        ];

        
        
        return $response;

        
    }

    public static function  calculateTax_v2(TaxMatrix $tax, $amount = 0, $accumulation = 0)
    {
        $metadata = collect(json_decode($tax->metadata)->ruleset)->sortBy('sequence');
        $rs_tax = [];
        $tax_total = 0;
        $wd_sum = $amount + $accumulation;
        $deduction = 0;
        $current_tier = collect(json_decode($tax->metadata)->ruleset)->sortBy('sequence')->where('from', '<=', $accumulation)->last(); 
        $base_accumulation = $accumulation;
        foreach($metadata as $dat){
            $amt = $dat->to;
            if($amt != '>'){
                if($dat->sequence < $current_tier->sequence){
                    array_push($rs_tax,[
                        'sequence'                => $dat->sequence,
                        'current_accumulation'    => $wd_sum,
                        'accumulation_deduction'  => $dat->to - $dat->from,
                        'accumulation_left'       => $wd_sum - ($dat->to - $dat->from),
                        'prorate_percentage'      => 0,
                        'prorate_withdraw'        => 0,
                        'tax_amount'              => 0
                    ]);
                    $wd_sum -= ($dat->to - $dat->from);
                    $base_accumulation -= ($dat->to - $dat->from);
                    continue;
                }
                else{
                    if($wd_sum <= 0) break;
                    $limit_deduction = $dat->to - $dat->from;
                    $deduction = $wd_sum - $limit_deduction <= 0 ? $wd_sum : ($wd_sum > $limit_deduction ? $limit_deduction : $wd_sum);
                    // $deduction = abs($base_accumulation - $limit_deduction < 0 ? $base_accumulation : ($base_accumulation > $limit_deduction ? $limit_deduction : $base_accumulation - $limit_deduction));
                    $prorate = $deduction / $amount;
                    array_push($rs_tax,[
                        'sequence'                => $dat->sequence,
                        'current_accumulation'    => $wd_sum,
                        'accumulation_deduction'  => $deduction,
                        'accumulation_left'       => $wd_sum - $deduction,
                        'prorate_percentage'      => $prorate,
                        'prorate_withdraw'        => round($deduction,0,PHP_ROUND_HALF_UP),
                        'tax_amount'              => round($deduction * $dat->tax_percentage,0,PHP_ROUND_HALF_UP)
                    ]);
                    $tax_total += round(($deduction * $dat->tax_percentage),0,PHP_ROUND_HALF_UP);
                    $wd_sum -= $deduction;
                    $base_accumulation -= $deduction;
                    if($wd_sum <= 0) break;

                }
                   
            }
            else{
                $deduction = $amount;
                $prorate = $deduction / $base_accumulation;
                array_push($rs_tax,[
                    'sequence'                => $dat->sequence,
                    'current_accumulation'    => $amount,
                    'accumulation_deduction'  => $amount,
                    'accumulation_left'       => $amount - $deduction,
                    'prorate_percentage'      => $prorate,
                    'prorate_withdraw'        => round($deduction,0,PHP_ROUND_HALF_UP),
                    'tax_amount'              => round($deduction * $dat->tax_percentage,0,PHP_ROUND_HALF_UP)
                ]);
                $tax_total += round($deduction * $dat->tax_percentage,0,PHP_ROUND_HALF_UP);
                $amount-=$deduction;
                break;
            }
        }

        $response = [
            "income_total" => (double)$accumulation,
            "withdraw_total" => (double)$base_accumulation,
            "tax_total" => round($tax_total,0,PHP_ROUND_HALF_UP),
            "nett_total" => $base_accumulation - round($tax_total,0,PHP_ROUND_HALF_UP),
            "tax_template" => $metadata,
            "tax_summary" => $rs_tax
        ];

        
        
        return $response;

        
    }
    // public static function prorateTax($amt,)
    // {
        // return $reseller->withdrawals()->whereYear('created_date', Carbon::now()->year)->whereIn('status', [self::APPROVED, self::SUCCESS])->sum('amount');
    // }



}
