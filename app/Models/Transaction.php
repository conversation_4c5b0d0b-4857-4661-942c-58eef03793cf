<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Transaction extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'product_name',
        'order_no',
        'customer_id',
        'sales_order_no',
        'order_status',
        'sub_total',
        'total_discount',
        'total',
        'total_item',
        'total_available',
        'total_unavailable',
        'transaction_date',
        'status',
        'customer_shipment_id',
        'sales_name',
        'items'
    ];

    public function items()
    {
        return $this->hasMany(TransactionItem::class);
    }

    public function data_products()
    {
        return $this->hasMany(TransactionItem::class);
    }


    // public function customer_shipment()
    // {
    //     return $this->hasOne(CustomerShipment::class);
    // }
}