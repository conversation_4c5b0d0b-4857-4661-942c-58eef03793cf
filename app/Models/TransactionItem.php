<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransactionItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'article',
        'product_name',
        'product_variant',
        'product_size',
        'stock',
        'qty',
        'issued_qty',
        'is_available',
        'merchandise_category',
        'location',
        'sub_total'
    ];

    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }
}
