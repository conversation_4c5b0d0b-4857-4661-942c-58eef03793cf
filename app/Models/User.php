<?php

namespace App\Models;

use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\DB;
use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Jobs\MailSender;
use App\Notifications\ResetPasswordNotification;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $table = 'user';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'user_id';

    public $incrementing = false;

    protected $keyType = 'string';

    protected $unMaskedRoles = ['Admin', 'Senior Sales', 'Sales'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'reference_id',
        'reference_object',
        'username',
        'email',
        'name',
        'password',
        'remember_token',
        'expired_token',
        'activation_token',
        'activation_token_expired_at',
        'is_change_password',
        'is_active',
        'verified_otp_date',
        'created_by',
        'modified_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime'
    ];

    public function getIdAttribute()
    {
        return @DB::table('user')->select('user_id')->where('email', $this->email)->first()->user_id;
    }

    /**
     * Get the user that owns the Customer
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'reference_id', 'customer_id');
    }

    /**
     * Get the user that owns the Sales
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function sales()
    {
        return $this->belongsTo(Sales::class, 'reference_id', 'sales_id');
    }

    /**
     * Get the user that owns the Sales by name
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function salesByName()
    {
        return $this->belongsTo(Sales::class, 'name', 'sales_name');
    }

    /**
     * The roles that belong to the User
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function roles()
    {
        return $this->belongsToMany(Roles::class, 'user_matrix', 'user_id');
    }

    /**
     * The business unit that belong to the User
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function business_units()
    {
        return $this->belongsToMany(BusinessUnit::class, 'user_matrix', 'user_id')->withPivot('tier_level');
    }

    /**
     * Get the user that owns the Reseller
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function reseller()
    {
        return $this->belongsTo(Reseller::class, 'reference_id', 'id');
    }


    public function sendPasswordResetNotification($token)
    {
        $internal = ['sales', 'finance'];

        if ($this->reference_object == 'customer') {
            switch ($this->customer->distribution_channel) {
                case in_array($this->customer->distribution_channel, Customer::$wholesalesChan):
                    $param['url'] = env('WHOLESALES_APP_URL');
                    $type_auth = "auth";
                    break;
                case in_array($this->customer->distribution_channel, Customer::$b2bChan):
                    $param['url'] = env('B2B_APP_URL');
                    $type_auth = "authb2b";
                    break;
                default:
                    $param['url'] = '';
                    break;
            }
            $param['url'] .= "/{$type_auth}/forgot-password/?token={$token}&email={$this->email}";

        }

        if (strtolower($this->reference_object) == 'rsl_customers') {
            $param['url'] = env('RESELLER_APP_URL');
            $param['url'] .= "forgot-password/?token={$token}&email={$this->email}";
        }

        if (strtolower($this->reference_object) == 'reseller') {
            $param['url'] = env('RSL_RESELLER_APP_URL');
            $param['url'] .= "/auth-reseller/forgot-password/?token={$token}&email={$this->email}";
        }

        if (in_array($this->reference_object, $internal)) {
            $param['url'] = env('INTERNAL_APP_URL');
            $param['url'] .= "/forgot-password/?token={$token}&email={$this->email}";
        }

        MailSender::dispatch($this->email, json_encode($param), 'mail_forgot');
    }

    public function getMenusAttribute()
    {
        if ($this->reference_object != 'sales') {
            return [];
        }

        return @$this->business_units()->selectRaw('LOWER(name) as name')->pluck('name')->toArray() ?: [];
    }

    protected $isMasked = true;

    public function setMasking(bool $masked = true)
    {
        $this->isMasked = $masked;
        return $this;
    }

    public function getEmailAttribute($value)
    {
        if (!$this->isMasked) {
            return $value;
        }

        if (auth()->user() && auth()->user()->reference_id != $this->reference_id) {
            return strlen($value) > 6
                ? str_repeat('*', 6) . substr($value, 6)
                : str_repeat('*', strlen($value));
        }

        return $value;
    }

    public function getUsernameAttribute($value)
    {
        if (!$this->isMasked) {
            return $value;
        }

        if (auth()->user() && auth()->user()->reference_id != $this->reference_id && !in_array(auth()->user()->roles[0]->name, $this->unMaskedRoles) == true) {
            return strlen($value) > 6
                ? str_repeat('*', 6) . substr($value, 6)
                : str_repeat('*', strlen($value));
        }

        return $value;
    }

    public function rsl_customer()
    {
        return $this->belongsTo(ResellerCustomer::class, 'reference_id', 'id');
    }
}