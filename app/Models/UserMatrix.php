<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserMatrix extends Model
{
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';
    
    protected $table = 'user_matrix';
    protected $keyType = 'string';
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'roles_id',
        'business_unit_id',
        'tier_level',
        'created_by',
        'modified_by'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_date' => 'datetime',
        'modified_date' => 'datetime'
    ];

    public function roles()
    {
        return $this->belongsTo(Roles::class, 'roles_id');
    }

    public function business_unit()
    {
        return $this->belongsTo(BusinessUnit::class, 'business_unit_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }
}
