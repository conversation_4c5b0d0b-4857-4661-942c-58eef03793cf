<?php

namespace App\Models;
use App\Events\GenericEvent;
use App\Events\RegisterNotification;
use App\Models\LogIntegration;
use App\Http\Resources\User\NotificationResource;
class UserNotification extends FilterableModel
{
    public $timestamps = false;
    protected $table = 'notifications';
    protected $primaryKey = 'id';
    public $incrementing = false;
    protected $keyType = 'string';
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    const RESELLER_COMMISSION = 'commission';
    const RESELLER_TRANSACTION = 'transaction';
    const RESELLER_INFORMATION = 'information';


    protected static function boot()
    {
        parent::boot();
        static::created(function ($userNotif) {

            $masterSalesNotif = MasterParameter::select('value')->where('key', 'ALL_SALES_NOTIF')->first();

            LogIntegration::create([
                'reference_no' => $userNotif->user_id,
                'module' => 'Notification',
                'name' => 'Notification Dispatch',
                'type' => 'Inbound',
                'status' => 'success',
                'description' => json_encode($userNotif),
                'created_by' => 'SYSTEM',
                'updated_by' => 'SYSTEM'
            ]);
            $userNotif->created_date = date('Y-m-d H:i:s');

            if ($userNotif->user_id == $masterSalesNotif) {
                event(new RegisterNotification($userNotif->user_id, (new NotificationResource($userNotif)), 'notification.new'));
            } else {
                event(new GenericEvent($userNotif->user_id, (new NotificationResource($userNotif)), 'notification.new'));
            }
        });
    }

    protected $fillable = [
        'user_id',
        'name',
        'module',
        'message',
        'is_read',
        'created_by',
        'modified_by',
        'category',
        'channel',
        'level'
    ];

    public static $validator = [
        'user_id' => 'nullable|string',
        'name' => 'required|max:255',
        'module' => 'nullable|max:255',
        'message' => 'nullable',
        'is_read' => 'required|boolean',
        'created_by' => 'nullable',
        'modified_by' => 'nullable',
        'category' => 'nullable',
        'channel' => 'nullable',
        'level' => 'nullable'
    ];

    public function scopeUser($query, $customer_id)
    {
        return $query->where('user_id', (int) $customer_id);
    }
}