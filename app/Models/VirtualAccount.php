<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VirtualAccount extends Model
{
    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    protected $table = 'customer_virtual_account';

    protected $fillable = [
        'customer_id',
        'bank_name',
        'virtual_account_no',
        'created_by',
        'modified_by',
    ];
    
    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id');
    }
}
