<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Voucher extends Model
{
    use HasFactory;

    const CREATED_AT = 'created_date';
    const UPDATED_AT = 'modified_date';

    public $table = "rsl_vouchers";

    protected $fillable = [
        'id',
        'voucher_master_id',
        'promotion_id',
        'location_code',
        'order_number',
        'code',
        'category',
        'type',
        'is_used',
        'used_at',
        'start_date',
        'end_date',
        'status',
        'amount',
        'used_amount',
        'expired_amount',
        'remaining_amount',
        'created_by',
        'modified_by'
    ];

    public function scopeActive($q)
    {
        return $q->whereHas('master',function($q){
            $q->where('status','active');
        })->where('status', 'active');
    }

    public function scopeActiveDate($q)
    {
        return $q->where('start_date', '<=', now()->format('Y-m-d'))
            ->where('end_date', '>=', now()->format('Y-m-d'));
    }

    public function scopeUsable($q)
    {
        return $q->where(function($q){
            $q->where(function($q){
                $q->whereHas('master',function($q){
                    $q->where('is_multiple',1);
                })->where('remaining_amount','!=',0);
            })->orWhere(function($q){
                $q->whereHas('master',function($q){
                    $q->where('is_multiple',0);
                })->where('is_used','!=',1);
            });
        });
    }

    public function master()
    {
        return $this->belongsTo(VoucherMaster::class,'voucher_master_id','id');
    }
    
}
