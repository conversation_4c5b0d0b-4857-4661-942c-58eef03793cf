<?php

namespace App\Observers;

use App\Models\OrderItemReseller;
use App\Models\OrderReseller;
use App\Models\ResellerOrderPromotion;

class OrderItemResellerObserver
{
    /**
     * Handle the OrderItemReseller "created" event.
     *
     * @param  \App\Models\OrderItemReseller  $orderItemReseller
     * @return void
     */
    public function created(OrderItemReseller $orderItemReseller)
    {
        //
    }

    /**
     * Handle the OrderItemReseller "updated" event.
     *
     * @param  \App\Models\OrderItemReseller  $orderItemReseller
     * @return void
     */
    public function updated(OrderItemReseller $orderItemReseller)
    {
        // dd($orderItemReseller->isDirty('total_amount'));
        if ($orderItemReseller->isDirty('total_amount')) {
            $order = $orderItemReseller->orderReseller;

            $ids = $order->items()->pluck('id')->toArray();
            $ids[] = $order->id;
            $discount_amount = ResellerOrderPromotion::whereIn('reference_id',$ids)->sum('amount');

            $order->sub_total_amount = $order->all_items()->sum('line_amount');
            $order->discount_amount = $discount_amount;
            $order->total_amount = $order->sub_total_amount - $discount_amount;
            $order->pay_amount = $order->total_amount + $order->shipment_charges;
            $order->save();
            
        }
    }

    /**
     * Handle the OrderItemReseller "deleted" event.
     *
     * @param  \App\Models\OrderItemReseller  $orderItemReseller
     * @return void
     */
    public function deleted(OrderItemReseller $orderItemReseller)
    {
        //
    }

    /**
     * Handle the OrderItemReseller "restored" event.
     *
     * @param  \App\Models\OrderItemReseller  $orderItemReseller
     * @return void
     */
    public function restored(OrderItemReseller $orderItemReseller)
    {
        //
    }

    /**
     * Handle the OrderItemReseller "force deleted" event.
     *
     * @param  \App\Models\OrderItemReseller  $orderItemReseller
     * @return void
     */
    public function forceDeleted(OrderItemReseller $orderItemReseller)
    {
        //
    }
}
