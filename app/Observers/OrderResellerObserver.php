<?php

namespace App\Observers;

use App\Models\Color;
use App\Jobs\MailSender;
use App\Models\OrderReseller;
use App\Models\MasterParameter;
use Illuminate\Support\Facades\DB;
use App\Repositories\GetSocialsRepo;
use Illuminate\Support\Facades\Auth;
use App\Models\ResellerOrderShipment;
use App\Models\ResellerOrderPromotion;

class OrderResellerObserver
{
    /**
     * Handle the OrderReseller "created" event.
     *
     * @param  \App\Models\OrderReseller  $orderReseller
     * @return void
     */
    public function created(OrderReseller $orderReseller)
    {
        //
    }

    /**
     * Handle the OrderReseller "updated" event.
     *
     * @param  \App\Models\OrderReseller  $orderReseller
     * @return void
     */
    public function updated(OrderReseller $oh)
    {
        // if ($oh->isDirty('total_amount')) {
        //     $mp = MasterParameter::where('group_key','RESELLER_COMMISSION')->where('key','COMMISSION_PERCENTAGE')->first();
        //     $oh->commission_amount = $oh->total_amount == 0 ? 0 : $oh->total_amount*($mp->value/100);
        //     $oh->save();
        // }

        if ($oh->isDirty('order_status')) {
            if ($oh->order_status == OrderReseller::ORDER_RESELLER_MENUNGGU_PEMBAYARAN) {
                $socmed = new GetSocialsRepo();
                $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
                $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
                $twitter = $socmed->getSocialMediaParameters('TWITTER');
                $linkedin = $socmed->getSocialMediaParameters('LINKEDIN');

                $items = $oh->items()->with('mainImageVariant')->get();
                $items_bundling = $oh->item_bundlings()->with('mainImageVariant')->get();
                foreach ($items as $data) {
                    $ops = ResellerOrderPromotion::where('reference_id',$data['id'])->whereIn('discount_type',['coupon','voucher'])->get();
                    $rev_disc_amount = 0;
                    if (!$ops->isEmpty()) {
                        foreach ($ops as $op) {
                            $rev_disc_amount += $op->amount;
                        }
                    }
                    $data['discount_amount'] = $data['discount_amount'] - $rev_disc_amount;
                    $data['total_amount'] = $data['total_amount'] + $rev_disc_amount;
                    $data['color'] = Color::where('key',$data->product_variant)->first()->value;
                    $data['tag_promo'] = null;
                    if ($data->promotion?->promo != null) {
                        if (strtolower($data->promotion->promo->action) == 'bogo') {
                            $data['tag_promo'] = 'BUY 1 GET 1';
                        }

                        if (strtolower($data->promotion->promo->action) != 'bogo' && (int)$data->promotion->amount != 0) {
                            if (strtolower($data->promotion->promo->discount_type) == 'percentage') {
                                $percent = ($data->promotion->amount/$data->line_amount)*100;
                                $data['tag_promo'] = 'Diskon '.round($percent,2).'%';
                            } else {
                                $data['tag_promo'] = 'Diskon RP'.number_format($data->promotion->amount,0,'','.');
                            }
                        }
                    }
                }
                $bundling_qty = 0;
                foreach ($items_bundling as $data) {
                    $data['color'] = Color::where('key',$data->product_variant)->first()->value;
                    $bundling_qty += $data->qty;
                    
                }
                $param = [
                    'data' => [
                        'order_no' => $oh->order_no,
                        'due_payment_date' => $oh->due_payment_date,
                        'items' => $items->toArray(),
                        'item_bundlings' => $items_bundling->toArray(),
                        'bundling_qty' => $bundling_qty,
                        'sub_total' => $oh->sub_total_amount,
                        'ongkos_kirim' => $oh->shipment_charges,
                        'total' => $oh->pay_amount,
                        'payment_link' => $oh->payment_link,
                        'promo' => 0,
                        'voucher' => 0,
                        'coupon' => 0,

                        'facebook' => $facebook,
                        'twitter' => $twitter,
                        'instagram' => $instagram,
                        'linkedin' => $linkedin,
                    ]
                ];
                
                $ids = $oh->items()->pluck('id')->toArray();
                $ids[] = $oh->id;
                
                $op = ResellerOrderPromotion::whereIn('reference_id',$ids)->get();
                if (!$op->isEmpty()) {
                    foreach($op as $d){
                        if ($d->discount_type == 'promotions'){
                            $param['data']['promo'] += $d->amount;
                        }
                        if ($d->discount_type == 'voucher'){
                            $param['data']['voucher'] += $d->amount;
                        }
                        if ($d->discount_type == 'coupon'){
                            $param['data']['coupon'] += $d->amount;
                        }
                    }
                }
                MailSender::dispatch($oh->customer_email,json_encode($param),'mail_waiting_for_payment_customer_reseller');
            }

            if ($oh->order_status == OrderReseller::ORDER_RESELLER_BATAL) {
                $socmed = new GetSocialsRepo();
                $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
                $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
                $twitter = $socmed->getSocialMediaParameters('TWITTER');
                $linkedin = $socmed->getSocialMediaParameters('LINKEDIN');

                $items = $oh->items()->with('mainImageVariant')->get();
                $items_bundling = $oh->item_bundlings()->with('mainImageVariant')->get();
                foreach ($items as $data) {
                    $ops = ResellerOrderPromotion::where('reference_id',$data['id'])->whereIn('discount_type',['coupon','voucher'])->get();
                    $rev_disc_amount = 0;
                    if (!$ops->isEmpty()) {
                        foreach ($ops as $op) {
                            $rev_disc_amount += $op->amount;
                        }
                    }
                    $data['discount_amount'] = $data['discount_amount'] - $rev_disc_amount;
                    $data['total_amount'] = $data['total_amount'] + $rev_disc_amount;
                    $data['color'] = Color::where('key',$data->product_variant)->first()->value;
                    $data['tag_promo'] = null;
                    if ($data->promotion?->promo != null) {
                        if (strtolower($data->promotion->promo->action) == 'bogo') {
                            $data['tag_promo'] = 'BUY 1 GET 1';
                        }

                        if (strtolower($data->promotion->promo->action) != 'bogo' && (int)$data->promotion->amount != 0) {
                            if (strtolower($data->promotion->promo->discount_type) == 'percentage') {
                                $percent = ($data->promotion->amount/$data->line_amount)*100;
                                $data['tag_promo'] = 'Diskon '.round($percent,2).'%';
                            } else {
                                $data['tag_promo'] = 'Diskon RP'.number_format($data->promotion->amount,0,'','.');
                            }
                        }
                    }
                }
                $bundling_qty = 0;
                foreach ($items_bundling as $data) {
                    $data['color'] = Color::where('key',$data->product_variant)->first()->value;
                    $bundling_qty += $data->qty;
                }
                $param = [
                    'data' => [
                        'order_no' => $oh->order_no,
                        'items' => $items->toArray(),
                        'item_bundlings' => $items_bundling->toArray(),
                        'bundling_qty' => $bundling_qty,
                        'sub_total' => $oh->sub_total_amount,
                        'ongkos_kirim' => $oh->shipment_charges,
                        'total' => $oh->pay_amount,
                        'promo' => 0,
                        'voucher' => 0,
                        'coupon' => 0,

                        'facebook' => $facebook,
                        'twitter' => $twitter,
                        'instagram' => $instagram,
                        'linkedin' => $linkedin,
                    ]
                ];
                
                $ids = $oh->items()->pluck('id')->toArray();
                $ids[] = $oh->id;
                
                $op = ResellerOrderPromotion::whereIn('reference_id',$ids)->get();
                if (!$op->isEmpty()) {
                    foreach($op as $d){
                        if ($d->discount_type == 'promotions'){
                            $param['data']['promo'] += $d->amount;
                        }
                        if ($d->discount_type == 'voucher'){
                            $param['data']['voucher'] += $d->amount;
                        }
                        if ($d->discount_type == 'coupon'){
                            $param['data']['coupon'] += $d->amount;
                        }
                    }
                }
                $user = Auth::guard('sanctum')->user();
                $customer_email = $oh->customer_email??null != null ? $oh->customer_email : ($user->email??null != null ? $user->email : null );
                if ($customer_email != null) {
                    MailSender::dispatch($customer_email,json_encode($param),'mail_order_cancel_customer_reseller');
                }
            }

            if ($oh->order_status == OrderReseller::ORDER_RESELLER_DITERIMA 
            || $oh->order_status == OrderReseller::ORDER_RESELLER_SELESAI
            || $oh->order_status == OrderReseller::ORDER_RESELLER_PENGEMBALIAN
            ) {
                $full_address = [];
                $full_address[] = $oh->customer_shipment_address;
                $full_address[] = $oh->customer_shipment_district_name;
                $full_address[] = $oh->customer_shipment_subdistrict_name;
                $full_address[] = $oh->customer_shipment_city_name;
                $full_address[] = $oh->customer_shipment_region_name;
                $full_address[] = $oh->customer_shipment_zip_code;

                $socmed = new GetSocialsRepo();
                $instagram = $socmed->getSocialMediaParameters('INSTAGRAM');
                $facebook = $socmed->getSocialMediaParameters('FACEBOOK');
                $twitter = $socmed->getSocialMediaParameters('TWITTER');
                $linkedin = $socmed->getSocialMediaParameters('LINKEDIN');

                $store = DB::table('rsl_reseller_link')->select('identifier')->where('reseller_id', $oh->reseller_id)->where('type', "STORE")->first();

                $sumItems = $oh->items()->sum('qty');
                $items = $oh->items()->with('mainImageVariant')->get();
                $items_bundling = $oh->item_bundlings()->with('mainImageVariant')->get();
                foreach ($items as $data) {
                    $ops = ResellerOrderPromotion::where('reference_id',$data['id'])->whereIn('discount_type',['coupon','voucher'])->get();
                    $rev_disc_amount = 0;
                    if (!$ops->isEmpty()) {
                        foreach ($ops as $op) {
                            $rev_disc_amount += $op->amount;
                        }
                    }
                    $data['discount_amount'] = $data['discount_amount'] - $rev_disc_amount;
                    $data['total_amount'] = $data['total_amount'] + $rev_disc_amount;
                    $data['color'] = Color::where('key',$data->product_variant)->first()->value;
                    $data['tag_promo'] = null;
                    if ($data->promotion?->promo != null) {
                        if (strtolower($data->promotion->promo->action) == 'bogo') {
                            $data['tag_promo'] = 'BUY 1 GET 1';
                        }

                        if (strtolower($data->promotion->promo->action) != 'bogo' && (int)$data->promotion->amount != 0) {
                            if (strtolower($data->promotion->promo->discount_type) == 'percentage') {
                                $percent = ($data->promotion->amount/$data->line_amount)*100;
                                $data['tag_promo'] = 'Diskon '.round($percent,2).'%';
                            } else {
                                $data['tag_promo'] = 'Diskon RP'.number_format($data->promotion->amount,0,'','.');
                            }
                        }
                    }
                }
                $bundling_qty = 0;
                foreach ($items_bundling as $data) {
                    $data['color'] = Color::where('key',$data->product_variant)->first()->value;
                    $bundling_qty += $data->qty;
                }
                $itemsArray = $items->toArray();
                $param = [
                    'data' => [
                        'order_no' => $oh->order_no,
                        'items' => $itemsArray,
                        'item_bundlings' => $items_bundling->toArray(),
                        'ongkos_kirim' => $oh->shipment_charges,
                        'sub_total_amount' => $oh->sub_total_amount,
                        'total_amount' => $oh->pay_amount,
                        'commission_amount' => $oh->commission_amount,
                        'type' => 'Customer',
                        'bundling_qty' => $bundling_qty,

                        'subtotal' => $sumItems,
    
                        'courier' => $oh->shipment_method,
                        'no_resi' => ResellerOrderShipment::where('order_header_id', $oh->id)->where('order_status', OrderReseller::ORDER_RESELLER_DIKIRIM)->latest()->first()->awb_no??null,
                        'name' => $oh->customer_shipment_name,
                        'phone_number' => $oh->customer_shipment_phone_number,
                        'store' => $oh->reseller->name,
                        'address' => implode(', ',array_filter($full_address)).'.',
    
                        'promo' => 0,
                        'voucher' => 0,
                        'coupon' => 0,
    
                        'facebook' => $facebook,
                        'twitter' => $twitter,
                        'instagram' => $instagram,
                        'linkedin' => $linkedin,
                    ]
                ];
                
                $ids = $oh->detail()->pluck('id')->toArray();
                $ids[] = $oh->id;
                
                $op = ResellerOrderPromotion::whereIn('reference_id',$ids)->get();
                if (!$op->isEmpty()) {
                    foreach($op as $d){
                        if ($d->discount_type == 'promotions'){
                            $param['data']['promo'] += $d->amount;
                        }
                        if ($d->discount_type == 'voucher'){
                            $param['data']['voucher'] += $d->amount;
                        }
                        if ($d->discount_type == 'coupon'){
                            $param['data']['coupon'] += $d->amount;
                        }
                    }
                }
                
                switch($oh->order_status) {
                    case OrderReseller::ORDER_RESELLER_DITERIMA:
                        $mail_param = 'mail_reseller_order_received';

                        #Send Mail to Customer
                        if (isset($oh->customer_email) && $oh->customer_email != null) {
                            MailSender::dispatch($oh->customer_email, json_encode($param), $mail_param);
                        }

                        #Send Mail to Reseller
                        if (isset($oh->reseller->email) && $oh->reseller->email != null) {
                            $param['data']['type'] = 'Reseller';
                            MailSender::dispatch($oh->reseller->email, json_encode($param), $mail_param);
                        }
                        break;
                    case OrderReseller::ORDER_RESELLER_SELESAI:
                        $mail_param = 'mail_reseller_order_complete';
                        $internal = DB::table('user as u')
                        ->leftJoin('sales as s', 's.sales_id','=','u.reference_id')
                        ->leftJoin('user_matrix as um', 'um.user_id','=','u.user_id')
                        ->leftJoin('business_unit as bu', 'bu.id','=','um.business_unit_id')
                        ->Select('u.email')
                        ->Where('bu.name', 'Reseller')
                        ->whereIn('um.tier_level',[4,5])
                        ->get()
                        ->toArray();
                        
                        #Send Mail to internal
                        if (isset($internal) && $internal != null) {
                            foreach ($internal as $admin) {
                                MailSender::dispatch($admin->email, json_encode($param), $mail_param);
                            }
                        }

                        #Send Mail to Reseller
                        if (isset($oh->reseller->email) && $oh->reseller->email != null) {
                            $param['data']['type'] = 'Reseller';
                            MailSender::dispatch($oh->reseller->email, json_encode($param), $mail_param);
                        }
                        break;
                    case OrderReseller::ORDER_RESELLER_PENGEMBALIAN:
                        $mail_param = 'mail_reseller_order_return';

                        #Send Mail to Customer
                        if (isset($oh->customer_email) && $oh->customer_email != null) {
                            MailSender::dispatch($oh->customer_email, json_encode($param), $mail_param);
                        }

                        #Send Mail to Reseller
                        if (isset($oh->reseller->email) && $oh->reseller->email != null) {
                            $param['data']['type'] = 'Reseller';
                            MailSender::dispatch($oh->reseller->email, json_encode($param), $mail_param);
                        }
                        break;
                }
            }
        }
    }

    /**
     * Handle the OrderReseller "deleted" event.
     *
     * @param  \App\Models\OrderReseller  $orderReseller
     * @return void
     */
    public function deleted(OrderReseller $orderReseller)
    {
        //
    }

    /**
     * Handle the OrderReseller "restored" event.
     *
     * @param  \App\Models\OrderReseller  $orderReseller
     * @return void
     */
    public function restored(OrderReseller $orderReseller)
    {
        //
    }

    /**
     * Handle the OrderReseller "force deleted" event.
     *
     * @param  \App\Models\OrderReseller  $orderReseller
     * @return void
     */
    public function forceDeleted(OrderReseller $orderReseller)
    {
        //
    }
}
