<?php

namespace App\Observers;

use App\Models\ResellerOrderDetails;

class ResellerOrderDetailsObserver
{
    /**
     * Handle the ResellerOrderDetails "created" event.
     *
     * @param  \App\Models\ResellerOrderDetails  $resellerOrderDetails
     * @return void
     */
    public function created(ResellerOrderDetails $resellerOrderDetails)
    {
        if ($resellerOrderDetails->isDirty('total_amount')) {
            $order = $resellerOrderDetails->order;
            $sub_total_amount = $order->detail->sum('total_amount');

            $order->sub_total_amount = $sub_total_amount;
            $order->total_amount = $sub_total_amount - $order->discount_amount;
            $order->pay_amount = ($sub_total_amount - $order->discount_amount) + $order->shipment_charges;
            $order->save();
        }
    }

    /**
     * Handle the ResellerOrderDetails "updated" event.
     *
     * @param  \App\Models\ResellerOrderDetails  $resellerOrderDetails
     * @return void
     */
    public function updated(ResellerOrderDetails $resellerOrderDetails)
    {
        // if ($resellerOrderDetails->isDirty('total_amount')) {
        //     $order = $resellerOrderDetails->order;

        //     if ($order->detail->sum('total_amount') <= 0) {
        //         $order->sub_total_amount = 0;
        //         $order->total_amount = 0;
        //         $order->discount_amount = $order->detail->sum('line_amount');
        //         $order->pay_amount = $order->shipment_charges;
        //         $order->save();
        //     } else {
        //         $order->sub_total_amount = $order->detail->sum('line_amount');
        //         $order->discount_amount = $order->detail->sum('discount_amount');
        //         $order->total_amount = $order->detail->sum('total_amount');
        //         $order->pay_amount = $order->total_amount + $order->shipment_charges;
        //         $order->save();
        //     }
        // }
    }

    /**
     * Handle the ResellerOrderDetails "deleted" event.
     *
     * @param  \App\Models\ResellerOrderDetails  $resellerOrderDetails
     * @return void
     */
    public function deleted(ResellerOrderDetails $resellerOrderDetails)
    {
        if ($resellerOrderDetails->isDirty('total_amount')) {
            $order = $resellerOrderDetails->order;
            $sub_total_amount = $order->detail->sum('total_amount');

            $order->sub_total_amount = $sub_total_amount;
            $order->total_amount = $sub_total_amount - $order->discount_amount;
            $order->pay_amount = ($sub_total_amount - $order->discount_amount) + $order->shipment_charges;
            $order->save();
        }
    }

    /**
     * Handle the ResellerOrderDetails "restored" event.
     *
     * @param  \App\Models\ResellerOrderDetails  $resellerOrderDetails
     * @return void
     */
    public function restored(ResellerOrderDetails $resellerOrderDetails)
    {
        //
    }

    /**
     * Handle the ResellerOrderDetails "force deleted" event.
     *
     * @param  \App\Models\ResellerOrderDetails  $resellerOrderDetails
     * @return void
     */
    public function forceDeleted(ResellerOrderDetails $resellerOrderDetails)
    {
        //
    }
}
