<?php

namespace App\Observers;

use App\Models\MasterParameter;
use App\Models\ResellerOrderHeaders;

class ResellerOrderHeadersObserver
{
    /**
     * Handle the ResellerOrderHeaders "created" event.
     *
     * @param  \App\Models\ResellerOrderHeaders  $resellerOrderHeaders
     * @return void
     */
    public function created(ResellerOrderHeaders $resellerOrderHeaders)
    {
        //
    }

    /**
     * Handle the ResellerOrderHeaders "updated" event.
     *
     * @param  \App\Models\ResellerOrderHeaders  $resellerOrderHeaders
     * @return void
     */
    public function updated(ResellerOrderHeaders $oh)
    {
        // if ($oh->isDirty('total_amount')) {
        //     $mp = MasterParameter::where('group_key','RESELLER_COMMISSION')->where('key','COMMISSION_PERCENTAGE')->first();
        //     $oh->commission_amount = $oh->total_amount == 0 ? 0 : $oh->total_amount*($mp->value/100);
        //     $oh->save();
        // }
    }

    /**
     * Handle the ResellerOrderHeaders "deleted" event.
     *
     * @param  \App\Models\ResellerOrderHeaders  $resellerOrderHeaders
     * @return void
     */
    public function deleted(ResellerOrderHeaders $resellerOrderHeaders)
    {
        //
    }

    /**
     * Handle the ResellerOrderHeaders "restored" event.
     *
     * @param  \App\Models\ResellerOrderHeaders  $resellerOrderHeaders
     * @return void
     */
    public function restored(ResellerOrderHeaders $resellerOrderHeaders)
    {
        //
    }

    /**
     * Handle the ResellerOrderHeaders "force deleted" event.
     *
     * @param  \App\Models\ResellerOrderHeaders  $resellerOrderHeaders
     * @return void
     */
    public function forceDeleted(ResellerOrderHeaders $resellerOrderHeaders)
    {
        //
    }
}
