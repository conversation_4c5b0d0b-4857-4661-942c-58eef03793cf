<?php

namespace App\Observers;

use App\Models\ResellerOrderPromotion;

class ResellerOrderPromotionObserver
{
    /**
     * Handle the ResellerOrderPromotion "created" event.
     *
     * @param  \App\Models\ResellerOrderPromotion  $resellerOrderPromotion
     * @return void
     */
    public function created(ResellerOrderPromotion $resellerOrderPromotion)
    {
        //
    }

    /**
     * Handle the ResellerOrderPromotion "updated" event.
     *
     * @param  \App\Models\ResellerOrderPromotion  $resellerOrderPromotion
     * @return void
     */
    public function updated(ResellerOrderPromotion $resellerOrderPromotion)
    {
        //
    }

    /**
     * Handle the ResellerOrderPromotion "deleted" event.
     *
     * @param  \App\Models\ResellerOrderPromotion  $resellerOrderPromotion
     * @return void
     */
    public function deleted(ResellerOrderPromotion $resellerOrderPromotion)
    {
        // if ($resellerOrderPromotion->header != null) {
        //     $order = $resellerOrderPromotion->header;

        //     $order->total_amount = $order->total_amount + $resellerOrderPromotion->amount;
        //     $order->pay_amount = $order->pay_amount + $resellerOrderPromotion->amount;
        //     $order->save();
        // }

        // if ($resellerOrderPromotion->detail != null) {
        //     $item = $resellerOrderPromotion->detail;

        //     $item->discount_amount = $item->discount_amount - $resellerOrderPromotion->amount;
        //     $item->total_amount = $item->total_amount + $resellerOrderPromotion->amount;
        //     $item->save();
        // }

        // if (strtolower($resellerOrderPromotion->discount_type) == 'voucher') {
        //     $voucher = $resellerOrderPromotion->voucher;

        //     if (strtolower($voucher->discount_type) == 'percentage') {
        //         $voucher->remaining_amount = $voucher->amount;
        //         $voucher->used_amount = 0;
        //         $voucher->save();
        //     }

        //     if (strtolower($voucher->discount_type) == 'absolute') {
        //         $voucher->remaining_amount = $voucher->remaining_amount + $resellerOrderPromotion->amount;
        //         $voucher->used_amount = $voucher->used_amount - $resellerOrderPromotion->amount;
        //         $voucher->save();
        //     }
        // }
    }

    /**
     * Handle the ResellerOrderPromotion "restored" event.
     *
     * @param  \App\Models\ResellerOrderPromotion  $resellerOrderPromotion
     * @return void
     */
    public function restored(ResellerOrderPromotion $resellerOrderPromotion)
    {
        //
    }

    /**
     * Handle the ResellerOrderPromotion "force deleted" event.
     *
     * @param  \App\Models\ResellerOrderPromotion  $resellerOrderPromotion
     * @return void
     */
    public function forceDeleted(ResellerOrderPromotion $resellerOrderPromotion)
    {
        //
    }
}
