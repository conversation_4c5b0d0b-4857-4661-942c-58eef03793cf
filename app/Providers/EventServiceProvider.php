<?php

namespace App\Providers;

use App\Models\OrderItemReseller;
use App\Models\OrderReseller;
use App\Models\ResellerOrderDetails;
use App\Models\ResellerOrderPromotion;
use App\Observers\OrderItemResellerObserver;
use App\Observers\OrderResellerObserver;
use App\Observers\ResellerOrderDetailsObserver;
use App\Observers\ResellerOrderPromotionObserver;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        \App\Events\ExampleEvent::class => [
            \App\Listeners\ExampleListener::class,
        ],
        \Illuminate\Mail\Events\MessageSending::class => [
            // \App\Listeners\LogSendingMessage::class,
        ],
        \Illuminate\Mail\Events\MessageSent::class => [
            // \App\Listeners\LogSentMessage::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        OrderItemReseller::observe(OrderItemResellerObserver::class);
        ResellerOrderDetails::observe(ResellerOrderDetailsObserver::class);
        ResellerOrderPromotion::observe(ResellerOrderPromotionObserver::class);
        OrderReseller::observe(OrderResellerObserver::class);
    }
}
