<?php

namespace App\Providers;
use App\Interfaces\{UserInterface,InvoiceInterface,ProductInterface,CustomerInterface,ParameterInterface, ResellerInterface};
use App\Repositories\{UserRepository,InvoiceRepository,ProductRepository,CustomerRepository,ParameterRepository, ResellerRepository};
use Illuminate\Support\ServiceProvider;
use App\Interfaces\Reseller\ProductInterface as RslProductInterface;
use App\Repositories\Reseller\ProductRepository as RslProductRepository;


class RepositoryServiceProvider extends ServiceProvider
{
    public function register()
    {
        // Register Interface and Repository in here
        // You must place Interface in first place
        // If you dont, the Repository will not get readed.
        $this->app->bind(ProductInterface::class, ProductRepository::class);
        $this->app->bind(UserInterface::class, UserRepository::class);
        $this->app->bind(InvoiceInterface::class, InvoiceRepository::class);
        $this->app->bind(ParameterInterface::class, ParameterRepository::class);
        $this->app->bind(CustomerInterface::class, CustomerRepository::class);
        $this->app->bind(ResellerInterface::class, ResellerRepository::class);
        $this->app->bind(RslProductInterface::class, RslProductRepository::class);

    }
}
