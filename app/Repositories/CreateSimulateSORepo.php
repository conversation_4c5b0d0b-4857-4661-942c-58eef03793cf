<?php

namespace App\Repositories;

use App\Helpers\ApiClient;
use App\Services\LoggerIntegration;
use Illuminate\Support\Facades\Log;
use App\Models\LogIntegration;
use App\Models\Order;


class CreateSimulateSORepo
{
    public function createSO($data)
    {

            $base_url= env('API_EIGER_URL');
            $username= env('API_USERNAME');
            $password= env('API_PASSWORD');
            $url=$base_url . "/api/care-om/orders";
            $sapusername = $data['sap_username'];

            $headers = ['sapuser' => $sapusername];
            $options = [
                'json' => $data,
            ];
            Log::info('[REQUEST CREATE OR SIMULATE SO]', $options);

            $client = ApiClient::request('POST', $url,  $options, $username, $password, $headers);
            // var_dump($client);
            if (is_array($client)) {
                $response = $client;
            } elseif ($client->getStatusCode() == 200) {
                // try{
                    $response = json_decode($client->content(), true);
                // }
                // catch(Exception $e){
                //     $log = new LogIntegration;
                //     $log->reference_no  = $data['externalno'];
                //     $log->module  = 'SAP';
                //     $log->name  = 'Create & Simulate SO';
                //     $log->type  = 'Outbound';
                //     $log->status  = 'failed';
                //     $log->description  = json_encode(($data));
                //     $log->save();

                //     return ["destination"=>"SLO","message"=>[["tax"=>"0.0000","gross"=>"000000000000000","discount"=>"000000000000000","net"=>"000000000000000","netbtax"=>"000000000000000"]],"code"=>"201"];
                // }
            } elseif ($client->getStatusCode() == 201) {
                $response = json_decode($client->getBody(), true);
            }

            //check "message" in response
            //failing commit, ganti
            // $messageResponse = json_decode($response);
            // $type = $messageResponse->message[0]->type;

            if ($response['code'] == 500) {
                $log = new LogIntegration;
                $log->reference_no  = $data['externalno'];
                $log->module  = 'SAP';
                $log->name  = $data['flag'] == "i" ? 'Create SO' : 'Simulate SO';
                $log->type  = 'Outbound';
                $log->status  = 'failed';
                $log->description  = json_encode([
                    'payload' => $data,
                    'response' => ($response['message'])
                ]);
                $log->save();
            } elseif($response['code'] == 201 || $response['code'] == 200) {
                $log = new LogIntegration;
                $log->reference_no  = $data['externalno'];
                $log->module  = 'SAP';
                $log->name  = $data['flag'] == "i" ? 'Create SO' : 'Simulate SO';
                $log->type  = 'Outbound';
                $log->status  = 'success';
                $log->description  = json_encode([
                    'payload' => $data,
                    'response' => $response
                ]);
                $log->save();


                // $ordr  = Order::where('order_no', $data['externalno'])->first();
                // $ordr->total_discount = $data['discount'];
            }

            // (new LoggerIntegration())->InsertLogger($logs);

            return $client;

    }

    public function getStatusCode($response)
    {
        $re = json_decode($response->getStatusCode(), true);

    }

    public function getDataWhole($response)
    {
        return json_decode($response->getBody(),true);
    }


    public function getData($response)
    {
        $result = json_decode($response->getBody(), true);
        // Log::channel('api')->info('Response data: ' . json_encode($result));

        //$result = json_decode($response->getBody(), true);

        // $destination = $result['destination'];
        // $code = $result['code'];
        // $messages = $result['message'];

        // foreach ($messages as $message) {
        //     $type = $message['type'];
        //     $text = $message['message'];
        //     $vbeln = $message['vbeln'];

        //     // Lakukan sesuatu dengan nilai yang diambil dari $result
        // }
    //     foreach ($result['message'] as $message) {
    //         $vbeln = $message['vbeln'];
    // // Gunakan nilai $vbeln untuk keperluan selanjutnya
    //     }

        return $result['message'][0];

    }

}

