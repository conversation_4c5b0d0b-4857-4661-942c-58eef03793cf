<?php

namespace App\Repositories;

use Carbon\Carbon;
use App\Models\Customer;
use App\Traits\ResponseAPI;
use App\Models\CustomerSales;
use App\Jobs\CreditLimitQueue;
use App\Models\CustomerShipment;
use Illuminate\Support\Facades\DB;
use App\Interfaces\CustomerInterface;
use App\Http\Resources\CustomerOrderResource;
use App\Http\Resources\CustomerInternalResource;
use App\Http\Resources\InternalCustomerResource;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class CustomerRepository implements CustomerInterface
{
    use ResponseAPI;

    const OrderStatusWait = 'Menunggu Konfirmasi';
    const OrderStatusPending = 'Pending';
    const OrderStatusOnHold = 'On Hold';
    const OrderStatusOnProcess = 'Diproses';
    const OrderStatusBaru = 'Baru';
    const OrderStatusGI = 'Siap Dikirim';
    const OrderStatusOnShipping = 'Dikirim';
    const OrderStatusDelivered = 'Diterima';
    const OrderStatusFinish = 'Selesai';
    const OrderStatusCancel = 'Batal';
    const OrderStatusPembayaran = 'Pembayaran';
    const OrderStatusSemua = 'Semua';
    const OrderStatusVerif = 'Menunggu Verifikasi';

    protected $limit = 15;

    public function getData($customers, $req)
    {
        try {
            $data = $this->filter($customers, $req);
            $modifiedDates = array_column($data, 'modified_date');
            rsort($modifiedDates);
            $latest = !empty($modifiedDates) ? $modifiedDates[0] : date('Y-m-d H:i:s');

            $responseData = [
                'latest_update' => $latest,
                'data' => $data
            ];

            return $this->sendSuccess("Customers retrieved successfully.", $responseData);
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    private function filter($customers, $request)
    {
        //$customers = $customers->whereHas('customer');

        if ($id = $request->query('customer_id')) {
            $customers = $customers->where('customer_id', 'like', '%' . $id . '%');
        }

        if ($ownerName = $request->query('owner_name')) {
            $customers = $customers->whereHas('customer', function ($query) use ($ownerName) {
                $query->where('owner_name', 'like', '%' . $ownerName . '%');
            });
        }

        if ($storeName = $request->query('store_name')) {
            $customers = $customers->where('name', 'like', '%' . $storeName . '%');
            // $customers = $customers->whereHas('customer', function($query) use($storeName) {
            //     $query->where('instance_name', 'like', '%'. $storeName .'%');
            // });
        }

        if ($salesName = $request->query('sales_name')) {
            $customerIds = CustomerSales::select('customer_id')->whereHas('sales', function ($query) use ($salesName) {
                $query->where('sales_name', 'like', '%' . $salesName . '%');
            });
            $customers = $customers->whereIn('customer_id', $customerIds);
        }

        $status = $request->query('status');

        if ($status) {
            $statusValues = explode(',', $status);

            $customers = $customers->where(function ($query) use ($statusValues) {
                foreach ($statusValues as $value) {
                    switch ($value) {
                        case 'reject':
                            $query->orWhereHas('customer', function ($query) {
                                $query->where('is_rejected', 1);
                            });
                            break;

                        case 'freeze':
                            $query->orWhereHas('customer', function ($query) {
                                $query->where('is_pending_payment', 1);
                            });
                            break;

                        case 'active':
                            $query->orWhereHas('customer', function ($query) {
                                $query->where('is_active', 1);
                            });
                            break;

                        case 'non-active':
                            $query->orWhereHas('customer', function ($query) {
                                $query->where('is_active', 0);
                            });
                            break;
                    }
                }
            });
        }

        if ($type = $request->query('type')) {
            if ($type == 'cash') {
                $customers = $customers->whereHas('customer', function ($query) {
                    $query->where('top', 'T001');
                });
            } elseif ($type = 'tempo') {
                $customers = $customers->whereHas('customer', function ($query) {
                    $query->where('top', '!=', 'T001');
                });
            }
        }

        if ($type = $request->query('channel')) {
            if ($type == 'wholesales') {
                $customers = $customers->whereHas('customer', function ($query) {
                    $query->where('distribution_channel', 'WHOLESALES');
                });
            } elseif ($type = 'b2b') {
                $customers = $customers->whereHas('customer', function ($query) {
                    $query->where('distribution_channel', 'B2B')->where('is_verified', 1)->Where('is_active', 1)->orWhere('is_rejected', 1);
                });

            }
        }

        // if ($limit = $request->query('credit_limit')) {
        //     foreach (explode(',', $limit) as $value) {
        //         list($from, $to) = explode('-', $value);
        //         $customers = $customers->where('credit_limit_used_percentage', '>=', $from)->where('credit_limit_used_percentage', '<=', $to);
        //     }
        // }

        if ($limits = $request->query('credit_limit')) {
            $limitRanges = explode(',', $limits);
            $customers = $customers->where(function ($query) use ($limitRanges) {
                foreach ($limitRanges as $limitRange) {
                    $rangeValues = explode('-', $limitRange);

                    if (count($rangeValues) === 2) {
                        list($from, $to) = $rangeValues;
                        $query->orWhereBetween('credit_limit_used_percentage', [$from, $to]);
                    }
                }
            });
        }


        if ($limit = $request->query('limit')) {
            $this->limit = $limit;
        }

        $customers = $customers->orderBy('customer_shipment.created_date', 'desc');
        $customers = $customers->paginate($this->limit);
        //$customers = $customers->sortByDesc('created_date')->values;
        $data = InternalCustomerResource::collection($customers)->response()->getData(true);

        if ($order_by = $request->query('order_by')) {
            switch ($order_by) {
                case 'highest':
                    $data['data'] = collect($data['data'])->sortByDesc('total')->values();
                    break;
                case 'lowest':
                    $data['data'] = collect($data['data'])->sortBy('total')->values();
                    break;
                case 'alphabetA':
                    $data['data'] = collect($data['data'])->sortBy('owner_name', SORT_NATURAL | SORT_FLAG_CASE)->values();
                    break;
                case 'alphabetZ':
                    $data['data'] = collect($data['data'])->sortByDesc('owner_name', SORT_NATURAL | SORT_FLAG_CASE)->values();
                    break;
                default:
                    $data['data'] = collect($data['data'])->sortByDesc('registration_date')->values();
            }
        }

        return $data;
    }


    public function getById($request, $id)
    {
        try {
            // CreditLimitQueue::dispatch($id);
            $isMasking = filter_var($request->query('is_masking', true), FILTER_VALIDATE_BOOLEAN);
            $customer = Customer::findOrFail($id)->setMasking($isMasking);
            return $this->sendSuccess('Customer detail retrieved successfully', new CustomerInternalResource($customer, $isMasking));
        } catch (ModelNotFoundException $e) {
            return $this->sendError("Customer with id:" . $id . " not found.");
        } catch (Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function getOrders($req, $customerId, $customerType = null, $limit = 12)
    {
        try {
            if (!$customerType) {
                $customer = Customer::findOrFail($customerId);
            } else {
                $customer = Customer::channel($customerType)->findOrFail($customerId);
            }
    
            $orders = $customer->orders()->with(['invoice', 'invoice_billing', 'invoice_dp', 'customer_shipment', 'delivery_order']);
            
            if ($req->filled('invoice_no')) {
                $invoiceNo = $req->query('invoice_no');
                $orders = $orders->whereHas('invoice', function ($query) use ($invoiceNo) {
                    $query->where('invoice_no', 'LIKE', '%' . $invoiceNo . '%');
                });
            }
    
            if ($req->filled('order_no')) {
                $orderNo = $req->query('order_no');
                $orders->where('order_no', 'LIKE', '%' . $orderNo . '%');
            }
    
            if ($req->filled('billing_status')) {
                $billingStatus = $req->query('billing_status');
                $statuses = explode(',', strtoupper(str_replace('_', ' ', $billingStatus)));
                if (strtolower($customer->distribution_channel) == 'wholesales') {
                    $orders = $orders->whereHas('invoice', function ($query) use ($statuses) {
                        $query->whereIn('status', $statuses);
                    });
                } elseif (strtolower($customer->distribution_channel) == 'b2b') {
                    $orders = $orders->where(function ($q) use ($statuses) {
                        $q->whereHas('invoice_billing', function ($q2) use ($statuses) {
                            $q2->whereIn('status', $statuses);
                        })->orWhereHas('invoice_dp', function ($q3) use ($statuses) {
                            $q3->whereIn('status', $statuses);
                        });
                    });
                }
            }
    
            if ($req->filled('date_from') && $req->filled('date_to')) {
                $orders = $orders->whereHas('delivery_order', function ($q) use ($req) {
                    $q->whereDate('created_date', '>=', $req->query('date_from'))
                      ->whereDate('created_date', '<=', $req->query('date_to'));
                });
            }
    
            $order_by = $req->query('order_by');
            if ($order_by === 'highest') {
                $orders = $orders->orderBy('total', 'desc');
            } elseif ($order_by === 'lowest') {
                $orders = $orders->orderBy('total', 'asc');
            }
            $orders = $orders->orderBy('created_date', 'desc');
    
            $orders = $orders->get();
    
            if ($req->filled('order_status')) {
                $filterStatuses = explode(',', $req->query('order_status'));
    
                $orders = $orders->filter(function ($order) use ($filterStatuses, $customer) {
                    $status = $order->order_status;
    
                    if (strtolower($order->distribution_channel) == 'wholesales' && $customer->top == 'T001') {
                        if (!in_array($status, [
                            self::OrderStatusPending,
                            self::OrderStatusOnHold,
                            self::OrderStatusWait,
                            self::OrderStatusBaru,
                            self::OrderStatusCancel
                        ])) {
                            if (isset($order->invoice) && $order->invoice->status !== 'LUNAS') {
                                if ($status !== self::OrderStatusOnProcess) {
                                    $status = self::OrderStatusPembayaran;
                                }
                            }
                        }
                    }
    
                    return in_array($status, $filterStatuses);
                });
            }
    
            $limit = $req->query('limit', $limit);
            $page = $req->query('page', 1);
            $offset = ($page - 1) * $limit;
            $paginated = $orders->slice($offset, $limit)->values();
            $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
                $paginated,
                $orders->count(),
                $limit,
                $page,
                ['path' => $req->url(), 'query' => $req->query()]
            );
    
            return $this->sendSuccess(
                'Customer orders retrieved successfully',
                CustomerOrderResource::collection($paginator)->response()->getData(true)
            );
        } catch (ModelNotFoundException $e) {
            return $this->sendError("Customer with id:" . $customerId . " not found.");
        } catch (Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }    
}