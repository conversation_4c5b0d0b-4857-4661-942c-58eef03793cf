<?php

namespace App\Repositories;


use App\Helpers\ApiClient;
use App\Models\Customer;
use Illuminate\Support\Facades\Log;



class GetLimitRepo
{
    public function getLimit($data, $custId = null)
    {
        $base_url= env('API_EIGER_URL');
        $username= env('API_USERNAME');
        $password= env('API_PASSWORD');
        $url=$base_url . "/api/care-om/plafond";
        
        $sapuser = $custId === null ? '' : @Customer::where('customer_id', $custId)->customer_sales->sales->sap_username??'';

        $headers = ['sapuser' => $sapuser];
        $options = [
            'json' => $data
        ];
        Log::channel('stderr')->info('[REQUEST getLimit]', $data);
        Log::channel('stderr')->info('[REQUEST SAPUSER]', [$sapuser, $custId]);

        //to do SAP URL on off
       $response = ApiClient::request('POST', $url,  $options, $username, $password, $headers);
       
        return $response;
    }

    public function getStatusCode($response)
    {
        $re = json_decode($response->getStatusCode(), true);
    
    }

    public function getData($response)
    {
        $result = json_decode($response->getBody(), true);
        return $result['data'];
        
    }

}
