<?php

namespace App\Repositories;

use Illuminate\Support\Facades\DB;


class GetSocialsRepo
{
    public function getSocialMediaParameters($name)
    {
        $value = '';
        $query =  DB::table('master_parameter')
            ->select('value','key')
            ->where('key', 'like', 'SOCIAL_MEDIA%')
            ->get()->toArray();
            
        foreach ($query as $item) {
            if (strpos($item->key, $name)!== false) {
                $value = $item->value;
                break;
            }
        }

        return $value;
    }
}

