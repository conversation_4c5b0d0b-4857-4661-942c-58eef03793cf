<?php

namespace App\Repositories;

use App\Helpers\ApiClient;
use App\Models\Customer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Log;


class GetStokRepo
{

  private $limit = 12;
  protected $getStockURI;

  public function getStock($data, $custId = null)
  {
    $base_url = env('API_EIGER_URL');
    $username = env('API_USERNAME');
    $password = env('API_PASSWORD');
    $length = count($data['detail']) ?? 12;
    $url = $base_url . "/api/care-om/stock?item_numbers=$length";

    // $sapuser = $custId == null ?
    //   env('ARTICLE_STOCK_SAPUSER', 'sapadmin') :
    //   Customer::where('customer_id', $custId)->first()->customer_sales[0]->sap_username ?? env('ARTICLE_STOCK_SAPUSER', 'sapadmin');

    $sapuser = env('ARTICLE_STOCK_SAPUSER', 'eg_taryono');

    $headers = ['sapuser' => $sapuser];

    $options = [
      'json' => $data
    ];
    Log::channel('stderr')->info('[REQUEST getStock]', $data);
    Log::channel('stderr')->info('[REQUEST SAPUSER]', [$sapuser, $custId]);


    $response = ApiClient::request('POST', $url, $options, $username, $password, $headers);

    return $response;
  }

  public function getStatusCode($response)
  {
    $re = json_decode($response->getStatusCode(), true);
  }


  public function getData($response)
  {
    return ($response instanceof JsonResource || $response instanceof JsonResponse) ? $response->getData(true) : json_decode($response->getBody(), true);
  }





  public function getQty($response)
  {
    $result = json_decode($response->getBody(), true);
    if (array_key_exists('qty', $result['data'][0])) {
      return $result['data'][0]['qty'];
    }
  }

  public function getMoq($response)
  {
    $result = json_decode($response->getBody(), true);
    if (array_key_exists('moq', $result['data'][0])) {
      return (int) $result['data'][0]['moq'];
    } else {
      return 0;
    }
  }
}