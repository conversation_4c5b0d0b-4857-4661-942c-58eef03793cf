<?php

namespace App\Repositories;

use http\Exception;
use App\Models\Invoice;
use App\Models\Customer;
use App\Models\Proforma;
use App\Models\OrderItem;
use App\Traits\ResponseAPI;
use App\Models\InvoiceDetail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Interfaces\InvoiceInterface;
use App\Http\Resources\InvoiceResource;
use Illuminate\Pagination\LengthAwarePaginator;

class InvoiceRepository implements InvoiceInterface
{
    use ResponseAPI;
    private $limit = 6;
    private $offset = 0;

    const Semua = "Semua";
    const BelumDibayar = "Belum Dibayar";
    const BelumLunas = "Belum Lunas";
    const Lunas = "Lunas";

    public function getData($req, $customer_id)
    {
        try {
            $customer_id = is_array($customer_id) ? $customer_id : [$customer_id];
            $customer = Customer::whereIn('customer_id', $customer_id)->where('is_active', 1)->first();
            $status = $req->input('status');
            $no_inv = $req->input('text');
            $start_date = $req->input('date_from');
            $end_date = $req->input('date_to');
            $page = $req->input('page');
            $this->limit = $req->input('per_page');

            $invoices = Invoice::query()
                ->when($status, function ($query) use ($status) {
                    if ($status != self::Semua) {
                        return $query->where('status', '=', $status);
                    }
                    return $query;
                })
                ->when($no_inv, function ($query) use ($no_inv) {
                    return $query->where('order_no', 'like', '%' . $no_inv . '%')
                        ->orWhereHas('detail', function ($query) use ($no_inv) {
                            $query->where('product_name', 'like', '%' . $no_inv . '%');
                        });
                })
                ->when($start_date, function ($query) use ($start_date, $end_date) {
                    if ($start_date && !$end_date) {
                        return $this->sendError("must define start date and end date");
                    }

                    if ($end_date < $start_date) {
                        return $this->sendError("end date must greater than start date");
                    }

                    return $query->whereBetween('billing_date', [$start_date, $end_date]);
                })
                ->select('invoice_no', 'customer_id', 'gross_price', 'status', 'billing_date', 'order_no', 'due_date')
                ->whereIn('customer_id', $customer_id)
                ->groupBy('invoice_no')
                ->orderBy('billing_date', 'desc');

            $datas = [];

            if ($customer->top_days == 0 && ($status == self::BelumDibayar || $status == self::BelumLunas || $status == self::Semua || $status == '')) {
                $proforma_invoices = Proforma::query()->when($no_inv, function ($query) use ($no_inv) {
                    return $query->where('po_no', 'like', '%' . $no_inv . '%')
                        ->orWhereHas('detail', function ($query) use ($no_inv) {
                            $query->where('product_name', 'like', '%' . $no_inv . '%');
                        });
                })->when($start_date, function ($query) use ($start_date, $end_date) {
                    if ($start_date && !$end_date) {
                        return $this->sendError("must define start date and end date");
                    }

                    if ($end_date < $start_date) {
                        return $this->sendError("end date must greater than start date");
                    }

                    return $query->whereBetween('created_date', [$start_date, $end_date]);
                })->select('sales_order_no as invoice_no', 'customer_external_id as customer_id', 'gross_price', 'created_date', 'po_no as order_no', 'due_date')
                    ->whereIn('customer_external_id', $customer_id)
                    ->groupBy('sales_order_no')
                    ->orderBy('created_date', 'desc')
                    ->get();

                $mergedCollection = collect($invoices->get())
                    ->merge($proforma_invoices)
                    ->values();

                $invoices = new LengthAwarePaginator(
                    $mergedCollection->forPage($page, $this->limit),
                    $mergedCollection->count(),
                    $this->limit,
                    $page,
                    ['path' => request()->url(), 'query' => request()->query()]
                );

            } else {
                $invoices = $invoices->paginate($this->limit, ['*'], 'page', $page);
            }

            $inv_ids = $invoices->pluck('order_no');

            $dtl = DB::table('order_detail')
                ->whereIn('order_no', $inv_ids)
                ->select('order_no', 'qty')
                ->get()->groupBy('order_no');

            $img = OrderItem::whereIn('order_no', $inv_ids)
                ->with([
                    'article' => function ($query) {
                        $query->with([
                            'image_generic' => function ($query) {
                                $query->select('file_path', 'sku_code_c'); // Select specific fields in image_generic
                            }
                        ]);
                    }
                ])
                ->select('order_no', 'article_id') // Select specific fields in order_detail
                ->get()
                ->groupBy('order_no');

            $datas = $invoices->map(function ($inv) use ($dtl, $img) {
                return [
                    'invoice_no' => (int) $inv->invoice_no,
                    'order_no' => $inv->order_no,
                    'customer_id' => (string) $inv->customer_id,
                    'gross_price' => $inv->gross_price,
                    'status' => $inv->status ?? 'Belum Lunas',
                    'product_name' => !isset($img[$inv->order_no]) || !isset($img[$inv->order_no][0]->article) ? '-' : $img[$inv->order_no][0]->article->product_name_c,
                    'product_category_name' => !isset($img[$inv->order_no]) || !isset($img[$inv->order_no][0]->article) ? '-' : $img[$inv->order_no][0]->article->lvl3_description,
                    'billing_date' => $inv->billing_date ?? $inv->created_date,
                    'due_date' => $inv->due_date,
                    'total_product' => isset($dtl[$inv->order_no]) ? (string) collect($dtl[$inv->order_no])->sum('qty') : 0,
                    'main_image' => !isset($img[$inv->order_no]) || !isset($img[$inv->order_no][0]->image_generic->file_path) ? 'https://careom-middleware.eigerindo.co.id/assets/product/default-product.webp' : env('S3_STREAM_URL') . $img[$inv->order_no][0]->image_generic->file_path
                ];
            });

            $res = [
                'total_data' => $invoices->total(),
                'size' => intval($invoices->perPage()),
                'active_page' => $invoices->currentPage(),
                'total_page' => $invoices->lastPage(),
                'data' => $datas->values() ?? $datas
            ];

            return $this->sendSuccess("Invoice retrieved successfully.", $res);
        } catch (\Exception $e) {
            return $this->sendError([$e->getMessage(), $e->getCode(), $e->getFile(), $e->getLine()]);
        }
    }

    private function getTotalPage($total_data, $size)
    {
        $tot_page = $total_data % $size;

        if ($tot_page == 0) {
            return $total_data / $size;
        }

        return (int) ($total_data / $size) + 1;
    }

    public function getStatuses()
    {
        $status = [
            self::Semua,
            self::BelumDibayar,
            self::Lunas
        ];
        return $this->sendSuccess("success retrieved status", $status);
    }

    public function getDetailItems($invoice_no)
    {
        // TODO: Implement getDetailItems() method.
    }


}