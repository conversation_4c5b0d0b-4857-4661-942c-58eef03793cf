<?php

namespace App\Repositories;

use App\Http\Resources\ParameterResource;
use App\Interfaces\ParameterInterface;
use App\Models\MasterParameter;
use App\Traits\ResponseAPI;

class ParameterRepository implements ParameterInterface
{
    use ResponseAPI;
    public function getParameter($key)
    {
        try {
            $data = MasterParameter::query()
                ->where('group_key','=',$key)->get();
            if (!$data->count()) {
                return $this->sendError("There's no data found.", 404, '404 not found.');
            }
            return $this->sendSuccess('parameter '.$key.' retrieved successfully',
                ParameterResource::collection($data));
        }catch (\Exception $e) {
            return $this->sendError('failed get parameter, '.$e->getMessage());
        }
    }

}
