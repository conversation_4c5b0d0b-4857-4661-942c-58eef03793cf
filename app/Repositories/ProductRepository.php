<?php

namespace App\Repositories;

use Exception;
use App\Models\Cart;
use App\Models\Color;
use App\Models\Article;
use App\Models\Product;
use App\Helpers\ApiClient;
use App\Jobs\SyncStockJob;
use App\Models\CartDetail;
use App\Models\ProductSku;
use App\Helpers\RestHelper;
use App\Traits\ResponseAPI;
use App\Models\PublicProduct;
use App\Jobs\StockUpdateBatch;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\MenuResource;
use App\Interfaces\ProductInterface;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Queue;
use App\Http\Resources\ProductCollection;
use App\Http\Resources\ProductDetailResource;
use App\Http\Resources\ProductVariantResource;
use App\Http\Resources\ProductDetailCollection;
use App\Http\Resources\PublicProductCollection;
use App\Http\Resources\ProductDetailNewResource;
use App\Http\Resources\InternalProductCollection;
use App\Http\Resources\ResellerProductCollection;
use App\Http\Resources\ProductDetailPublicResource;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Http\Resources\InternalProductResellerCollection;
use App\Http\Resources\ProductDetailPublicInternalResource;
use App\Http\Resources\ProductVariantInternalResellerResource;

class ProductRepository implements ProductInterface
{
    use ResponseAPI;

    private $limit = 12;

    protected $getStockURI;

    public function getData($product, $req, $withStock = true, $isGeneric = false, $isB2B = false)
    {
        try {
            if($isB2B == true){
                $req->is_b2b = true;
                $fakeUser = new \stdClass();
                $fakeUser->customer = new \stdClass();
                $fakeUser->reference_object = 'customer';
                $fakeUser->customer->distribution_channel = 'B2B';
                $fakeUser->customer->cart = null;
                $req->setUserResolver(function () use ($fakeUser) {
                    return $fakeUser;
                });
                
                $data = $this->filter($product, $req, $withStock, $isGeneric);
            }
            if (strpos($req->route()->uri, 'rsl/internal')) {
                $data = $this->filterReseller($product, $req, $withStock, $isGeneric);
            } else {
                if (!$req->user() || strpos($req->route()->uri, 'rsl/')) {
                    $data = $this->filterPublic($product, $req, $withStock, $isGeneric);
                } else {
                    $data = $this->filter($product, $req, $withStock, $isGeneric);
                }
            }


            return $this->sendSuccess("Products retrieved successfully.", $data);
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
        }
    }

    public function getFilter($data, $isColor = false)
    {
        try {
            if ($isColor) {
                $data = $this->getColors($data);
            }
            return $this->sendSuccess('Data retrieved successfully.', $data);
        } catch (Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }
    
    private function getColors($codes)
    {
        return collect($codes)->map(function ($item) {
            return [
                'key' => $item->key,
                'value' => $item->value
            ];
        })->all();
    }

    private function filter($product, $request, $withStock, $isGeneric)
    {
        $orderField = 'article.transfer_date';
        $orderBy = 'desc';

        $product->when($request->query('category'), function ($query, $category) {
            return $this->category($query, $category);
            })->when($request->filled('product_category'), function ($query) use ($request) {
                $productCategory = $request->query('product_category');
                $productCategory = is_array($productCategory) ? $productCategory : explode(',', $productCategory);
                return $query->whereIn('lvl4_description', $productCategory);
            })->when($request->filled('subcategory'), function ($query) use ($request) {
                $subcategory = $request->query('subcategory');
                $subcategory = is_array($subcategory) ? $subcategory : explode(',', $subcategory);
                return $query->whereIn('lvl4_description', $subcategory);
            })->when($request->filled('activity'), function ($query) use ($request) {
                $activity = $request->query('activity');
                $activity = is_array($activity) ? $activity : explode(',', $activity);
                return $query->whereIn('product_activity', $activity);
            })->when($request->filled('color'), function ($query) use ($request) {
                $color = $request->query('color');
                $color = is_array($color) ? $color : explode(',', $color);
                return $query->whereIn('product_variant_c', $color);
            })->when($request->filled('size'), function ($query) use ($request) {
                $size = $request->query('size');
                $size = is_array($size) ? $size : explode(',', $size);
                return $query->whereIn('product_size_c', $size);
            });

        $product->when($request->query('search'), function ($query, $search) {
            return $query->where(function ($subQuery) use ($search) {
                $subQuery->where('article_description', 'like', "%$search%")
                    ->orWhere('article.sku_code_c', 'like', "%$search%")
                    ->orWhere('lvl4_description', 'like', "%$search%");
            });
        });

        $product->when($request->query('minPrice') !== null && $request->query('maxPrice') !==null, function ($query) use ($request) {
            return $query->whereHas('price', function ($q) use ($request) {
                $q->whereBetween('amount', [$request->query('minPrice'), $request->query('maxPrice')]);
            });
        });

        $product->when($request->query('type'), function ($query, $type) {
            return match ($type) {
                'wab' => $query->where('is_wab', 1),
                'reseller' => $query->where('is_reseller', 1),
                'wholesales' => $query->where('is_wholesales', 1),
                'b2b' => $query->where('is_b2b', 1),
                default => $query
            };
        });

        $product->when(
            $request->query('order_by', 'newest'),
            function ($query, $order_by) use ($request) {
                return match ($order_by) {
                    'highest', 'lowest' => $query
                        ->leftJoin('article_price', 'article_price.sku_code_c', '=', 'article.sku_code_c')
                        ->orderBy('article_price.amount', $order_by === 'highest' ? 'desc' : 'asc'),

                    'alphabetA' => $query->orderBy('product_name_c', 'asc'),
                    'alphabetZ' => $query->orderBy('product_name_c', 'desc'),

                    'newest', 'oldest' => $query->when(
                        ($request->user()->reference_object == 'customer' && $request->user()->customer->distribution_channel == 'B2B')
                        || ($request->user()->reference_object == 'sales' && $request->type == 'b2b'),
                        fn ($q) => $q->orderBy('b2b_published_date', $order_by === 'newest' ? 'desc' : 'asc')
                                    ->orderBy('product_name_c', 'asc'),
                        fn ($q) => $q->orderBy('transfer_date', $order_by === 'newest' ? 'desc' : 'asc')
                    ),

                    default => $query
                };
            }
        );

        $product->when($request->query('misc') === 'newarrival', function ($query) {
            $date = now()->format('Y-m-d');
            // return $query->where('transfer_date', '<=', $date)->where('expired_date', '>=', $date);
            return $query->orderBy('transfer_date', 'desc');
        });

        $product->when($request->query('custom'), function ($query, $custom) {
            return match ($custom) {
                'logo' => $query->where('is_custom_logo', 1),
                'size' => $query->where('is_custom_size', 1),
                default => $query
            };
        });

        if ($statuses = $request->query('status')) {
            $statusArray = explode(',', $statuses);
        
            $product = $product->where(function ($query) use ($statusArray) {
                foreach ($statusArray as $status) {
                    switch (trim($status)) {
                        case 'tersedia':
                            $query->orWhere('total_stock', '>', 3);
                            break;
                        case 'kosong':
                            $query->orWhere('total_stock', 0);
                            break;
                        case 'terbatas':
                            $query->orWhere(function ($q) {
                                $q->where('total_stock', '<=', 3)
                                  ->where('total_stock', '>', 0);
                            });
                            break;
                    }
                }
            });
        }

        $limit = $request->query('limit', $this->limit);
        $item_status = $request->query('status') ?? null;
        $prepareData = $product->orderBy($orderField, $orderBy)->paginate($limit);
        
        return $withStock
            ? $this->getStock($prepareData, $item_status, $isGeneric)
            : $this->getWithoutStock($prepareData, $request->query('type'), $item_status);
    }

    private function filterReseller($product, $request, $withStock, $isGeneric)
    {
        $product = $product->leftJoin('article_price', function ($join) {
            $join->on('article.sku_code_c', '=', 'article_price.sku_code_c');
            $join->whereRaw('? BETWEEN article_price.valid_from AND article_price.valid_to', [now()->format('Y-m-d')]);
            $join->latest();
        })->leftJoin('rsl_article_stock', function ($join) {
            $join->on('article.article', '=', 'rsl_article_stock.article');
        });

        $orderField = 'article.transfer_date';
        $orderBy = 'desc';

        if ($category = $request->query('category')) {
            $product = $this->category($product, $category);
        }

        if ($subcategory = $request->query('subcategory')) {
            $product = $product->where('lvl4_description', explode(',', $subcategory));
        }

        if ($activity = $request->query('activity')) {
            $product = $product->whereIn('lvl2_description', explode(',', $activity));
        }

        if ($color = $request->query('color')) {
            $product = $product->whereIn('product_variant_c', explode(',', $color));
        }

        if ($size = $request->query('size')) {
            $product = $product->whereIn('product_size_c', explode(',', $size));
        }

        if ($search = $request->query('search')) {
            $product = $product->where(function ($query) use ($search) {
                return $query->where('article_description', 'like', '%' . $search . '%')
                    ->orWhere('article.sku_code_c', 'like', '%' . $search . '%')
                    ->orWhere('lvl4_description', 'like', '%' . $search . '%');
            });
        }

        if ($request->has('minPrice') || $request->has('maxPrice')) {
            $minPrice = $request->query('minPrice') ?? 0;
            $maxPrice = $request->query('maxPrice') ?? PHP_INT_MAX;
            $product = $product->whereBetween('article_price.amount', [$minPrice, $maxPrice]);
        }

        $product = $product->where('is_reseller', 1);

        if ($order_by = $request->query('order_by')) {
            switch ($order_by) {
                case 'stockAsc':
                case 'stockDesc':
                    $orderBy = $order_by == 'stockDesc' ? 'desc' : 'asc';
                    $product = $product->orderBy('available_stock', $orderBy);
                    break;

                case 'alphabetA':
                case 'alphabetZ':
                    $orderBy = $order_by == 'alphabetZ' ? 'desc' : 'asc';
                    $product = $product->orderBy('product_name_c', $orderBy);
                    break;

                case 'newest':
                case 'oldest':
                    $orderBy = $order_by == 'newest' ? 'desc' : 'asc';
                    if (
                        $request->user()->reference_object == 'customer' && $request->user()->customer->distribution_channel == 'B2B'
                        || $request->user()->reference_object == 'sales' && $request->type == 'b2b'
                    ) {
                        $product = $product->orderBy('b2b_published_date', $orderBy)->orderBy('product_name_c', 'asc');
                    } else {
                        $product = $product->orderBy('transfer_date', $orderBy);
                    }
                    break;
                default:
                    $product = $product->orderBy('available_stock', 'desc');
                    break;
            }
        }

        if ($order_by = $request->query('misc')) {
            $date = now()->format('Y-m-d');
            switch ($order_by) {
                case 'newarrival':
                    // $product = $product->where('transfer_date', '<=', $date)->where('expired_date', '>=', $date);
                    $product = $product->orderBy('transfer_date', $orderBy);
                    break;

            }
        }

        if ($limit = $request->query('limit')) {
            $this->limit = $limit;
        }

        if ($custom = $request->query('custom')) {
            switch ($custom) {
                case 'logo':
                    $product = $product->where('is_custom_logo', 1);
                    break;
                case 'size':
                    $product = $product->where('is_custom_size', 1);
                    break;
            }
        }

        if ($item_status = $request->query('status')) {
            switch ($item_status) {
                case 'tersedia':
                    $product = $product->where('available_stock', '>', 3);
                    break;
                case 'kosong':
                    $product = $product->where('available_stock', 0);
                    break;
                case 'terbatas':
                    $product = $product->where('available_stock', '<=', 3)->where('available_stock', '>', 0);
                    break;
            }
        }


        $prepareData = $product->orderBy($orderField, $orderBy)->paginate($this->limit);
        foreach ($data = $prepareData as $i => $product) {
            $data[$i]->available_stock = !$isGeneric ? intval(Product::leftJoin('rsl_article_stock', function ($join) {
                $join->on('article.article', '=', 'rsl_article_stock.article');
            })
                ->leftJoin('rsl_master_site', function ($join) {
                    $join->on('rsl_article_stock.location_code', '=', 'rsl_master_site.code');
                })
                ->where('article.sku_code_c', $data[$i]->sku_code_c)->where('rsl_master_site.is_active', true)->sum('available_stock')) : $data[$i]->available_stock;
        }
        $prepareData = $data;

        foreach ($prepareData as $data) {
            if ($data->sku_code_c == null || $data->article == null) {
                $fill = Product::where('article_description', $data->article_description)->first();
                $data->sku_code_c = $fill->sku_code_c;
                $data->article = $fill->article;
            } else {
                $data->sku_code_c;
                $data->article;
            }
        }

        return $this->getStockReseller($prepareData);

    }

    private function filterPublic($product, $request, $withStock, $isGeneric)
    {

        $product = $product->leftJoin('article_price', function ($join) {
            $join->on('article.sku_code_c', '=', 'article_price.sku_code_c');
            $join->whereRaw('? BETWEEN article_price.valid_from AND article_price.valid_to', [now()->format('Y-m-d')]);
            $join->latest();
        });

        if ($request->query('order_by') == 'promo') {
            $product = $product->leftJoin('rsl_article_filter', 'article.article', '=', 'rsl_article_filter.article');
        }

        $orderField = 'article.transfer_date';
        $orderBy = 'desc';

        if ($category = $request->query('category')) {
            $product = $this->category($product, $category);
        }

        if ($subcategory = $request->query('subcategory')) {
            $product = $product->where('lvl4_description', explode(',', $subcategory));
        }

        if ($activity = $request->query('activity')) {
            $product = $product->whereIn('lvl2_description', explode(',', $activity));
        }

        if ($color = $request->query('color')) {
            $product = $product->whereIn('product_variant_c', explode(',', $color));
        }

        if ($size = $request->query('size')) {
            $product = $product->whereIn('product_size_c', explode(',', $size));
        }

        if ($search = $request->query('search')) {
            $product = $product->where(function ($query) use ($search) {
                return $query->where('article_description', 'like', '%' . $search . '%')
                    ->orWhere('article.sku_code_c', 'like', '%' . $search . '%')
                    ->orWhere('lvl4_description', 'like', '%' . $search . '%')
                    ->orWhere('lvl3_description', 'like', '%' . $search . '%');
            });
        }

        if ($request->has('minPrice') || $request->has('maxPrice')) {
            $minPrice = $request->query('minPrice') ?? 0;
            $maxPrice = $request->query('maxPrice') ?? PHP_INT_MAX;
            $product = $product->whereBetween('article_price.amount', [$minPrice, $maxPrice]);
        }

        if ($type = $request->query('type')) {
            switch ($type) {
                case 'wab':
                    $product = $product->where('is_wab', 1);
                    break;
                case 'reseller':
                    $product = $product->where('is_reseller', 1);
                    break;
                case 'wholesales':
                    $product = $product->where('is_wholesales', 1);
                    break;
                case 'b2b':
                    $product = $product->where('is_b2b', 1);
                    break;
            }
        }


        if ($order_by = $request->query('order_by', 'newest')) {
            switch ($order_by) {
                case 'highest':
                case 'lowest':
                    $orderBy = $order_by == 'highest' ? 'desc' : 'asc';
                    // $product = $product->whereHas('price', function($q) use ($orderBy){
                    //     $q->orderBy('amount',$orderBy);
                    // });
                    $product = $product->orderBy('article_price.amount', $orderBy);
                    break;

                case 'alphabetA':
                case 'alphabetZ':
                    $orderBy = $order_by == 'alphabetZ' ? 'desc' : 'asc';
                    $product = $product->orderBy('product_name_c', $orderBy);
                    break;

                case 'newest':
                case 'oldest':
                    $orderBy = $order_by == 'newest' ? 'desc' : 'asc';
                    $product = $product->orderBy('transfer_date', $orderBy);
                    break;
                case 'promo':
                    $product = $product->orderBy('rsl_article_filter.discount_percentage', 'desc');
                    break;
            }
        }

        if ($order_by = $request->query('misc')) {
            $date = now()->format('Y-m-d');
            switch ($order_by) {
                case 'newarrival':
                    $product = $product->where('transfer_date', '<=', $date)->where('expired_date', '>=', $date);
                    break;

            }
        }

        if ($limit = $request->query('limit')) {
            $this->limit = $limit;
        }

        if ($custom = $request->query('custom')) {
            switch ($custom) {
                case 'logo':
                    $product = $product->where('is_custom_logo', 1);
                    break;
                case 'size':
                    $product = $product->where('is_custom_size', 1);
                    break;
            }
        }

        return $this->getPublic($product->orderBy($orderField, $orderBy)->paginate($this->limit));
    }

    private function category($product, $key)
    {
        $explode = explode(',', $key);
        if (in_array('semua', $explode)) {
            return $product;
        }
        if (count($explode) > 1) {
            if (in_array('non-bags', $explode) && !in_array('bags', $explode)) {
                return $product->whereNotIn('lvl3_description', ['bags']);
            }
            if (in_array('non-bags', $explode) && !in_array('footwear', $explode)) {
                return $product->whereNotIn('lvl3_description', ['footwear']);
            }
            return $product->whereIn('lvl3_description', $explode);
        } else {
            switch ($key) {
                case 'non-bags':
                    return $product->whereNotIn('lvl3_description', ['bags', 'footwear']);
                default:
                    return $product->where('lvl3_description', $key);
            }
        }
    }

    public function getDetail($id)
    {
        try {
            Log::info("ini id = " . $id);
            $datas = Product::where('sku_code_c', $id)->get();
            if (!$datas->count()) {
                return $this->sendError("There's no data found.", 404, '404 not found.');
            }

            return $this->sendSuccess("Products retrieved successfully.", new ProductDetailCollection($datas));
        } catch (\Exception $e) {
            Log::info("ini err = " . $e->getMessage());
            return $this->sendError($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    public function getDetailNew($id, $customer_type = null, $isReseller = false)
    {
        if ($isReseller) {
            try {

                $datas = Product::where('sku_code_c', $id)->firstOrFail();
                
                return $this->sendSuccess("Products retrieved successfully.", new ProductDetailNewResource($datas));
            } catch (\Exception $e) {
                return $this->sendError($e->getMessage() . ' on line ' . $e->getLine());
            }
        }

        if (!$customer_type) {
            $customer_type = @Auth::user()->customer->customer_type;
        }
        try {
            $datas = Product::exclude($customer_type)->where('sku_code_c', $id)->firstOrFail();
            return $this->sendSuccess("Products retrieved successfully.", new ProductDetailNewResource($datas));
        } catch (ModelNotFoundException) {
            return $this->sendError('Product with sku: ' . $id . ' not found.');
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage() . ' in file ' . $e->getFile() . ' on line ' . $e->getLine());
        }
    }

    public function getDetailPublic($id)
    {
        try {
            $datas = PublicProduct::where('sku_code_c', $id)->firstOrFail();
            return $this->sendSuccess("Products retrieved successfully.", new ProductDetailPublicResource($datas));
        } catch (ModelNotFoundException) {
            return $this->sendError('Product with sku: ' . $id . ' not found.');
        } catch (Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function getDetailPublicInternalReseller($id)
    {
        try {
            $datas = PublicProduct::where('sku_code_c', $id)->firstOrFail();
            return $this->sendSuccess("Products retrieved successfully.", new ProductDetailPublicInternalResource($datas));
        } catch (ModelNotFoundException) {
            return $this->sendError('Product with sku: ' . $id . ' not found.');
        } catch (Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function getDetailNewInternal($id)
    {
        try {
            $datas = Product::where('sku_code_c', $id)->firstOrFail();
            return $this->sendSuccess("Products retrieved successfully.", new ProductDetailNewResource($datas));
        } catch (ModelNotFoundException) {
            return $this->sendError('Product with sku: ' . $id . ' not found.');
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function getDetailVariantNew($sku, $color, $customer_type = null)
    {
        if (!$customer_type) {
            $customer_type = @Auth::user()->customer->customer_type;
        }
        try {
            $datas = Product::exclude($customer_type)
                ->where('sku_code_c', $sku)
                ->whereIn('product_variant_c', [$color->key, $color->value]);

            SyncStockJob::dispatch($datas->pluck('article')->toArray(), $sku);
            
            $datas = $datas->with('price', 'skuStock')->get();
            // $stock_cache = RestHelper::stockCache($datas->pluck('article')->toArray(), auth()->user()->customer->customer_id ?? null);
            // $datas = RestHelper::addFieldStockMoq($stock_cache, $datas->toArray());
            // $datas = RestHelper::vArrayToObject($datas);
            // $stock_cache = 'test';
            return $this->sendSuccess("Products retrieved successfully.", ProductVariantResource::collection($datas));
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage() . ' in file ' . $e->getFile() . ' on line ' . $e->getLine());
        }
    }

    public function getDetailVariantNewCustomer($sku, $color, $customer_type = null, $customer_id)
    {
        if (!$customer_type) {
            $customer_type = @Auth::user()->customer->customer_type;
        }
        try {
            $datas = Product::exclude($customer_type)
                ->where('sku_code_c', $sku)
                ->whereIn('product_variant_c', [$color->key, $color->value])
                ->with('price')->get();
            $stock_cache = RestHelper::stockCache($datas->pluck('article')->toArray(), auth()->user()->customer->customer_id ?? null);
            $datas = RestHelper::addFieldStockMoq($stock_cache, $datas->toArray());
            $datas = RestHelper::vArrayToObject($datas);
            return $this->sendSuccess("Products retrieved successfully.", ProductVariantResource::collection($datas));
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    public function getDetailVariant($id)
    {
        try {
            $codes = explode('.', $id);
            $datas = Product::query()
                ->where('sku_code_c', $codes[0])
                ->where('product_variant_c', $codes[1])
                ->with('price')->get();
            if (!$datas->count()) {
                return $this->sendError("There's no data found.", 404, '404 not found.');
            }
            $stock_cache = RestHelper::stockCache($datas->pluck('article')->toArray(), auth()->user()->customer->customer_id ?? null);
            $datas = RestHelper::addFieldStockMoq($stock_cache, $datas->toArray());
            $datas = RestHelper::vArrayToObject($datas);
            Log::info("id " . $id);
            if (auth()->user()->sales) {
                return $this->sendSuccess("Products retrieved successfully.", ProductVariantInternalResellerResource::collection($datas));
            } else {
                return $this->sendSuccess("Products retrieved successfully.", ProductVariantResource::collection($datas));
            }
        } catch (\Exception $e) {
            Log::info("ini err = " . $e->getMessage());
            return $this->sendError($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    public function getMenus($req, $cid)
    {
        try {
            if ($menu = $req->query('menu')) {
                log::info("get sub menu = " . $menu);
                $datas = Product::query()->where('lvl2_description', '=', $menu)
                    ->groupBy('lvl2_description')->get();
            } else {
                $datas = Product::query()
                    ->whereNotNull('lvl2_description')
                    ->groupBy('lvl2_description')->get();
            }

            $extras = CartDetail::whereHas(
                'cart',
                function ($query) use ($cid) {
                    $query->where('customer_id', $cid);
                }
            )->count();

            $respEx = ['customer_id' => $cid, 'cartcount' => $extras];


            if (!$datas->count()) {
                return $this->sendError("There's no data found.", 404, '404 not found.');
            }
            return $this->sendExtraSuccess("menu retrieved successfully.", MenuResource::collection($datas), '200 OK', $respEx);
        } catch (Exception $e) {
            log::info("ini err get menu = " . $e->getMessage());
            return $this->sendError($e->getMessage(), $e->getCode() ?: 500);
        }
    }

    private function getStock($products, $request_status, $isGeneric)
    {
        $detail = [];
        foreach ($products as $i => $product) {
            $detail[$i] = [
                'source' => 'CAREOM',
                'destination' => 'STK',
                'article' => $product->article,
                // 'site' => '1200'
            ];
        }

        if (Auth::user() && Auth::user()->reference_object == 'sales') {
            $products = json_decode((new InternalProductCollection($products, $isGeneric))->toJson(), true);
        } else {
            if (Auth::user() && Auth::user()->reference_object == 'reseller') {
                $products = json_decode((new ResellerProductCollection($products))->toJson(), true);
            } else {
                $products = json_decode((new ProductCollection($products))->toJson(), true);
            }
        }

        //Handling stock update async
        if (!Auth::check() || Auth::user()->reference_object != 'reseller') {
            // StockUpdateBatch::dispatch($detail, Auth::user()->reference_id);
            // $stock = RestHelper::stockCache(data_get($products, 'data.*.id'),auth()->user()->customer->customer_id ?? null);
            // foreach ($data = data_get($products, 'data') as $i => $product) {
            // if ($data[$i]['stock'] !== 0) {
            //     $data[$i]['stock'] = !$isGeneric
            //         ? intval($data[$i]['stock']) ?? 0
            //         : array_reduce(ProductSku::select('stock')->where('sku_code_c', $data[$i]['sku'])->get()->all(), function($total, $article)
            //             {
            //                 return $total += intval($article['stock']) ?? 0;
            //             }, 0);

            //     $data[$i]['stock'] = max(0, intval($data[$i]['stock']));
            // } else {
            // $data[$i]['stock'] = !$isGeneric
            //     ? intval(RestHelper::searchStock($stock,$product['id'])['qty'] ?? 100)
            //     :array_reduce(Product::select('article')->where('sku_code_c', $data[$i]['sku'])->get()->all(), function($total, $article) use($stock)
            //     {
            //         return $total += RestHelper::searchStock($stock,$article['article'])['qty'] ?? 0;
            //     }, 0);

            // $data[$i]['stock'] = max(0, intval($data[$i]['stock']));
            // }
            // }

            $data = data_get($products, 'data', []);

            if(Auth::check() && Auth::user()->reference_object == 'sales'){

                $skuStocks = !$isGeneric ? [] : ProductSku::select('sku_code_c', 'stock')->whereIn('sku_code_c', array_column($data, 'sku'))->get()->groupBy('sku_code_c');
                foreach ($data as $i => $product) {
                    if (!$isGeneric) {
                        $data[$i]['stock'] = intval($data[$i]['stock'] ?? 0);
                    } else {
                        $totalStock = $skuStocks->get($data[$i]['sku'], collect())->sum('stock');
                        $data[$i]['stock'] = intval($totalStock);
                    }
    
                    $data[$i]['stock'] = max(0, $data[$i]['stock']);
                }
            }

            $products['data'] = $data;

            // $request_status_array = explode(",", $request_status);
            // $isAllStatus = in_array("semua", $request_status_array);
            // $isEmpty = in_array("kosong", $request_status_array);
            // $isTerbatas = in_array("terbatas", $request_status_array);
            // $isTersedia = in_array("tersedia", $request_status_array);

            // if (!$isAllStatus && $request_status != "") {
            //     $products_filtered = [];

            //     foreach ($products['data'] as $index => $product) {
            //         if ($isTersedia) if ($product['stock'] > 3)
            //             array_push($products_filtered, $product);
            //         if ($isTerbatas) if ($product['stock'] > 0 && $product['stock'] <= 3)
            //             array_push($products_filtered, $product);
            //         if ($isEmpty) if ($product['stock'] == 0)
            //             array_push($products_filtered, $product);
            //     }

            //     $products['data'] = $products_filtered;
            // }
        }

        // if ($sort = request()->query('sort')) {
        //     $sort = strtolower($sort);
        //     list($sortKey, $sortValue) = explode(':', $sort);
        //     $productData = collect(data_get($products, 'data'))->sortBy([
        //         [$sortKey, $sortValue]
        //     ])->values()->all();
        //     $products['data'] = $productData;
        // }
        // } else {
        // $products['data'] = collect(data_get($products, 'data'))->values()->all();
        // }

        return $products;
    }

    private function mergeArrays(...$arrays)
    {

        $length = count($arrays[0]);
        $result = [];
        for ($i = 0; $i < $length; $i++) {
            $temp = [];
            foreach ($arrays as $array)
                $temp[] = $array[$i];

            $result[] = $temp;
        }

        return $result;

    }

    private function getWithoutStock($products, $type, $item_status)
    {
        $detail = [];

        if (Auth::user()->reference_object == 'sales') {
            $products = json_decode((new InternalProductCollection($products))->toJson(), true);
        } else {
            if (Auth::user()->reference_object == 'reseller') {
                $products = json_decode((new ResellerProductCollection($products))->toJson(), true);
            } else {
                $products = json_decode((new ProductCollection($products))->toJson(), true);
            }
        }

        foreach ($data = data_get($products, 'data') as $i => $product) {
            $data[$i]['stock'] = 0;
        }
        $products['data'] = $data;

        if ($item_status) {
            $data = collect(data_get($products, 'data'))->filter(function ($item) use ($item_status) {
                return data_get($item, 'item_status') === $item_status;
            })->values()->all();

            $products['data'] = $data;
        }

        if ($sort = request()->query('sort')) {
            $sort = strtolower($sort);
            list($sortKey, $sortValue) = explode(':', $sort);
            $productData = collect(data_get($products, 'data'))->sortBy([
                [$sortKey, $sortValue]
            ])->values()->all();
            $products['data'] = $productData;
        } else {
            $productData = collect(data_get($products, 'data'))->sortByDesc('stock')->values()->all();
            $products['data'] = $productData;
        }

        return $products;
    }

    private function getStockReseller($products)
    {
        $products = json_decode((new InternalProductResellerCollection($products))->toJson(), true);
        return $products;
    }

    private function getPublic($products)
    {

        $products = json_decode((new PublicProductCollection($products))->toJson(), true);

        // foreach ($data = data_get($products, 'data') as $i => $product) {
        //     $data[$i]['stock'] = 199; //TODO hardcoded
        // }
        // $products['data'] = $data;

        if ($sort = request()->query('sort')) {
            $sort = strtolower($sort);
            list($sortKey, $sortValue) = explode(':', $sort);
            $productData = collect(data_get($products, 'data'))->sortBy([
                [$sortKey, $sortValue]
            ])->values()->all();
            $products['data'] = $productData;
        } else {
            $productData = collect(data_get($products, 'data'))->sortBy([
                ['stock', 'desc'],
                ['published_date', 'desc'],
                ['image', 'desc']
            ])->values()->all();
            $products['data'] = $productData;
        }

        return $products;
    }
}