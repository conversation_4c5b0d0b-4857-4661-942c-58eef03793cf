<?php
namespace App\Repositories;
use App\Models\Region;
use App\Models\City;
use App\Models\District;
use App\Models\Subdistrict;
// use App\Models\MasterAddress;
use App\Models\ResellerMasterAddress;
use App\Models\TransportationZone;
// use App\Models\ResellerMasterSite;
use Illuminate\Support\Facades\DB;
// use Illuminate\Support\Facades\Log;
// use Illuminate\Support\Facades\Storage;

class RegionRepository
{

    public function getRegionsNew($page, $perPage, $search)
    {
        $region = ResellerMasterAddress::select('region_code as code', 'region_name as name', 'region_name as island')->distinct('region_code');

        if (!empty($search)) {
            $region = $region->where(function ($query) use($search) {
                $query->where('region_code', 'like', '%'.$search.'%')
                      ->orWhere('region_name', 'like', '%'.$search.'%');
            });
        }
        $region = $region->orderBy('region_name')->paginate($perPage, ['*'], 'page', $page);

        return $region;
    }

    public function getRegionCityNew($page, $perPage, $search, $regionCode)
    {
        $transportationDescriptions = TransportationZone::pluck('description');
        $city = ResellerMasterAddress::Select(DB::raw("CONCAT(city_type, ' ',city_name) AS name, city_code as id, city_code as code, city_name"))->distinct('city_code')
        ->where('region_code', '=', $regionCode)
        ->where(function ($query) use ($transportationDescriptions) {
            $query->whereIn(DB::raw("CONCAT(city_type, ' ', city_name)"), $transportationDescriptions)
                  ->orWhereIn('city_name', $transportationDescriptions);
        });

        if (!empty($search)) {
            $city = $city->where(function ($query) use($search) {
                $query->where('city_code', 'like', '%'.$search.'%')
                      ->orWhere('city_name', 'like', '%'.$search.'%');
            });
        }

        $city = $city->orderBy(DB::raw("CONCAT(city_type, ' ', city_name)"))->paginate($perPage, ['*'], 'page', $page);

        return $city;
    }

    public function getRegionDistrictNew($page, $perPage, $search, $cityCode)
    {
        $district = ResellerMasterAddress::select('district_code as code', 'district_name as name')->distinct('district_code')
        ->where('city_code', '=', $cityCode);

        if (!empty($search)) {
            $district = $district->where(function ($query) use($search) {
                $query->where('district_code', 'like', '%'.$search.'%')
                      ->orWhere('district_name', 'like', '%'.$search.'%');
            });
        }

        $district = $district->orderBy('district_name')->paginate($perPage, ['*'], 'page', $page);

        return $district;
    }

    public function getRegionSubdistrictNew($page, $perPage, $search, $districtCode)
    {
        $subDistrict = ResellerMasterAddress::select('sub_district_code as code', 'sub_district_name as name', 'zip_code as postal_code')->distinct('sub_district_code')
        ->where('district_code', '=', $districtCode);

        if (!empty($search)) {
            $subDistrict = $subDistrict->where(function ($query) use($search) {
                $query->where('sub_district_code', 'like', '%'.$search.'%')
                      ->orWhere('sub_district_name', 'like', '%'.$search.'%');
            });
        }

        $subDistrict = $subDistrict->paginate($perPage, ['*'], 'page', $page);

        return $subDistrict;
    }

    public function getRegionPostalCode($page, $perPage, $search, $subDistrictCode)
    {
        $postalCode = ResellerMasterAddress::select('zip_code as postal_code')
        ->where('sub_district_code', '=', $subDistrictCode);

        if (!empty($search)) {
            $postalCode = $postalCode->where(function ($query) use($search) {
                $query->where('zip_code', 'like', '%'.$search.'%');
            });
        }

        $postalCode = $postalCode->paginate($perPage, ['*'], 'page', $page);

        return $postalCode;
    }
    #code above are not implemented yet because CARE-OMNI haven't updated / used this master address data

    public function getRegions($page, $perPage, $search)
    {
        
        $region = Region::select('code', 'name', 'island');
        // $region = DB::table('region')->paginate($perPage);

        if (!empty($search)) {
            $region = $region->where(function ($query) use($search) {
                $query->where('code', 'like', '%'.$search.'%')
                      ->orWhere('name', 'like', '%'.$search.'%')
                      ->orWhere('island', 'like', '%'.$search.'%');
            });
        }
        $region = $region->paginate($perPage, ['*'], 'page', $page);

        return $region;
    }

    public function getRegionCity($page, $perPage, $search, $regionCode)
    {
        $city = City::select('id', 'code', 'name')
        ->where('region_code', '=', $regionCode);

        if (!empty($search)) {
            $city = $city->where(function ($query) use($search) {
                $query->where('code', 'like', '%'.$search.'%')
                      ->orWhere('name', 'like', '%'.$search.'%');
            });
        }

        $city = $city->paginate($perPage, ['*'], 'page', $page);

        return $city;
    }

    public function getRegionDistrict($page, $perPage, $search, $cityId)
    {
        $district = District::select('code', 'name')
        ->where('city_id', '=', $cityId);

        if (!empty($search)) {
            $district = $district->where(function ($query) use($search) {
                $query->where('code', 'like', '%'.$search.'%')
                      ->orWhere('name', 'like', '%'.$search.'%');
            });
        }

        $district = $district->paginate($perPage, ['*'], 'page', $page);

        return $district;
    }

    public function getRegionSubdistrict($page, $perPage, $search, $districtCode)
    {
        $city = Subdistrict::select('code', 'name', 'postal_code')
        ->where('district_code', '=', $districtCode);

        if (!empty($search)) {
            $city = $city->where(function ($query) use($search) {
                $query->where('code', 'like', '%'.$search.'%')
                      ->orWhere('name', 'like', '%'.$search.'%');
            });
        }

        $city = $city->paginate($perPage, ['*'], 'page', $page);

        return $city;
    }
}
?>