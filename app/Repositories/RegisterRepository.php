<?php
namespace App\Repositories;
use App\Models\ResellerRegistration;
use App\Models\Transportation;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class RegisterRepository
{
    public function getRegisterDashboardData()
    {

        $currentMonth = Carbon::now()->month;
        $lastMonth = Carbon::now()->subMonth();
        $currentYear = Carbon::now()->year;
        
        //Pendaftar baru
        $newLastMonth = ResellerRegistration::whereMonth('created_date', $lastMonth)
        ->whereYear('created_date', $currentYear)
        ->count();
        $newThisMonth = ResellerRegistration::whereMonth('created_date', $currentMonth)
            ->whereYear('created_date', $currentYear)
            ->count();
        $newDiff = $newThisMonth - $newLastMonth;

        //diterima
        $approveLastMonth = ResellerRegistration::where('status',ResellerRegistration::REGISTRATION_APPROVED)->whereMonth('action_date', $lastMonth)
        ->whereYear('action_date', $currentYear)
        ->count();
        $approveThisMonth = ResellerRegistration::where('status',ResellerRegistration::REGISTRATION_APPROVED)->whereMonth('action_date', $currentMonth)
            ->whereYear('action_date', $currentYear)
            ->count();
        $approvedDiff = $approveThisMonth - $approveLastMonth;

        //ditolak
        $rejectLastMonth = ResellerRegistration::where('status',ResellerRegistration::REGISTRATION_REJECTED)->whereMonth('action_date', $lastMonth)
        ->whereYear('action_date', $currentYear)
        ->count();
        $rejectThisMonth = ResellerRegistration::where('status',ResellerRegistration::REGISTRATION_REJECTED)->whereMonth('action_date', $currentMonth)
            ->whereYear('action_date', $currentYear)
            ->count();
        $rejectDiff = $rejectThisMonth - $rejectLastMonth;

        $data = new Collection([
                'new30d' => $newDiff,
                'newThisMonth' => $newThisMonth,
                'approved30d' => $approvedDiff,
                'approveThisMonth' => $approveThisMonth,
                'reject30d' => $rejectDiff,
                'rejectThisMonth' => $rejectThisMonth
        ]);

        return $data;
    }
}
?>