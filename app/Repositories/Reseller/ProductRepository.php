<?php

namespace App\Repositories\Reseller;

use Exception;
use App\Models\Cart;
use App\Models\Color;
use App\Models\Article;
use App\Models\Product;
use App\Helpers\ApiClient;
use App\Models\CartDetail;
use App\Helpers\RestHelper;
use App\Traits\ResponseAPI;
use Illuminate\Support\Str;
use App\Models\PublicProduct;
use App\Jobs\StockUpdateBatch;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Resources\MenuResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;
use App\Http\Resources\ProductCollection;
use App\Models\Promotions\PromotionItems;
use App\Http\Resources\ProductDetailResource;
use App\Interfaces\Reseller\ProductInterface;
use App\Http\Resources\ProductVariantResource;
use App\Http\Resources\ProductDetailCollection;
use App\Http\Resources\PublicProductCollection;
use App\Http\Resources\ProductDetailNewResource;
use App\Http\Resources\InternalProductCollection;
use App\Http\Resources\ResellerProductCollection;
use App\Models\Promotions\PromotionExceptionItems;
use App\Http\Resources\ProductDetailPublicResource;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Http\Resources\InternalProductResellerCollection;
use App\Http\Resources\ProductDetailPublicInternalResource;
use App\Http\Resources\ProductVariantInternalResellerResource;

class ProductRepository implements ProductInterface
{
    use ResponseAPI;

    private $limit = 12;

    protected $getStockURI;

    public function getData($product, $req)
    {
        try {
            $data = $this->filter($product, $req);

            return $this->sendSuccess("Products retrieved successfully.", $data);
        } catch (Exception $e) {
            return $this->sendException("Get product catalog reseller error",500,'',$e);
        }
    }

    public function getFilter($data, $isColor = false)
    {
        
    }

    private function getColors($codes)
    {
        
    }

    private function filter($product, $request)
    {
        $site = $request->input('site', '0000');

        $queries = ['article.*'];
        if($site != '0000'){
            $queries[] = 'view_article_stock_site.available_stock';
            $product = $product
            ->leftJoin('view_article_stock_site', fn($join) => $join->on('article.sku_code_c', '=', 'view_article_stock_site.sku_code_c')
            ->where('view_article_stock_site.location_code', $site));
        }
        if($site == '0000'){
            $queries[] = 'view_article_stock_all.available_stock';
            $product = $product
            ->leftJoin('view_article_stock_all', fn($join) => $join->on('article.sku_code_c', '=', 'view_article_stock_all.sku_code_c'));
            // $product = $product->leftJoin('rsl_master_site', fn($join) => $join->on('view_article_stock_site.location_code', '=', 'rsl_master_site.code'))
            // ->where('rsl_master_site.is_active', true);
        }
      

        // if($request->input('order_by', null) != 'promo'){
            // $queries[] = 'view_reseller_active_promotions_v3.maximum_tier';
            // $queries[] = 'view_reseller_active_promotions_v3.maximum_single_percentage';
            // $queries[] = 'view_reseller_active_promotions_v3.is_bogo';
            // $product = $product->leftJoin('view_reseller_active_promotions_v3', fn($join) => $join->on('article.sku_code_c', '=', 'view_reseller_active_promotions_v3.sku_code_c'));
        // }
        $product->select(...$queries);
            // dd($product->limit(100)->get());


        // if ($request->query('order_by') == 'promo') {
            
        //     // $product = $product->leftJoin('rsl_article_filter','article.article','=','rsl_article_filter.article');
        // }
        
        $orderField = 'article.transfer_date';
        $orderBy = 'desc';

        if ($category = $request->query('category')) {
            $product = $this->category($product, $category);
        }

        if ($subcategory = $request->query('subcategory')) {
            $product = $product->where('lvl4_description', explode(',', $subcategory));
        }

        if ($activity = $request->query('activity')) {
            $product = $product->whereIn('lvl2_description', explode(',', $activity));
        }

        if ($color = $request->query('color')) {
            $product = $product->whereIn('product_variant_c', explode(',', $color));
        }

        if ($size = $request->query('size')) {
            $product = $product->whereIn('product_size_c', explode(',', $size));
        }

        if ($search = $request->query('search')) {
            $product = $product->where(function($query) use($search){
                return $query->where('article_description', 'like', '%'. $search . '%')
                      ->orWhere('article.sku_code_c', 'like', '%'. $search . '%')
                      ->orWhere('lvl4_description', 'like', '%'. $search . '%')
                      ->orWhere('lvl3_description', 'like', '%'. $search . '%');
            });
        }

        if ($request->has('minPrice') || $request->has('maxPrice')) {
            $minPrice = $request->query('minPrice')??0;
            $maxPrice = $request->query('maxPrice')??PHP_INT_MAX;
            $product = $product->whereBetween('amount', [$minPrice, $maxPrice]);
        }

        if ($type = $request->query('type')) {
            switch ($type) {
                case 'wab':
                    $product = $product->where('is_wab', 1);
                    break;
                case 'reseller':
                    $product = $product->where('is_reseller', 1);
                    break;
                case 'wholesales':
                    $product = $product->where('is_wholesales', 1);
                    break;
                case 'b2b':
                    $product = $product->where('is_b2b', 1);
                    break;
            }
        }


        if ($order_by = $request->query('order_by')) {
            switch ($order_by) {
                case 'highest':
                case 'lowest':
                    $orderBy = $order_by == 'highest' ? 'desc' : 'asc';
                    // $product = $product->whereHas('price', function($q) use ($orderBy){
                    //     $q->orderBy('amount',$orderBy);
                    // });
                    $product = $product->orderBy('amount', $orderBy);
                    break;

                case 'alphabetA':
                case 'alphabetZ':
                    $orderBy = $order_by == 'alphabetZ' ? 'desc' : 'asc';
                    $product = $product->orderBy('product_name_c', $orderBy);
                    break;

                case 'newest':
                case 'oldest':
                    $orderBy = $order_by == 'newest' ? 'desc' : 'asc';
                    $product = $product->orderBy('transfer_date', $orderBy);
                    break;
                case 'promo':
                    $product = $product->orderBy('name', 'desc')->orderBy('available_stock', 'desc');
                    break;
                default:
                $product = $product->orderBy('available_stock', 'desc');

            }
        }

        if ($order_by = $request->query('misc')) {
            $date = now()->format('Y-m-d');
            switch ($order_by) {
                case 'newarrival':
                    $product = $product->where('transfer_date','<=', $date)->where('expired_date', '>=', $date);
                    break;

            }
        }

        if ($limit = $request->query('limit')) {
            $this->limit = $limit;
        }

        if($custom = $request->query('custom')){
            switch($custom){
                case 'logo':
                    $product = $product->where('is_custom_logo', 1);
                    break;
                case 'size':
                    $product = $product->where('is_custom_size', 1);
                    break;
            }
        }

        $product = $request->query('order_by') ? $product->groupBy('article.sku_code_c') : $product->groupBy('article.sku_code_c')->orderBy('available_stock', 'desc');
        // $sql_with_bindings = Str::replaceArray('?', $product->getBindings(), $product->toSql());
        // return $sql_with_bindings;
        // return $product->paginate($this->limit);
        // dd(Str::replaceArray('?', $product->getBindings(), $product->toSql()));
        return $this->getPublic($product->paginate($this->limit));
    }

    private function category($product, $key)
    {
        $explode = explode(',', $key);
        if(in_array('semua', $explode)){
            return $product;
        }
        if (count($explode) > 1) {
            if (in_array('non-bags', $explode) && !in_array('bags', $explode)) {
                return $product->whereNotIn('lvl3_description', ['bags']);
            }
            if (in_array('non-bags', $explode) && !in_array('footwear', $explode)) {
                return $product->whereNotIn('lvl3_description', ['footwear']);
            }
            return $product->whereIn('lvl3_description', $explode);
        } else {
            switch ($key) {
                case 'non-bags':
                    return $product->whereNotIn('lvl3_description', ['bags', 'footwear']);
                default:
                    return $product->where('lvl3_description', $key);
            }
        }
    }

    public function getDetailPublic($id, $request)
    {
        try{
            $datas = DB::table('view_reseller_article AS article');
                        // ->leftJoin('view_reseller_active_promotions', fn($join) => $join->on('article.sku_code_c', '=', 'view_reseller_active_promotions.sku_code_c'));
                        // dd($datas);
            $site = $request->query('site', '0000');
            if($site != '0000'){
                $queries[] = 'view_article_stock_site.available_stock';
                $queries[] = 'view_article_stock_site.sku_code_c';
                $datas = $datas->leftJoin('view_article_stock_site', fn($join) => $join->on('article.sku_code_c', '=', 'view_article_stock_site.sku_code_c')->where('view_article_stock_site.location_code', $site));
            }
            if($site == '0000'){
                $queries[] = 'view_article_stock_all.available_stock';
                $datas = $datas->leftJoin('view_article_stock_all', fn($join) => $join->on('article.sku_code_c', '=', 'view_article_stock_all.sku_code_c'));
                // $datas = $datas->leftJoin('rsl_master_site', fn($join) => $join->on('view_article_stock_site.location_code', '=', 'rsl_master_site.code'))
                // ->where('rsl_master_site.is_active', true);
            }
            $queries[] = 'article.*';
            // $queries[] = 'view_reseller_active_promotions.item_id';
            // $queries[] = 'view_reseller_active_promotions.promotion_id';
            // $queries[] = 'view_reseller_active_promotions.generic_discount';
            // $queries[] = 'view_reseller_active_promotions.article_discount'; 
            // $queries[] = 'view_reseller_active_promotions.generic_max_value'; 
            // $queries[] = 'view_reseller_active_promotions.article_max_value'; 
            // $queries[] = 'view_reseller_active_promotions.highest_promotion'; 
            // $queries[] = 'view_reseller_active_promotions.name';
            // $queries[] = 'view_reseller_active_promotions.action';
            // $queries[] = 'view_reseller_active_promotions.discount_type';
            $datas = $datas->whereRaw("article.sku_code_c = ?",$id)->select(...$queries)->first();
            // $datas = PublicProduct::where('sku_code_c', $id)->firstOrFail();
            return $this->sendSuccess("Products retrieved successfully.", new ProductDetailPublicResource($datas));
        }catch(ModelNotFoundException){
            return $this->sendError('Product with sku: '. $id .' not found.');
        }catch(Exception $e){
            return $this->sendException('err',500,'',$e);
        }
    }

    public function getDetailPublicInternalReseller(Request $request, $id)
    {
        try{
            $datas = DB::table('view_reseller_article AS article')
                        ->leftJoin('view_reseller_active_promotions', fn($join) => $join->on('article.sku_code_c', '=', 'view_reseller_active_promotions.sku_code_c'));
                        // dd($datas);
            $site = $request->query('site', '0000');
            if($site != '0000'){
                $queries[] = 'view_article_stock_site.available_stock';
                $queries[] = 'view_article_stock_site.sku_code_c';
                $datas = $datas->leftJoin('view_article_stock_site', fn($join) => $join->on('article.sku_code_c', '=', 'view_article_stock_site.sku_code_c')->where('view_article_stock_site.location_code', $site));
            }
            if($site == '0000'){
                $queries[] = 'view_article_stock_all.available_stock';
                $datas = $datas->leftJoin('view_article_stock_all', fn($join) => $join->on('article.sku_code_c', '=', 'view_article_stock_all.sku_code_c'));
                // $datas = $datas->leftJoin('rsl_master_site', fn($join) => $join->on('view_article_stock_site.location_code', '=', 'rsl_master_site.code'))
                // ->where('rsl_master_site.is_active', true);
            }
            $queries[] = 'article.*';
            $queries[] = 'view_reseller_active_promotions.item_id';
            $queries[] = 'view_reseller_active_promotions.promotion_id';
            $queries[] = 'view_reseller_active_promotions.generic_discount';
            $queries[] = 'view_reseller_active_promotions.article_discount'; 
            $queries[] = 'view_reseller_active_promotions.generic_max_value'; 
            $queries[] = 'view_reseller_active_promotions.article_max_value'; 
            $queries[] = 'view_reseller_active_promotions.highest_promotion'; 
            $queries[] = 'view_reseller_active_promotions.name';
            $queries[] = 'view_reseller_active_promotions.action';
            $queries[] = 'view_reseller_active_promotions.discount_type';
            $datas = $datas->whereRaw("article.sku_code_c = ?",$id)->select(...$queries)->first();

            // $datas = PublicProduct::where('sku_code_c', $id)->firstOrFail();
            return $this->sendSuccess("Products retrieved successfully.", new ProductDetailPublicInternalResource($datas));
        }catch(ModelNotFoundException){
            return $this->sendError('Product with sku: '. $id .' not found.');
        }catch(Exception $e){
            return $this->sendError($e->getMessage());
        }
    }

    public function getDetailVariantNew($sku, $color, $customer_type = null)
    {
        if (!$customer_type) {
            $customer_type = @Auth::user()->customer->customer_type;
        }
        try{
            $datas = Product::exclude($customer_type)
                ->where('sku_code_c', $sku)
                ->whereIn('product_variant_c', [$color->key, $color->value])
                ->with(['price, skuStock'])->get();
            // $stock_cache = RestHelper::stockCache($datas->pluck('article')->toArray);
            // $datas = RestHelper::addFieldStockMoq($stock_cache,$datas->toArray());
            // $datas = RestHelper::vArrayToObject($datas);
            return $this->sendSuccess("Products retrieved successfully.", ProductVariantResource::collection($datas));
        }catch(\Exception $e){
            return $this->sendError($e->getMessage(), $e->getCode()?: 500);
        }
    }

    public function getDetailVariantNewCustomer($sku, $color, $customer_type = null, $customer_id)
    {
        if (!$customer_type) {
            $customer_type = @Auth::user()->customer->customer_type;
        }
        try{
            $datas = Product::exclude($customer_type)
                ->where('sku_code_c', $sku)
                ->whereIn('product_variant_c', [$color->key, $color->value])
                ->with('price')->get();
            // $stock_cache = RestHelper::stockCache($datas->pluck('article'));
            // $datas = RestHelper::addFieldStockMoq($stock_cache,$datas->toArray());
            // $datas = RestHelper::vArrayToObject($datas);
            return $this->sendSuccess("Products retrieved successfully.", ProductVariantResource::collection($datas));
        }catch(\Exception $e){
            return $this->sendError($e->getMessage(), $e->getCode()?: 500);
        }
    }

    public function getDetailVariant($id)
    {
        try{
            $codes = explode('.',$id);
            $datas = Product::query()
                ->where('sku_code_c',$codes[0])
                ->where('product_variant_c',$codes[1])
                ->with('price')->get();
            if (!$datas->count()) {
                return $this->sendError("There's no data found.", 404, '404 not found.');
            }
            // $stock_cache = RestHelper::stockCache($datas->pluck('article'));
            // $datas = RestHelper::addFieldStockMoq($stock_cache,$datas->toArray());
            // $datas = RestHelper::vArrayToObject($datas);
            Log::info("id ".$id);
            if(auth()->user()->sales){
                return $this->sendSuccess("Products retrieved successfully.", ProductVariantInternalResellerResource::collection($datas));
            }else {
                return $this->sendSuccess("Products retrieved successfully.", ProductVariantResource::collection($datas));
            }
        }catch(\Exception $e){
            Log::info("ini err = ".$e->getMessage());
            return $this->sendError($e->getMessage(), $e->getCode()?: 500);
        }
    }

    public function getMenus($req, $cid)
    {
        try{
            if($menu = $req->query('menu')){
                log::info("get sub menu = ".$menu);
                $datas = Product::query()->where('lvl2_description','=',$menu)
                    ->groupBy('lvl2_description')->get();
            }else{
                $datas = Product::query()
                    ->whereNotNull('lvl2_description')
                    ->groupBy('lvl2_description')->get();
            }

            $extras = CartDetail::whereHas('cart', function($query) use($cid) {
                $query->where('customer_id', $cid);
            }
        )->count();

            $respEx = ['customer_id' => $cid, 'cartcount' => $extras];


            if (!$datas->count()) {
                return $this->sendError("There's no data found.", 404, '404 not found.');
            }
            return $this->sendExtraSuccess("menu retrieved successfully.", MenuResource::collection($datas), '200 OK', $respEx);
        }catch(Exception $e){
            log::info("ini err get menu = ".$e->getMessage());
            return $this->sendError($e->getMessage(), $e->getCode()?:500);
        }
    }

    private function getPublic($products)
    {
        $products = json_decode((new PublicProductCollection($products))->toJson(), true);
        // $execution_time = ($time_end - $time_start);
        // dd($execution_time);
        if ($sort = request()->query('sort')) {
            $sort = strtolower($sort);
            list($sortKey, $sortValue) = explode(':', $sort);
            $productData = collect(data_get($products, 'data'))->sortBy([
                [$sortKey, $sortValue]
            ])->values()->all();
            $products['data'] = $productData;
        } else {
            // $productData = collect(data_get($products, 'data'))->sortBy([
            //     ['stock', 'desc'],
            //     ['published_date', 'desc'],
            //     ['image', 'desc']
            // ])->values()->all();
            // $products['data'] = $productData;
        }

        return $products;
    }
}
