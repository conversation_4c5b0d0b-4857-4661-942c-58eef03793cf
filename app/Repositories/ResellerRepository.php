<?php

namespace App\Repositories;
use App\Helpers\FormatHelper;
use App\Models\Color;
use App\Models\CommissionLedgers;
use App\Models\CommissionWithdrawal;
use App\Models\OrderItemReseller;
use App\Models\OrderReseller;
use App\Models\Product;
use App\Models\Reseller;
use App\Models\ResellerLinkHistory;
use DateInterval;
use DateTime;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;


class ResellerRepository
{
    public function getResellerDashboardData(){

        $currentMonth = Carbon::now()->month;
        $lastMonth = Carbon::now()->subMonth();
        $currentYear = Carbon::now()->year;

        //jumlah reseller
        $resellerLastMonth = Reseller::where('is_active',Reseller::RESELLER_ACTIVE)->whereMonth('created_date', $lastMonth)
        ->whereYear('created_date', $currentYear)
        ->count();
        $resellerThisMonth = Reseller::where('is_active',Reseller::RESELLER_ACTIVE)->whereMonth('created_date', $currentMonth)
        ->whereYear('created_date', $currentYear)
        ->count();
        $resellerDiff = $resellerThisMonth - $resellerLastMonth;


        $orderLastMonth = OrderReseller::with('items')->whereIn('order_status',[
            OrderReseller::ORDER_RESELLER_BARU,
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA,
            OrderReseller::ORDER_RESELLER_SELESAI])
        ->whereMonth('created_date', $lastMonth)
        ->whereYear('created_date', $currentYear)
        ->get();
        $orderThisMonth = OrderReseller::with('items')->whereIn('order_status',[
            OrderReseller::ORDER_RESELLER_BARU,
            OrderReseller::ORDER_RESELLER_DIPROSES,
            OrderReseller::ORDER_RESELLER_DIKEMAS,
            OrderReseller::ORDER_RESELLER_DIKIRIM,
            OrderReseller::ORDER_RESELLER_DITERIMA,
            OrderReseller::ORDER_RESELLER_SELESAI])
        ->whereMonth('created_date', $currentMonth)
        ->whereYear('created_date', $currentYear)
        ->get();

        //jumlah transaksi
        $orderDiff = $orderThisMonth->count() - $orderLastMonth->count();
        
        //total transaksi
        $totalOrderThisMonthSum = $orderThisMonth->sum('total_amount');
        $totalOrderLastMonthSum = $orderLastMonth->sum('total_amount');
        if($totalOrderLastMonthSum > 0){
            $totalOrderDiffPercentage  = (($totalOrderThisMonthSum - $totalOrderLastMonthSum)/ $totalOrderLastMonthSum ) * 100;
        } else {
            $totalOrderDiffPercentage = 0;
        }


        //credit
        $lastmonthCredit = CommissionLedgers::
        leftJoin('rsl_commission', 'rsl_commission_ledgers.commission_id' ,'=','rsl_commission.id')
        ->where('rsl_commission_ledgers.type','Credit')
        ->whereMonth('rsl_commission_ledgers.created_date', $lastMonth)
        ->whereYear('rsl_commission_ledgers.created_date', $currentYear)
        ->get()->sum('rsl_commission_ledgers.amount');
        $lastmonthDebit = CommissionLedgers::
        leftJoin('rsl_commission', 'rsl_commission_ledgers.commission_id' ,'=','rsl_commission.id')
        ->where('rsl_commission_ledgers.type','Debit')
        ->whereMonth('rsl_commission_ledgers.created_date', $lastMonth)
        ->whereYear('rsl_commission_ledgers.created_date', $currentYear)
        ->get()->sum('rsl_commission_ledgers.amount');
        $lastMontCommissions = $lastmonthCredit - $lastmonthDebit;

        $thisMmonthCredit = CommissionLedgers::
        leftJoin('rsl_commission', 'rsl_commission_ledgers.commission_id' ,'=','rsl_commission.id')
        ->where('rsl_commission_ledgers.type','Credit')
        ->whereMonth('rsl_commission_ledgers.created_date', $lastMonth)
        ->whereYear('rsl_commission_ledgers.created_date', $currentYear)
        ->get()->sum('rsl_commission_ledgers.amount');
        $thisMonthDebit = CommissionLedgers::
        leftJoin('rsl_commission', 'rsl_commission_ledgers.commission_id' ,'=','rsl_commission.id')
        ->where('rsl_commission_ledgers.type','Debit')
        ->whereMonth('rsl_commission_ledgers.created_date', $lastMonth)
        ->whereYear('rsl_commission_ledgers.created_date', $currentYear)
        ->get()->sum('rsl_commission_ledgers.amount');
        $thisMonthCommissions = $thisMmonthCredit - $thisMonthDebit;

        if($lastMontCommissions > 0){
            $commissionsDiffPercentage  = (($thisMonthCommissions - $lastMontCommissions)/ $lastMontCommissions ) * 100;
        } else {
            $commissionsDiffPercentage = 0;
        }

        $data = new Collection([
            'reseller30d' => $resellerDiff,
            'resellerThisMonth' => $resellerThisMonth,
            'order30d' => $orderDiff,
            'orderThisMonth' => $orderThisMonth->count(),
            'totalOrder30d' => number_format($totalOrderDiffPercentage,0),
            'totalOrderThisMonth' => $totalOrderThisMonthSum,
            'totalCommissions30d' => number_format($commissionsDiffPercentage,0),
            'totalCommisionsThisMonth' => $thisMonthCommissions
        ]);

        return $data;

    }

    public function getResellerSummaryDashboardData($start, $to){

        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;

        //query data last month
        if($start !== null){
            $lastMonth = Carbon::parse($start)->subMonthNoOverflow()->month;
            $year = Carbon::parse($start)->year;
        } else {
            $lastMonth = Carbon::now()->subMonthNoOverflow();
            $year = Carbon::now()->year;
        }
        $ordersLastMonth = OrderReseller::ongoing()
        ->whereMonth('order_date', $lastMonth)
        ->whereYear('order_date', $year)
        ->get();

        //query data this month
        $ordersThisMonth = OrderReseller::ongoing()
        ->when($start !== null && $to !== null, function ($query) use ($start, $to) {
            $query->whereDate('order_date', '>=', $start)
            ->whereDate('order_date', '<=', $to);
        })
        ->when($start == null && $to == null, function ($query) use ($currentMonth, $currentYear) {
            $query->whereMonth('order_date', $currentMonth)
            ->whereYear('order_date', $currentYear);
          })
        ->get();

        //jumlah item
        $orderIdsLastMonth = [];
        $orderIdsThisMonth = [];
        foreach($ordersLastMonth as $orderLastMonth){
            array_push($orderIdsLastMonth, $orderLastMonth->id);
        }
        foreach($ordersThisMonth as $orderThisMonth){
            array_push($orderIdsThisMonth, $orderThisMonth->id);
        }

        $totalItemsLastMonthSum = OrderItemReseller::whereIn('order_header_id',$orderIdsLastMonth)->sum('qty');

        $totalItemsThisMonthSum = OrderItemReseller::whereIn('order_header_id',$orderIdsThisMonth)->sum('qty');
        $itemsDiff = $totalItemsThisMonthSum - $totalItemsLastMonthSum;

        //jumlah transaksi
        $orderDiff = $ordersThisMonth->count() - $ordersLastMonth->count();
        
        //total transaksi
        $totalOrderThisMonthSum = $ordersThisMonth->sum('pay_amount');
        $totalOrderLastMonthSum = $ordersLastMonth->sum('pay_amount');
        if($totalOrderLastMonthSum > 0){
            $totalOrderDiffPercentage  = (($totalOrderThisMonthSum - $totalOrderLastMonthSum)/ $totalOrderLastMonthSum ) * 100;
            if($totalOrderDiffPercentage > 100){
                $totalOrderDiffPercentage = 100;
            }
        } else {
            $totalOrderDiffPercentage = 0;
        }

        //rata" transaksi
        $avgOrderThisMonthSum = $ordersThisMonth->avg('pay_amount');
        $avgOrderLastMonthSum = $ordersLastMonth->avg('pay_amount');
        if($avgOrderLastMonthSum > 0){
            $avgOrderDiffPercentage  = (($avgOrderThisMonthSum - $avgOrderLastMonthSum)/ $avgOrderLastMonthSum ) * 100;
            if($avgOrderDiffPercentage > 100){
                $avgOrderDiffPercentage = 100;
            }
        } else {
            $avgOrderDiffPercentage = 0;
        }

        //collect
        $data = new Collection([
            'items30d' => $itemsDiff,
            'itemsThisMonth' => $totalItemsThisMonthSum,
            'order30d' => $orderDiff,
            'orderThisMonth' => $ordersThisMonth->count(),
            'totalOrder30d' => $totalOrderDiffPercentage,
            'totalOrderThisMonth' => $totalOrderThisMonthSum,
            'avgOrder30d' => $avgOrderDiffPercentage,
            'avgOrderThisMonth' => $avgOrderThisMonthSum
        ]);

        return $data;

    }

    public function getResellerCommissionsDashboardData($start, $to){


        //query data last month
        $commissions = CommissionWithdrawal::
        when($start !== null && $to !== null, function ($query) use ($start, $to) {
            $query->whereDate('request_date', '>=', $start)
            ->whereDate('request_date', '<=', $to);
        })
        ->get();

        $requestValue = $commissions->where('status',CommissionWithdrawal::WAITING_FOR_APPROVAL)->count();
        $requestAmount = $commissions->where('status',CommissionWithdrawal::WAITING_FOR_APPROVAL)->sum('amount');
        $processedValue = $commissions->where('status',CommissionWithdrawal::APPROVED)->count();
        $processedAmount = $commissions->where('status',CommissionWithdrawal::APPROVED)->sum('amount');
        $paidValue = $commissions->where('status',CommissionWithdrawal::SUCCESS)->count();
        $paidAmount = $commissions->where('status',CommissionWithdrawal::SUCCESS)->sum('amount');

        //collect
        $data = new Collection([
            'requestValue' => $requestValue,
            'requestAmount' => $requestAmount,
            'processedValue' => $processedValue,
            'processedAmount' => $processedAmount,
            'paidValue' => $paidValue,
            'paidAmount' => $paidAmount,
        ]);

        return $data;

    }

    public function getTopResellers(){
        $sumOrders = 0.0;
        $resellerData = collect();
        $resellers = Reseller::with('orders')->with('link')->with('commissions')->get();
        foreach($resellers as $reseller){
          $orders = $reseller->orders;
          $links = $reseller->link;
          $resellerId = $reseller->reseller_id;
          $name = $reseller->name;
          $status = $reseller->is_active;

            $sumOrders = $orders->whereNotIn('order_status',[OrderReseller::ORDER_RESELLER_BATAL])->sum('sub_total_amount');
    
          $clicks = $links->reduce(function ($carry, $link) {
            $count = ResellerLinkHistory::where('link_id', $link->id)->count();
            return $carry + $count;
          }, 0);

    
          $resellerData->push([
            'reseller_id' => $resellerId,
            'name' => $name,
            'status' => $status,
            'sum_orders' => $sumOrders,
            'clicks' => $clicks,
            'shared_links' => $links->count()
          ]);
        }
    
        $resellerData = $resellerData->sortByDesc(function ($item) {
            return [$item['sum_orders'], $item['clicks'], $item['shared_links']];
        });

        $resellerData = $resellerData->values()->map(function ($item, $key) {
          $item['position'] = $key + 1;
          return $item;
        });

        $resellerData = $resellerData->take(5);

        return $resellerData;
    }

    public function getTopCommissions(){
        $resellerData = collect();
        $resellers = Reseller::with('commissions')->with(['orders' => function ($query) {
            $query->whereIn('order_status', [
              OrderReseller::ORDER_RESELLER_BARU,
              OrderReseller::ORDER_RESELLER_DIPROSES,
              OrderReseller::ORDER_RESELLER_DIKEMAS,
              OrderReseller::ORDER_RESELLER_DIKIRIM,
              OrderReseller::ORDER_RESELLER_DITERIMA,
              OrderReseller::ORDER_RESELLER_SELESAI]);
          }])->get();
        foreach($resellers as $reseller){
    
            $orders = $reseller->orders->count();
            $resellerId = $reseller->reseller_id;
            $name = $reseller->name;
            $status = $reseller->is_active;
            $commissions = $reseller->commissions;
        
            $resellerData->push([
                'reseller_id' => $resellerId,
                'name' => $name,
                'status' => $status,
                'orders' => $orders,
                'commissions' => $commissions->sum('commission_amount')
            ]);
        }
    
        $resellerData = $resellerData->sortByDesc(function ($item) {
            return [$item['commissions'], $item['orders']];
        });

        $resellerData = $resellerData->values()->map(function ($item, $key) {
          $item['position'] = $key + 1;
          return $item;
        });

        $resellerData = $resellerData->take(5);
        return $resellerData;
    }
    
    public function getTopSellingItems($id){
        $orderIds = [];
        $orderData = collect();
        $orders = OrderReseller::where('reseller_id',$id)->currentMonth()->ongoing()->get();
        foreach($orders as $order){
            array_push($orderIds, $order->id);
        }

        $orderDetails = OrderItemReseller::whereIn('order_header_id',$orderIds)->groupBy('article_id')->select('article_id', DB::raw('SUM(qty) as total_qty'))
        ->get();
        foreach($orderDetails as $orderDetail){
            $product = Product::where('article',$orderDetail->article_id)->first();
            if($product){
                $color = Color::select('value')->where('key',$product->product_variant_c)->first()->value;
                $orderData->push([
                    'sku' => $orderDetail->article_id,
                    'name' => $product->product_name_c.', '.$color.', '.$product->product_size_c,
                    'qty' => $orderDetail->total_qty
                ]);
            }
        }

        $orderData = $orderData->sortByDesc(function ($item) {
            return [$item['qty']];
        });

        $orderData = $orderData->values()->map(function ($item, $key) {
          $item['position'] = $key + 1;
          return $item;
        });

        $orderData = $orderData->take(5);

        return $orderData;
    }

    public function getTopSellingItemsAll(){
        $orderIds = [];
        $orderData = collect();
        $orders = OrderReseller::ongoing()->get();
        foreach($orders as $order){
            array_push($orderIds, $order->id);
        }

        $orderDetails = OrderItemReseller::whereIn('order_header_id',$orderIds)->groupBy('article_id')->select('article_id', DB::raw('SUM(qty) as total_qty'))
        ->get();
        foreach($orderDetails as $orderDetail){
            $product = Product::where('article',$orderDetail->article_id)->first();
            if($product){
                $color = Color::select('value')->where('key',$product->product_variant_c??'NOCOLOR')->first()->value??'NOCOLOR';
                $orderData->push([
                    'sku' => $orderDetail->article_id,
                    'name' => $product->product_name_c.', '.$color.', '.$product->product_size_c,
                    'qty' => $orderDetail->total_qty
                ]);
            }
          
        }

        $orderData = $orderData->sortByDesc(function ($item) {
            return [$item['qty']];
        });

        $orderData = $orderData->values()->map(function ($item, $key) {
          $item['position'] = $key + 1;
          return $item;
        });

        $orderData = $orderData->take(5);

        return $orderData;
    }

    public function getResellerChartData($param, $id){

        //data chart
        $currentYear = Carbon::now()->year;
        $firstMonthOfYear = 1;
        // $currentMonth = Carbon::now()->month;
        $currentMonth = 12;
        $monthsOfYear = range($firstMonthOfYear,$currentMonth);
        $monthResults = [];
        $valueResults = [];
        $sum = 0.0;
        switch($param){
            case 'total_transaction':
                $transactions = OrderReseller::selectRaw('YEAR(order_date) as year, MONTH(order_date) as month, SUM(total_amount) as total')
                ->ongoing()
                ->where('reseller_id',$id)
                ->whereYear('order_date', $currentYear)
                ->groupBy('month')
                ->get();
                break;
            case 'number_transaction':
                $transactions = OrderReseller::selectRaw('YEAR(order_date) as year, MONTH(order_date) as month, COUNT(*) as total')
                ->ongoing()
                ->where('reseller_id',$id)
                ->whereYear('order_date', $currentYear)
                ->groupBy('month')
                ->get();
                break;
            case 'sold_items':
                $transactions = OrderReseller::
                leftJoin('rsl_order_details', 'rsl_order_details.order_header_id' ,'=','rsl_order_headers.id')
                ->selectRaw('YEAR(rsl_order_headers.order_date) as year, MONTH(rsl_order_headers.order_date) as month, SUM(rsl_order_details.qty) as total')
                ->ongoing()
                ->where('reseller_id',$id)
                ->whereYear('rsl_order_headers.order_date', $currentYear)
                ->groupBy('month')
                ->get();
                break;
            default:
                $transactions = OrderReseller::selectRaw('YEAR(order_date) as year, MONTH(order_date) as month, SUM(total_amount) as total')
                ->ongoing()
                ->where('reseller_id',$id)
                ->whereYear('order_date', $currentYear)
                ->groupBy('month')
                ->get();
                break;
        }

        foreach ($monthsOfYear as $month) {
            $found = false;
            foreach ($transactions as $transaction) {
                if ($transaction->month == $month) {
                    array_push($monthResults, $month);
                    if($param == 'total_transaction'){
                        array_push($valueResults, (double)$transaction->total);
                    } else {
                        array_push($valueResults, (int)$transaction->total);
                    }
                    $sum += $transaction->total;
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                array_push($monthResults, $month);
                array_push($valueResults, 0);
            }
        }

        //average
        $average = $sum/$currentMonth;
        //highest
        $highest = max($valueResults);
        $keysOfHighestValue = array_keys($valueResults, $highest);
        $indexOfHighestValue = $keysOfHighestValue[0];
        $monthHighest = $monthResults[$indexOfHighestValue];
        //lowest
        $lowest = min($valueResults);
        $keysOfLowestValue = array_keys($valueResults, $lowest);
        $indexOfLowestValue = $keysOfLowestValue[0];
        $monthLowest = $monthResults[$indexOfLowestValue];

        $data = new Collection([
            'monthResults' => FormatHelper::mapNumbersToMonth3LF($monthResults),
            'valueResults' => $valueResults,
            'startPeriod' => FormatHelper::getMonthfromNumber($firstMonthOfYear).' '.$currentYear,
            'endPeriod' => FormatHelper::getMonthfromNumber($currentMonth).' '.$currentYear,
            'avgValue' => $average,
            'highestValue' => $highest,
            'highestMonth' => FormatHelper::getMonthfromNumber($monthHighest).' '.$currentYear,
            'lowestValue' => $lowest,
            'lowestMonth' => FormatHelper::getMonthfromNumber($monthLowest).' '.$currentYear,
        ]);

        return $data;

    }

    public function getResellerChartDataAll($param, $start, $to){

        //data chart
        if($start && $to){
            $newdateStart = strtotime($start);
            $newDateTo = strtotime($to);
            $firstMonthOfYear = (int)date('m',$newdateStart);
            $currentMonth = (int)date('m',$newDateTo);
            $startYear = (int)date('Y',$newdateStart);
            $toYear = (int)date('Y',$newDateTo);
            $startDate = new DateTime($start);
            $endDate = new DateTime($to);
            $interval = new DateInterval('P1M');
            $monthsAndYears = [];
            while ($startDate <= $endDate) {
                $year = $startDate->format('Y');
                $month = $startDate->format('m');
            
                if (!isset($monthsAndYears[$year])) {
                    $monthsAndYears[$year] = [];
                }
                $monthsAndYears[$year][] = $month;
                $startDate->add($interval);
            }
        } else {
            $startYear =  Carbon::now()->year;
            $toYear =  Carbon::now()->year;
            $firstMonthOfYear = 1;
            $currentMonth = 12;
            $year = date('Y');
            $startOfYear = new DateTime($year . '-01-01');
            $year = date('Y');
            $endOfYear = new DateTime($year . '-12-31');            
            $interval = new DateInterval('P1M');
            $monthsAndYears = [];
            while ($startOfYear <= $endOfYear) {
                $year = $startOfYear->format('Y');
                $month = $startOfYear->format('m');
            
                if (!isset($monthsAndYears[$year])) {
                    $monthsAndYears[$year] = [];
                }
                $monthsAndYears[$year][] = $month;
                $startOfYear->add($interval);
            }
        }

        $monthResults = [];
        $valueResults = [];
        $sum = 0.0;
        switch($param){
            case 'total_transaction':
                $transactions = OrderReseller::selectRaw('YEAR(order_date) as year, MONTH(order_date) as month, SUM(pay_amount) as total')
                ->ongoing()
                ->where(function ($query) use ($startYear, $firstMonthOfYear, $toYear, $currentMonth) {
                    $query->where(function ($q) use ($startYear, $firstMonthOfYear, $toYear) {
                        $q->whereYear('order_date', '>', $startYear)
                            ->orWhere(function ($subQuery) use ($startYear, $firstMonthOfYear) {
                                $subQuery->whereYear('order_date', $startYear)
                                    ->whereMonth('order_date', '>=', $firstMonthOfYear);
                            });
                    })
                    ->orWhere(function ($q) use ($toYear, $currentMonth) {
                        $q->whereYear('order_date', '<', $toYear)
                            ->orWhere(function ($subQuery) use ($toYear, $currentMonth) {
                                $subQuery->whereYear('order_date', $toYear)
                                    ->whereMonth('order_date', '<=', $currentMonth);
                            });
                    });
                })
                ->groupBy('month')
                ->get();
                break;
            case 'number_transaction':
                $transactions = OrderReseller::selectRaw('YEAR(order_date) as year, MONTH(order_date) as month, COUNT(*) as total')
                ->ongoing()
                ->where(function ($query) use ($startYear, $firstMonthOfYear, $toYear, $currentMonth) {
                    $query->where(function ($q) use ($startYear, $firstMonthOfYear, $toYear) {
                        $q->whereYear('order_date', '>', $startYear)
                            ->orWhere(function ($subQuery) use ($startYear, $firstMonthOfYear) {
                                $subQuery->whereYear('order_date', $startYear)
                                    ->whereMonth('order_date', '>=', $firstMonthOfYear);
                            });
                    })
                    ->orWhere(function ($q) use ($toYear, $currentMonth) {
                        $q->whereYear('order_date', '<', $toYear)
                            ->orWhere(function ($subQuery) use ($toYear, $currentMonth) {
                                $subQuery->whereYear('order_date', $toYear)
                                    ->whereMonth('order_date', '<=', $currentMonth);
                            });
                    });
                })
                ->groupBy('month')
                ->get();
                break;
            case 'sold_items':
                $transactions = OrderReseller::
                leftJoin('rsl_order_details', 'rsl_order_details.order_header_id' ,'=','rsl_order_headers.id')
                ->selectRaw('YEAR(rsl_order_headers.order_date) as year, MONTH(rsl_order_headers.order_date) as month, SUM(rsl_order_details.qty) as total')
                ->ongoing()
                ->where(function ($query) use ($startYear, $firstMonthOfYear, $toYear, $currentMonth) {
                    $query->where(function ($q) use ($startYear, $firstMonthOfYear, $toYear) {
                        $q->whereYear('rsl_order_headers.order_date', '>', $startYear)
                            ->orWhere(function ($subQuery) use ($startYear, $firstMonthOfYear) {
                                $subQuery->whereYear('rsl_order_headers.order_date', $startYear)
                                    ->whereMonth('rsl_order_headers.order_date', '>=', $firstMonthOfYear);
                            });
                    })
                    ->orWhere(function ($q) use ($toYear, $currentMonth) {
                        $q->whereYear('rsl_order_headers.order_date', '<', $toYear)
                            ->orWhere(function ($subQuery) use ($toYear, $currentMonth) {
                                $subQuery->whereYear('rsl_order_headers.order_date', $toYear)
                                    ->whereMonth('rsl_order_headers.order_date', '<=', $currentMonth);
                            });
                    });
                })
                ->groupBy('month')
                ->get();
                break;
            default:
                $transactions = OrderReseller::selectRaw('YEAR(order_date) as year, MONTH(order_date) as month, SUM(pay_amount) as total')
                ->ongoing()
                ->where(function ($query) use ($startYear, $firstMonthOfYear, $toYear, $currentMonth) {
                    $query->where(function ($q) use ($startYear, $firstMonthOfYear, $toYear) {
                        $q->whereYear('order_date', '>', $startYear)
                            ->orWhere(function ($subQuery) use ($startYear, $firstMonthOfYear) {
                                $subQuery->whereYear('order_date', $startYear)
                                    ->whereMonth('order_date', '>=', $firstMonthOfYear);
                            });
                    })
                    ->orWhere(function ($q) use ($toYear, $currentMonth) {
                        $q->whereYear('order_date', '<', $toYear)
                            ->orWhere(function ($subQuery) use ($toYear, $currentMonth) {
                                $subQuery->whereYear('order_date', $toYear)
                                    ->whereMonth('order_date', '<=', $currentMonth);
                            });
                    });
                })
                ->groupBy('month')
                ->get();
                break;
        }

        foreach ($monthsAndYears as $year => $months) {
            foreach ($months as $month) {
                $found = false;
                foreach ($transactions as $transaction) {
                    if ($transaction->year == $year && $transaction->month == $month) {
                        array_push($monthResults, $month);
                        if ($param == 'total_transaction') {
                            array_push($valueResults, (double)$transaction->total);
                        } else {
                            array_push($valueResults, (int)$transaction->total);
                        }
                        $sum += $transaction->total;
                        $found = true;
                        break;
                    }
                }
        
                if (!$found) {
                    array_push($monthResults, $month);
                    array_push($valueResults, 0);
                }
            }
        }

        //average
        if($start && $to){
            $startDateAvg = new DateTime($start);
            $endDateAvg = new DateTime($to);
            $interval = $startDateAvg->diff($endDateAvg);
            $months = (($interval->y * 12) + $interval->m)+1;
            $average = $sum/$months;
        } else {
            $average = $sum/$currentMonth;
        }

        //highest

        $highest = max($valueResults);
        $largestTransactionRecord = $transactions->where('total', $highest)->first();
        if($largestTransactionRecord){
            $monthHighest = $largestTransactionRecord->month;
            $highestYear = $largestTransactionRecord->year;
            $highestDate  = FormatHelper::getMonthfromNumber($monthHighest).' '.$highestYear;
        }
        if($highest == 0){
            $highestDate = '-';
        }
        //lowest
        $lowest = min($valueResults);
        $lowestTransactionRecord = $transactions->where('total', $lowest)->first();
        if($lowestTransactionRecord){
            $monthLowest= $lowestTransactionRecord->month;
            $lowestYear = $lowestTransactionRecord->year;
            $lowestDate = FormatHelper::getMonthfromNumber($monthLowest).' '.$lowestYear;
        }
        if($lowest == 0){
            $lowestDate = '-';
        }

        $data = new Collection([
            'monthResults' => FormatHelper::mapNumbersToMonth3LF($monthResults),
            'valueResults' => $valueResults,
            'startPeriod' => FormatHelper::getMonthfromNumber($firstMonthOfYear).' '.$startYear,
            'endPeriod' => FormatHelper::getMonthfromNumber($currentMonth).' '.$toYear,
            'avgValue' => round($average),
            'highestValue' => $highest,
            'highestMonth' => $highestDate,
            'lowestValue' => $lowest,
            'lowestMonth' => $lowestDate,
        ]);

        return $data;

    }

    public function getResellerChartDataAllDownload($start, $to)
    {

        if($start && $to){
            $newdateStart = strtotime($start);
            $newDateTo = strtotime($to);
            $firstMonthOfYear = (int)date('m',$newdateStart);
            $currentMonth = (int)date('m',$newDateTo);
            $startYear = (int)date('Y',$newdateStart);
            $toYear = (int)date('Y',$newDateTo);
            $startDate = new DateTime($start);
            $endDate = new DateTime($to);
            $interval = new DateInterval('P1M');
            $monthsOfYear = array();
            while ($startDate <= $endDate) {
                $monthsOfYear[] = $startDate->format('m');
                $startDate->add($interval);
            }
        } else {
            $startYear =  Carbon::now()->year;
            $toYear =  Carbon::now()->year;
            $firstMonthOfYear = 1;
            $currentMonth = 12;
            $monthsOfYear = range($firstMonthOfYear,$currentMonth);
        }

        $transactions = OrderReseller::selectRaw('YEAR(order_date) as year, MONTH(order_date) as month, SUM(pay_amount) as total')
        ->ongoing()
        ->where(function ($query) use ($startYear, $firstMonthOfYear, $toYear, $currentMonth) {
            $query->where(function ($q) use ($startYear, $firstMonthOfYear, $toYear) {
                $q->whereYear('order_date', '>', $startYear)
                    ->orWhere(function ($subQuery) use ($startYear, $firstMonthOfYear) {
                        $subQuery->whereYear('order_date', $startYear)
                            ->whereMonth('order_date', '>=', $firstMonthOfYear);
                    });
            })
            ->orWhere(function ($q) use ($toYear, $currentMonth) {
                $q->whereYear('order_date', '<', $toYear)
                    ->orWhere(function ($subQuery) use ($toYear, $currentMonth) {
                        $subQuery->whereYear('order_date', $toYear)
                            ->whereMonth('order_date', '<=', $currentMonth);
                    });
            });
        })
        ->groupBy('month')
        ->get();

        $transactionValue = $this->processReportVariableDate($transactions, "total_transaction", $start, $to);

        $numberOfTransactions = OrderReseller::selectRaw('YEAR(order_date) as year, MONTH(order_date) as month, COUNT(*) as total')
        ->ongoing()
        ->where(function ($query) use ($startYear, $firstMonthOfYear, $toYear, $currentMonth) {
            $query->where(function ($q) use ($startYear, $firstMonthOfYear, $toYear) {
                $q->whereYear('order_date', '>', $startYear)
                    ->orWhere(function ($subQuery) use ($startYear, $firstMonthOfYear) {
                        $subQuery->whereYear('order_date', $startYear)
                            ->whereMonth('order_date', '>=', $firstMonthOfYear);
                    });
            })
            ->orWhere(function ($q) use ($toYear, $currentMonth) {
                $q->whereYear('order_date', '<', $toYear)
                    ->orWhere(function ($subQuery) use ($toYear, $currentMonth) {
                        $subQuery->whereYear('order_date', $toYear)
                            ->whereMonth('order_date', '<=', $currentMonth);
                    });
            });
        })
        ->groupBy('month')
        ->get();
        $numberOfTransactions = $this->processReportVariableDate($numberOfTransactions, "total_transaction", $start, $to);


        $soldItems = OrderReseller::
        leftJoin('rsl_order_details', 'rsl_order_details.order_header_id' ,'=','rsl_order_headers.id')
        ->selectRaw('YEAR(rsl_order_headers.order_date) as year, MONTH(rsl_order_headers.order_date) as month, SUM(rsl_order_details.qty) as total')
        ->ongoing()
        ->where(function ($query) use ($startYear, $firstMonthOfYear, $toYear, $currentMonth) {
            $query->where(function ($q) use ($startYear, $firstMonthOfYear, $toYear) {
                $q->whereYear('rsl_order_headers.order_date', '>', $startYear)
                    ->orWhere(function ($subQuery) use ($startYear, $firstMonthOfYear) {
                        $subQuery->whereYear('rsl_order_headers.order_date', $startYear)
                            ->whereMonth('rsl_order_headers.order_date', '>=', $firstMonthOfYear);
                    });
            })
            ->orWhere(function ($q) use ($toYear, $currentMonth) {
                $q->whereYear('rsl_order_headers.order_date', '<', $toYear)
                    ->orWhere(function ($subQuery) use ($toYear, $currentMonth) {
                        $subQuery->whereYear('rsl_order_headers.order_date', $toYear)
                            ->whereMonth('rsl_order_headers.order_date', '<=', $currentMonth);
                    });
            });
        })
        ->groupBy('month')
        ->get();
        $soldItems = $this->processReportVariableDate($soldItems, "sold_items", $start, $to);

        $startPeriod = FormatHelper::getMonthfromNumber($firstMonthOfYear).' '.$startYear;
        $endPeriod = FormatHelper::getMonthfromNumber($currentMonth).' '.$toYear;

        //ranks

        $data = new Collection([
            'monthResult' => FormatHelper::mapNumbersToMonth3LF($transactionValue["monthResults"]),
            'transactionValue' => [
                'valueResult' => $transactionValue["valueResults"],
                'startPeriod' => $startPeriod,
                'endPeriod' => $endPeriod,
                'avgValue' => $transactionValue["avgValue"],
                'highestValue' => $transactionValue["highestValue"],
                'highestMonth' => $transactionValue['highestDate'],
                'lowestValue' => $transactionValue["lowestValue"],
                'lowestMonth' => $transactionValue['lowestDate'],
            ],
            'numberOfTransactions' => [
                'valueResult' => $numberOfTransactions["valueResults"],
                'startPeriod' => $startPeriod,
                'endPeriod' => $endPeriod,
                'avgValue' => $numberOfTransactions["avgValue"],
                'highestValue' => $numberOfTransactions["highestValue"],
                'highestMonth' => $numberOfTransactions['highestDate'],
                'lowestValue' => $numberOfTransactions["lowestValue"],
                'lowestMonth' => $numberOfTransactions['lowestDate'],
            ],
            'soldItems' => [
                'valueResult' => $soldItems["valueResults"],
                'startPeriod' => $startPeriod,
                'endPeriod' => $endPeriod,
                'avgValue' => $soldItems["avgValue"],
                'highestValue' => $soldItems["highestValue"],
                'highestMonth' => $soldItems['highestDate'],
                'lowestValue' => $soldItems["lowestValue"],
                'lowestMonth' => $soldItems['lowestDate'],
            ],
            'topResellers' => $this->getTopResellersVariableDate($start, $to, 'txValue'),
            'topItems' => $this->getTopSellingItemsAllVariableDate($start, $to),
            'topClicks' => $this->getTopResellersVariableDate($start, $to, 'clicks'),
            'topNumbers' => $this->getTopResellersVariableDate($start, $to, 'txAmount'),
            'topValue' => $this->getTopResellersVariableDate($start, $to,'txValue')
        ]);
        return $data;
    // } catch (Exception $e) {
    //     dd($e->getMessage());
    // }

    }

    public function getSalesReport($resellerId)
    {
        $currentYear = Carbon::now()->year;
        $firstMonthOfYear = 1;
        $currentMonth = 12;

        //transactions value
        $transactions = OrderReseller::selectRaw('YEAR(created_date) as year, MONTH(created_date) as month, SUM(total_amount) as total')
            ->ongoing()
            ->where('reseller_id', $resellerId)
            ->whereYear('created_date', $currentYear)
            ->groupBy('year','month')
            ->get();

        $transactionValue = $this->processReport($transactions, "total_transaction");
        
        //number of transactions
        $numberOfTransactions = OrderReseller::selectRaw('YEAR(created_date) as year, MONTH(created_date) as month, COUNT(*) as total')
            ->ongoing()
            ->where('reseller_id', $resellerId)
            ->whereYear('created_date', $currentYear)
            ->groupBy('month')
            ->get();

        $numberOfTransactions = $this->processReport($numberOfTransactions, "total_transaction");
        
        // sold items
        $soldItems = OrderReseller::leftJoin('rsl_order_details', 'rsl_order_details.order_header_id' ,'=','rsl_order_headers.id')
            ->selectRaw('YEAR(rsl_order_headers.created_date) as year, MONTH(rsl_order_headers.created_date) as month, SUM(rsl_order_details.qty) as total')
            ->ongoing()
            ->where('reseller_id', $resellerId)
            ->whereYear('rsl_order_headers.created_date', $currentYear)
            ->groupBy('month')
            ->get();

        $soldItems = $this->processReport($soldItems, "total_transaction");
        
        // commissions
        $rsl_id = Reseller::where('id', $resellerId)->first()->reseller_id??'0';
        $commissions = CommissionLedgers::
        selectRaw('YEAR(rsl_commission_ledgers.created_date) as year, MONTH(rsl_commission_ledgers.created_date) as month, SUM(rsl_commission_ledgers.amount) as total')
            ->where('reseller_id', $rsl_id)
            ->whereYear('rsl_commission_ledgers.created_date', $currentYear)
            ->groupBy('month')
            ->get();

        $commissions = $this->processReport($commissions, "total_transaction");
        $startPeriod = FormatHelper::getMonthfromNumber($firstMonthOfYear).' '.$currentYear;
        $endPeriod = FormatHelper::getMonthfromNumber($currentMonth).' '.$currentYear;
        $data = new Collection([
            'monthResult' => FormatHelper::mapNumbersToMonth3LF($transactionValue["monthResult"]),
            'transactionValue' => [
                'valueResult' => $transactionValue["valueResult"],
                'startPeriod' => $startPeriod,
                'endPeriod' => $endPeriod,
                'avgValue' => $transactionValue["average"],
                'highestValue' => $transactionValue["highest"],
                'highestMonth' => FormatHelper::getMonthfromNumber($transactionValue["highestMonth"]).' '.$currentYear,
                'lowestValue' => $transactionValue["lowest"],
                'lowestMonth' => FormatHelper::getMonthfromNumber($transactionValue["lowestMonth"]).' '.$currentYear,
            ],
            'numberOfTransactions' => [
                'valueResult' => $numberOfTransactions["valueResult"],
                'startPeriod' => $startPeriod,
                'endPeriod' => $endPeriod,
                'avgValue' => $numberOfTransactions["average"],
                'highestValue' => $numberOfTransactions["highest"],
                'highestMonth' => FormatHelper::getMonthfromNumber($numberOfTransactions["highestMonth"]).' '.$currentYear,
                'lowestValue' => $numberOfTransactions["lowest"],
                'lowestMonth' => FormatHelper::getMonthfromNumber($numberOfTransactions["lowestMonth"]).' '.$currentYear,
            ],
            'soldItems' => [
                'valueResult' => $soldItems["valueResult"],
                'startPeriod' => $startPeriod,
                'endPeriod' => $endPeriod,
                'avgValue' => $soldItems["average"],
                'highestValue' => $soldItems["highest"],
                'highestMonth' => FormatHelper::getMonthfromNumber($soldItems["highestMonth"]).' '.$currentYear,
                'lowestValue' => $soldItems["lowest"],
                'lowestMonth' => FormatHelper::getMonthfromNumber($soldItems["lowestMonth"]).' '.$currentYear,
            ],
            'commissions' => [
                'valueResult' => $commissions["valueResult"],
                'startPeriod' => $startPeriod,
                'endPeriod' => $endPeriod,
                'avgValue' => $commissions["average"],
                'highestValue' => $commissions["highest"],
                'highestMonth' => FormatHelper::getMonthfromNumber($commissions["highestMonth"]).' '.$currentYear,
                'lowestValue' => $commissions["lowest"],
                'lowestMonth' => FormatHelper::getMonthfromNumber($commissions["lowestMonth"]).' '.$currentYear,
            ],
        ]);

        return $data;
    }

    private function processReport($transactions, $param = null)
    {
        $firstMonthOfYear = 1;
        $currentMonth = 12;
        $monthsOfYear = range($firstMonthOfYear,$currentMonth);
        $monthResults = [];
        $valueResults = [];
        $sum = 0.0;

        foreach ($monthsOfYear as $month) {
            $found = false;
            foreach ($transactions as $transaction) {
                if ($transaction->month == $month) {
                    array_push($monthResults, $month);
                    if($param == 'total_transaction'){
                        array_push($valueResults, (double)$transaction->total);
                    } else {
                        array_push($valueResults, (int)$transaction->total);
                    }
                    $sum += $transaction->total;
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                array_push($monthResults, $month);
                array_push($valueResults, 0);
            }
        }
        
        //average
        $average = $sum/$currentMonth;

        //highest
        $highest = max($valueResults);
        $keysOfHighestValue = array_keys($valueResults, $highest);
        $indexOfHighestValue = $keysOfHighestValue[0];
        $monthHighest = $monthResults[$indexOfHighestValue];

        //lowest
        $lowest = min($valueResults);
        $keysOfLowestValue = array_keys($valueResults, $lowest);
        $indexOfLowestValue = $keysOfLowestValue[0];
        $monthLowest = $monthResults[$indexOfLowestValue];

        return [
            "monthResult" => $monthResults,
            "valueResult" => $valueResults,
            "average" => $average,
            "highest" => $highest,
            "highestMonth" => $monthHighest,
            "lowest" => $lowest,
            "lowestMonth" => $monthLowest
        ];
    }

    private function processReportVariableDate($transactions, $param = null, $start, $to)
    {
        if($start && $to){
            $newdateStart = strtotime($start);
            $newDateTo = strtotime($to);
            $firstMonthOfYear = (int)date('m',$newdateStart);
            $currentMonth = (int)date('m',$newDateTo);
            $startYear = (int)date('Y',$newdateStart);
            $toYear = (int)date('Y',$newDateTo);
            $startDate = new DateTime($start);
            $endDate = new DateTime($to);
            $interval = new DateInterval('P1M');
            $monthsAndYears = [];
            while ($startDate <= $endDate) {
                $year = $startDate->format('Y');
                $month = $startDate->format('m');
            
                if (!isset($monthsAndYears[$year])) {
                    $monthsAndYears[$year] = [];
                }
                $monthsAndYears[$year][] = $month;
                $startDate->add($interval);
            }
        } else {
            $startYear =  Carbon::now()->year;
            $toYear =  Carbon::now()->year;
            $firstMonthOfYear = 1;
            $currentMonth = 12;
            $year = date('Y');
            $startOfYear = new DateTime($year . '-01-01');
            $year = date('Y');
            $endOfYear = new DateTime($year . '-12-31');            
            $interval = new DateInterval('P1M');
            $monthsAndYears = [];
            while ($startOfYear <= $endOfYear) {
                $year = $startOfYear->format('Y');
                $month = $startOfYear->format('m');
            
                if (!isset($monthsAndYears[$year])) {
                    $monthsAndYears[$year] = [];
                }
                $monthsAndYears[$year][] = $month;
                $startOfYear->add($interval);
            }
        }
        $monthResults = [];
        $valueResults = [];
        $sum = 0.0;
        foreach ($monthsAndYears as $year => $months) {
            foreach ($months as $month) {
                $found = false;
                foreach ($transactions as $transaction) {
                    if ($transaction->year == $year && $transaction->month == $month) {
                        array_push($monthResults, $month);
                        if ($param == 'total_transaction') {
                            array_push($valueResults, (double)$transaction->total);
                        } else {
                            array_push($valueResults, (int)$transaction->total);
                        }
                        $sum += $transaction->total;
                        $found = true;
                        break;
                    }
                }
        
                if (!$found) {
                    array_push($monthResults, $month);
                    array_push($valueResults, 0);
                }
            }
        }
        
        //average
        if($start && $to){
            $startDateAvg = new DateTime($start);
            $endDateAvg = new DateTime($to);
            $interval = $startDateAvg->diff($endDateAvg);
            $months = (($interval->y * 12) + $interval->m)+1;
            $average = $sum/$months;
        } else {
            $average = $sum/$currentMonth;
        }
        //highest

        $highest = max($valueResults);
        $largestTransactionRecord = $transactions->where('total', $highest)->first();
        if($largestTransactionRecord){
            $monthHighest = $largestTransactionRecord->month;
            $highestYear = $largestTransactionRecord->year;
            $highestDate  = FormatHelper::getMonthfromNumber($monthHighest).' '.$highestYear;
        }
        if($highest == 0){
            $highestDate = '-';
        }
        //lowest
        $lowest = min($valueResults);
        $lowestTransactionRecord = $transactions->where('total', $lowest)->first();
        if($lowestTransactionRecord){
            $monthLowest= $lowestTransactionRecord->month;
            $lowestYear = $lowestTransactionRecord->year;
            $lowestDate = FormatHelper::getMonthfromNumber($monthLowest).' '.$lowestYear;
        }
        if($lowest == 0){
            $lowestDate = '-';
        }

        return [
            'monthResults' => $monthResults,
            'valueResults' => $valueResults,
            'startPeriod' => FormatHelper::getMonthfromNumber($firstMonthOfYear).' '.$startYear,
            'endPeriod' => FormatHelper::getMonthfromNumber($currentMonth).' '.$toYear,
            'avgValue' => round($average),
            'highestValue' => $highest,
            'highestDate' => $highestDate,
            'lowestValue' => $lowest,
            'lowestDate' => $lowestDate,
        ];
    }

    public function getTopResellersVariableDate($start, $to, $param = null){
        $sumOrders = 0.0;
        $resellerData = collect();
        $resellers = Reseller::with(['orders', 'link', 'commissions'])
        ->when($start && $to, function ($query) use ($start, $to) {
            $query->whereHas('orders', function ($orderQuery) use ($start, $to) {
                $orderQuery->whereDate('created_date', '>=', $start)
                           ->whereDate('created_date', '<=', $to);
            })->whereHas('link', function ($linkQuery) use ($start, $to) {
                $linkQuery->whereDate('created_date', '>=', $start)
                          ->whereDate('created_date', '<=', $to);
            });
        })
        ->get();
    
        foreach($resellers as $reseller){
          $orders = $reseller->orders;
          $links = $reseller->link;
          $resellerId = $reseller->reseller_id;
          $name = $reseller->name;
          $status = $reseller->is_active;
          $commissions = $reseller->commissions;

            $sumOrders = $orders->whereNotIn('order_status',[OrderReseller::ORDER_RESELLER_BATAL])->sum('total_amount');
    
          $clicks = $links->reduce(function ($carry, $link) {
            $count = ResellerLinkHistory::where('link_id', $link->id)->count();
            return $carry + $count;
          }, 0);

    
          $resellerData->push([
            'reseller_id' => $resellerId,
            'name' => $name,
            'status' => $status,
            'sum_orders' => $sumOrders,
            'amount' => $orders->count(),
            'clicks' => $clicks,
            'commissions' => $commissions->sum('commission_amount')
          ]);
        }
    
        switch($param){
            case 'clicks':
                $resellerData = $resellerData->sortByDesc(function ($item) {
                    return [$item['clicks']];
                });
                break;
            case 'txAmount':
                $resellerData = $resellerData->sortByDesc(function ($item) {
                    return [$item['amount']];
                });
                break;
            case 'txValue':
                $resellerData = $resellerData->sortByDesc(function ($item) {
                    return [$item['sum_orders']];
                });
                break;
            default:
                $resellerData = $resellerData->sortByDesc(function ($item) {
                    return [$item['sum_orders'], $item['clicks'], $item['commissions']];
                });
                break;
        }


        $resellerData = $resellerData->values()->map(function ($item, $key) {
          $item['position'] = $key + 1;
          return $item;
        });

        $resellerData = $resellerData->take(5);

        return $resellerData;
    }
    public function getTopSellingItemsAllVariableDate($start, $to){
        $orderIds = [];
        $orderData = collect();
        $orders = OrderReseller::ongoing()
        ->when($start && $to, function ($query) use ($start, $to) {
            $query->whereDate('order_date', '>=', $start)
                  ->whereDate('order_date', '<=', $to);
        })
        ->get();
        foreach($orders as $order){
            array_push($orderIds, $order->id);
        }

        $orderDetails = OrderItemReseller::whereIn('order_header_id',$orderIds)->groupBy('article_id')->select('article_id', DB::raw('SUM(qty) as total_qty'),'unit_price')
        ->get();
        foreach($orderDetails as $orderDetail){
            $product = Product::where('article',$orderDetail->article_id)->first();
            $color = Color::select('value')->where('key',$product->product_variant_c)->first()->value;
            $orderData->push([
                'sku' => $orderDetail->article_id,
                'name' => $product->product_name_c.', '.$color.', '.$product->product_size_c,
                'qty' => $orderDetail->total_qty,
                'unit_price' => (double)$orderDetail->unit_price
            ]);
        }

        $orderData = $orderData->sortByDesc(function ($item) {
            return [$item['qty']];
        });

        $orderData = $orderData->values()->map(function ($item, $key) {
          $item['position'] = $key + 1;
          return $item;
        });

        $orderData = $orderData->take(5);

        return $orderData;
    }

}
