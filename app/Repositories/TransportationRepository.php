<?php
namespace App\Repositories;
use App\Models\Transportation;

class TransportationRepository
{

    public function getTransportations($page, $perPage, $search)
    {
        $transportations = Transportation::select('*');

        if (!empty($search)) {
            $transportations = $transportations->where(function ($query) use($search) {
                $query->where('zone_code', 'like', '%'.$search.'%')
                      ->orWhere('description', 'like', '%'.$search.'%');
            });
        }
        $transportations = $transportations->paginate($perPage, ['*'], 'page', $page);

        return $transportations;
    }
}
?>