<?php

namespace App\Repositories;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Order;
use App\Models\Customer;
use App\Models\Reseller;
use App\Helpers\FileHelper;
use App\Models\CreditLimit;
use App\Traits\ResponseAPI;
use Illuminate\Support\Str;
use App\Models\ResellerLink;
use App\Jobs\CreditLimitQueue;
use App\Models\MasterParameter;
use App\Models\CustomerShipment;
use App\Interfaces\UserInterface;
use App\Models\TransportationZone;
use App\Http\Resources\UserResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Http\Resources\SalesResource;
use App\Http\Resources\SalesCollection;
use App\Http\Resources\CustomerResource;
use App\Http\Resources\StoreListResource;
use App\Http\Resources\CreditLimitResource;
use App\Http\Resources\UserResellerResource;
use App\Http\Resources\UserInternalListResource;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class UserRepository implements UserInterface
{
    use ResponseAPI;
    use FileHelper;

    public function login($req)
    {
        $credentials = [
            'username' => $req->has('email') ? $req->email : $req->username,
            'password' => $req->password,
        ];

        if ($req->type == 'reseller') {
            $credentials = [
                'username' => $req->has('email') ? $req->email : $req->username,
                'password' => $req->password,
                'reference_object' => $req->type
            ];
        }
        $akses = [
            'internal' => [
                'sales',
                'finance'
            ],
            'external' => [
                'customer'
            ],
            'reseller' => [
                'reseller'
            ]
        ];
        try {
            if (!Auth::attempt($credentials)) {
                return $this->sendError('The provided credentials do not match our records.', 400);
            }
            $mp = MasterParameter::where('group_key', 'FOOTER')->where('key', 'SOCIAL_MEDIA_SUPPORT_TEAM')->first();
            $user = $req->user();

            //log in reseller
            if ($user->reference_object == 'reseller') {
                $reseller = Reseller::where('reseller_id', $user->reference_id)->first();
                if (
                    !$req->type
                    || $req->type != 'reseller'
                    || !in_array($user->reference_object, $akses[$req->type])
                    || $user->is_active == 0
                    || $reseller->is_active == 0
                ) {
                    try {
                        return $this->sendError('You are not allowed to login to this page.', 401);
                    } catch (\Exception $e) {
                        return $this->sendError('Login unauthorize error :' . $e->getMessage());
                    }
                } else {
                    $getProfile = Reseller::Where('reseller_id', "=", $user->reference_id)->first();
                    $getStore = ResellerLink::Select("identifier")->Where('reseller_id', '=', $getProfile->id)->Where('type', '=', 'STORE')->first();
                    $user->reseller_store_name = $getStore ? $getStore->identifier : null;

                    $user->phone = $getProfile->phone_number;
                    $user->nationalId = $getProfile->national_id;
                    $user->npwp = $getProfile->npwp;
                    $user->address = $getProfile->address;
                    $user->province_code = $getProfile->province_code;
                    $user->city_code = $getProfile->city_code;
                    $user->district_code = $getProfile->district_code;
                    $user->zip_code = $getProfile->zip_code;
                    $user->is_freeze = $getProfile->is_active;

                    $data = $this->authResponse($user);

                    return $this->sendSuccessWithCookies('Login successfully.', $data, cookie('jwt', $data['auth']['access_token'], 60 * 24, null, null, null, true, false, 'lax'));
                }
            }

            if (
                !$req->type
                || !in_array($user->reference_object, $akses[$req->type])
                || ($req->type == 'internal' and $user->business_units->isEmpty())
                || $user->is_active == 0
            ) {
                try {
                    return $this->sendError('You are not allowed to login to this page.', 401);
                } catch (\Exception $e) {
                    return $this->sendError('Login unauthorize error :' . $e->getMessage());
                }
            }

            if (!is_null($user->customer) and $user->customer->is_blocked == 1) {
                return $this->sendError('Maaf saat ini akun Anda tidak bisa melakukan login. Silakan hubungi Layanan Pelanggan kami di +628112311632 untuk informasi lebih lanjut.', 401);
            }



            $data = $this->authResponse($user);

            if (!in_array($user->reference_object, ['rsl_customers', 'reseller', 'sales', 'finance'])) {
                if ($user->customer->distribution_channel != $req->reference) {
                    try {
                        return $this->sendError('You are not allowed to login to this page.', 401);
                    } catch (\Exception $e) {
                        return $this->sendError('Login unauthorize error :' . $e->getMessage());
                    }
                }
            }
            if (in_array($user->reference_object, ['rsl_customers', 'reseller', 'sales', 'finance'])) {
                return $this->sendSuccessWithCookies('Login successfully.', $data, cookie('jwt', $data['auth']['access_token'], 60 * 24, null, null, null, true, false, 'lax'));
            }
            return $this->sendSuccess('Login successfully.', $data);
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage() . ' : ' . $e->getLine());
        }
    }

    public function initChangePassword($req)
    {
        try {
            $user = $req->user();
            $user->password = Hash::make($req->password);
            $user->is_change_password = 1;
            $user->save();
            return $this->sendSuccess('Your password has been change successfully.', new StoreListResource($user->customer));
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function changePassword($req)
    {
        try {
            $user = $req->user();
            $user->password = Hash::make($req->new_password);
            $user->save();
            $data = [];
            if ($user->reference_object == 'customer') {
                $data = new CustomerResource($user->customer);
            } elseif ($user->reference_object == 'sales') {
                $data = new SalesResource($user->sales);
            }
            return $this->sendSuccess('Your password has been change successfully.', $data);
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function generateTokenForgotPassword($req)
    {
        try {
            $user = User::whereEmail($req->email)->firstOrFail();
            $token = Str::random(100);
            $user->remember_token = $token;
            $user->expired_token = now()->addHours(24);
            $user->save();
        } catch (ModelNotFoundException) {
            return $this->sendError('User with email: ' . $req->email . ' not found.');
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }

        return $this->sendSuccess('Your forgotten password has been requested.', [
            'remember_token' => $token
        ]);
    }

    public function forgotChangePassword($req)
    {
        try {
            $user = User::where('remember_token', $req->remember_token)->firstOrFail();
            if ($user->expired_token < now()) {
                return $this->sendError('Your token has been expired.', 463);
            }
            $user->password = Hash::make($req->new_password);
            $user->remember_token = null;
            $user->expired_token = null;
            $user->save();
            $data = [];
            if ($user->reference_object == 'customer') {
                $data = new CustomerResource($user->customer);
            } elseif (in_array($user->reference_object, ['finance', 'sales'])) {
                $data = new SalesResource($user->sales);
            }
            return $this->sendSuccess('Your password has been change successfully.', $data);
        } catch (ModelNotFoundException) {
            return $this->sendError('Your token could not be found.');
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function storeList($customer)
    {
        try {
            return $this->sendSuccess('Stores retrieved successfully.', new StoreListResource($customer));
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function activation($user)
    {
        try {
            $customer = $user->customer;
            if (!$user->is_change_password) {
                return $this->sendError('You have not change default password.', 403, 'Access denied!');
            }
            if ($customer->is_verified) {
                return $this->sendError('Your account has been verified before.', 403, 'Access denied!');
            }
            $customer->is_verified = 1;
            $customer->save();
            $user->currentAccessToken()->delete();
            return $this->sendSuccess('Congrats! Your account has been verified.', $this->authResponse($user));
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    public function getProfile($user)
    {
        $data = [];
        if ($user->reference_object == 'customer') {
            $data = new CustomerResource($user->customer);
        } elseif ($user->reference_object == 'sales') {
            $data = new SalesResource($user->sales);
        }
        return $this->sendSuccess('Your profile retrieved successfully.', $data);
    }

    public function getCreditLimit($customerId)
    {

        try {
            $customer = Customer::find($customerId);
            CreditLimitQueue::dispatch($customerId);
            $total = Order::where('customer_id', $customer->customer_id)->sum('total_nett');
            return $this->sendSuccess('Your credit limit retrieved successfully', new CreditLimitResource($customer, $total));
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    private function getToken($user)
    {
        $token = $user->createToken('customer-' . $user->customer->customer_type);

        return $token;
    }

    private function authResponse($user)
    {
        $data = [];
        if ($user->reference_object == 'customer') {
            $token = $user->createToken('customer-' . @$user->customer->customer_type);
            $data = new CustomerResource($user->customer);
        } elseif ($user->reference_object == 'sales') {
            $token = $user->createToken('sales-' . @$user->sales->position->code);
            $data = new SalesResource($user->sales);
        } else {
            $token = $user->createToken('user-' . $user->reference_object);
        }

        if ($user->reference_object === 'reseller') {
            $data = [
                "name" => $user->name,
                "email" => $user->email,
                "phone" => $user->phone,
                "nationalId" => $user->nationalId,
                "npwp" => $user->npwp,
                "address" => $user->address,
                "province_code" => $user->province_code,
                "city_code" => $user->city_code,
                "district_code" => $user->district_code,
                "zip_code" => $user->zip_code,
                "is_freeze" => $user->is_freeze == 9 ? true : false
            ];

            return [
                'auth' => [
                    'access_token' => $token->plainTextToken,
                    'token' => 'bearer'
                ],
                'user' => new UserResellerResource($user),
                'profile' => $data,
            ];
        } else {
            return [
                'auth' => [
                    'access_token' => $token->plainTextToken,
                    'token' => 'bearer'
                ],
                'user' => new UserResource($user),
                'profile' => $data,
            ];
        }
    }

    public function getData($users, $req)
    {
        try {
            $data = $this->filter($users, $req);
            $latest = $data->max('modified_date') ?? date('Y-m-d H:i:s');
            $dataCollection = UserInternalListResource::collection($data)->response()->getData(true);

            return $this->sendSuccess(
                "Users retrieved successfully.",
                array_merge(['latest_update' => $latest], $dataCollection)
            );
        } catch (\Exception $e) {
            return $this->sendError($e->getMessage());
        }
    }

    private function filter($users, $request)
    {
        $orderField = 'user.created_date';
        $orderBy = 'desc';
        $limit = 15;

        if ($ownerName = $request->query('name')) {
            $users = $users->where('name', 'like', '%' . $ownerName . '%');
        }

        if ($id = $request->query('id')) {
            $users = $users->where('id', 'like', '%' . $id . '%');
        }

        if ($status = $request->query('status')) {
            $statusValues = explode(',', $status);

            $users = $users->where(function ($query) use ($statusValues) {
                foreach ($statusValues as $value) {
                    switch ($value) {
                        case 'active':
                            $query->orWhere('is_active', 1);
                            break;

                        case 'non-active':
                            $query->orWhere('is_active', 0);
                            break;
                    }
                }
            });
        }

        if ($unit = $request->query('business_unit')) {
            $business_unit = explode(',', $unit);
            $users = $users->whereHas('business_units', function ($query) use ($business_unit) {
                $query->where(function ($subQuery) use ($business_unit) {
                    foreach ($business_unit as $value) {
                        switch ($value) {
                            case 'wholesales':
                                $subQuery->orWhere('name', 'like', 'wholesales');
                                break;
                            case 'b2b':
                                $subQuery->orWhere('name', 'like', 'b2b');
                                break;
                            case 'reseller':
                                $subQuery->orWhere('name', 'like', 'reseller');
                                break;
                            case 'wab':
                                $subQuery->orWhere('name', 'like', 'wab');
                                break;
                        }
                    }
                });
            });
        }

        if ($tier = $request->query('tier')) {
            $tiers = str_replace('tier', '', explode(',', $tier));

            $users = $users->whereHas('business_units', function ($query) use ($tiers) {
                $query->where(function ($subQuery) use ($tiers) {
                    $subQuery->whereIn('tier_level', $tiers);
                });
            });
        }

        if ($roles = $request->query('role')) {
            $roles = explode(',', $roles);
            $users = $users->whereHas('roles', function ($query) use ($roles) {
                $query->where(function ($subQuery) use ($roles) {
                    foreach ($roles as $role) {
                        switch ($role) {
                            case 'generalmanager':
                                $subQuery->orWhere('name', 'like', 'General Manager');
                                break;
                            case 'manager':
                                $subQuery->orWhere('name', 'like', 'Manager');
                                break;
                            case 'supervisor':
                                $subQuery->orWhere('name', 'like', 'Supervisor');
                                break;
                            case 'seniorofficer':
                                $subQuery->orWhere('name', 'like', 'Senior Officer');
                                break;
                            case 'officer':
                                $subQuery->orWhere('name', 'like', 'Officer');
                                break;
                            case 'seniorsales':
                                $subQuery->orWhere('name', 'like', 'Senior Sales');
                                break;
                            case 'sales':
                                $subQuery->orWhere('name', 'like', 'Sales');
                                break;
                            case 'admin':
                                $subQuery->orWhere('name', 'like', 'Admin');
                                break;
                        }
                    }
                });
            });
        }

        if ($positionName = $request->query('position')) {
            $users = $users->whereHas('sales', function ($query) use ($positionName) {
                $query->where('position_name', 'like', '%' . $positionName . '%');
            });
        }

        if ($request->has('limit')) {
            $limit = $request->query('limit');
        }

        if ($order = $request->query('order')) {
            list($by, $sorted) = explode(':', $order);
            $orderField = $by;
            $orderBy = $sorted;
        }

        $users = $users->orderBy($orderField, $orderBy)->paginate($limit);

        return $users;
    }

    public function updateProfile($request)
    {
        $custId = auth()->user()->customer->customer_id;
        $customerNPWP = auth()->user()->customer->npwp_file;

        $envValue = env('S3_STREAM_URL');

        if ($request->npwp_file !== $envValue . $customerNPWP) {
            if ($customerNPWP) {
                \Storage::disk('s3')->delete(ltrim($customerNPWP, '/'));
            }
            $tf_file = $this->fileTransfer($request->npwp_file, 'npwp-dev', true);
            if ($tf_file['error'] == true) {
                return $this->sendError($tf_file['message']);
            }
        }

        $user = User::where('reference_id', $custId)->first();
        if (!$user) {
            return $this->sendError('User not found', 404);
        }

        $user->name = $request->owner_name;
        $user->username = $request->email;
        $user->email = $request->email;
        $user->modified_by = Auth::user()->name;
        $user->save();

        $customer = Customer::where('customer_id', $custId)->first();
        if (!$customer) {
            return $this->sendError('Customer not found', 404);
        }

        $customer->owner_name = $request->owner_name;
        $customer->email = $request->email;
        $customer->address = $request->shipment_address;
        $customer->phone_number = $request->phone_number;
        $customer->national_id = $request->national_id;
        $customer->npwp = $request->npwp;

        if ($request->npwp_file !== $envValue . $customer->npwp_file) {
            $customer->npwp_file = '/' . $tf_file['filepath'];
        }

        $customer->npwp_name = $request->npwp_name;
        $customer->npwp_address = $request->npwp_address;
        $customer->npwp_province = $request->npwp_province;
        $customer->npwp_province_code = $request->npwp_province_code;
        $customer->npwp_city = $request->npwp_city;
        $customer->npwp_city_code = $request->npwp_city_code;
        $customer->npwp_district = $request->npwp_district;
        $customer->npwp_district_code = $request->npwp_district_code;
        $customer->npwp_zip_code = $request->npwp_zip_code;
        $customer->tax_type = $request->tax_type;
        $customer->tax_invoice = $request->tax_invoice;

        if ($customer->status === 'Perlu Revisi') {
            $customer->status = 'Baru';
            $customer->is_revised = 0;
            $customer->remarks = 'Verifikasi akun Anda sedang diproses. Silakan tunggu beberapa saat.';
        }
        $customer->modified_by = Auth::user()->name;
        $customer->save();

        $customerShipment = CustomerShipment::where('customer_id', $custId)->first();
        if (!$customerShipment) {
            return $this->sendError('Customer shipment not found', 404);
        }

        $customerShipment->name = $request->owner_name;
        $customerShipment->address = $request->shipment_address;
        $customerShipment->province = $request->shipment_province;
        $customerShipment->province_code = $request->shipment_province_code;
        $customerShipment->city = $request->shipment_city;
        $customerShipment->city_code = $request->shipment_city_code;
        $customerShipment->district = $request->shipment_district;
        $customerShipment->district_code = $request->shipment_district_code;
        // $customerShipment->subdistrict = $request->shipment_subdistrict;
        $customerShipment->zip_code = $request->shipment_zip_code;

        $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->district . '%')->first();
        if (!$zone_code) {
            $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->city . '%')->first();
        }
        $customerShipment->zone_code = $zone_code->zone_code ?? null;

        $customerShipment->phone_number = $request->phone_number;
        $customerShipment->modified_by = Auth::user()->name;
        $customerShipment->save();

        $m = ['user' => $user, 'customer' => $customer, 'customer_shipment' => $customerShipment];
        return $this->sendSuccess('Profile updated Successfully', $m);
    }

    public function addShipmentAddress($request)
    {
        $user = $request->user();
        if ($user->reference_object == 'customer') {
            $custId = auth()->user()->customer->customer_id;
        } else if ($user->reference_object == 'sales') {
            $custId = $request->input('customer_id');
        }
        ;

        $customer = Customer::where('customer_id', $custId)->first();
        if (!$customer) {
            return $this->sendError('Customer not found', 404);
        }

        $customerShipmentCount = CustomerShipment::where('customer_id', $custId)->count();
        if ($customerShipmentCount >= 2) {
            return $this->sendError('Customers already have 2 shipping addresses', 400);
        }

        $customerShipment = new CustomerShipment();
        $customerShipment->customer_id = $custId;
        $customerShipment->name = $customer->owner_name;
        $customerShipment->address = $request->shipment_address;
        $customerShipment->country = 'Indonesia';
        $customerShipment->country_code = 'ID';
        $customerShipment->province = $request->shipment_province;
        $customerShipment->province_code = $request->shipment_province_code;
        $customerShipment->city = $request->shipment_city;
        $customerShipment->city_code = $request->shipment_city_code;
        $customerShipment->district = $request->shipment_district;
        $customerShipment->district_code = $request->shipment_district_code;
        $customerShipment->subdistrict = $request->shipment_subdistrict ?? '';
        // $customerShipment->subdistrict_code = $request->shipment_subdistrict_code ?? '';
        $customerShipment->zip_code = $request->shipment_zip_code;

        $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->district . '%')->first();
        if (!$zone_code) {
            $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->city . '%')->first();
        }
        $customerShipment->zone_code = $zone_code->zone_code ?? null;

        $customerShipment->phone_number = $customer->phone_number;
        $customerShipment->is_primary = false;
        $customerShipment->is_selected = false;
        $customerShipment->created_by = Auth::user()->name;
        $customerShipment->modified_by = Auth::user()->name;
        $customerShipment->save();

        return $this->sendSuccess('Create shipment address succesfully', [
            'customer_shipment' => $customerShipment
        ]);
    }

    public function editShipmentAddress($id, $request)
    {
        $user = $request->user();
        if ($user->reference_object == 'customer') {
            $custId = auth()->user()->customer->customer_id;
        } else if ($user->reference_object == 'sales') {
            $custId = $request->input('customer_id');
        }
        ;

        $customer = Customer::where('customer_id', $custId)->first();
        if (!$customer) {
            return $this->sendError('Customer not found', 404);
        }

        $customerShipment = CustomerShipment::find($id);
        if (!$customerShipment) {
            return $this->sendError('Customer shipment not found', 404);
        }

        $customerShipment->address = $request->shipment_address;
        $customerShipment->province = $request->shipment_province;
        $customerShipment->province_code = $request->shipment_province_code;
        $customerShipment->city = $request->shipment_city;
        $customerShipment->city_code = $request->shipment_city_code;
        $customerShipment->district = $request->shipment_district;
        $customerShipment->district_code = $request->shipment_district_code;
        $customerShipment->subdistrict = $request->shipment_subdistrict ?? '';
        // $customerShipment->subdistrict_code = $request->shipment_subdistrict_code ?? '';
        $customerShipment->zip_code = $request->shipment_zip_code;

        $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->district . '%')->first();
        if (!$zone_code) {
            $zone_code = TransportationZone::where('description', 'LIKE', '%' . $customerShipment->city . '%')->first();
        }
        $customerShipment->zone_code = $zone_code->zone_code ?? null;

        $customerShipment->modified_by = Auth::user()->name;
        $customerShipment->update();

        if ($customerShipment->is_primary) {
            $customer->address = $request->shipment_address;
            $customer->modified_by = Auth::user()->name;
            $customer->update();
        }

        $m = ['customer' => $customer, 'customer_shipment' => $customerShipment];
        return $this->sendSuccess('Update shipment address successfully', $m);
    }

    public function changeShipmentAddress($id, $request)
    {
        $user = $request->user();
        if ($user->reference_object == 'customer') {
            $custId = auth()->user()->customer->customer_id;
        } else if ($user->reference_object == 'sales') {
            $custId = $request->input('customer_id');
        }
        ;

        $customerShipment = CustomerShipment::find($id);
        if (!$customerShipment) {
            return $this->sendError('Customer shipment not found', 404);
        }

        CustomerShipment::where('customer_id', $custId)->update(['is_selected' => false]);

        $customerShipment->is_selected = true;
        $customerShipment->update();

        return $this->sendSuccess('Change shipment address succesfully', [
            'customer_shipment' => $customerShipment
        ]);
    }

    public function deleteShipmentAddress($id, $request)
    {
        $user = $request->user();
        if ($user->reference_object == 'customer') {
            $custId = auth()->user()->customer->customer_id;
        } else if ($user->reference_object == 'sales') {
            $custId = $request->input('customer_id');
        }
        ;

        $customerShipment = CustomerShipment::find($id);

        if (!$customerShipment) {
            return $this->sendError('Customer shipment not found', 404);
        }

        $shipmentCount = CustomerShipment::where('customer_id', $custId)->count();
        if ($shipmentCount <= 1) {
            return $this->sendError('Cannot delete the only shipment address', 400);
        }

        $otherShipment = CustomerShipment::where('customer_id', $custId)
            ->where('customer_shipment_id', '!=', $id)
            ->first();

        if ($otherShipment) {
            if ($customerShipment->is_primary) {
                $otherShipment->is_primary = true;

                $customer = Customer::where('customer_id', $custId)->first();
                if (!$customer) {
                    return $this->sendError('Customer not found', 404);
                }

                $customer->address = $otherShipment->address;
                $customer->modified_by = Auth::user()->name;
                $customer->update();
            }

            if ($customerShipment->is_selected) {
                $otherShipment->is_selected = true;
            }
            $otherShipment->update();
        }

        $customerShipment->delete();

        return $this->sendSuccess('Delete shipment address successfully');
    }
}