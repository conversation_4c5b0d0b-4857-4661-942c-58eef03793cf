<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class DecimalPlaces implements Rule
{
    protected $places;

    public function __construct($places)
    {
        $this->places = $places;
    }

    public function passes($attribute, $value)
    {
        // Use regular expression to validate decimal places
        $pattern = "/^\d+(\.\d{1," . $this->places . "})?$/";
        return preg_match($pattern, $value);
    }

    public function message()
    {
        return "The :attribute must have a maximum of {$this->places} decimal places.";
    }
}