<?php

namespace App\Rules;

use App\Models\OrderReseller;
use App\Models\ResellerToken;
use Illuminate\Support\Facades\Auth;
use Illuminate\Contracts\Validation\Rule;

class OrderAuthorization implements Rule
{
    protected $errorMessage1 = 'Order is not exist.';
    protected $errorMessage2 = 'You dont have access for this order';
    public $errorMessage = '';
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $order = OrderReseller::where('order_no', $value)->first();
        $user = Auth::guard('sanctum')->user();
        $customer_id = !$user ? ResellerToken::customerID(request()->header('X-AUTH-DEVICE')) : $user->reference_id;

        if (!$order) {
            $this->errorMessage = $this->errorMessage1;
            return false;
        } elseif ($order->customer_id != $customer_id) {
            $this->errorMessage = $this->errorMessage2;
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->errorMessage;
    }
}
