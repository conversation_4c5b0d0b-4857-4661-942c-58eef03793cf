<?php

namespace App\Services;

use App\Services\LoggerIntegration;
use Illuminate\Support\Facades\Http;

class CareOmni 
{
    public function create_order($payload)
    {
        $care_omni_order = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'x-server-key' => env('RSL_CAREOMNI_ORDER_KEY')
        ])->post(env('RSL_CAREOMNI_ORDER_URL').'/api/server/reseller/orders', $payload);

        return $care_omni_order;
    }

    public function use_voucher($payload)
    {
        $req = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'x-server-key' => env('RSL_VOUCHER_KEY')
        ])->post(env('RSL_VOUCHER_URL').'/api/server/v1/vouchers/use-voucher', $payload);

        if ($req->failed()) {
            $status = 'failed';
        } else {
            $status = 'success';
        }

        (new LoggerIntegration())->InsertLogger([
            'reference_no' => $payload['order_number'],
            'module' => 'Care Omni',
            'name' => 'Use Voucher',
            'type' => 'Outbound',
            'status' => $status,
            'description' => [
                'payload' => $payload,
                'response' => $req->json()
            ]
        ]);

        return $req;
    }

    public function cancel_order($ext_order_id)
    {
        $req = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'x-server-key' => env('RSL_CAREOMNI_ORDER_KEY'),
          ])
            ->post(env('RSL_CAREOMNI_ORDER_URL') . '/api/server/orders/' . $ext_order_id . '/cancel', [
              'apply_cancel_to' => 'order',
              'cancel_reason' => 'failed payment link from xendit'
            ]);

        if ($req->failed()) {
            $status = 'failed';
        } else {
            $status = 'success';
        }

        (new LoggerIntegration())->InsertLogger([
            'reference_no' => $ext_order_id,
            'module' => 'Care Omni',
            'name' => 'Cancel Order',
            'type' => 'Outbound',
            'status' => $status,
            'description' => [
                'payload' => [
                    'apply_cancel_to' => 'order',
                    'cancel_reason' => 'failed payment link from xendit'
                  ],
                'response' => $req->json()
            ]
        ]);

        return $req;
    }
}
