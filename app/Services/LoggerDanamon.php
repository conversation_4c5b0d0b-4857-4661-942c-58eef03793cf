<?php

namespace App\Services;
use App\Models\LogDanamon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LoggerDanamon {
    public static function log($request){
        try{
            Log::info($request,array('req insert danamon log'));
            LogDanamon::create($request);
            
        }catch (\Exception $e){
            Log::info('error '.$e->getMessage());
        }
    }
}
