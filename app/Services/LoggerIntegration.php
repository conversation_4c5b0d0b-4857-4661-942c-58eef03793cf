<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class LoggerIntegration {
    public function InsertLogger($request){
        try{
            Log::info($request,array('req insert log'));
            DB::beginTransaction();
            DB::table('log_integration')
                ->insert([
                    'reference_no'=> $request['reference_no'],
                    'module' => $request['module'],
                    'name' => $request['name'],
                    'type'=> $request['type'],
                    'status' => $request['status'],
                    'description' => json_encode(($request['description']))
                ]);
            DB::commit();
        }catch (\Exception $e){
            DB::rollBack();
            Log::info('error '.$e->getMessage());
        }
    }
}
