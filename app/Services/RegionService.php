<?php
namespace App\Services;
use Illuminate\Http\Request;
use App\Repositories\RegionRepository;

class RegionService
{

    protected $regionRepository;

    public function __construct(RegionRepository $regionRepository) 
    {
        $this->regionRepository = $regionRepository;
    }

    public function getRegions($page, $perPage, $search)
    {
        $result = $this->regionRepository->getRegionsNew($page, $perPage, $search);

        return $result;
    }

    public function getRegionCity($page, $perPage, $search, $regionCode)
    {
        $result = $this->regionRepository->getRegionCityNew($page, $perPage, $search, $regionCode);

        return $result;
    }

    public function getRegionDistrict($page, $perPage, $search, $cityCode)
    {
        $result = $this->regionRepository->getRegionDistrictNew($page, $perPage, $search, $cityCode);

        return $result;
    }

    public function getRegionSubdistrict($page, $perPage, $search, $districtCode)
    {
        $result = $this->regionRepository->getRegionSubdistrictNew($page, $perPage, $search, $districtCode);

        return $result;
    }

    public function getRegionPostalCode($page, $perPage, $search, $subDistrictCode)
    {
        $result = $this->regionRepository->getRegionPostalCode($page, $perPage, $search, $subDistrictCode);

        return $result;
    }
}

?>