<?php

namespace App\Services\Reseller;

use App\Traits\RequestService;

use function config;

class ProductService
{
    use RequestService;

    /**
     * @var string
     */
    protected $baseUri;

    public function __construct()
    {
        $this->baseUri = config('reseller.products.url');
    }

    /**
     * @return string
     */
    public function fetchProducts($data)
    {
        return $this->request('GET', '', $data);
    }

    /**
     * @param $product
     *
     * @return string
     */
    public function fetchProduct($product)
    {
        return $this->request('GET', "/api/product/{$product}");
    }

    /**
     * @param $data
     *
     * @return string
     */
    public function createProduct($data)
    {
        return $this->request('POST', '/api/product', $data);
    }

    /**
     * @param $product
     * @param $data
     *
     * @return string
     */
    public function updateProduct($product, $data)
    {
        return $this->request('PATCH', "/api/product/{$product}", $data);
    }

    /**
     * @param $product
     *
     * @return string
     */
    public function deleteProduct($product)
    {
        return $this->request('DELETE', "/api/product/{$product}");
    }
}
