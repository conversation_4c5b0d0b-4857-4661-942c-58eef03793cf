<?php
namespace App\Services;
use App\Repositories\ResellerRepository;

class ResellerService
{

    protected $resellerRepository;

    public function __construct(ResellerRepository $resellerRepository) 
    {
        $this->resellerRepository = $resellerRepository;
    }

    public function getResellerDashboardData()
    {
        $result = $this->resellerRepository->getResellerDashboardData();
        return $result;
    }
    public function getResellerSummaryDashboardData($start, $to)
    {
        $result = $this->resellerRepository->getResellerSummaryDashboardData($start, $to);
        return $result;
    }
    public function getResellerCommissionsDashboardData($start, $to)
    {
        $result = $this->resellerRepository->getResellerCommissionsDashboardData($start, $to);
        return $result;
    }
    public function getTopResellers()
    {
        $result = $this->resellerRepository->getTopResellers();
        return $result;
    }
    public function getTopCommissions()
    {
        $result = $this->resellerRepository->getTopCommissions();
        return $result;
    }
    public function getTopSellingItems($id)
    {
        $result = $this->resellerRepository->getTopSellingItems($id);
        return $result;
    }
    public function getTopSellingItemsAll()
    {
        $result = $this->resellerRepository->getTopSellingItemsAll();
        return $result;
    }
    public function getResellerChartData($data,$id)
    {
        $result = $this->resellerRepository->getResellerChartData($data,$id);
        return $result;
    }

    public function getResellerChartDataAll($data, $start, $to)
    {
        $result = $this->resellerRepository->getResellerChartDataAll($data, $start, $to);
        return $result;
    }

    public function getResellerChartDataAllDownload($start, $to)
    {
        $result = $this->resellerRepository->getResellerChartDataAllDownload($start, $to);
        return $result;
    }

    public function getSalesReport($resellerId)
    {
        $result = $this->resellerRepository->getSalesReport($resellerId);
        return $result;
    }
}

?>