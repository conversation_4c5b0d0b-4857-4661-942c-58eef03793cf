<?php

namespace App\Services;

use Carbon\Carbon;
use App\Models\Order;
use App\Models\Product;
use App\Models\Customer;
use App\Models\OrderItem;
use App\Models\CreditLimit;
use App\Models\OrderCustom;
use Illuminate\Http\Request;
use App\Models\OrderApproval;
use App\Models\MasterParameter;
use App\Models\CustomerShipment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Models\OrderCustomAttachment;
use App\Repositories\CreateSimulateSORepo;
use App\Services\LoggerIntegration;

class SAP 
{
    public function salesOrder($type, $order_no, $is_custom = false)
    {
        $str_type = [
            'i' => 'Create',
            'd' => 'Delete',
            'u' => 'Update',
            's' => 'Simulate'
        ];

        try {
            if (!$is_custom) {
                $od = DB::table('order_detail')
                        ->leftJoin('order_header as oh','order_detail.order_no','=','oh.order_no')
                        ->leftJoin('article','order_detail.article_id', '=', 'article.article')
                        ->leftJoin('customers','oh.customer_id','=','customers.customer_id')
                        ->leftJoin('customer_shipment','oh.customer_shipment_id','=','customer_shipment.customer_shipment_id')
                        ->leftJoin('sales','oh.sales_id','=','sales.sales_id')
                        ->where('oh.order_no',$order_no)
                        ->orderBy('order_detail.created_date','desc')
                        ->select(['sales.sap_username','order_detail.article_id','order_detail.qty','order_detail.issued_qty',
                                'customer_shipment.customer_shipment_id', 'article.sku_code_c',
                                'customers.distribution_channel as c_distribution_channel','customers.customer_id',
                                'customers.status as customer_status', 'oh.dp_percentage','oh.dp_amount',
                                'customers.registered_sap_at', 'oh.sales_order_no', 'oh.dp_due_date', 'customers.sap_id'])
                        // ->select(['order_custom.*'])
                        ->get();
            } else {
                $od = DB::table('order_custom')
                        ->leftJoin('order_header as oh','order_custom.reference_id','=','oh.order_no')
                        ->leftJoin('article','order_custom.article_id', '=', 'article.article')
                        ->leftJoin('customers','oh.customer_id','=','customers.customer_id')
                        ->leftJoin('customer_shipment','oh.customer_shipment_id','=','customer_shipment.customer_shipment_id')
                        ->leftJoin('sales','oh.sales_id','=','sales.sales_id')
                        ->where('oh.order_no',$order_no)
                        ->orderBy('order_custom.created_date','desc')
                        ->select(['sales.sap_username','order_custom.article_id','order_custom.qty','order_custom.attachment_group_id',
                                'customer_shipment.customer_shipment_id', 'article.sku_code_c',
                                'customers.distribution_channel as c_distribution_channel','customers.customer_id',
                                'customers.status as customer_status', 'oh.dp_percentage','oh.dp_amount',
                                'customers.registered_sap_at', 'oh.sales_order_no', 'oh.dp_due_date', 'customers.sap_id'])
                        ->get();
            }

            $oh = Order::where('order_no', $order_no)->first();

            // $oc_exs = OrderCustom::where('reference_name','order_header')
            //             ->where('reference_id',$order_no)
            //             ->exists();

            // $sap_username = $od[0]->sap_username??env('ARTICLE_STOCK_SAPUSER');
            $sap_username = env('ARTICLE_STOCK_SAPUSER');
            
            if ($od[0]->c_distribution_channel == 'WHOLESALES') {
                $destination = MasterParameter::where('group_key','CREATE_SO')->where('key',$od[0]->c_distribution_channel)->first();
            } elseif ($od[0]->c_distribution_channel == 'B2B') {
                $key = $od[0]->registered_sap_at != null ? 'B2B_EXISTING' : 'B2B_NEW';
                // $key = 'B2B_NEW';
                // $key2 = $oc_exs == true ? $key.'_C' : $key;
                $key2 = !$is_custom ? $key.'_C' : $key;
                $destination = MasterParameter::where('group_key','CREATE_SO')->where('key',$key2)->first();
            }

            $distrchan = MasterParameter::where('group_key','CHANNEL_CODE')->where('value',$od[0]->c_distribution_channel)->first();

            $dummy_customer_id = MasterParameter::where('group_key','B2B_NEW_CUSTOMER')->where('key','CUSTOMER_ID')->first();

            $custId = $od[0]->registered_sap_at != null ? $od[0]->sap_id : $dummy_customer_id->value;
            // $custId = $dummy_customer_id->value;
            //Log::channel('stderr')->info('[ITEMS REQUEST KE SAP]',$item);
            $soDetail = [];
            
            $date = Carbon::now();
            $yymmdd = $date->format('Ymd');
            $no = 1;
            foreach ($od as $item) {
                if ($is_custom) {
                    $ocs = OrderCustom::where('reference_id', $order_no)
                        ->where('sku', $item->sku_code_c)
                        ->where('attachment_group_id', $item->attachment_group_id)
                        ->pluck('attachment_group_id')->toArray();

                    // $ocs = OrderCustom::where('reference_name','order_header')
                    //     ->where('reference_id',$order_no)
                    //     ->where('sku', $item->sku_code_c)
                    //     ->pluck('id')->toArray();

                    $custom_price = OrderCustomAttachment::where('order_custom_id', $ocs)->sum('custom_price');

                    // $custom_price = OrderCustomAttachment::whereIn('order_custom_id',$ocs)
                    //     ->sum('custom_price');

                    $so = [
                        'destination' => $destination->value,
                        'flag' => $type,
                        'itmnumber' => sprintf("%d0", $no),
                        'article' => $item->article_id,
                        // 'targetqty'=> STRVAL($item->issued_qty && $item->issued_qty > 0 ? $item->issued_qty : $item->qty),
                        'targetqty'=> STRVAL($item->qty),
                        'uom'=>'PC',
                        'currency' => 'IDR',
                        'price' => (string)($custom_price*$item->qty)
                    ];
                } else {
                    $so = [
                        'destination' => $destination->value,
                        'flag' => $type,
                        'itmnumber' => sprintf("%02d", $no),
                        'article' => $item->article_id,
                        'targetqty'=> STRVAL($item->issued_qty && $item->issued_qty > 0 ?$item->issued_qty : $item->qty),
                        'uom'=>'PC',
                    ];
                }
                array_push($soDetail,$so);
                $no = $no+1;
            }
            // dd($customer_shipment_id ? str_pad($customer_shipment_id, 10, '0', STR_PAD_LEFT) : strval($custId));die();
            $soDetailSet=$soDetail;
            $soPartnerSet = ($type == "i" && $custId != $od[0]->customer_shipment_id && $custId != $dummy_customer_id->value) ? [
                [
                    'destination' => $destination->value,
                    'partnrole' => 'SH',
                    'partnnumb'=> strval($custId),
                    'customer_id' => strval($od[0]->customer_id)
                ]
                // [
                // 'destination' => $destination->value,
                // 'partnrole' => 'SH',
                // 'partnnumb'=> $od[0]->customer_shipment_id,
                // ],
            ] : 
            [
                [
                    'destination' => $destination->value,
                    'partnrole' => 'SH',
                    'partnnumb'=> strval($custId),
                    'customer_id' => strval($od[0]->customer_id)
                ]
            ];
            $soReturnSet= [];

            if ($od[0]->c_distribution_channel == 'WHOLESALES') {
                $data = [
                    "source"=> "CAREOM",
                    "destination" => $destination->value,
                    "flag"=> $type,
                    "doctype"=> "ZESD",
                    "salesorg"=> "1000",
                    "distrchan"=> $distrchan->key,
                    "division"=> "00",
                    "reqdate"=> $yymmdd,
                    "externalno" => $order_no,
                    "salesorder"=> $od[0]->sales_order_no??"",
                    "dpvalue"=> $od[0]->dp_percentage != 0 || $od[0]->dp_percentage != null ? (string)$od[0]->dp_amount : "0",
                    "currency"=> "IDR",
                    "soDetailSet"=>$soDetailSet,
                    "soPartnerSet"=>$soPartnerSet,
                    "soReturnSet" =>$soReturnSet,
                    "sap_username" =>$sap_username,
                ];
            } elseif ($od[0]->c_distribution_channel == 'B2B') {
                if ($is_custom) {
                    $data = [
                        "source"=> "CAREOM",
                        "destination" => $destination->value,
                        "flag"=> $type,
                        "doctype"=> "ZESD",
                        "salesorg"=> "1000",
                        "distrchan"=> $distrchan->key,
                        "division"=> "00",
                        "reqdate"=> $yymmdd,
                        "externalno" => $order_no,
                        "salesorder"=> $od[0]->sales_order_no??"",
                        "dpvalue"=> $od[0]->dp_percentage != 0 || $od[0]->dp_percentage != null ? (string)$od[0]->dp_amount : "0",
                        "dpduedate"=> Carbon::parse($od[0]->dp_due_date)->format('Ymd'),
                        "currency"=> "IDR",
                        "soDetailSet"=>$soDetailSet,
                        "soPartnerSet"=>$soPartnerSet,
                        "soReturnSet" =>$soReturnSet,
                        "sap_username" =>$sap_username,
                    ];
                } else {
                    $data = [
                        "source"=> "CAREOM",
                        "destination" => $destination->value,
                        "flag"=> $type,
                        "doctype"=> "ZESD",
                        "salesorg"=> "1000",
                        "distrchan"=> $distrchan->key,
                        "division"=> "00",
                        "reqdate"=> $yymmdd,
                        "externalno" => $order_no,
                        "salesorder"=> $od[0]->sales_order_no??"",
                        "dpvalue"=> $od[0]->dp_percentage != 0 || $od[0]->dp_percentage != null ? (string)$od[0]->dp_amount : "0",
                        "currency"=> "IDR",
                        "soDetailSet"=>$soDetailSet,
                        "soPartnerSet"=>$soPartnerSet,
                        "soReturnSet" =>$soReturnSet,
                        "sap_username" =>$sap_username,
                    ];
                }
            }
            
            $sap_so = Http::withHeaders([
                'sapuser' => $sap_username,
                'Accept' => 'application/json'
            ])->withBasicAuth(env('API_USERNAME'),env('API_PASSWORD'))
            ->withOptions([
                'json' => $data
            ])
            ->post(env('API_EIGER_URL').'/api/care-om/orders');

            $res_sap = $sap_so->json();

            if ($sap_so->failed() or $res_sap == null or $res_sap['message'][0]['type'] == 'E') {
                $status = 'failed';
            } else {
                $status = 'success';
            }

            $logs = [
                'reference_no'=> $order_no,
                'module' => 'SAP',
                'name' => $str_type[$type].' Sales Order SAP',
                'type'=> 'Outbound',
                'status' => $status,
                'description' => [
                    'payload' => $data,
                    'response' => $res_sap
                ]
            ];

            (new LoggerIntegration())->InsertLogger($logs);

            // return $result;

            return $sap_so;
        } catch (\Exception $e) {

            $logs = [
                'reference_no'=> $order_no,
                'module' => 'SAP',
                'name' => $str_type[$type].' Sales Order SAP',
                'type'=> 'Outbound',
                'status' => 'failed',
                'description' => [
                    'type' => 'error care-om',
                    'error' => $e->getMessage()
                ]
            ];

            (new LoggerIntegration())->InsertLogger($logs);

            return [
                'error' => $e->getMessage()
            ];
        }
    }

    public function createCustomer(Customer $customer, $type, OrderApproval $order_approval, Order $order)
    {
        try {
            $customer_shipment = CustomerShipment::where('customer_shipment_id',$order->customer_shipment_id)->first();
            
            $db_customer = DB::table('customers')->where('customer_id',$customer->customer_id)->first();

            if ($db_customer->registered_sap_at != null) {
                $credit_limit = CreditLimit::where('customer_external_id',$db_customer->customer_id)->first();
                if ($credit_limit == null) {
                    $plafond = $this->plafond($db_customer);

                    if (isset($plafond['error'])) {
                        $credit_limit = 0;
                    } else {
                        $plafond_res = $plafond->json();
                        $credit_limit = $plafond_res['data'][0]['plafond'];
                    }
                } else {
                    $credit_limit = @$credit_limit->credit_limit;
                }
            } else {
                $credit_limit = CreditLimit::where('customer_external_id',$db_customer->id)->first();
                $credit_limit = @$credit_limit->credit_limit??0;
            }

            // $extcustcode = MasterParameter::where('group_key','CHANNEL_CODE')->where('value',$customer->distribution_channel)->first();

            $category = [];
            $data = [];
            $ocs_exs = OrderCustom::where('reference_id',$order->order_no)->exists();
            if ($ocs_exs) {
                $order_detail = $order->custom;
            } else {
                $order_detail = $order->items;
            }

            foreach($order_detail as $item)
            {
                if ($ocs_exs) {
                    $article = Product::where('article',$item->article_id)->first();
                    $ocs = OrderCustom::where('reference_id', $order->order_no)
                            ->where('sku',$article->sku_code_c)
                            ->where('attachment_group_id', $item->attachment_group_id)
                            ->pluck('attachment_group_id')->toArray();

                    // $ocs = OrderCustom::where('reference_id',$order->order_no)
                    //         ->where('sku',$article->sku_code_c)->pluck('id')->toArray();

                    $custom_price = OrderCustomAttachment::where('order_custom_id', $ocs)->sum('custom_price');

                    // $custom_price = OrderCustomAttachment::whereIn('order_custom_id',$ocs)->sum('custom_price');
                    
                    $data[] = [
                        'article' => $item->article_id,
                        'qty' => $item->qty,
                        'custom_price' => $custom_price ?? 0,
                    ];
                }
                
                $category[] = $item->product->lvl3_description == 'BAGS' ? 'BAGS' : 'NON BAGS';
            }

            $datas = [
                'is_custom' => (int)$ocs_exs,
                'data' => $data
            ];

            $u_category = array_unique($category,SORT_REGULAR);
            // $discount = $order->total_discount/($order->total/100);

            $request = [
                [
                    'requeststatus' => 'Draft',
                    'requesttype' => $type,
                    'requestdate' => Carbon::parse($order_approval->action_date)->format('Ymd'),
                    'requester' => $customer->sap_username,
                    'top' => $customer->top_days == '0' ? 'Cash' : $customer->top_days,
                    'topaft' => $type == 'Update' ? $customer->top_days : null,
                    // 'marginbags' => $u_category[0] == 'BAGS' ? ((float)$discount == 0 ? 0 : (float)$discount) : 5,
                    'marginbags' => $u_category[0] == 'BAGS' ? ($customer->discount_percent == 0 ? 0 : $customer->discount_percent) : 5,
                    // 'marginbagsaft' => $type == 'Update' ? ($u_category[0] == 'BAGS' ? ((float)$discount == 0 ? 0 : (float)$discount) : 5) : 5,
                    'marginbagsaft' => $type == 'Update' ? ($u_category[0] == 'BAGS' ? ($customer->discount_percent == 0 ? 0 : $customer->discount_percent) : 5) : 5,
                    // 'marginnbag' => $u_category[0] == 'NON BAGS' ? ((float)$discount == 0 ? 0 : (float)$discount) : 5,
                    'marginnbag' => $u_category[0] == 'NON BAGS' ? ($customer->discount_percent == 0 ? 0 : $customer->discount_percent ) : 5,
                    // 'marginnbagaft' => $type == 'Update' ? ($u_category[0] == 'NON BAGS' ?((float)$discount == 0 ? 0 : (float)$discount) : 5) : 5,
                    'marginnbagaft' => $type == 'Update' ? ($u_category[0] == 'NON BAGS' ?($customer->discount_percent == 0 ? 0 : $customer->discount_percent) : 5) : 5,
                    'creditlimit' => $db_customer->registered_sap_at != null ? $credit_limit : (int)$order->total + 15000000,
                    'creditlimitaft' => $db_customer->registered_sap_at != null ? (int)$order->total + 15000000 : null,
                    'currency' => 'IDR',
                    'salesname' => $order->sales_name,
                    'brand' => 'EIGER',
                    'kunnr' => $db_customer->registered_sap_at != null ? $customer->sap_id : "",
                    // 'customernumber' => $db_customer->registered_sap_at != null ? $customer->customer_id : "",
                    'name1' => substr($customer->owner_name,0,40),
                    'name2' => substr($customer->owner_name,40,40),
                    'name3' => substr($customer->owner_name,80,40),
                    'name4' => substr($customer->owner_name,120,40),
                    'name1vat' => substr($customer->npwp_name,0,40),
                    'name2vat' => substr($customer->npwp_name,40,40),
                    'name3vat' => substr($customer->npwp_name,80,40),
                    'name4vat' => substr($customer->npwp_name,120,40),
                    'taxtype' => $customer->tax_type,
                    'npwp' => $customer->npwp,
                    'nppkp' => "",
                    'ppkpdate' => "",
                    'street' => substr($customer->address,0,40),
                    'strsuppl1' => substr($customer->address,40,40),
                    'strsuppl2' => substr($customer->address,80,40),
                    'strsuppl3' => substr($customer->address,120,40),
                    'location' => substr($customer->address,160,40),
                    'country' => $customer_shipment->country_code??'ID',
                    'region' => $customer_shipment->province_code??'',
                    'city1' => $customer_shipment->city,
                    'postcode1' => $customer_shipment->zip_code,
                    'transpzone' => $order->location_code,
                    'timezone' => 'UTC+7',
                    'telnumber' => "",
                    'mobnumber' => $customer->phone_number,
                    'smtpaddr' => $customer->email,
                    'streetvat' => substr($customer->npwp_address,0,40),
                    'strsuppl1vat' => substr($customer->npwp_address,40,40),
                    'zterm' => $customer->top,
                    'issuekey' => "",
                    'extordcare' => $order->order_no,
                    'extcustcode' => $customer->id,
                    'imgurlktp' => @$customer->national_id_file != null || $customer->national_id_file != '' ? env('S3_FILE_URL').substr($customer->national_id_file,1) : null,
                    'imgurlnpwp' => @$customer->npwp_file != null || $customer->npwp_file != '' ? env('S3_FILE_URL').substr($customer->npwp_file,1) : null,
                ]
            ];

            $ccustomer_sap = Http::withBasicAuth(env('API_USERNAME'), env('API_PASSWORD'))
                ->post(env('API_EIGER_URL').'/api/care-om/customers', $request);

            // $ccustomer_sap = Http::withHeaders([
            //     'sapuser' => env('eg_taryono'),
            //     'Accept' => 'application/json'
            // ])->withBasicAuth(env('API_USERNAME'), env('API_PASSWORD'))
            // ->post(env('API_EIGER_URL').'/api/care-om/customers', $request);
            
            $res_sap = $ccustomer_sap->json();

            if ($ccustomer_sap->failed() || isset($res_sap['error']) || (isset($res_sap['success']) && $res_sap['success'] == 'false') || (isset($res_sap['d']['requeststatus']) && $res_sap['d']['requeststatus'] != 'OK')) {
                $status = 'failed';
            } else {
                $status = 'success';
            }
            
            $logs = [
                'reference_no'=> $order->order_no,
                'module' => 'SAP',
                'name' => 'Create Customer to SAP',
                'type'=> 'Outbound',
                'status' => $status,
                'description' => [
                    'payload' => $request,
                    'response' => $res_sap
                ]
            ];

            (new LoggerIntegration())->InsertLogger($logs);

            return $ccustomer_sap;
        } catch (\Exception $e) {
            
            $logs = [
                'reference_no'=> null,
                'module' => 'SAP',
                'name' => 'Create Customer to SAP',
                'type'=> 'Outbound',
                'status' => 'failed',
                'description' => [
                    'type' => $type,
                    'customers' => $customer,
                    'error' => $e->getMessage()
                ]
            ];

            (new LoggerIntegration())->InsertLogger($logs);

            return [
                'error' => $e->getMessage()
            ];
        }
    }
    
    public function simulateDiscountB2B($datas){
        try {
            $bruto = 0;
            $qty = 0;
            foreach($datas['data'] as $data)
            {
                $article = Product::where('article',$data['article'])->first();
                if($article != null)
                {

                    $category = $article->lvl3_description == 'BAGS' ? 'BAGS' : 'NON BAGS';
                    $bruto += ($article->price->amount*$data['qty'])+($data['custom_price']??0);
                    $qty += $data['qty'];
                    
                } else {
                    $failedMessage[] = [
                        'Article' => $data['article'],
                        'Message' => 'Not Found'
                    ];
                }
            }

            $m_d = DB::table('matrix_discount')
                                ->where('is_custom',$datas['is_custom']??0)
                                ->whereRaw('? BETWEEN min_bruto_from AND min_bruto_to',[(int)$bruto])
                                ->whereRaw('? BETWEEN qty_from AND qty_to',[(int)$qty])
                                ->where('category',$category)
                                ->first();

            Log::info([
                'total_discount' => $m_d->discount*($bruto/100),
                'error' => @$failedMessage??null
            ]);

            return [
                'category' => $category,
                'discount' => $m_d->discount
            ];

        } catch (\Exception $e) {
            Log::info($e->getMessage());
            return false;
        }
    }

    public function plafond($customer){
        try {
            $data = [
                'source' => 'CAREOM',
                'destination' => 'PLF',
                'detailPlafond' => [[
                    'destination' => 'PLF',
                    'customer' => $customer->customer_id
                ]]
            ];
            $plafond = Http::withHeaders([
                'sapuser' => env('ARTICLE_STOCK_SAPUSER'),
                'Accept' => 'application/json'
            ])->withBasicAuth(env('API_USERNAME'),env('API_PASSWORD'))
            ->withOptions([
                'json' => $data
            ])
            ->post(env('API_EIGER_URL').'/api/care-om/plafond');
    
            $plafond_res = $plafond->json();
    
            if ($plafond->failed()) {
                $logs = [
                    'reference_no'=> $customer->customer_id,
                    'module' => 'SAP',
                    'name' => 'Integration Plafond',
                    'type'=> 'Outbound',
                    'status' => 'failed',
                    'description' => [
                        'payload' => $data,
                        'response' => $plafond_res
                    ]
                ];
                (new LoggerIntegration())->InsertLogger($logs);

                return ['error' => $plafond_res];
            }

            $logs = [
                'reference_no'=> $customer->customer_id,
                'module' => 'SAP',
                'name' => 'Integration Plafond',
                'type'=> 'Outbound',
                'status' => 'success',
                'description' => [
                    'payload' => $data,
                    'response' => $plafond_res
                ]
            ];
            (new LoggerIntegration())->InsertLogger($logs);

            return $plafond;
        } catch (\Exception $e) {
            $logs = [
                'reference_no'=> $customer->customer_id,
                'module' => 'SAP',
                'name' => 'Integration Plafond',
                'type'=> 'Outbound',
                'status' => 'failed',
                'description' => [
                    'customers' => $customer,
                    'error' => $e->getMessage()
                ]
            ];
            (new LoggerIntegration())->InsertLogger($logs);

            return ['error' => $e->getMessage()];
        }
    }
}
