<?php
namespace App\Services;
use Illuminate\Http\Request;
use App\Repositories\TransportationRepository;

class TransportationService
{

    protected $transportationRepository;

    public function __construct(TransportationRepository $transportationRepository) 
    {
        $this->transportationRepository = $transportationRepository;
    }

    public function getTransportations($request)
    {
        $page = (int)($request->input('page')??1);
        $perPage = (int)($request->input('per_page')??10);
        $search = $request->input('search')??'';

        $result = $this->transportationRepository->getTransportations($page, $perPage, $search);

        return $result;
    }
}

?>