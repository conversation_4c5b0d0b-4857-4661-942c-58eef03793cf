<?php

namespace App\Services;

use App\Services\LoggerIntegration;
use Illuminate\Support\Facades\Http;

class Xendit 
{
    public function invoices($payloadXendit)
    {
        $xendit_inv = Http::withBasicAuth(
            env("RSL_XENDIT_SECRET_KEY"),
            ''
        )
        // ->withHeaders([
        //     'for-user-id' => $xendit_acc->account_id
        // ])
        ->post('https://api.xendit.co/v2/invoices', $payloadXendit);

        return $xendit_inv;
    }
}
