<?php

namespace App\Traits;

use Illuminate\Http\Resources\Json\JsonResource;

class CustomJsonResource extends JsonResource
{
    public static function collection($resource)
    {
        return tap(new CustomMetadataCollection($resource, static::class), function ($collection) {
            if (property_exists(static::class, 'preserveKeys')) {
                $collection->preserveKeys = (new static([]))->preserveKeys === true;
            }
        });
    }
}