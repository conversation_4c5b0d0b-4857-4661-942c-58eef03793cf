<?php

namespace App\Traits;
use Illuminate\Http\Resources\Json\ResourceCollection;
class PaginationResource extends ResourceCollection
{
    private $customDataAttributes;
    private $jsonResource;
    public function __construct($resource, $jsonResource = null, $customDataAttributes = 'data')
    {
        parent::__construct($resource);
        $this->resource = $resource;
        $this->jsonResource = $jsonResource;
        $this->customDataAttributes = $customDataAttributes;

    }

    public function toArray($request)
    {
        return [
            'total_data' => $this->total()??0,
            'size' => intval($this->perPage())??0,
            'active_page' => $this->currentPage()??0,
            'total_page' => $this->lastPage()??0,
            $this->customDataAttributes => $this->jsonResource === null ? $this->collection : $this->jsonResource::collection(($this->collection))
        ];
    }

}
