<?php

namespace App\Traits;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Traits\PaginationResource;
trait ResponseAPI
{
   /*Send Success Modifikasi
        Enhance sendSuccess untuk ngerapihin bloated code di controller
        Atribut Tambahan :
        $legacyResponse = untuk handling code2 dari controller yang pakai fungsi ini yang belum dirapihkan
        $resourceClass = Parameter untuk apakah Response yang dipassing di transform atau engga
        $customAttribute = Parameter untuk mengganti attribute 'data' dengan yang lain 
   */
    public function sendSuccess($message, $data = [], string $status = null, $legacyResponse = true, $resourceClass = null, $customAttribute = 'data'): JsonResponse
    {   
        return response()->json([
                'error'   => false,
                'status'  => $status ?: '200 OK',
                'message' => $message,
                'data'    => $legacyResponse ? $data : new PaginationResource($data, $resourceClass, $customAttribute)
            ],
            200,
            [],
            JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
        );
    }

    public function sendSuccessWithCookies($message, $data = [], $cookies, string $status = null, $legacyResponse = true, $resourceClass = null, $customAttribute = 'data'): JsonResponse
    {   
        return response()->json([
                'error'   => false,
                'status'  => $status ?: '200 OK',
                'message' => $message,
                'data'    => $legacyResponse ? $data : new PaginationResource($data, $resourceClass, $customAttribute)
            ],
            200,
            [],
            JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
        )->withCookie($cookies);
    }

    public function sendExtraSuccess($message, $data = [], string $status = null, $extra = [], $withPagination = false, $resourceClass = null, $customAttribute = 'data'): JsonResponse
    {
        return response()->json([
                'error'   => false,
                'status'  => $status ?: '200 OK',
                'message' => $message,
                'data'    => $data,
                'extra'   => $extra
            ],
            200,
            [],
            JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
        );
    }

    /**
     * return error response.
     *
     * @param       $message
     * @param array $data
     * @param       $status
     * @param       $errorCode
     * @return \Illuminate\Http\Response
     */
    public function sendError($message, $errorCode = 500, $status = '', $data = []) : JsonResponse
    {
        return response()->json([
                'error'   => true,
                'status'  => $status ?: 'Something Wrong!',
                'message' => $message,
                'data'    => $data
            ],
            $errorCode ?: 500,
            [],
            JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
        );
    }

    public function sendException($message, $errorCode = 500, $status = '', Exception $e) : JsonResponse
    {
        return response()->json([
                'error'   => true,
                'status'  => $status ?: 'Exception Occured!',
                'message' => $message,
                'data'    => [
                    'code' => $e->getCode(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'message' => $e->getMessage(),

                ]
            ],
            $errorCode ?: 500,
            [],
            JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
        );
    }


     /**
     * success response method.
     *
     * @param       $message
     * @param array $data
     * @param       $status
     * @return \Illuminate\Http\Response
     */
    public function sendSuccessCreated($message, $data = [], string $status = null) : JsonResponse
    {
        return response()->json([
                'error'   => false,
                'status'  => $status ?: '201 OK',
                'message' => $message,
                'data'    => $data
            ],
            201,
            [],
            JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT
        );
    }

    public function sendDownloadResponse($path, $filename, $fileType){
        $headers = [
            'Content-Type: application/'.$fileType
        ];
        return response()->download($path,$filename,$headers);
    }
    
    public function pagedResponse($data, $currentPage, $perPage)
    {
        $totalElements = $data->total();
        $totalPages = ceil($totalElements / $perPage);
        $isLastPage = $currentPage == $totalPages;

        return [
            'totalElements' => (int)$totalElements,
            'totalPage' => (int)$totalPages,
            'isLastPage' => $isLastPage,
            'page' => (int)$currentPage,
            'size' => (int)$perPage,
            'data' => $data
        ];
    }

    public function webhookException(Exception $e, Request $request){
        //males di env takut lupa
    }
}
