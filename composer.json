{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "barryvdh/laravel-dompdf": "^2.0", "fruitcake/laravel-cors": "^3.0", "guzzlehttp/guzzle": "^7.0.1", "laravel/framework": "^8.75", "laravel/sanctum": "^2.15", "laravel/tinker": "^2.5", "league/flysystem-aws-s3-v3": "^1.0", "maatwebsite/excel": "3.1.31", "predis/predis": "^2.3", "pusher/pusher-php-server": "^7.2", "spatie/laravel-data": "^2.1", "spatie/simple-excel": "*", "vladimir-yuldashev/laravel-queue-rabbitmq": "11.3.0", "vlucas/valitron": "^1.4", "vxm/laravel-async": "^2.2", "webpatser/laravel-uuid": "^4.0"}, "require-dev": {"facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["laravel/telescope"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}