<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class CustomerFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $faker = $this->faker;
        return [
            'customer_id' => 1, //$faker->unique()->randomDigit(),
            'owner_name' => $faker->name,
            'email' => $faker->unique()->safeEmail,
            'is_verified' => 0,
            'phone_number' => $faker->phoneNumber,
            'distribution_channel' => 'NONE',
            'npwp' => $faker->numberBetween(123456789101112, 999999999999999),
            'npwp_city' => $faker->city(),
            'npwp_address' => $faker->address(),
            'top' => $faker->randomElement(['CASH', 'TEMPO']),
            'top_days' => $faker->randomNumber(3, false),
            'discount_percent' => '0.00',
            'customer_type' => 'Z1', //wholesales
            'is_active' => 0,
            'is_pending_payment' => 0,
            'created_date' => now(),
            'created_by' => NULL,
            'modified_date' => now(),
            'modified_by' => NULL,
        ];
    }

    /**
     * Indicate that the customer was b2b.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function b2b()
    {
        return $this->state(function (array $attributes) {
            return [
                'customer_type' => 'Z4',
            ];
        });
    }
}
