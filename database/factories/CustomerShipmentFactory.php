<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class CustomerShipmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $faker = $this->faker;
        return [
            'customer_shipment_id' => $faker->unique()->randomDigit(),
            'customer_id' => 1,
            'name' => $faker->name,
            'address' => $faker->address(),
            'city' => $faker->city(),
            'province' => $faker->city(),
            'district' => $faker->city(),
            'subdistrict' => $faker->streetName(),
            'zip_code' => $faker->postcode,
            'shipment_type' => 'NONE',
            'phone_number' => $faker->phoneNumber(),
        ];
    }
}
