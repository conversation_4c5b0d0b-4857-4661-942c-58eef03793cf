<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Product;

class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'sku' => $this->faker->word(),
            'external_product_id' => $this->faker->randomNumber(4, true),
            'product_name' => $this->faker->text(20),
            'product_variant' => $this->faker->text(20),
            'product_size' => $this->faker->words(2, true),
            'product_description' => $this->faker->text(20),
            'product_style' => $this->faker->text(20),
            'product_tag' => $this->faker->text(20),
            'product_category' => $this->faker->text(20),
            'product_gender' => $this->faker->text(20),
            'product_activity' => $this->faker->text(20),
            'sku_name' => $this->faker->text(20),
            'article_id' => $this->faker->text(20),
            'article_description' => $this->faker->text(20),
            'article_category' => $this->faker->text(20),
            'release_date' => $this->faker->date(),
            'expired_date' => null,
            'fash_grade' => $this->faker->text(20),
            'lvl2_description' => $this->faker->text(20),
            'lvl3_description' => $this->faker->text(20),
            'lvl4_description' => $this->faker->text(20),
            'season' => $this->faker->text(20),
            'weight' => $this->faker->text(20),
            'dimension' => $this->faker->text(20),
            'stock' => $this->faker->randomDigit(),
            'is_customizable' => 0,
            'selling_type' => $this->faker->text(20),
            'price' => $this->faker->randomFloat(2),
            'photo_url' => $this->faker->imageUrl(640, 480, 'bag', true),
            'created_date' => $this->faker->date(),
            'created_by' => $this->faker->name(),
            'modified_date' => $this->faker->date(),
            'modified_by' => null,
        ];
    }

    /**
     * Indicate that the product which article1.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function article1()
    {
        return $this->state(function (array $attributes) {
            return [
                'article_id' => '910000001001',
            ];
        });
    }

    /**
     * Indicate that the user is suspended.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function article2()
    {
        return $this->state(function (array $attributes) {
            return [
                'article_id' => '910000001002',
            ];
        });
    }

    /**
     * Indicate that the product which sku1.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function sku1()
    {
        return $this->state(function (array $attributes) {
            return [
                'sku' => '910000001',
            ];
        });
    }

    /**
     * Indicate that the product which sku1.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function sku2()
    {
        return $this->state(function (array $attributes) {
            return [
                'sku' => '910000008',
            ];
        });
    }
}
