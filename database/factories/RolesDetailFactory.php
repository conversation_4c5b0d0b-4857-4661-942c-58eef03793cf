<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class RolesDetailFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $faker = $this->faker;
        return [
            'created_date' => now(),
            'created_by' => NULL,
            'modified_date' => now(),
            'modified_by' => NULL,
        ];
    }
}
