<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

class RolesFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $faker = $this->faker;
        return [
            'is_active' => 1,
            'created_date' => now(),
            'created_by' => NULL,
            'modified_date' => now(),
            'modified_by' => NULL,
        ];
    }

    /**
     * Indicate that the roles is superadmin.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function superadmin()
    {
        return $this->state(function (array $attributes) {
            return [
                'roles_name' => 'superadmin',
            ];
        });
    }

    /**
     * Indicate that the roles is admin.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function admin()
    {
        return $this->state(function (array $attributes) {
            return [
                'roles_name' => 'admin',
            ];
        });
    }

    /**
     * Indicate that the roles is sales_mgr.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function sales_mgr()
    {
        return $this->state(function (array $attributes) {
            return [
                'roles_name' => 'sales_mgr',
            ];
        });
    }

    /**
     * Indicate that the roles is sales_senior_wholesales.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function sales_senior_wholesales()
    {
        return $this->state(function (array $attributes) {
            return [
                'roles_name' => 'sales_senior_wholesales',
            ];
        });
    }

    /**
     * Indicate that the roles is sales_senior_b2b.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function sales_senior_b2b()
    {
        return $this->state(function (array $attributes) {
            return [
                'roles_name' => 'sales_senior_b2b',
            ];
        });
    }

    /**
     * Indicate that the roles is sales_wholesales.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function sales_wholesales()
    {
        return $this->state(function (array $attributes) {
            return [
                'roles_name' => 'sales_wholesales',
            ];
        });
    }

    /**
     * Indicate that the roles is sales_b2b.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function sales_b2b()
    {
        return $this->state(function (array $attributes) {
            return [
                'roles_name' => 'sales_b2b',
            ];
        });
    }

    /**
     * Indicate that the roles is finance_manager.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function finance_manager()
    {
        return $this->state(function (array $attributes) {
            return [
                'roles_name' => 'finance_manager',
            ];
        });
    }

    /**
     * Indicate that the roles is finance_officer.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function finance_officer()
    {
        return $this->state(function (array $attributes) {
            return [
                'roles_name' => 'finance_officer',
            ];
        });
    }

    /**
     * Indicate that the roles is customer_wholesales.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function customer_wholesales()
    {
        return $this->state(function (array $attributes) {
            return [
                'roles_name' => 'customer_wholesales',
            ];
        });
    }

    /**
     * Indicate that the roles is customer_b2b.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function customer_b2b()
    {
        return $this->state(function (array $attributes) {
            return [
                'roles_name' => 'customer_b2b',
            ];
        });
    }
}
