<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $faker = $this->faker;
        return [
            'reference_object' => 'customer',
            'username' => 'test',
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'is_change_password' => 0,
            'created_date' => now(),
            'created_by' => NULL,
            'modified_date' => now(),
            'modified_by' => NULL,
        ];
    }

    /**
     * Indicate that the user is sales.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function sales()
    {
        return $this->state(function (array $attributes) {
            return [
                'reference_object' => 'sales',
            ];
        });
    }
}