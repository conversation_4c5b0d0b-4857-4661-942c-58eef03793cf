<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->string('article')->primary();
            $table->text('article_description');
            $table->string('cross_site');
            $table->string('sku_code_c');
            $table->string('external_product_id');
            $table->string('product_name_c');
            $table->string('product_variant_c');
            $table->string('product_size_c');
            $table->text('product_description')->nullable();
            $table->string('product_style')->nullable();
            $table->string('product_tag')->nullable();
            $table->string('product_type')->nullable();
            $table->string('product_gender')->nullable();
            $table->string('product_activity')->nullable();
            $table->string('product_status')->nullable();
            $table->string('collection')->nullable();
            $table->string('fabric_type')->nullable();
            $table->string('fabric_composition')->nullable();
            $table->string('fabric_structure')->nullable();
            $table->string('fash_grade')->nullable();
            $table->string('lvl2_description')->nullable();
            $table->string('lvl3_description')->nullable();
            $table->string('lvl4_description')->nullable();
            $table->string('season')->nullable();
            $table->boolean('is_wholesales')->default(0);
            $table->boolean('is_b2b')->default(0);
            $table->tinyInteger('deletion_flag')->default(0);
            $table->boolean('is_customizable')->default(0);
            $table->string('weight', 20)->nullable();
            $table->string('dimension')->nullable();
            $table->string('min_qty')->nullable();
            $table->timestamp('transfer_date')->nullable();
            $table->timestamp('expired_date')->nullable();
            $table->timestamp('created_date');
            $table->string('created_by', 20)->nullable();
            $table->timestamp('modified_date');
            $table->string('modified_by', 20)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products');
    }
}
