<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCartTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Schema::create('cart', function (Blueprint $table) {
        //     $table->id();
        //     $table->integer('customer_id');
        //     $table->timestamp('created_date');
        //     $table->string('created_by', 20)->nullable();
        //     $table->timestamp('modified_date');
        //     $table->string('modified_by', 20)->nullable();
        // });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Schema::dropIfExists('cart');
    }
}
