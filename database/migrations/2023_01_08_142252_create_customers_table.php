<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->string('customer_id', 20)->primary();
            $table->string('owner_name');
            $table->string('email');
            $table->boolean('is_verified')->default(0);
            $table->string('phone_number', 20);
            $table->string('distribution_channel', 20);
            $table->string('npwp', 20);
            $table->string('npwp_city');
            $table->string('npwp_address');
            $table->string('top', 20);
            $table->integer('top_days');
            $table->decimal('discount_percent', 5, 2);
            $table->string('customer_type');
            $table->boolean('is_active')->default(0);
            $table->boolean('is_pending_payment')->default(0);
            $table->timestamp('created_date');
            $table->string('created_by', 20)->nullable();
            $table->timestamp('modified_date');
            $table->string('modified_by', 20)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customers');
    }
}
