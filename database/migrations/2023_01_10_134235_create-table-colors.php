<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableColors extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('master_color', function (Blueprint $table) {
            $table->id();
            $table->string('key', 20)->nullable();
            $table->string('value', 20)->nullable();
            $table->timestamp('created_date');
            $table->string('created_by');
            $table->timestamp('modified_date');
            $table->string('modified_by');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('master_color');
    }
}
