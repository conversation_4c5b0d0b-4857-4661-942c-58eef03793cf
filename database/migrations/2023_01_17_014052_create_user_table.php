<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user', function (Blueprint $table) {
            $table->increments('user_id');
            $table->string('reference_id', 20);
            $table->string('reference_object', 20);
            $table->string('username');
            $table->string('email');
            $table->string('password');
            $table->tinyInteger('is_change_password')->default(0);
            $table->timestamp('created_date');
            $table->string('created_by', 20)->nullable();
            $table->timestamp('modified_date');
            $table->string('modified_by', 20)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user');
    }
}
