<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateRolesWithDetailTables extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('roles', function (Blueprint $table) {
            $table->increments('roles_id');
            $table->string('roles_name')->unique();
            $table->boolean('is_active')->default(0);
            $table->timestamp('created_date');
            $table->string('created_by', 20)->nullable();
            $table->timestamp('modified_date');
            $table->string('modified_by', 20)->nullable();
        });

        Schema::create('roles_detail', function (Blueprint $table) {
            $table->increments('roles_detail_id');
            $table->foreignId('roles_id');
            $table->foreignId('user_id');
            $table->timestamp('created_date');
            $table->string('created_by', 20)->nullable();
            $table->timestamp('modified_date');
            $table->string('modified_by', 20)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('roles_detail');
        Schema::dropIfExists('roles');
    }
}
