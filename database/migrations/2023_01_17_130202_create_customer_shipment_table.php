<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomerShipmentTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customer_shipment', function (Blueprint $table) {
            $table->string('customer_shipment_id', 20)->primary();
            $table->string('customer_id', 20);
            $table->string('name');
            $table->string('address');
            $table->string('city');
            $table->string('province');
            $table->string('district');
            $table->string('subdistrict');
            $table->string('zip_code');
            $table->string('shipment_type');
            $table->string('phone_number');
            $table->timestamp('created_date');
            $table->string('created_by', 20)->nullable();
            $table->timestamp('modified_date');
            $table->string('modified_by', 20)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customer_shipment');
    }
}
