<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInvoiceTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoice', function (Blueprint $table) {
            $table->string('invoice_no')->primary();
            $table->string('customer_id');
            $table->string('delivery_order_no');
            $table->timestamp('billing_date');
            $table->timestamp('due_date');
            $table->string('order_no',20);
            $table->string('sales_order_no',20);
            $table->string('vat_no',20);
            $table->double('down_payment');
            $table->double('due_payment');
            $table->double('gross_price');
            $table->double('nett_price');
            $table->double('dpp');
            $table->double('tax_amount');
            $table->string('currency',20);
            $table->string('uom',20);
            $table->string('status',20);
            $table->string('signature',20);
            $table->string('signature_job',20);
            $table->string('invoice_file_path');
            $table->string('tax_invoice_file_name');
            $table->string('tax_invoice_file_type');
            $table->string('tax_invoice_file_path');
            $table->timestamp('created_date');
            $table->string('created_by');
            $table->timestamp('modified_date');
            $table->string('modified_by');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoice');
    }
}
