<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInvoiceDetailTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoice_detail', function (Blueprint $table) {
            $table->increments('invoice_detail_id');
            $table->string('invoice_no',20);
            $table->string('reference_do');
            $table->string('reference_so');
            $table->string('article',20);
            $table->string('sequence_no', 20);
            $table->string('characteristic_value',30);
            $table->string('product_name');
            $table->string('product_variant',20);
            $table->string('product_size',20);
            $table->string('article_description');
            $table->string('uom',20);
            $table->double('price');
            $table->double('discount_percent');
            $table->double('gross_price');
            $table->double('nett_price');
            $table->string('currency',20);
            $table->string('status',20);
            $table->timestamp('created_date');
            $table->string('created_by');
            $table->timestamp('modified_date');
            $table->string('modified_by');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoice_detail');
    }
}
