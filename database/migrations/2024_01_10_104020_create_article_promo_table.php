<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('article_promo', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('sku', 36);
            $table->string('customer_id', 36);
            $table->decimal('discount', 16, 0);
            $table->string('currency', 36);
            $table->date('valid_from');
            $table->date('valid_to');
            $table->string('created_by', 36)->default('_utf8mb4\\\\\'\'INTEGRATION\\\\\'\'');
            $table->dateTime('created_date')->useCurrent();
            $table->string('modified_by', 36)->default('_utf8mb4\\\\\'\'INTEGRATION\\\\\'\'');
            $table->dateTime('modified_date')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('article_promo');
    }
};
