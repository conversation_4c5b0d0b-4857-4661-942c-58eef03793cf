<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('article', function (Blueprint $table) {
            $table->string('article')->index('article');
            $table->text('article_description');
            $table->string('sku_code_c');
            $table->string('product_name_c');
            $table->string('product_variant_c')->nullable();
            $table->string('product_size_c');
            $table->text('product_description')->nullable();
            $table->text('product_short_description')->nullable();
            $table->string('product_style')->nullable();
            $table->text('product_feature')->nullable();
            $table->string('product_tag')->nullable();
            $table->string('product_type')->nullable();
            $table->string('product_gender')->nullable();
            $table->string('product_activity')->nullable();
            $table->string('warranty')->nullable();
            $table->string('brand')->nullable();
            $table->string('creation_status')->nullable();
            $table->string('fabric_type')->nullable();
            $table->string('fabric_composition')->nullable();
            $table->string('fabric_structure')->nullable();
            $table->string('fash_grade')->nullable();
            $table->string('lvl2_description')->nullable();
            $table->string('lvl3_description')->nullable();
            $table->string('lvl4_description')->nullable();
            $table->string('season')->nullable();
            $table->tinyInteger('is_custom_logo')->default(0);
            $table->tinyInteger('is_custom_size')->default(0);
            $table->tinyInteger('is_wholesales')->nullable();
            $table->date('wholesales_published_date')->nullable();
            $table->tinyInteger('is_b2b')->nullable();
            $table->date('b2b_published_date')->nullable();
            $table->tinyInteger('is_reseller')->nullable();
            $table->date('reseller_published_date')->nullable();
            $table->tinyInteger('is_wab')->nullable();
            $table->date('wab_published_date')->nullable();
            $table->tinyInteger('deletion_flag')->nullable();
            $table->decimal('weight')->nullable();
            $table->string('uom')->nullable();
            $table->string('dimension')->nullable();
            $table->date('transfer_date')->nullable();
            $table->date('expired_date')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');
            $table->string('main_image', 256)->nullable();
            $table->string('min_qty', 50)->nullable();

            $table->primary(['article']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('article');
    }
};
