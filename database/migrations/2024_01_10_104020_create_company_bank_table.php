<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('company_bank', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->index('company_id');
            $table->string('company_id', 36);
            $table->string('bank_name');
            $table->string('bank_account_name');
            $table->string('bank_account_no');
            $table->tinyInteger('is_active');
            $table->text('logo')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('company_bank');
    }
};
