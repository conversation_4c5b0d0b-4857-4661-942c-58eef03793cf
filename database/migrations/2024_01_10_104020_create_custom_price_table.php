<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('custom_price', function (Blueprint $table) {
            $table->string('id', 50)->nullable();
            $table->string('item', 50)->nullable();
            $table->string('description', 50)->nullable();
            $table->integer('size_from')->nullable();
            $table->integer('size_to')->nullable();
            $table->integer('price')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('custom_price');
    }
};
