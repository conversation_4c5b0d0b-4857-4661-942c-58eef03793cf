<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('custom_tag', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('product_group')->nullable();
            $table->string('product_type', 36)->nullable();
            $table->string('image_sequence', 2);
            $table->json('coordinate')->nullable();
            $table->string('position_side')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('custom_tag');
    }
};
