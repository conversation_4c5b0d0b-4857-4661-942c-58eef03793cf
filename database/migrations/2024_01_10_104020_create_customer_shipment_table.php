<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customer_shipment', function (Blueprint $table) {
            $table->string('customer_shipment_id', 36)->default('uuid()')->index('indexes_customer_shipment');
            $table->string('customer_id', 20);
            $table->string('name');
            $table->string('address_name')->nullable();
            $table->text('address');
            $table->string('country')->nullable();
            $table->string('country_code', 20)->nullable();
            $table->string('province');
            $table->string('province_code', 20)->nullable();
            $table->string('city');
            $table->string('city_code', 20)->nullable();
            $table->string('district');
            $table->string('district_code', 20)->nullable();
            $table->string('subdistrict');
            $table->string('zip_code');
            $table->string('zip_code_id', 100)->nullable();
            $table->string('zone_code', 36)->nullable();
            $table->string('shipment_type')->nullable();
            $table->string('phone_number');
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['customer_shipment_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customer_shipment');
    }
};
