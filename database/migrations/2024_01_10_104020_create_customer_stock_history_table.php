<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customer_stock_history', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('customer_id', 36);
            $table->string('customer_shipment_id', 36);
            $table->text('file_name')->nullable();
            $table->text('file_type')->nullable();
            $table->text('file_path')->nullable();
            $table->integer('file_size')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by', 36)->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date');
            $table->string('modified_by', 36);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customer_stock_history');
    }
};
