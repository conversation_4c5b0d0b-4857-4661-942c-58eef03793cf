<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customer_stock', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('customer_id', 20)->nullable();
            $table->string('customer_shipment_id', 20)->nullable();
            $table->string('sku_code', 36)->nullable();
            $table->string('article_id', 36)->nullable();
            $table->string('product_name')->nullable();
            $table->string('product_variant')->nullable();
            $table->string('product_size')->nullable();
            $table->integer('qty')->nullable();
            $table->string('file_path_url', 500)->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date')->nullable();
            $table->string('modified_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customer_stock');
    }
};
