<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customer_virtual_account', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('customer_id', 20)->nullable();
            $table->string('bank_name')->nullable();
            $table->string('virtual_account_no')->nullable();
            $table->dateTime('created_date');
            $table->string('created_by');
            $table->dateTime('modified_date');
            $table->string('modified_by');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customer_virtual_account');
    }
};
