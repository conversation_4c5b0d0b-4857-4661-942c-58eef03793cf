<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()');
            $table->string('customer_id', 36)->index('indexes_customer');
            $table->string('owner_name');
            $table->string('instance_name')->nullable();
            $table->string('email');
            $table->string('address', 160)->nullable();
            $table->string('phone_number', 20);
            $table->string('distribution_channel', 20);
            $table->string('national_id')->nullable();
            $table->text('national_id_file')->nullable();
            $table->string('npwp', 100);
            $table->string('npwp_name')->nullable();
            $table->text('npwp_file')->nullable();
            $table->string('npwp_city')->nullable();
            $table->string('npwp_address');
            $table->string('tax_type', 36)->nullable();
            $table->string('tax_invoice', 20)->nullable();
            $table->string('top', 20);
            $table->string('top_days', 20);
            $table->string('customer_type');
            $table->string('status')->nullable();
            $table->boolean('is_active')->default(false);
            $table->boolean('is_pending_payment')->default(false);
            $table->boolean('is_blocked')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_rejected')->default(false);
            $table->dateTime('registered_sap_at')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['customer_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customers');
    }
};
