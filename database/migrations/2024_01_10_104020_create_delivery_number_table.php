<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('delivery_number', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('invoice_no')->nullable();
            $table->string('delivery_order_no')->nullable();
            $table->string('delivery_name')->nullable();
            $table->string('delivery_no')->nullable();
            $table->dateTime('created_date')->nullable();
            $table->string('created_by')->nullable();
            $table->dateTime('modified_date')->nullable();
            $table->string('modified_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('delivery_number');
    }
};
