<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('delivery_order_detail', function (Blueprint $table) {
            $table->string('delivery_order_detail_id', 36)->default('uuid()')->index('indexes_delivery_order_detail');
            $table->string('delivery_order_no');
            $table->string('article');
            $table->string('sequence_no', 20);
            $table->string('product_name');
            $table->string('product_variant');
            $table->string('product_size');
            $table->tinyInteger('is_new_arrival')->nullable();
            $table->string('uom', 20);
            $table->integer('qty');
            $table->integer('issued_qty')->nullable();
            $table->decimal('sub_total', 16);
            $table->string('merchandise_category')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['delivery_order_detail_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('delivery_order_detail');
    }
};
