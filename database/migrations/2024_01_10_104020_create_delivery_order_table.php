<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('delivery_order', function (Blueprint $table) {
            $table->string('delivery_order_no')->index('indexes_delivery_order');
            $table->string('sales_order_no');
            $table->string('customer_id');
            $table->string('customer_shipment_id');
            $table->string('delivery_no')->nullable();
            $table->dateTime('delivery_date')->nullable();
            $table->string('delivery_type')->nullable();
            $table->string('route')->nullable();
            $table->dateTime('good_issue_date')->nullable();
            $table->decimal('issued_total', 16)->nullable()->default(0);
            $table->decimal('issued_discount', 16)->nullable();
            $table->decimal('total', 16);
            $table->decimal('discount', 16)->nullable();
            $table->string('parent_location')->nullable();
            $table->string('location_type')->nullable();
            $table->string('destination')->nullable();
            $table->string('source')->nullable();
            $table->string('remark')->nullable();
            $table->string('status')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['delivery_order_no']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('delivery_order');
    }
};
