<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('district', function (Blueprint $table) {
            $table->string('code', 36)->primary();
            $table->string('city_id', 36)->nullable();
            $table->text('name')->nullable();
            $table->dateTime('created_date')->default('curdate()');
            $table->string('created_by', 36)->nullable();
            $table->dateTime('modified_date')->default('curdate()');
            $table->string('modified_by', 36)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('district');
    }
};
