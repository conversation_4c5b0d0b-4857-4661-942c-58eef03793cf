<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('down_payment_request', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('request_no');
            $table->char('request_year', 4);
            $table->string('sales_order_no');
            $table->string('order_no');
            $table->char('currency', 5)->default('_utf8mb4\\\\\'\'IDR\\\\\'\'');
            $table->decimal('dp_value', 16)->default(0);
            $table->string('status');
            $table->dateTime('created_date')->default('curdate()');
            $table->string('created_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date')->default('curdate()');
            $table->string('modified_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('down_payment_request');
    }
};
