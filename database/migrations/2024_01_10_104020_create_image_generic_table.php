<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('image_generic', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->unique('id');
            $table->string('sku_code_c');
            $table->tinyInteger('is_main_image');
            $table->integer('sequence_no');
            $table->text('url')->nullable();
            $table->text('file_path')->nullable();
            $table->dateTime('created_date')->nullable();
            $table->string('created_by')->nullable();
            $table->dateTime('modified_date')->nullable();
            $table->string('modified_by')->nullable();

            $table->primary(['id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('image_generic');
    }
};
