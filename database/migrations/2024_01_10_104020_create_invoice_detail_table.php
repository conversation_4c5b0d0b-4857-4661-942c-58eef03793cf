<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoice_detail', function (Blueprint $table) {
            $table->string('invoice_detail_id', 36)->default('uuid()')->index('indexes_invoice_detail');
            $table->string('invoice_no', 20);
            $table->string('reference_do');
            $table->string('reference_so');
            $table->string('article', 20);
            $table->string('sequence_no', 20);
            $table->string('product_name');
            $table->string('product_variant', 20);
            $table->string('product_size', 20);
            $table->string('article_description');
            $table->integer('qty');
            $table->string('uom', 20);
            $table->decimal('price', 16)->nullable()->default(0);
            $table->decimal('discount_percent', 5)->nullable()->default(0);
            $table->decimal('gross_price', 16)->nullable()->default(0);
            $table->decimal('nett_price', 16)->nullable()->default(0);
            $table->string('currency', 20);
            $table->string('status', 20);
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['invoice_detail_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoice_detail');
    }
};
