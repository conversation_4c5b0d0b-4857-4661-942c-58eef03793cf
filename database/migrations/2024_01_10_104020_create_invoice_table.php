<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoice', function (Blueprint $table) {
            $table->string('invoice_no')->index('indexes_invoice');
            $table->string('invoice_type', 36)->nullable();
            $table->string('customer_id');
            $table->string('customer_name')->nullable();
            $table->string('address')->nullable();
            $table->string('delivery_order_no');
            $table->string('no_resi')->nullable();
            $table->string('company_name');
            $table->string('parameter_value', 48);
            $table->dateTime('billing_date');
            $table->dateTime('due_date');
            $table->string('order_no', 20);
            $table->string('po_no')->nullable();
            $table->string('sales_order_no', 20);
            $table->string('vat_no', 20);
            $table->decimal('down_payment', 16);
            $table->decimal('due_payment', 16);
            $table->decimal('gross_price', 16);
            $table->decimal('nett_price', 16);
            $table->decimal('dpp', 16);
            $table->decimal('tax_amount', 16);
            $table->string('currency', 20);
            $table->string('uom', 20);
            $table->string('koli', 10)->nullable();
            $table->string('status', 20);
            $table->string('signature', 20);
            $table->string('signature_job', 20);
            $table->string('invoice_file_path');
            $table->string('tax_invoice_file_name');
            $table->string('tax_invoice_file_type');
            $table->string('tax_invoice_file_path');
            $table->string('cancelled_invoice_no')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['invoice_no']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoice');
    }
};
