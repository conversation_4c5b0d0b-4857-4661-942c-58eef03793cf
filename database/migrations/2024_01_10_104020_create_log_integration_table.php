<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('log_integration', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->index('indexes_log_integration');
            $table->string('reference_no')->nullable();
            $table->string('module')->nullable();
            $table->string('name');
            $table->string('type')->nullable();
            $table->string('status', 20)->nullable();
            $table->mediumText('description')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('log_integration');
    }
};
