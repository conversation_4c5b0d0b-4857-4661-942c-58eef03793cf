<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('master_parameter', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->index('indexes_master_parameter');
            $table->string('group_key')->nullable();
            $table->string('key')->nullable();
            $table->text('value')->nullable();
            $table->text('description')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('master_parameter');
    }
};
