<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('matrix_discount', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('category', 36)->nullable();
            $table->tinyInteger('is_custom')->nullable();
            $table->decimal('min_bruto_from', 16)->nullable();
            $table->decimal('min_bruto_to', 16)->nullable();
            $table->integer('qty_from')->nullable();
            $table->integer('qty_to')->nullable();
            $table->decimal('discount', 16)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('matrix_discount');
    }
};
