<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('user_id', 36)->nullable();
            $table->string('name')->nullable();
            $table->string('module')->nullable();
            $table->string('category')->nullable();
            $table->text('message')->nullable();
            $table->tinyInteger('is_read')->default(0);
            $table->dateTime('created_date')->nullable()->useCurrent();
            $table->string('created_by')->nullable();
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notifications');
    }
};
