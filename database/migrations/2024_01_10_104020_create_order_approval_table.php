<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_approval', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('order_no');
            $table->string('status');
            $table->string('action_by')->nullable();
            $table->dateTime('action_date')->nullable();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('created_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_approval');
    }
};
