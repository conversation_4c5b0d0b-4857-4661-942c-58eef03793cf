<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_custom_attachment', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('order_custom_id', 36)->nullable();
            $table->string('custom_type')->nullable();
            $table->decimal('custom_price', 16)->nullable();
            $table->string('position')->nullable();
            $table->string('position_x', 36)->nullable();
            $table->string('position_y', 36)->nullable();
            $table->string('dimension_width')->nullable();
            $table->string('dimension_height')->nullable();
            $table->text('file_path')->nullable();
            $table->string('notes')->nullable();
            $table->string('size', 36)->nullable();
            $table->string('custom_text', 128)->nullable();
            $table->string('material', 128)->nullable();
            $table->dateTime('created_date')->default('curdate()');
            $table->string('created_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date')->nullable();
            $table->string('modified_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_custom_attachment');
    }
};
