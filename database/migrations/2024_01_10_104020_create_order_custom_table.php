<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_custom', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('sku', 36)->nullable();
            $table->string('article_id')->nullable();
            $table->string('reference_name', 36)->nullable();
            $table->string('reference_id', 36)->nullable();
            $table->decimal('custom_price', 16)->nullable();
            $table->string('position_side', 36)->nullable();
            $table->text('generated_file_path')->nullable();
            $table->dateTime('created_date')->nullable();
            $table->string('created_by')->nullable();
            $table->dateTime('modified_date')->nullable();
            $table->string('modified_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_custom');
    }
};
