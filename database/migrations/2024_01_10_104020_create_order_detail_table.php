<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_detail', function (Blueprint $table) {
            $table->string('order_detail_id', 36)->default('uuid()')->index('indexes_order_detail');
            $table->string('order_no');
            $table->string('article_id');
            $table->tinyInteger('is_custom')->default(0);
            $table->string('product_name');
            $table->string('product_variant');
            $table->string('product_size');
            $table->tinyInteger('is_new_arrival')->nullable();
            $table->decimal('price', 16);
            $table->integer('qty');
            $table->integer('issued_qty')->default(0);
            $table->decimal('sub_total', 16);
            $table->string('discount_code', 20)->nullable();
            $table->decimal('additional_discount', 16)->nullable();
            $table->decimal('primary_discount', 16)->nullable();
            $table->decimal('total', 16);
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['order_detail_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_detail');
    }
};
