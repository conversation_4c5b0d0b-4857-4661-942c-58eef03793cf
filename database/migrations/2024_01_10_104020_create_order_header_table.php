<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_header', function (Blueprint $table) {
            $table->string('order_no')->index('indexes_order_header');
            $table->string('customer_id');
            $table->string('bill_to')->nullable();
            $table->text('bill_to_address')->nullable();
            $table->string('bill_to_email')->nullable();
            $table->string('bill_to_phone_number')->nullable();
            $table->string('customer_shipment_id');
            $table->string('ship_to')->nullable();
            $table->text('ship_to_address')->nullable();
            $table->string('ship_to_phone_number')->nullable();
            $table->string('sales_id')->nullable();
            $table->string('sales_name')->nullable();
            $table->string('sales_order_no')->nullable();
            $table->string('external_order_no')->nullable();
            $table->string('interaction_no')->nullable();
            $table->string('delivery_no')->nullable();
            $table->dateTime('delivery_date')->nullable();
            $table->dateTime('est_delivery_date')->nullable();
            $table->string('awb_no')->nullable();
            $table->dateTime('order_date')->nullable();
            $table->string('order_status', 20);
            $table->decimal('total', 16);
            $table->string('discount_type')->nullable();
            $table->string('discount_code')->nullable();
            $table->decimal('dp_percentage', 5)->nullable()->default(0);
            $table->decimal('dp_amount', 16)->nullable();
            $table->date('dp_due_date')->nullable();
            $table->decimal('total_discount', 16);
            $table->decimal('nett_before_tax', 16);
            $table->decimal('total_tax', 16);
            $table->decimal('total_nett', 16);
            $table->string('currency')->nullable();
            $table->string('payment_id')->nullable();
            $table->string('payment_link')->nullable();
            $table->string('payment_status')->nullable();
            $table->dateTime('shipping_date')->nullable();
            $table->string('shipping_method')->nullable();
            $table->dateTime('completed_date')->nullable();
            $table->string('sales_organization')->nullable();
            $table->string('distribution_channel')->nullable();
            $table->string('division')->nullable();
            $table->string('shipping_code', 10)->nullable();
            $table->string('shipping_name')->nullable();
            $table->decimal('shipping_charges', 16)->nullable();
            $table->string('location_code', 20)->nullable();
            $table->string('location_name')->nullable();
            $table->text('pks_file_path')->nullable();
            $table->string('pks_file_name')->nullable();
            $table->dateTime('verified_date')->nullable();
            $table->string('verified_by')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['order_no']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_header');
    }
};
