<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('proforma_invoice_detail', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('proforma_invoice_id', 36);
            $table->integer('sequence_no');
            $table->string('article');
            $table->string('sku_code');
            $table->string('product_name')->nullable();
            $table->string('product_variant')->nullable();
            $table->integer('qty');
            $table->string('currency')->nullable();
            $table->decimal('price', 16);
            $table->decimal('gross_price', 16);
            $table->decimal('nett_price', 16);
            $table->decimal('discount', 16)->nullable();
            $table->dateTime('created_date')->nullable();
            $table->string('created_by')->nullable();
            $table->dateTime('modified_date')->nullable();
            $table->string('modified_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('proforma_invoice_detail');
    }
};
