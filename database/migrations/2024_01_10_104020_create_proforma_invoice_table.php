<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('proforma_invoice', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('sales_order_no')->nullable();
            $table->date('sales_order_date')->nullable();
            $table->date('due_date')->nullable();
            $table->string('po_no')->nullable();
            $table->string('customer_external_id')->nullable();
            $table->string('customer_name')->nullable();
            $table->text('customer_address')->nullable();
            $table->string('customer_npwp')->nullable();
            $table->decimal('down_payment', 16)->nullable();
            $table->decimal('down_payment_percentage', 5)->nullable();
            $table->decimal('gross_price', 16)->nullable();
            $table->decimal('nett_price', 16)->nullable();
            $table->decimal('dpp', 16)->nullable();
            $table->decimal('tax', 16)->nullable();
            $table->string('currency')->nullable();
            $table->string('signature')->nullable();
            $table->string('signature_job')->nullable();
            $table->date('signature_date')->nullable();
            $table->dateTime('created_date')->nullable();
            $table->string('created_by')->nullable();
            $table->dateTime('modified_date')->nullable();
            $table->string('modified_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('proforma_invoice');
    }
};
