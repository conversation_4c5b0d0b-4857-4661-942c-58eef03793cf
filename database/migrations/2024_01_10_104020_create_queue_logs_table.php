<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('queue_logs', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('uuid', 64)->nullable();
            $table->string('job_id', 64)->nullable();
            $table->text('queue_name')->nullable();
            $table->text('queue_resolve_name')->nullable();
            $table->text('connection_name')->nullable();
            $table->boolean('isFailed')->nullable();
            $table->boolean('isReleased')->nullable();
            $table->boolean('isDeleted')->nullable();
            $table->text('payload')->nullable();
            $table->text('exceptions')->nullable();
            $table->dateTime('created_date')->nullable()->useCurrent();
            $table->dateTime('modified_date')->nullable()->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('queue_logs');
    }
};
