<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('region_omni', function (Blueprint $table) {
            $table->char('code', 2)->nullable();
            $table->string('description', 50)->nullable();
            $table->string('created_date', 50)->nullable();
            $table->string('created_by', 50)->nullable();
            $table->string('modified_date', 50)->nullable();
            $table->string('modified_by', 50)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('region_omni');
    }
};
