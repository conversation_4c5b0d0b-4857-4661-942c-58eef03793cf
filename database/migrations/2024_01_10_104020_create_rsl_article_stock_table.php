<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_article_stock', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('article')->index('article');
            $table->string('location_code');
            $table->integer('stock')->default(0);
            $table->integer('booked_stock')->default(0);
            $table->integer('available_stock')->default(0);
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('INTEGRATION');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('INTEGRATION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_article_stock');
    }
};
