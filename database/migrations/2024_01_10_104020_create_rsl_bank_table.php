<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_bank', function (Blueprint $table) {
            $table->string('id')->default('uuid()')->primary();
            $table->string('beneficiary_bi_code', 10)->nullable();
            $table->char('beneficiary_brach_code', 3)->nullable();
            $table->char('bank_code', 3);
            $table->string('bic_code', 10)->nullable();
            $table->string('bank_name');
            $table->string('branch_name')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_bank');
    }
};
