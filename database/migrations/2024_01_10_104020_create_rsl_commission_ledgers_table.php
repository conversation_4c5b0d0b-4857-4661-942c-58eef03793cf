<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_commission_ledgers', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('commission_id', 36)->index('commission_id');
            $table->string('transaction_id', 36)->index('rsl_commission_ledgers_ibfk_2');
            $table->string('reseller_id')->index('reseller_id');
            $table->string('type');
            $table->string('commission_type');
            $table->decimal('amount', 20);
            $table->text('remarks')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_commission_ledgers');
    }
};
