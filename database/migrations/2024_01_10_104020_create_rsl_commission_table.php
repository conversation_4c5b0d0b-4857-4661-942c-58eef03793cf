<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_commission', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('reseller_id', 36)->index('reseller_id');
            $table->decimal('potential_amount', 20)->default(0);
            $table->decimal('commission_amount', 20)->default(0);
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_commission');
    }
};
