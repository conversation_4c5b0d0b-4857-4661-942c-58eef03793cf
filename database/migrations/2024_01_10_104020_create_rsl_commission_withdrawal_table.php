<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_commission_withdrawal', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('reseller_id', 36)->index('reseller_id');
            $table->string('request_id', 36);
            $table->string('ext_request_id')->nullable();
            $table->dateTime('request_date');
            $table->string('bank_name', 100)->nullable();
            $table->string('account_name')->nullable();
            $table->string('account_no')->nullable();
            $table->string('transfer_method')->nullable();
            $table->dateTime('payment_date')->nullable();
            $table->string('status', 36);
            $table->decimal('amount', 16)->default(0);
            $table->decimal('transfer_fee', 16)->default(0);
            $table->decimal('tax_amount', 16)->default(0);
            $table->string('tax_reference', 36)->nullable();
            $table->json('tax_metadata')->nullable();
            $table->decimal('total', 16)->default(0);
            $table->decimal('payout_amount', 16)->nullable();
            $table->string('action_by')->nullable();
            $table->string('action_type')->nullable();
            $table->string('action_notes')->nullable();
            $table->dateTime('action_date')->nullable();
            $table->dateTime('created_date')->nullable()->useCurrent();
            $table->string('created_by')->nullable()->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_commission_withdrawal');
    }
};
