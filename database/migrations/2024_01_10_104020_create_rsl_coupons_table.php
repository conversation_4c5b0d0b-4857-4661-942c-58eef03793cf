<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_coupons', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->string('id_code')->nullable();
            $table->string('coupon_code');
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('amount', 20)->default(0);
            $table->string('remarks')->nullable();
            $table->dateTime('start_date');
            $table->dateTime('end_date');
            $table->boolean('mix_promotion')->default(false);
            $table->integer('max_use_count')->default(0);
            $table->integer('max_per_user')->default(0);
            $table->boolean('sunday_enabled')->default(false);
            $table->time('sunday_start_at')->nullable();
            $table->time('sunday_end_at')->nullable();
            $table->boolean('monday_enabled')->default(false);
            $table->time('monday_start_at')->nullable();
            $table->time('monday_end_at')->nullable();
            $table->boolean('tuesday_enabled')->default(false);
            $table->time('tuesday_start_at')->nullable();
            $table->time('tuesday_end_at')->nullable();
            $table->boolean('wednesday_enabled')->default(false);
            $table->time('wednesday_start_at')->nullable();
            $table->time('wednesday_end_at')->nullable();
            $table->boolean('thursday_enabled')->default(false);
            $table->time('thursday_start_at')->nullable();
            $table->time('thursday_end_at')->nullable();
            $table->boolean('friday_enabled')->default(false);
            $table->time('friday_start_at')->nullable();
            $table->time('friday_end_at')->nullable();
            $table->boolean('saturday_enabled')->default(false);
            $table->time('saturday_start_at')->nullable();
            $table->time('saturday_end_at')->nullable();
            $table->string('type')->nullable();
            $table->string('category')->nullable();
            $table->string('condition_type')->nullable();
            $table->string('discount_type')->nullable();
            $table->string('status', 45)->nullable();
            $table->integer('used_count')->nullable()->default(0);
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('INTEGRATION');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('INTEGRATION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_coupons');
    }
};
