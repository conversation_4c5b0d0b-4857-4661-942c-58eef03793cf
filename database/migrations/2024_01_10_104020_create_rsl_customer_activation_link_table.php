<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_customer_activation_link', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('customer_id', 36)->index('customer_id');
            $table->string('token');
            $table->dateTime('expired_at');
            $table->dateTime('activation_at')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('SYSTEM');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_customer_activation_link');
    }
};
