<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_customer_shipments', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('customer_id', 36)->index('customer_id');
            $table->string('name');
            $table->string('phone_number', 13);
            $table->text('address');
            $table->string('region_code');
            $table->string('region_name');
            $table->string('city_code');
            $table->string('city_name');
            $table->string('district_code')->nullable();
            $table->string('district_name')->nullable();
            $table->string('subdistrict_code');
            $table->string('subdistrict_name');
            $table->string('zip_code');
            $table->boolean('is_active')->default(false);
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('SYSTEM');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_customer_shipments');
    }
};
