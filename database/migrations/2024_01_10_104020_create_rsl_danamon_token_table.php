<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_danamon_token', function (Blueprint $table) {
            $table->char('id', 1)->default('_utf8mb4\\\\\'\'1\\\\\'\'')->primary();
            $table->string('token', 500);
            $table->dateTime('expired_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_danamon_token');
    }
};
