<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_income_tax_matrix', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->json('metadata')->nullable();
            $table->string('remarks')->nullable();
            $table->boolean('is_active')->nullable()->default(false);
            $table->date('valid_from')->nullable();
            $table->date('valid_to')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_income_tax_matrix');
    }
};
