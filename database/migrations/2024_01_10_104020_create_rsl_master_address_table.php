<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_master_address', function (Blueprint $table) {
            $table->string('region_code')->nullable();
            $table->text('region_name')->nullable();
            $table->string('city_code')->nullable();
            $table->string('city_type')->nullable();
            $table->text('city_name')->nullable();
            $table->string('district_code')->nullable();
            $table->text('district_name')->nullable();
            $table->string('sub_district_code')->nullable();
            $table->text('sub_district_name')->nullable();
            $table->char('zip_code', 20)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_master_address');
    }
};
