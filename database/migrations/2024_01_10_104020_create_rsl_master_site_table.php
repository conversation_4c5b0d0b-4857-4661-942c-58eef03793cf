<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_master_site', function (Blueprint $table) {
            $table->string('code')->primary();
            $table->string('name');
            $table->text('address')->nullable();
            $table->string('district_code')->nullable();
            $table->char('zip_code', 5)->nullable();
            $table->string('transportation_zone_code')->nullable();
            $table->boolean('is_active')->nullable()->default(true);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_master_site');
    }
};
