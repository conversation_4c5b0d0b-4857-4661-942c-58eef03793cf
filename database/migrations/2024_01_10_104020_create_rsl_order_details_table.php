<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_order_details', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('order_header_id', 36)->index('order_header_id');
            $table->string('sku_code');
            $table->string('article_id')->index('article_id');
            $table->string('product_name');
            $table->string('product_variant');
            $table->string('product_size');
            $table->integer('qty');
            $table->integer('additional_qty')->nullable();
            $table->char('location_code', 4);
            $table->decimal('unit_price', 16);
            $table->string('base_uom')->nullable()->default('_utf8mb4\\\\\'\'PC\\\\\'\'');
            $table->decimal('line_amount', 16);
            $table->decimal('discount_amount', 16)->nullable()->default(0);
            $table->decimal('total_amount', 16)->nullable()->default(0);
            $table->text('remarks')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('SYSTEM');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_order_details');
    }
};
