<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_order_headers', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('order_no')->index('indexes_order');
            $table->string('ext_order_id')->nullable();
            $table->string('ext_order_no')->nullable();
            $table->string('invoice_no')->nullable();
            $table->dateTime('order_date')->useCurrent();
            $table->dateTime('completed_date')->useCurrent();
            $table->string('reseller_id')->nullable()->index('reseller_id');
            $table->string('link_id')->nullable();
            $table->string('customer_id')->nullable();
            $table->string('customer_name')->nullable();
            $table->string('customer_phone_number')->nullable();
            $table->string('customer_email')->nullable();
            $table->string('customer_shipment_id')->nullable();
            $table->string('customer_shipment_name')->nullable();
            $table->text('customer_shipment_address')->nullable();
            $table->string('customer_shipment_phone_number', 20)->nullable();
            $table->string('customer_shipment_region_name')->nullable();
            $table->string('customer_shipment_city_name')->nullable();
            $table->string('customer_shipment_district_name')->nullable();
            $table->string('customer_shipment_subdistrict_name')->nullable();
            $table->string('customer_shipment_zip_code')->nullable();
            $table->string('shipment_method')->nullable();
            $table->string('transporter_id')->nullable();
            $table->decimal('shipment_charges', 16)->nullable()->default(0);
            $table->decimal('handling_charges', 16)->nullable()->default(0);
            $table->decimal('sub_total_amount', 16)->nullable()->default(0);
            $table->decimal('discount_amount', 16)->nullable()->default(0);
            $table->decimal('total_amount', 16)->nullable()->default(0);
            $table->decimal('pay_amount', 16)->nullable()->default(0);
            $table->string('payment_method')->nullable();
            $table->string('payment_link')->nullable();
            $table->string('payment_status')->default('_utf8mb4\\\\\'\'Belum Dibayar\\\\\'\'');
            $table->string('payment_date')->nullable();
            $table->dateTime('due_payment_date')->nullable();
            $table->decimal('commission_amount', 16)->default(0);
            $table->char('currency', 3)->nullable()->default('_utf8mb4\\\\\'\'IDR\\\\\'\'');
            $table->string('order_status')->default('_utf8mb4\\\\\'\'Pending\\\\\'\'');
            $table->string('ext_order_status')->nullable();
            $table->dateTime('sla_date')->nullable();
            $table->string('sla_status')->nullable();
            $table->text('remarks')->nullable();
            $table->string('location_code')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('SYSTEM');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_order_headers');
    }
};
