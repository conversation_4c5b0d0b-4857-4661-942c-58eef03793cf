<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_order_promotions', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('reference_name');
            $table->string('reference_id');
            $table->string('discount_type');
            $table->string('discount_id');
            $table->decimal('amount', 20)->nullable();
            $table->string('bundling_code', 100)->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('SYSTEM');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_order_promotions');
    }
};
