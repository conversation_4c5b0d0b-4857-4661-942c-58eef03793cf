<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_order_shipments', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('order_header_id')->index('order_header_id');
            $table->char('location_code', 4)->nullable();
            $table->string('transporter_id')->nullable();
            $table->string('awb_no')->nullable();
            $table->string('delivery_number')->nullable();
            $table->dateTime('shipment_date')->nullable();
            $table->dateTime('est_shipment_date')->nullable();
            $table->string('shipment_status');
            $table->string('order_status');
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('_utf8mb4\\\\\'\'INTEGRATION\\\\\'\'');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('_utf8mb4\\\\\'\'INTEGRATION\\\\\'\'');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_order_shipments');
    }
};
