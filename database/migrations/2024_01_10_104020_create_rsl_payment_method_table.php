<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_payment_method', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('name');
            $table->string('code');
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by', 36)->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_payment_method');
    }
};
