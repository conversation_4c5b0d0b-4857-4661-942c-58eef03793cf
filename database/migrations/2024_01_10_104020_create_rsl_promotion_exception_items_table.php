<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_promotion_exception_items', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->bigInteger('promotion_id')->nullable()->index('promotion_id');
            $table->string('item_type');
            $table->string('item_id');
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('INTEGRATION');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('INTEGRATION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_promotion_exception_items');
    }
};
