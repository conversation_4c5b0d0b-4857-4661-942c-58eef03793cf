<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_promotion_parameters', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->bigInteger('promotion_id')->nullable()->index('promotion_id');
            $table->string('type')->nullable();
            $table->string('statement')->nullable();
            $table->decimal('value', 20)->nullable();
            $table->string('edc_id')->nullable();
            $table->string('card_type_id')->nullable();
            $table->string('prefix')->nullable();
            $table->string('gender', 36)->nullable();
            $table->string('payment_type_id')->nullable();
            $table->dateTime('deleted_at')->nullable();
            $table->string('deleted_by')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('INTEGRATION');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('INTEGRATION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_promotion_parameters');
    }
};
