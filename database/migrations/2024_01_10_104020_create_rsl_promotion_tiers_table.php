<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_promotion_tiers', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->bigInteger('promotion_id')->nullable()->index('promotion_id');
            $table->integer('sequence_no')->nullable();
            $table->integer('minimum_qty')->nullable();
            $table->decimal('discount_amount', 20)->nullable()->default(0);
            $table->decimal('maximum_value', 20)->nullable()->default(0);
            $table->string('n_customer')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('INTEGRATION');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('INTEGRATION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_promotion_tiers');
    }
};
