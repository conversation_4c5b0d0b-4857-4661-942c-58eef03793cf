<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_promotions', function (Blueprint $table) {
            $table->bigInteger('id')->unique('idx_rsl_promotions_1');
            $table->string('name')->nullable();
            $table->longText('description')->nullable();
            $table->bigInteger('customer_group_id')->nullable();
            $table->string('status')->nullable();
            $table->string('action')->nullable();
            $table->dateTime('start_date', 6)->nullable();
            $table->dateTime('end_date', 6)->nullable();
            $table->string('discount_type')->nullable();
            $table->string('item_discount')->nullable();
            $table->string('item_bundling')->nullable();
            $table->string('item_exception')->nullable();
            $table->decimal('discount_amount', 20)->nullable();
            $table->integer('point_amount')->nullable();
            $table->boolean('is_all_location')->nullable();
            $table->boolean('is_include_new_store')->nullable();
            $table->longText('based_on')->nullable();
            $table->integer('coupon_expired_days')->nullable();
            $table->dateTime('coupon_expired_at', 6)->nullable();
            $table->integer('first_n_customers')->nullable();
            $table->string('boolean')->nullable();
            $table->string('loadshare_discount_eiger')->nullable();
            $table->string('loadshare_discount_partner')->nullable();
            $table->boolean('sunday_enabled')->nullable();
            $table->time('sunday_start_at', 6)->nullable();
            $table->time('sunday_end_at', 6)->nullable();
            $table->boolean('monday_enabled')->nullable();
            $table->time('monday_start_at', 6)->nullable();
            $table->time('monday_end_at', 6)->nullable();
            $table->boolean('tuesday_enabled')->nullable();
            $table->time('tuesday_start_at', 6)->nullable();
            $table->time('tuesday_end_at', 6)->nullable();
            $table->boolean('wednesday_enabled')->nullable();
            $table->time('wednesday_start_at', 6)->nullable();
            $table->time('wednesday_end_at', 6)->nullable();
            $table->boolean('thursday_enabled')->nullable();
            $table->time('thursday_start_at', 6)->nullable();
            $table->time('thursday_end_at', 6)->nullable();
            $table->boolean('friday_enabled')->nullable();
            $table->time('friday_start_at', 6)->nullable();
            $table->time('friday_end_at', 6)->nullable();
            $table->boolean('saturday_enabled')->nullable();
            $table->time('saturday_start_at', 6)->nullable();
            $table->time('saturday_end_at', 6)->nullable();
            $table->boolean('is_loadshare')->nullable();
            $table->longText('coupon_tnc')->nullable();
            $table->boolean('coupon_generated_multiple')->nullable();
            $table->boolean('is_homepage')->nullable();
            $table->string('schema')->nullable();
            $table->string('partner')->nullable();
            $table->decimal('max_value', 65, 30)->nullable();
            $table->string('discount_mechanism')->nullable();
            $table->boolean('is_multiple_qty')->nullable();
            $table->decimal('multiple_amount', 65, 30)->nullable();
            $table->bigInteger('max_qty')->nullable();
            $table->dateTime('deleted_at', 6)->nullable();
            $table->string('deleted_by')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('INTEGRATION');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('INTEGRATION');

            $table->primary(['id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_promotions');
    }
};
