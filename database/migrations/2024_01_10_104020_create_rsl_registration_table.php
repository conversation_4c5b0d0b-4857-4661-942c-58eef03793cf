<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_registration', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('reseller_id', 36);
            $table->string('email');
            $table->string('phone_number', 36);
            $table->string('password');
            $table->string('name');
            $table->string('gender');
            $table->date('date_of_birth')->nullable();
            $table->text('address');
            $table->string('province_code', 36);
            $table->string('city_code', 36);
            $table->string('district_code', 36);
            $table->string('zip_code', 36);
            $table->string('national_id', 36);
            $table->text('national_id_file');
            $table->string('npwp', 36);
            $table->text('npwp_file');
            $table->string('status', 36)->nullable();
            $table->string('bank_id')->nullable()->index('bank_id');
            $table->string('account_name')->nullable();
            $table->string('account_no')->nullable();
            $table->string('action_by', 36)->nullable();
            $table->text('action_notes')->nullable();
            $table->dateTime('action_date')->nullable();
            $table->dateTime('created_date')->nullable()->useCurrent();
            $table->string('created_by')->nullable()->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_registration');
    }
};
