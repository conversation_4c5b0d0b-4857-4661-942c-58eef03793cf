<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_reseller', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('reseller_id', 36)->index('index_reseller_id');
            $table->string('name', 36)->nullable();
            $table->string('gender');
            $table->date('date_of_birth')->nullable();
            $table->string('phone_number', 20);
            $table->string('email')->index('index_reseller_email');
            $table->string('address', 160)->nullable();
            $table->string('province_code', 20);
            $table->string('city_code', 20);
            $table->string('district_code', 20);
            $table->string('zip_code', 20);
            $table->string('national_id')->nullable();
            $table->text('national_id_file')->nullable();
            $table->string('npwp', 20);
            $table->text('npwp_file')->nullable();
            $table->boolean('is_active')->default(false);
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_reseller');
    }
};
