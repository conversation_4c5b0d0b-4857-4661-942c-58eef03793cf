<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_reseller_va', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('reseller_id');
            $table->string('reference_no');
            $table->string('bin_no')->nullable();
            $table->string('bin_name')->nullable();
            $table->string('virtual_account_no')->nullable();
            $table->string('virtual_account_name')->nullable();
            $table->string('currency')->nullable();
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone_number')->nullable();
            $table->string('national_id')->nullable();
            $table->string('status_code')->nullable();
            $table->string('status')->nullable();
            $table->dateTime('expired_date')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('INTEGRATION');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('INTEGRATION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_reseller_va');
    }
};
