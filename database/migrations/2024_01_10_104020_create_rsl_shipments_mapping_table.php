<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_shipments_mapping', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->unique('indexes_shipment_rsl');
            $table->string('service_code', 100);
            $table->string('service_name', 100);

            $table->primary(['id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_shipments_mapping');
    }
};
