<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_transactions', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('reference_name')->nullable();
            $table->string('reference_id')->nullable();
            $table->string('request_no')->nullable();
            $table->string('discount_type')->nullable();
            $table->string('discount_id')->nullable();
            $table->string('type');
            $table->string('status');
            $table->string('order_status');
            $table->decimal('amount', 20)->default(0);
            $table->text('remarks')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->default('SYSTEM');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_transactions');
    }
};
