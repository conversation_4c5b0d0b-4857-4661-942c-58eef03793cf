<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_transfer_method', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('min_amount', 16)->default(0);
            $table->decimal('max_amount', 16)->default(0);
            $table->decimal('fees', 16)->default(0);
            $table->integer('sequence_no');
            $table->boolean('is_active')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_transfer_method');
    }
};
