<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_transporters', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->integer('transporter_id')->nullable();
            $table->string('name');
            $table->string('service', 100)->nullable();
            $table->string('service_code', 100)->nullable();
            $table->string('status')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_transporters');
    }
};
