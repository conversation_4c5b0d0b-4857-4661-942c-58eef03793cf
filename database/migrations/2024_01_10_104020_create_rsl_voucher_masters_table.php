<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_voucher_masters', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->string('name');
            $table->string('code_prefix')->nullable();
            $table->integer('code_length')->nullable();
            $table->string('category')->nullable();
            $table->string('type')->nullable();
            $table->decimal('amount', 20)->nullable();
            $table->integer('count')->nullable();
            $table->dateTime('start_date')->nullable();
            $table->dateTime('end_date')->nullable();
            $table->string('status')->nullable();
            $table->boolean('is_active')->default(false);
            $table->boolean('is_void')->default(false);
            $table->boolean('is_multiple')->default(false);
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('INTEGRATION');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('INTEGRATION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_voucher_masters');
    }
};
