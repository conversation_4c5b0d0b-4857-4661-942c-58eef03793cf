<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rsl_vouchers', function (Blueprint $table) {
            $table->bigInteger('id')->primary();
            $table->bigInteger('voucher_master_id')->nullable()->index('voucher_master_id');
            $table->bigInteger('promotion_id')->nullable();
            $table->string('location_code')->nullable();
            $table->string('order_number')->nullable();
            $table->string('code');
            $table->string('category')->nullable();
            $table->string('type')->nullable();
            $table->boolean('is_used')->nullable()->default(false);
            $table->dateTime('used_at')->nullable();
            $table->dateTime('start_date')->useCurrent();
            $table->dateTime('end_date')->useCurrent();
            $table->string('status')->nullable();
            $table->decimal('amount', 16)->nullable();
            $table->decimal('used_amount', 16)->nullable();
            $table->decimal('expired_amount', 16)->nullable();
            $table->decimal('remaining_amount', 20)->default(0);
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('INTEGRATION');
            $table->dateTime('modified_date')->nullable()->useCurrent();
            $table->string('modified_by')->nullable()->default('INTEGRATION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('rsl_vouchers');
    }
};
