<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sales', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()');
            $table->string('sales_id')->unique('indexes_sales');
            $table->string('position_id', 36)->nullable();
            $table->string('position_name')->nullable();
            $table->string('sales_name')->nullable();
            $table->string('regional')->nullable();
            $table->decimal('target_sales', 16)->nullable();
            $table->string('phone_number', 20)->nullable();
            $table->string('sap_username')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['sales_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sales');
    }
};
