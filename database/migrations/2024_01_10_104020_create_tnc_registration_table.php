<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tnc_registration', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->text('section_name');
            $table->integer('sequence_no');
            $table->text('value');
            $table->boolean('is_active')->default(true);
            $table->dateTime('created_date')->nullable()->default('curdate()');
            $table->string('created_by')->nullable()->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date')->nullable()->default('curdate()');
            $table->string('modified_by')->nullable()->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tnc_registration');
    }
};
