<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transportation_zone', function (Blueprint $table) {
            $table->string('zone_code')->primary();
            $table->string('description')->nullable();
            $table->dateTime('created_date')->nullable();
            $table->string('created_by')->nullable();
            $table->dateTime('modified_date')->nullable();
            $table->string('modified_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transportation_zone');
    }
};
