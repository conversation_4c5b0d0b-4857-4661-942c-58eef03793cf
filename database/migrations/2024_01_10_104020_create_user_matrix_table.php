<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_matrix', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('user_id', 36)->nullable();
            $table->string('roles_id', 36)->nullable();
            $table->string('business_unit_id', 36)->nullable();
            $table->integer('tier_level')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('_utf8mb4\\\\\'\'SYSTEM\\\\\'\'');
            $table->dateTime('modified_date')->nullable();
            $table->string('modified_by')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_matrix');
    }
};
