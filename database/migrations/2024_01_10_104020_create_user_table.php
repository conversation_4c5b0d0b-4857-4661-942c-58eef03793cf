<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user', function (Blueprint $table) {
            $table->string('user_id', 36)->default('uuid()')->unique('indexes_user');
            $table->string('reference_id', 36)->nullable();
            $table->string('reference_object', 20);
            $table->string('username');
            $table->string('email')->nullable();
            $table->string('name')->nullable();
            $table->string('password');
            $table->tinyInteger('is_change_password')->default(0);
            $table->rememberToken();
            $table->dateTime('expired_token')->nullable();
            $table->tinyInteger('is_active')->nullable()->default(1);
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by')->default('SYSTEM');

            $table->primary(['user_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user');
    }
};
