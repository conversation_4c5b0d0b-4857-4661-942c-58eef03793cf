<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('xendit_accounts', function (Blueprint $table) {
            $table->string('id', 36)->default('uuid()')->primary();
            $table->string('business_id', 36)->nullable();
            $table->string('account_id', 36)->nullable();
            $table->string('account_name');
            $table->string('email');
            $table->string('type', 25);
            $table->char('country', 5)->nullable();
            $table->string('status', 10)->nullable();
            $table->text('suspended_reason')->nullable();
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by')->default('_utf8mb4\\\\\'\'INTEGRATION\\\\\'\'');
            $table->dateTime('modified_ate');
            $table->string('modified_by');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('xendit_accounts');
    }
};
