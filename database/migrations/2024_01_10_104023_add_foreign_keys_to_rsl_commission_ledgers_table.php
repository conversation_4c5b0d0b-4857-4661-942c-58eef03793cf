<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('rsl_commission_ledgers', function (Blueprint $table) {
            $table->foreign(['commission_id'], 'rsl_commission_ledgers_ibfk_1')->references(['id'])->on('rsl_commission')->onUpdate('NO ACTION')->onDelete('NO ACTION');
            $table->foreign(['transaction_id'], 'rsl_commission_ledgers_ibfk_2')->references(['id'])->on('rsl_transactions');
            $table->foreign(['reseller_id'], 'rsl_commission_ledgers_ibfk_3')->references(['reseller_id'])->on('rsl_reseller')->onUpdate('NO ACTION')->onDelete('NO ACTION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('rsl_commission_ledgers', function (Blueprint $table) {
            $table->dropForeign('rsl_commission_ledgers_ibfk_1');
            $table->dropForeign('rsl_commission_ledgers_ibfk_2');
            $table->dropForeign('rsl_commission_ledgers_ibfk_3');
        });
    }
};
