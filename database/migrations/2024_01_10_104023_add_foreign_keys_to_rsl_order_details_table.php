<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('rsl_order_details', function (Blueprint $table) {
            $table->foreign(['order_header_id'], 'rsl_order_details_ibfk_1')->references(['id'])->on('rsl_order_headers')->onUpdate('NO ACTION')->onDelete('NO ACTION');
            $table->foreign(['article_id'], 'rsl_order_details_ibfk_2')->references(['article'])->on('article')->onUpdate('NO ACTION')->onDelete('NO ACTION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('rsl_order_details', function (Blueprint $table) {
            $table->dropForeign('rsl_order_details_ibfk_1');
            $table->dropForeign('rsl_order_details_ibfk_2');
        });
    }
};
