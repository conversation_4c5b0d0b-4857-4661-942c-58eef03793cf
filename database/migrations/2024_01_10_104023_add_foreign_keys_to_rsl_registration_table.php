<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('rsl_registration', function (Blueprint $table) {
            $table->foreign(['bank_id'], 'rsl_registration_ibfk_1')->references(['id'])->on('rsl_bank')->onUpdate('NO ACTION')->onDelete('NO ACTION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('rsl_registration', function (Blueprint $table) {
            $table->dropForeign('rsl_registration_ibfk_1');
        });
    }
};
