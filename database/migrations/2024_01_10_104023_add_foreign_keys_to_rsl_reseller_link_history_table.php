<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('rsl_reseller_link_history', function (Blueprint $table) {
            $table->foreign(['link_id'], 'rsl_reseller_link_history_ibfk_1')->references(['id'])->on('rsl_reseller_link')->onUpdate('NO ACTION')->onDelete('NO ACTION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('rsl_reseller_link_history', function (Blueprint $table) {
            $table->dropForeign('rsl_reseller_link_history_ibfk_1');
        });
    }
};
