<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tnc_registration_detail', function (Blueprint $table) {
            $table->foreign(['tnc_id'], 'tnc_registration_detail_ibfk_1')->references(['id'])->on('tnc_registration')->onUpdate('NO ACTION')->onDelete('NO ACTION');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tnc_registration_detail', function (Blueprint $table) {
            $table->dropForeign('tnc_registration_detail_ibfk_1');
        });
    }
};
