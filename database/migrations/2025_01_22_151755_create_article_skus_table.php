<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateArticleSkusTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('article_skus', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('sku_id')->index('article');
            $table->string('sku_code_c')->nullable();
            $table->unsignedBigInteger('stock')->default(0);
            $table->unsignedBigInteger('mock')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('article_skus');
    }
}
