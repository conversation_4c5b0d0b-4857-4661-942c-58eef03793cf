<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateBulkDraftTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('bulk_draft', function (Blueprint $table) {
            $table->string('cart_id', 36)->default(DB::raw('(UUID())'));
            $table->string('bulk_draft_id', 36)->default(DB::raw('(UUID())'))->unique();
            $table->string('article');
            $table->integer('qty');
            $table->integer('stock');
            $table->dateTime('created_date')->useCurrent();
            $table->string('created_by', 255)->default('SYSTEM');
            $table->dateTime('modified_date')->useCurrent();
            $table->string('modified_by', 255)->default('SYSTEM');
            $table->timestamps();

            $table->unique(['cart_id', 'article']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('bulk_draft');
    }
}