<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveUniqueConstraintInCartDetail extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('cart_detail', function (Blueprint $table) {
            $table->dropUnique(['cart_id', 'article', 'is_custom']);
            $table->unique(['cart_id', 'article', 'is_custom', 'is_available']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('cart_detail', function (Blueprint $table) {
            $table->dropUnique(['cart_id', 'article', 'is_custom', 'is_available']);
            $table->unique(['cart_id', 'article', 'is_custom']);
        });
    }
}
