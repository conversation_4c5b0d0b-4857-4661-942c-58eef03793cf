<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddProvinceCityDistrictToCustomersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->string('npwp_province')->default('')->after('npwp_file');
            $table->string('npwp_province_code', 20)->nullable()->after('npwp_province');
            $table->string('npwp_city_code', 20)->nullable()->after('npwp_city');
            $table->string('npwp_district')->default('')->after('npwp_city_code');
            $table->string('npwp_district_code', 20)->nullable()->after('npwp_district');
            $table->string('npwp_zip_code', 20)->nullable()->after('npwp_district_code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('customers', function (Blueprint $table) {
            $table->dropColumn([
                'npwp_province',
                'npwp_province_code',
                'npwp_city_code',
                'npwp_district',
                'npwp_district_code',
                'npwp_zip_code',
            ]);
        });
    }
}
