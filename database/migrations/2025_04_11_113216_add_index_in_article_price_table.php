<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexInArticlePriceTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('article_price', function (Blueprint $table) {
            $table->index(['amount'], 'idx_price_filter');
            $table->index(['sku_code_c'], 'idx_sku_code_c');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('article_price', function (Blueprint $table) {
            //
        });
    }
}