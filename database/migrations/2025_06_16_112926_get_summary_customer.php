<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class GetSummaryCustomer extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::unprepared("
        DROP PROCEDURE IF EXISTS get_summary_customer;

        CREATE PROCEDURE get_summary_customer(
            IN date_from DATETIME,
            IN date_to DATETIME,
            IN channel_json JSON,
            IN customer_ids VARCHAR(255)
        )
        BEGIN
            DECLARE days_range INT;
            SET days_range = DATEDIFF(date_to, date_from);

            WITH current_period AS (
                SELECT
                    COALESCE(SUM(CASE WHEN sales_order_no IS NULL THEN od_qty ELSE do_qty END), 0) AS total_sales,
                    COUNT(DISTINCT order_no) AS transaction_totals,
                    COALESCE(ROUND(SUM(CASE
                        WHEN sales_order_no IS NULL THEN
                            CASE 
                                WHEN sub_total IS NULL OR total_discount IS NULL OR total IS NULL THEN sub_total
                                ELSE sub_total - (sub_total * total_discount / NULLIF(total, 0)) 
                            END
                        ELSE
                            CASE 
                                WHEN nett_price IS NULL THEN
                                    CASE 
                                        WHEN do_discount IS NULL OR do_total IS NULL THEN do_qty * price
                                        ELSE do_qty * price - (do_qty * price * do_discount / NULLIF(do_total, 0)) 
                                    END
                                ELSE nett_price 
                            END
                    END)), 0) AS transaction_amount
                FROM wholesales_do_detail
                WHERE created_date BETWEEN date_from AND date_to
                  AND JSON_CONTAINS(channel_json, JSON_QUOTE(distribution_channel))
                  AND FIND_IN_SET(customer_id, customer_ids)
            ),
            previous_period AS (
                SELECT
                    COALESCE(SUM(CASE WHEN sales_order_no IS NULL THEN od_qty ELSE do_qty END), 0) AS total_sales,
                    COUNT(DISTINCT order_no) AS transaction_totals,
                    COALESCE(ROUND(SUM(CASE
                        WHEN sales_order_no IS NULL THEN
                            CASE 
                                WHEN sub_total IS NULL OR total_discount IS NULL OR total IS NULL THEN sub_total
                                ELSE sub_total - (sub_total * total_discount / NULLIF(total, 0)) 
                            END
                        ELSE
                            CASE 
                                WHEN nett_price IS NULL THEN
                                    CASE 
                                        WHEN do_discount IS NULL OR do_total IS NULL THEN do_qty * price
                                        ELSE do_qty * price - (do_qty * price * do_discount / NULLIF(do_total, 0)) 
                                    END
                                ELSE nett_price 
                            END
                    END)), 0) AS transaction_amount
                FROM wholesales_do_detail
                WHERE created_date BETWEEN DATE_SUB(date_from, INTERVAL days_range DAY) 
                                      AND DATE_SUB(date_to, INTERVAL days_range DAY)
                  AND JSON_CONTAINS(channel_json, JSON_QUOTE(distribution_channel))
                  AND FIND_IN_SET(customer_id, customer_ids)
            )
            SELECT 
                c.total_sales,
                p.total_sales AS previous_total_sales,
                (c.total_sales - p.total_sales) AS total_sales_diff,
                ROUND((c.total_sales - p.total_sales) / NULLIF(p.total_sales, 0) * 100, 0) AS total_sales_change_percentage,

                c.transaction_totals,
                p.transaction_totals AS previous_transaction_totals,
                (c.transaction_totals - p.transaction_totals) AS transaction_totals_diff,
                ROUND((c.transaction_totals - p.transaction_totals) / NULLIF(p.transaction_totals, 0) * 100, 0) AS transaction_totals_change_percentage,

                c.transaction_amount,
                p.transaction_amount AS previous_transaction_amount,
                (c.transaction_amount - p.transaction_amount) AS transaction_amount_diff,
                ROUND((c.transaction_amount - p.transaction_amount) / NULLIF(p.transaction_amount, 0) * 100, 0) AS transaction_amount_change_percentage,

                ROUND(c.transaction_amount / NULLIF(c.transaction_totals, 0), 0) AS transaction_average,
                ROUND(p.transaction_amount / NULLIF(p.transaction_totals, 0), 0) AS previous_transaction_average,
                ROUND(c.transaction_amount / NULLIF(c.transaction_totals, 0), 0) - COALESCE(p.transaction_amount / NULLIF(p.transaction_totals, 0), 0) AS transaction_average_diff,
                ROUND((c.transaction_amount / NULLIF(c.transaction_totals, 0) - p.transaction_amount / NULLIF(p.transaction_totals, 0)) 
                        / NULLIF(p.transaction_amount / NULLIF(p.transaction_totals, 0), 0) * 100, 0) AS transaction_average_change_percentage
            FROM current_period c, previous_period p;
        END
    ");
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}