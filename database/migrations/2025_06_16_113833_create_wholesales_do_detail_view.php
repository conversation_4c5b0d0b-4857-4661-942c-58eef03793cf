<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateWholesalesDoDetailView extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $dbName = DB::connection('mysql')->getDatabaseName();

        DB::unprepared("
            DROP VIEW IF EXISTS wholesales_do_detail;

            CREATE VIEW wholesales_do_detail AS
            SELECT
                oh.created_date AS created_date,
                oh.customer_id AS customer_id,
                oh.order_no AS order_no,
                oh.sales_order_no AS sales_order_no,
                oh.order_status AS order_status,
                oh.distribution_channel AS distribution_channel,
                od.article_id AS od_article,
                od.qty AS od_qty,
                dod.article AS do_article,
                IF(dod.issued_qty IS NOT NULL, dod.issued_qty, dod.qty) AS do_qty,
                od.price AS price,
                od.total AS od_total,
                od.sub_total AS sub_total,
                od.additional_discount AS additional_discount,
                od.primary_discount AS primary_discount,
                oh.total_discount AS total_discount,
                oh.total AS total,
                oh.total_tax AS total_tax,
                oh.total_nett AS total_nett,
                do.total AS do_total,
                do.discount AS do_discount,
                invd.reference_so AS reference_so,
                invd.nett_price AS nett_price,
                invd.gross_price AS gross_price
            FROM
                `{$dbName}`.order_detail od
                LEFT JOIN `{$dbName}`.order_header oh ON oh.order_no = od.order_no
                LEFT JOIN `{$dbName}`.delivery_order do ON do.sales_order_no = oh.sales_order_no
                LEFT JOIN `{$dbName}`.delivery_order_detail dod ON do.delivery_order_no = dod.delivery_order_no AND od.article_id = dod.article
                LEFT JOIN `{$dbName}`.invoice_detail invd ON invd.reference_so = oh.sales_order_no AND od.article_id = invd.article
            WHERE
                oh.order_status IN ('Baru', 'Pembayaran', 'Diproses', 'Siap Dikirim', 'Dikirim', 'Selesai');
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wholesales_do_detail_view');
    }
}