<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWholesalesDownloadCsvView extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::unprepared("
            DROP VIEW IF EXISTS wholesales_download_csv;
            
            SELECT 
            oh.bill_to AS nama_akun,
            oh.customer_id AS customer_id,
            IF(
                c.top_days IS NULL OR c.top_days = '0' OR c.top_days = 'Cash',
                'toko_cash',
                'toko_tempo'
            ) AS tipe_toko,
            oh.ship_to AS nama_toko,
            oh.created_date AS tanggal_pesan,
            oh.order_status AS status_pesanan,
            oh.order_no AS no_order,
            oh.sales_order_no AS ref_order,
            do.delivery_order_no AS no_dn,
            invd.invoice_no AS no_billing,
            od.article_id AS article,
            a.article_description AS article_description,
            od.price AS price,
            IF(
                dod.issued_qty IS NOT NULL,
                dod.issued_qty,
                IF(dod.qty IS NOT NULL, dod.qty, od.qty)
            ) AS qty_order,
            (
                IF(
                    inv.invoice_no IS NOT NULL,
                    invd.gross_price,
                    (
                        IF(
                            dod.issued_qty IS NOT NULL,
                            dod.issued_qty,
                            IF(dod.qty IS NOT NULL, dod.qty, od.qty)
                        ) * od.price
                    )
                )
                -
                ROUND(
                    CASE 
                        WHEN oh.sales_order_no IS NULL THEN
                            CASE 
                                WHEN od.sub_total IS NULL OR oh.total_discount IS NULL OR oh.total IS NULL THEN od.sub_total
                                ELSE (od.sub_total - ((od.sub_total * oh.total_discount) / NULLIF(oh.total, 0)))
                            END
                        ELSE
                            CASE 
                                WHEN invd.nett_price IS NULL THEN
                                    CASE 
                                        WHEN do.discount IS NULL OR do.total IS NULL THEN
                                            (
                                                CASE 
                                                    WHEN dod.issued_qty IS NOT NULL THEN dod.issued_qty
                                                    WHEN dod.qty IS NOT NULL THEN dod.qty
                                                    ELSE od.qty
                                                END * od.price
                                            )
                                        ELSE
                                            (
                                                (
                                                    CASE 
                                                        WHEN dod.issued_qty IS NOT NULL THEN dod.issued_qty
                                                        WHEN dod.qty IS NOT NULL THEN dod.qty
                                                        ELSE od.qty
                                                    END * od.price
                                                )
                                                -
                                                (
                                                    (
                                                        (
                                                            CASE 
                                                                WHEN dod.issued_qty IS NOT NULL THEN dod.issued_qty
                                                                WHEN dod.qty IS NOT NULL THEN dod.qty
                                                                ELSE od.qty
                                                            END * od.price
                                                        ) * do.discount
                                                    ) / NULLIF(do.total, 0)
                                                )
                                            )
                                    END
                                ELSE invd.nett_price
                            END
                    END,
                0)
            ) AS diskon,
            a.lvl3_description AS item_product,
            a.product_style AS product_category,
            oh.sales_name AS nama_sales,
            do.good_issue_date AS tanggal_gi,
            inv.billing_date AS tanggal_billing,
            inv.status AS status_tagihan,
            IF(
                inv.invoice_no IS NOT NULL,
                invd.gross_price,
                (
                    IF(
                        dod.issued_qty IS NOT NULL,
                        dod.issued_qty,
                        IF(dod.qty IS NOT NULL, dod.qty, od.qty)
                    ) * od.price
                )
            ) AS gross,
            ROUND(
                CASE 
                    WHEN oh.sales_order_no IS NULL THEN
                        CASE 
                            WHEN od.sub_total IS NULL OR oh.total_discount IS NULL OR oh.total IS NULL THEN od.sub_total
                            ELSE (od.sub_total - ((od.sub_total * oh.total_discount) / NULLIF(oh.total, 0)))
                        END
                    ELSE
                        CASE 
                            WHEN invd.nett_price IS NULL THEN
                                CASE 
                                    WHEN do.discount IS NULL OR do.total IS NULL THEN
                                        (
                                            CASE 
                                                WHEN dod.issued_qty IS NOT NULL THEN dod.issued_qty
                                                WHEN dod.qty IS NOT NULL THEN dod.qty
                                                ELSE od.qty
                                            END * od.price
                                        )
                                    ELSE
                                        (
                                            (
                                                CASE 
                                                    WHEN dod.issued_qty IS NOT NULL THEN dod.issued_qty
                                                    WHEN dod.qty IS NOT NULL THEN dod.qty
                                                    ELSE od.qty
                                                END * od.price
                                            )
                                            -
                                            (
                                                (
                                                    CASE 
                                                        WHEN dod.issued_qty IS NOT NULL THEN dod.issued_qty
                                                        WHEN dod.qty IS NOT NULL THEN dod.qty
                                                        ELSE od.qty
                                                    END * od.price
                                                ) * do.discount / NULLIF(do.total, 0)
                                            )
                                        )
                                END
                            ELSE invd.nett_price
                        END
                END,
            0) AS nett
        FROM order_detail od
        LEFT JOIN order_header oh ON oh.order_no = od.order_no
        LEFT JOIN delivery_order do ON do.sales_order_no = oh.sales_order_no
        LEFT JOIN delivery_order_detail dod ON do.delivery_order_no = dod.delivery_order_no AND od.article_id = dod.article
        LEFT JOIN invoice_detail invd ON invd.reference_so = oh.sales_order_no AND od.article_id = invd.article
        LEFT JOIN invoice inv ON inv.sales_order_no = oh.sales_order_no
        LEFT JOIN article a ON a.article = od.article_id
        LEFT JOIN customers c ON c.customer_id = oh.customer_id
        WHERE oh.distribution_channel = 'WHOLESALES'
        ORDER BY oh.created_date DESC;
        
        
        
        
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wholesales_download_csv_view');
    }
}