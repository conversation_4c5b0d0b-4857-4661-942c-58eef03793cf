<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Customer;
use App\Models\CustomerShipment;
use App\Models\Roles;
use App\Models\User;
use Database\Factories\UserFactory;

class CustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $customer = Customer::factory()->state(function (array $attributes){
            return [
                'email' => '<EMAIL>'
            ];
        })->create();
        $shipment = CustomerShipment::factory()->count(3)->create();
        Roles::factory(1)->customer_wholesales()->create()->each(function (Roles $roles) {
            User::factory()->state(function (array $attributes){
                return [
                    'username' => '<EMAIL>',
                    'reference_id' => 1
                ];
            })
            ->create()->each(function (User $user) use ($roles) {
                $user->roles()->saveMany([
                    $roles,
                ]);
            });
        });
    }
}
