version: "3.7"
services:
  ws-eiger:
    image: quay.io/soketi/soketi:1.4-16-debian
    container_name: ws-eiger
    networks:
      - "careom"
    restart: always
    environment:
      SOKETI_DB_REDIS_HOST: redis-eiger
      SOKETI_DB_REDIS_PORT: ${REDIS_PORT}
      SOKETI_DB_REDIS_DB: 1
      SOKETI_DB_REDIS_PASSWORD: ${REDIS_PASSWORD}
      SOKETI_DB_REDIS_KEY_PREFIX: ws_
      SOKETI_USER_AUTHENTICATION_TIMEOUT: 1800000
    ports:
      - "6001:6001"
      - "9601:9601"
  redis-eiger:
    container_name: redis-eiger
    image: redis:latest
    networks:
      - "careom"
    command: ["redis-server", "/etc/redis/redis.conf"]
    volumes:
      - ./redis.conf:/etc/redis/redis.conf
    ports:
      - "6379:6379"
  rabbitmq:
    image: rabbitmq:management
    container_name: rabbitmq-eiger
    networks:
      - "careom"
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
      - RABBITMQ_DEFAULT_VHOST=${RABBITMQ_VHOST}
    volumes:
      - ./docker/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
      - ./docker/rabbitmq/definitions.json:/etc/rabbitmq/definitions.json:ro
    ports:
      - "5672:5672"
      - "15672:15672"
  wholesales-be:
    container_name: wholesales-be-eiger
    networks:
      - "careom"
    build:
      context: ./docker/fpm
      dockerfile: Dockerfile
    working_dir: /var/www/html
    volumes:
      - ./:/var/www/html
      - ./docker/fpm/21-custom.ini:/usr/local/etc/php/conf.d/docker-custom.ini
    links:
      - "redis-eiger"
      - "ws-eiger"
      - "rabbitmq"
  wholesales-be-nginx:
    container_name: wholesales-be-nginx-eiger
    networks:
      - "careom"
    build:
      context: ./docker/nginx
      dockerfile: Dockerfile
    working_dir: /var/www/html
    volumes_from:
      - wholesales-be
    ports:
      - 8100:80
networks:
  careom:
    name: "careom"
    driver: bridge
