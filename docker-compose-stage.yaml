version: "3.7"
services:
  # web-socket
  ws-eiger:
    image: quay.io/soketi/soketi:1.4-16-debian
    container_name: ws-eiger
    networks:
      - "careom-wholesales-backend-staging"
    restart: always
    environment:
      SOKETI_DB_REDIS_HOST: ${REDIS_HOST}
      SOKETI_DB_REDIS_PORT: ${REDIS_PORT}
      SOKETI_DB_REDIS_DB: 1
      SOKETI_DB_REDIS_PASSWORD: ${REDIS_PASSWORD}
      SOKETI_DB_REDIS_KEY_PREFIX: ws_
      SOKETI_USER_AUTHENTICATION_TIMEOUT: 1800000
    ports:
      - "6001:6001"
      - "9601:9601"
  # redis-server
  redis-server:
    container_name: redis-server
    image: redis:latest
    networks:
      - "careom-wholesales-backend-staging"
    command: ["redis-server", "/etc/redis/redis.conf"]
    volumes:
      - ./docker/redis/redis.conf:/etc/redis/redis.conf
    ports:
      - "6379:6379"
  # rabbitmq-server
  rabbitmq-server:
    image: rabbitmq:management
    container_name: rabbitmq-server
    networks:
      - "careom-wholesales-backend-staging"
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
      - RABBITMQ_DEFAULT_VHOST=${RABBITMQ_VHOST}
    volumes:
      - ./docker/rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
      - ./docker/rabbitmq/definitions.json:/etc/rabbitmq/definitions.json:ro
    ports:
      - "5672:5672"
      - "15672:15672"
  # wholesales-backend
  wholesales-backend:
    container_name: wholesales-backend
    networks:
      - "careom-wholesales-backend-staging"
    build:
      context: .
      dockerfile: .docker-config/Dockerfile
    image: wholesales-backend:latest
    ports:
      - "8100:80"

networks:
  careom-wholesales-backend-staging:
    name: "careom-wholesales-backend-staging"
    driver: bridge
