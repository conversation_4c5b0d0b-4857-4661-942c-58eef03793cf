FROM php:8.1-fpm-bookworm

RUN apt-get update && apt-get install -y --no-install-recommends \
        zlib1g-dev \
        libxml2-dev \
        librabbitmq-dev \
        libzip-dev \
        libonig-dev \
        supervisor \
    && docker-php-ext-install \
        pdo_mysql \
        zip \
        exif \
        sockets \
    && pecl install amqp \
    && pecl install redis \
    && docker-php-ext-enable amqp \
    && docker-php-ext-enable redis \
    && docker-php-ext-enable opcache
RUN  mkdir -p /var/www/html
RUN chown -R www-data:www-data /var/www/html
WORKDIR /var/www/html
RUN wget https://github.com/DataDog/dd-trace-php/releases/latest/download/datadog-setup.php -O datadog-setup.php
RUN php datadog-setup.php --php-bin=all --enable-appsec --enable-profiling
COPY supervisord.production.conf /etc/supervisord.conf
COPY 21-custom.ini /usr/local/etc/php/conf.d/docker-custom.ini
ENTRYPOINT ["/usr/bin/supervisord", "-n", "-c", "/etc/supervisord.conf"]

