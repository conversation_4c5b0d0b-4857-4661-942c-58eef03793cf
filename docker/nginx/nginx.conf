events {
  worker_connections  2048;
}

http {
    server {
        listen 80;
        index index.php index.html;
        root /var/www/html/public;

        location / {
            try_files $uri /index.php?$args;
        }

        location ~ \.css {
            add_header Content-Type text/css;
        }

        location ~ \.js {
            add_header Content-Type application/x-javascript;
        }

        location ~ ^/(assets/|css/|js/|index.html) {
            root /var/www/html/public;
            index index.html;
            access_log off;
        }

        location ~ \.php$ {
            fastcgi_split_path_info ^(.+\.php)(/.+)$;
            fastcgi_pass wholesales-be:9000;
            fastcgi_index index.php;
            include fastcgi_params;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
            fastcgi_param PATH_INFO $fastcgi_path_info;
        }
    }
}