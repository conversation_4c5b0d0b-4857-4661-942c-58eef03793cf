
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/html">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <style>
        .table-content tbody tr  td {
            padding: 20px
        }
        body {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 9pt;
        }

        .tableHeader {
            background: #cd5c5c;
            color: #fff;
            /* display: table-row; */
            font-weight: bold;
            font-size: 10px;
            height: 50px
        }

        .row {
            display: table-row;
            font-size: 10px;
            overflow: hidden;
        }

        .column {
            display: table-cell;
            padding: 6px 6px 6px 6px;
            font-size: 10px;
            overflow: hidden;
        }

        div.header {
            position: running(header);
            font-family: "Poppins";
            display: inline-table;
            height: auto;
            margin-left: auto;
            margin-right: auto;
            width: 100%;
            vertical-align: middle;
            padding-top: 10px;
        }

        .logo {
            width: 120px;
        }

        .logo-left,
        .title,
        .logo-right {
            display: table-cell;
        }

        .header>.logo-left {
            vertical-align: middle;
            text-align: left;
            width: 20%;
        }

        .header>.title {
            text-align: center;
            width: 60%;
        }

        .header>.logo-right {
            vertical-align: middle;
            text-align: right;
            width: 20%;
        }

        .logo-left>.logo {
            width: 160px;
            padding-left: 4px;
        }

        .logo-right>.logo {
            width: 120px;
            padding-right: 4px;
        }

        .title>h1 {
            margin: 0;
            font-weight: normal;
            font-size: 35px;
            padding-top: 10px;
        }

        tr {
            page-break-inside: avoid;
            page-break-after: auto
        }

        .table1 thead {
            background-color: #b22222;
            color: white;
        }

        .table1 tbody tr:nth-child(odd) {
            background-color: #C0C0C0;
        }

        .table1 tbody tr:nth-child(even) {
            background-color: #D3D3D3;
        }


        .table2 table {
            border: 0px;
            border-spacing: 0;
            border-collapse: collapse;
            width: 100%
        }


        .image-attachment img {
            width: auto;
            height: 530px;
            margin-left: auto;
            margin-right: auto;
            padding: 10px;
        }

        .image-attachment .first-data-img img {
            height: 480px !important;
        }


        .additional-attachment {
            display: table-cell;
            width: 100%;
            box-sizing: border-box;
        }

        .additional-attachment span {
            text-align: left;
        }

        .bullet ol {
            list-style: none;
            padding: 0;
        }

        .bullet ol li {
            counter-increment: step-counter;
        }

        .bullet ol li:before {
            content: counter(step-counter)". ";
        }

        div.pagebr {
            page-break-inside: avoid;
            page-break-after: auto
        }

        .mce-content-body-value {
            overflow-wrap: break-word;
            word-wrap: break-word;
            margin-bottom: 10px;
        }

        .mce-content-body-value div {
            line-height: 1.4;
            margin: 1rem;
        }

        .mce-content-body-value table {
            border-collapse: collapse;
        }

        .mce-content-body-value table[border]:not([border="0"]):not([style*="border-color"]) td,
        .mce-content-body-value table[border]:not([border="0"]):not([style*="border-color"]) th {
            border-color: #ccc;
        }

        .mce-content-body-value table[border]:not([border="0"]):not([style*="border-style"]) td,
        .mce-content-body-value table[border]:not([border="0"]):not([style*="border-style"]) th {
            border-style: solid;
        }

        .mce-content-body-value table:not([cellpadding]) td,
        .mce-content-body-value table:not([cellpadding]) th {
            padding: .4rem;
        }

        .mce-content-body-value ul {
            margin-top: 16pt;
            margin-bottom: 16pt;

        }

        .mce-content-body-value li ul {
            margin-top: 0;
            margin-bottom: 0;
        }

        td {
            padding-left: 2px;
            padding-right: 2px;
        }

        tr.border-bottom td {
            border-bottom: 1px solid #e7e6e6;
        }

        tr.border-bottom-th td {
            border-bottom: 2px solid #e7e6e6;
        }

        tr.border-top-th td {
            border-top: 2px solid #e7e6e6;
        }

        td.minus {
            color: red;
        }

        td.null {
            content: "-";
        }


        .border-left-white-2 {
            border-left: solid white 2px;
        }

        .h--50 {
            height: 50px;
        }
    </style>
</head>

<body>
    <table style="width: 100%;">
        <tr>
            <td>
                <div class="header">
                    <div class="title">
                        <h1>INVOICE</h1>
                        <p>
                            PT. Eigerindo Multi Produk Industri <br>
                            NPWP: 01.691.981.3-441.000
                        </p>
                    </div>
                </div>
            </td>
        </tr>
        <tr>
            <!-- HEADER -->
            <td>
                <div style="padding-left: 20px; padding-right: 20px;">
                    <table style="width: 100%;">
                        <tr>
                            <td style="width: 70%;">
                                <table style="width: 100%;">
                                    <tr>
                                        <td style="width: 30%; padding-top: 10px;">Customer Number</td>
                                        <td style="padding-left: 10px; padding-top: 10px;">{{ $invoice->customer_id  }}</td>
                                    </tr>
                                    <tr>
                                        <td style="width: 30%; vertical-align: baseline; padding-top: 10px;">Customer Name</td>
                                        <td style="padding-left: 10px; padding-top: 10px;">
                                            {{ $invoice->owner_name  }} <br>
                                            <div>
                                                {{ $invoice->address  }}, <br>
                                                {{ $invoice->city  }}, {{ $invoice->province  }}, <br>
                                                {{ $invoice->zip_code  }}
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 30%; padding-top: 10px;">NPWP</td>
                                        <td style="padding-left: 10px; padding-top: 10px;">{{ $invoice->npwp  }}</td>
                                    </tr>
                                </table>
                            </td>
                            <td style="width: 30%;">
                                <table style="width: 100%; ">
                                    <tr>
                                        <td style="width: 50%; padding-top: 5px;">Invoice No.</td>
                                        <td style="padding-left: 10px; padding-top: 5px;">{{ $invoice->invoice_no  }}</td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%; padding-top: 5px;">Invoice Date</td>
                                        <td style="padding-left: 10px; padding-top: 5px;">{{ date('d M Y', strtotime($invoice->created_date)) }}</td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%; padding-top: 5px;">Due Date</td>
                                        <td style="padding-left: 10px; padding-top: 5px;">{{ date('d M Y', strtotime($invoice->due_date)) }}</td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%; padding-top: 5px;">{{ $invoice->po_no=="" || $invoice->po_no==null ? '' :  'PO Number' }}</td>
                                        <td style="padding-left: 10px; padding-top: 5px;">{{ $invoice->po_no  }}</td>
                                    </tr>
                                </table>
                            </td>
                        </tr>

                    </table>
                </div>
            </td>
        </tr>
        <tr>
            <!-- LIST PRODUCT -->
            <td>
                <div style=" padding-right: 30px;">
                    <table class="table-content" style="width: 100%;">
                        <thead>
                            <tr>
                                <th
                                    style="padding-top: 10px; padding-bottom: 10px; width: 20px; border-bottom: 1px solid black; border-top: 1px solid black;">
                                    No
                                </th>
                                <th
                                    style="width: 35%; text-align: start; padding-left: 20px; padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid black; border-top: 1px solid black;">
                                    Article No Description
                                </th>
                                <th
                                    style="width: 20%; padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid black; border-top: 1px solid black;">
                                    UNIT PRICE (IDR)
                                </th>
                                <th
                                    style="width: 100px; padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid black; border-top: 1px solid black;">
                                    QTY
                                </th>
                                <th
                                    style="width: 20%; padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid black; border-top: 1px solid black;">
                                    BRUTO
                                </th>
                                <th
                                    style="width: 50px; padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid black; border-top: 1px solid black;">
                                    Disc (%)
                                </th>
                                <th
                                    style="width: 20%; padding-top: 10px; padding-bottom: 10px; border-bottom: 1px solid black; border-top: 1px solid black;">
                                    Netto
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($invoice_detail as $invoice_details)
                            <tr>
                                <td style="vertical-align: baseline;">
                                    {{ $loop->iteration }}
                                </td>
                                <td style="padding-left: 20px;">
                                    {{ $invoice_details->article }} {{ $invoice_details->article_description }}
                                </td>
                                <td style="text-align: center;">
                                    {{ number_format((int)$invoice_details->price) }}
                                </td>
                                <td style="text-align: center;">
                                    {{ $invoice_details->qty }}
                                </td>
                                <td style="text-align: center;">
                                    {{ number_format((int)$invoice_details->gross_price) }}
                                </td>
                                <td style="text-align: center;">
                                    {{ $invoice_details->discount_percent }}
                                </td>
                                <td style="text-align: center;">
                                    {{ number_format((int)$invoice_details->nett_price) }}
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <!-- SPARATOR -->
                            <tr>
                                <td colspan="7">
                                    <div style="width: 100%; height: 1px; background-color: black; margin-top: 35px;"></div>
                                </td>
                            </tr>
                        </tfoot>
                    </table>

                    <!-- calculate table -->
                    <table class="table-content" style="width: 100%;">
                        <thead>
                            <tr>
                                <td style="vertical-align: baseline; width: 20px; padding: 20px; "></td>
                                <td style=" text-align: center; width: 35%; padding: 20px">
                                    GRAND TOTAL
                                </td>
                                <td style="text-align: center; width: 20%; padding: 20px; "></td>
                                <td style="text-align: center; width: 20px; padding: 20px; ">{{ $grand_total->qty }}</td>
                                <td style="text-align: center; width: 20%; padding: 20px; ">
                                    {{ number_format((int)$grand_total->subtotal) }}</td>
                                <td style="text-align: center; width: 20px; padding: 20px; "></td>
                                <td style="text-align: center; width: 20%; padding: 20px; ">{{ number_format((int)$grand_total->total) }}</td>
                            </tr>
                        </thead>
                    </table>

                    <!-- SUMMARY & SIGN-->
                    <table style="width: 100%; margin-top: 30px;">
                        <tr>
                            <td style="width: 70%">
                               <div style="padding-top: 30px;
                               text-align: center;
                               padding-left: 25%;
                               padding-right: 40%;">
                                    <p>Bandung,{{date('d M Y')}}</p>
                                    <div style="height: 100px; border-bottom: 1px solid black;">

                                    </div>
                                    <p>Accounting</p>
                               </div> 
                            </td>
                            <td style="width: 30%; vertical-align: baseline;">
                                <div>
                                    <table style="width: 100%;">
                                        <tr>
                                            <td style="width: 50%;"> DP</td>
                                            <td style="width: 50%;"> {{ number_format((int)$invoice->due_payment) }}</td>
                                        </tr>
                                        <tr>
                                            <td style="width: 50%;"> DUE FOR PAYMENT</td>
                                            <td style="width: 50%;"> {{ number_format((int)$invoice->due_payment) }}</td>
                                        </tr>
                                        <tr>
                                            <td style="width: 50%;"> DPP</td>
                                            <td style="width: 50%;"> {{ number_format((int)$invoice->dpp) }}</td>
                                        </tr>
                                        <tr>
                                            <td style="width: 50%;"> PPN</td>
                                            <td style="width: 50%;"> {{ number_format((int)$invoice->tax_amount) }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
            </td>
        </tr>
    </table>
</body>

</html>