<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html lang="en" xmlns="http://www.w3.org/1999/html">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style>
        /* custom fonts */
        /* latin-ext */
        @font-face {
            font-family: '<PERSON> San<PERSON>';
            font-style: italic;
            font-weight: 400;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dfIFdwYjGaAMFtZd_QA1Zeelmy79QJ1HOSY9AX77f7ZxQR065sIcA4Zw.woff2') }});
            /* src: url(https://careom-api-dev.eigerindo.co.id/assets/fonts/i7dfIFdwYjGaAMFtZd_QA1Zeelmy79QJ1HOSY9AX77f7ZxQR065sIcA4Zw.woff2) format('woff2'); */
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        /* latin */
        @font-face {
            font-family: 'Albert Sans';
            font-style: italic;
            font-weight: 400;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dfIFdwYjGaAMFtZd_QA1Zeelmy79QJ1HOSY9AX77f1ZxQR065sIcA.woff2') }});
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        /* latin-ext */
        @font-face {
            font-family: 'Albert Sans';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dZIFdwYjGaAMFtZd_QA3xXSKZqhr-TenSHq5PPpYf3bRUz17ZtCcA.woff2') }});
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        /* latin */
        @font-face {
            font-family: 'Albert Sans';
            font-style: normal;
            font-weight: 400;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dZIFdwYjGaAMFtZd_QA3xXSKZqhr-TenSHq5PPpYf3bRUz17ZtCcA.woff2') }});
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        /* latin-ext */
        @font-face {
            font-family: 'Albert Sans';
            font-style: italic;
            font-weight: 500;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dfIFdwYjGaAMFtZd_QA1Zeelmy79QJ1HOSY9Al77f7ZxQR065sIcA4Zw.woff2') }});
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        /* latin */
        @font-face {
            font-family: 'Albert Sans';
            font-style: italic;
            font-weight: 500;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dfIFdwYjGaAMFtZd_QA1Zeelmy79QJ1HOSY9Al77f1ZxQR065sIcA.woff2') }});
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        /* latin-ext */
        @font-face {
            font-family: 'Albert Sans';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dZIFdwYjGaAMFtZd_QA3xXSKZqhr-TenSHmZPPpYf3bRUz17ZtCcA.woff2') }});
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        /* latin */
        @font-face {
            font-family: 'Albert Sans';
            font-style: normal;
            font-weight: 500;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dZIFdwYjGaAMFtZd_QA3xXSKZqhr-TenSHmZPPq4f3bRUz17Zt.woff2') }});
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        /* latin-ext */
        @font-face {
            font-family: 'Albert Sans';
            font-style: italic;
            font-weight: 600;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dfIFdwYjGaAMFtZd_QA1Zeelmy79QJ1HOSY9DJ6Lf7ZxQR065sIcA4Zw.woff2') }});
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        /* latin */
        @font-face {
            font-family: 'Albert Sans';
            font-style: italic;
            font-weight: 600;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dfIFdwYjGaAMFtZd_QA1Zeelmy79QJ1HOSY9DJ6Lf1ZxQR065sIcA.woff2') }});
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        /* latin-ext */
        @font-face {
            font-family: 'Albert Sans';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dZIFdwYjGaAMFtZd_QA3xXSKZqhr-TenSHdZTPpYf3bRUz17ZtCcA.woff2') }});
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        /* latin */
        @font-face {
            font-family: 'Albert Sans';
            font-style: normal;
            font-weight: 600;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dZIFdwYjGaAMFtZd_QA3xXSKZqhr-TenSHdZTPq4f3bRUz17Zt.woff2') }});
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        /* latin-ext */
        @font-face {
            font-family: 'Albert Sans';
            font-style: italic;
            font-weight: 700;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dfIFdwYjGaAMFtZd_QA1Zeelmy79QJ1HOSY9Dw6Lf7ZxQR065sIcA4Zw.woff2') }});
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        /* latin */
        @font-face {
            font-family: 'Albert Sans';
            font-style: italic;
            font-weight: 700;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dfIFdwYjGaAMFtZd_QA1Zeelmy79QJ1HOSY9Dw6Lf1ZxQR065sIcA.woff2') }});
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        /* latin-ext */
        @font-face {
            font-family: 'Albert Sans';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dZIFdwYjGaAMFtZd_QA3xXSKZqhr-TenSHTJTPpYf3bRUz17ZtCcA.woff2') }});
            unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
        }

        /* latin */
        @font-face {
            font-family: 'Albert Sans';
            font-style: normal;
            font-weight: 700;
            font-display: swap;
            src: url({{ public_path('assets/fonts/i7dZIFdwYjGaAMFtZd_QA3xXSKZqhr-TenSHTJTPq4f3bRUz17Zt.woff2') }});
            unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
        }

        .table-content tbody tr td {
            padding: 20px
        }

        * {
            font-family: "Albert Sans", sans-serif !important;
        }

        body {
            font-family: "Albert Sans", sans-serif !important;
            font-size: 9pt;
        }

        table {
            font-family: "Albert Sans", sans-serif !important;
        }

        .tableHeader {
            background: #cd5c5c;
            color: #fff;
            /* display: table-row; */
            font-weight: bold;
            font-size: 10px;
            height: 50px
        }

        .row {
            display: table-row;
            font-size: 10px;
            overflow: hidden;
        }

        .column {
            display: table-cell;
            padding: 6px 6px 6px 6px;
            font-size: 10px;
            overflow: hidden;
        }

        div.header {
            position: running(header);
            font-family: "Poppins";
            display: inline-table;
            height: auto;
            margin-left: auto;
            margin-right: auto;
            width: 100%;
            vertical-align: middle;
            padding-top: 10px;
        }

        .logo {
            width: 120px;
        }

        .logo-left,
        .title,
        .logo-right {
            display: table-cell;
        }

        .header>.logo-left {
            vertical-align: middle;
            text-align: left;
            width: 20%;
        }

        .header>.title {
            text-align: center;
            width: 60%;
        }

        .header>.logo-right {
            vertical-align: middle;
            text-align: right;
            width: 20%;
        }

        .logo-left>.logo {
            width: 160px;
            padding-left: 4px;
        }

        .logo-right>.logo {
            width: 120px;
            padding-right: 4px;
        }

        .title>h1 {
            margin: 0;
            font-weight: normal;
            font-size: 35px;
            padding-top: 10px;
        }

        tr {
            page-break-inside: avoid;
            page-break-after: auto
        }

        .table1 thead {
            background-color: #b22222;
            color: white;
        }

        .table1 tbody tr:nth-child(odd) {
            background-color: #C0C0C0;
        }

        .table1 tbody tr:nth-child(even) {
            background-color: #D3D3D3;
        }


        .table2 table {
            border: 0px;
            border-spacing: 0;
            border-collapse: collapse;
            width: 100%
        }


        .image-attachment img {
            width: auto;
            height: 530px;
            margin-left: auto;
            margin-right: auto;
            padding: 10px;
        }

        .image-attachment .first-data-img img {
            height: 480px !important;
        }


        .additional-attachment {
            display: table-cell;
            width: 100%;
            box-sizing: border-box;
        }

        .additional-attachment span {
            text-align: left;
        }

        .bullet ol {
            list-style: none;
            padding: 0;
        }

        .bullet ol li {
            counter-increment: step-counter;
        }

        .bullet ol li:before {
            content: counter(step-counter)". ";
        }

        div.pagebr {
            page-break-inside: avoid;
            page-break-after: auto
        }

        .mce-content-body-value {
            overflow-wrap: break-word;
            word-wrap: break-word;
            margin-bottom: 10px;
        }

        .mce-content-body-value div {
            line-height: 1.4;
            margin: 1rem;
        }

        .mce-content-body-value table {
            border-collapse: collapse;
        }

        .mce-content-body-value table[border]:not([border="0"]):not([style*="border-color"]) td,
        .mce-content-body-value table[border]:not([border="0"]):not([style*="border-color"]) th {
            border-color: #ccc;
        }

        .mce-content-body-value table[border]:not([border="0"]):not([style*="border-style"]) td,
        .mce-content-body-value table[border]:not([border="0"]):not([style*="border-style"]) th {
            border-style: solid;
        }

        .mce-content-body-value table:not([cellpadding]) td,
        .mce-content-body-value table:not([cellpadding]) th {
            padding: .4rem;
        }

        .mce-content-body-value ul {
            margin-top: 16pt;
            margin-bottom: 16pt;

        }

        .mce-content-body-value li ul {
            margin-top: 0;
            margin-bottom: 0;
        }

        td {
            padding-left: 2px;
            padding-right: 2px;
        }

        tr.border-bottom td {
            border-bottom: 1px solid #e7e6e6;
        }

        tr.border-bottom-th td {
            border-bottom: 2px solid #e7e6e6;
        }

        tr.border-top-th td {
            border-top: 2px solid #e7e6e6;
        }

        td.minus {
            color: red;
        }

        td.null {
            content: "-";
        }

        div.breakNow {
            page-break-inside: avoid;
            page-break-after: always;
            page-break-before: always;
        }



        .border-left-white-2 {
            border-left: solid white 2px;
        }

        .h--50 {
            height: 50px;
        }

        @media print {
            tr.page-break {
                display: block;
                page-break-before: always;
            }
        }

        body {
            margin: 0;
            padding: 0;
        }

        @page {
            margin: 0;
        }

        .invoice-header-logo {
            background-color: #18191A;
            border-radius: 0 0 30px 30px;
            height: 150px;
            width: 180px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>

<body style="margin: 0;padding:0">
    <table style="width: 100%; margin-top: -8px !important; margin-left: 30px; margin-right: 30px;">
        <tr>
            <table style="width: 100%; border-collapse: collapse;">
                <td style="width: 50%; vertical-align: middle; text-align: center;">
                    <div class="invoice-header-logo">
                        <img src="{{ public_path('assets/invoice-eiger-logo.png') }}" alt="Eiger logo"
                            style="width: 120px; margin-top: 50px; margin-left: 14px;">
                    </div>
                </td>
                <td style="width: 50%;">
                    <div style="margin-top: 30px; width: 200px; height: auto; margin-left: 68px">
                        <img src="{{ public_path('assets/logo-invoice.png') }}" alt="invoice"
                            style="width: 200px; height: auto;">
                        <p style="color: #686F72; font-size: 12px;margin-top: 8px">PT. Eigerindo Multi Produk Industri
                        </p>
                        <p style="font-weight: bold; font-weight: 700">NPWP: 016919813441000</p>
                    </div>
                </td>
            </table>
        </tr>
    </table>

    <table style="width: 100%; margin-top: 10px !important; margin-left: 30px; margin-right: 30px;">
        <tr>
            <div>
                <table style="width: 100%; border-collapse: collapse; padding-top: 10px;">
                    <tr>
                        <td style="width: 60%;padding: 5px; vertical-align: top; text-align: left;">
                            <p
                                style="font-size: 14px; color:#686F72; line-height: 1; margin-bottom: -16px;font-weight: 700;font-family: 'Albert Sans';">
                                INVOICE TO</p>
                            <p style="font-weight: 700; font-size: 26px; line-height: 1.1;font-family: 'Albert Sans';">
                                {{ $invoice->customer_name ?? '-' }}</p>
                            <p style="font-size: 14px; margin-top: -20px;font-family: 'Albert Sans';">
                                {{ $invoice->customer_id ?? '-' }}</p>
                            <p style="font-size: 14px; margin-top: -10px;font-family: 'Albert Sans';">
                                ______________________________________</p>
                        </td>
                        <td style="width: 40%;">
                        </td>
                    </tr>
                    <tr>
                        <td style="width: 50%;">
                            <table style="margin-top: 30px; color: #686F72; margin-top: -8px">
                                <tr>
                                    <td style="font-size: 12px; color:#686F72; line-height: 1; padding-bottom: 10px">
                                        Information Customer</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">
                                        Nomor NPWP</td>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">:
                                        {{ $invoice->npwp ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">
                                        Customer PO</td>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">:
                                        {{ $invoice->po_no ?? '-' }}</td>
                                </tr>
                            </table>
                        </td>
                        <td style="width: 10%;"></td>
                        <td style="width: 40%;">
                            <table style="margin-top: 30px; color: #686F72; margin-top: -10px">
                                <tr>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">SO
                                    </td>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">:
                                        {{ $invoice->sales_order_no ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">SO
                                        Date</td>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">:
                                        {{ \Carbon\Carbon::parse($invoice->billing_date)->format('Y/m/d') ?? '-' }}
                                    </td>
                                </tr>
                                <tr>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">
                                        Due Date</td>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">:
                                        {{ \Carbon\Carbon::parse($invoice->due_date)->format('Y/m/d') ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">
                                        Delivery No.</td>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">:
                                        {{ $invoice->delivery_order_no ?? '-' }}</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">
                                        Po Number</td>
                                    <td style="font-size: 12px; color: #18191A; line-height: 1; padding-bottom: 10px">:
                                        {{ $invoice->po_no ?? '-' }}</td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </div>
        </tr>
    </table>

    <div style="padding-left: 20px; padding-right: 20px;">
        <table class="table table-striped" style=" width: 100%;" cellpadding="0" cellspacing="0">
            <thead style="border-top: 1px solid #ACB1B4; border-bottom: 1px solid #ACB1B4;">
                <tr style="background-color: #3B3C3E; color: #ffffff">
                    <th style="text-align: left; width: 5%; padding: 12px;font-family: 'Albert Sans';">
                        No.
                    </th>
                    <th style="text-align: left; width: 26%; padding: 12px;font-family: 'Albert Sans';">
                        DESCRIPTION
                    </th>
                    <th style="text-align: left; width: 15%; padding: 12px;font-family: 'Albert Sans';">
                        UNIT PRICE
                    </th>
                    <th style="text-align: left; width: 15%; padding: 12px;font-family: 'Albert Sans';">
                        QUANTITY
                    </th>
                    <th style="text-align: left; width: 10%; padding: 12px;font-family: 'Albert Sans';">
                        BRUTO
                    </th>
                    <th style="text-align: left; width: 15%; padding: 12px;font-family: 'Albert Sans';">
                        DISCOUNT
                    </th>
                    <th style="text-align: left; width: 12%; padding: 12px;font-family: 'Albert Sans';">
                        NETTO
                    </th>
                </tr>
            </thead>
            <tbody style="border-bottom: 1px solid #ACB1B4;">
                @foreach ($invoice_detail as $invoice_details)
                    {{-- @if ($loop->iteration % 7 == 0)
                <tr class="breakNow" style="margin-top: 10px;">
                    <td style="padding: 12px;"> {{ $loop->iteration }}</td>
                <td style="padding: 12px; color: #18191A; font-size: 12px;">
                    <span>SKU {{ $invoice_details->article }}</span>
                    <br>
                    <strong style="font-size: 12px; line-height: 1.5;">{{ $invoice_details->article_description }}</strong>
                    <br>
                    <span>{{ $invoice_details->value}} {{ $invoice_details->product_size }}/{{ $invoice_details->qty }}</span>
                </td>
                <td style="padding: 12px; color: #18191A; font-size: 12px;">
                    {{ number_format((int)$invoice_details->price, 0, ',', '.') }}
                </td>
                <td style="padding: 12px; color: #18191A; font-size: 12px;">
                    {{ $invoice_details->qty }}
                </td>
                <td style="padding: 12px; color: #18191A; font-size: 12px;">
                    {{ number_format((int)$invoice_details->gross_price) }}
                </td>
                <td style="padding: 12px; color: #18191A; font-size: 12px;">
                    {{ $invoice_details->discount_percent }}
                </td>
                <td style="padding: 12px; color: #18191A; font-size: 12px;">
                    {{ number_format((int)$invoice_details->nett_price) }}
                </td>
                </tr>
                <div class="breakNow"></div>
                @else --}}
                    <tr style="margin-top: 10px;">
                        <td style="padding: 12px;"> {{ $loop->iteration }}</td>
                        <td style="padding: 12px; color: #18191A; font-size: 12px;">
                            <span>SKU {{ $invoice_details->article }}</span>
                            <br>
                            <strong
                                style="font-size: 12px; line-height: 1.5;">{{ $invoice_details->article_description }}</strong>
                            <br>
                            <span>{{ $invoice_details->value }}
                                {{ $invoice_details->product_size }}/{{ $invoice_details->qty }}</span>
                        </td>
                        <td style="padding: 12px; color: #18191A; font-size: 12px;">
                            {{ number_format((int) $invoice_details->price, 0, ',', '.') }}
                        </td>
                        <td style="padding: 12px; color: #18191A; font-size: 12px;">
                            {{ $invoice_details->qty }}
                        </td>
                        <td style="padding: 12px; color: #18191A; font-size: 12px;">
                            {{ number_format((int) $invoice_details->gross_price) }}
                        </td>
                        <td style="padding: 12px; color: #18191A; font-size: 12px;">
                            {{ $invoice_details->discount_percent }}
                        </td>
                        <td style="padding: 12px; color: #18191A; font-size: 12px;">
                            {{ number_format((int) $invoice_details->nett_price) }}
                        </td>
                    </tr>
                    {{-- @endif --}}
                    {{-- @if ($loop->iteration > 0 && $loop->iteration % 5 == 0)
                <tr class="page-break"></tr>
                @endif  --}}
                @endforeach
            </tbody>
        </table>

    </div>

    <div style="width: 100%">
        <table class="table" style="padding-top: 30px; width: 100%;">
            <tr>
                <td style="width: 60%">
                    <table style="width: 100%; border-collapse: collapse; padding-top: 20px; padding-left: 36px;">
                        <td style="width: 60%; padding: 5px; vertical-align: top; text-align: left;">
                            <p style="font-size: 13px; font-weight: 400;">Bandung, {{ date('d/m/Y') }}</p>
                            <p style="margin-bottom: 130px"></p>
                            <p style="border-bottom: 1px solid rgb(97, 97, 97); width: 204px;"></p>
                            <p style="font-size: 13px; font-weight: 400; margin-top: -8px">
                                PT. Eigerindo Multi Produk Industri
                            </p>
                        </td>
                    </table>
                </td>
                <td style="width: 40%">
                    <table style="width: 100%; margin-top: -10px; color: black; padding-right: 20px" cellpadding="0"
                        cellspacing="0">
                        <tr style="padding-bottom: 12px">
                            <td>Total Qty</td>
                            <td style="text-align: right; margin-bottom: 4px">{{ (int) $grand_total->qty }}</td>
                        </tr>
                        <tr style="padding-bottom: 12px">
                            <td style="margin-bottom: 4px">Total Bruto</td>
                            <td style="text-align: right; margin-bottom: 4px">
                                Rp{{ number_format((int) $grand_total->subtotal, 0, ',', '.') }}</td>
                        </tr>
                        <tr style="padding-top:6px; padding-bottom: 12px">
                            @php
                                $part = (int) $invoice->down_payment;
                                $whole = (int) $grand_total->total;
                                $percentage = ($part / $whole) * 100;
                            @endphp
                            <td>DP</td>
                            <td style="text-align: right; margin-bottom: 4px">{{ number_format($percentage, 2) }}</td>
                        </tr>
                        <tr style="padding-top:6px;padding-bottom: 4px; padding-bottom: 12px">
                            <td>DUE FOR PAYMENT</td>
                            <td style="text-align: right;">Rp{{ number_format((int) $due_for_payment, 0, ',', '.') }}</td>
                        </tr>
                        <tr style="padding-top:6px;padding-bottom: 4px; padding-bottom: 12px">
                            <td>NILAI PENYERAHAN</td>
                            <td style="text-align: right;">0</td>
                        </tr>
                        <tr style="padding-top:6px;padding-bottom: 4px; padding-bottom: 12px">
                            <td>PPN</td>
                            <td style="text-align: right;">0</td>
                        </tr>
                        <tr style="border-collapse: collapse;padding-top:6px;padding-bottom: 12px">
                            <td style="border-top: 1px solid #E5E6E8; margin-bottom: 10px"></td>
                            <td style="border-top: 1px solid #E5E6E8; margin-bottom: 10px"></td>
                        </tr>
                        <tr style="padding-top:6px;padding-bottom: 12px">
                            <td style="padding-bottom: 4px; margin-top: 8px">Total Discount (Rupiah)</td>
                            <td style="padding-bottom: 4px; text-align: right; color: #05964C;; margin-top: 8px">
                                {{ number_format((int) $grand_total->discount, 0, ',', '.') }}</td>
                        </tr>
                        <tr style="padding-top:6px;padding-bottom: 10px">
                            <td style="padding-bottom: 4px;margin-top: 8px"></td>
                            <td style="padding-bottom: 4px;margin-top: 8px"></td>
                        </tr>
                        <tr
                            style="background-color: #000000; color: #ffffff;width: 100%;font-size: 15px; font-weight: 700; padding-top:6px;padding-bottom: 4px;margin-right: auto;border-collapse: collapse;">
                            <td style="padding-top: 5px; padding-bottom: 5px; padding-left: 5px;">TOTAL</td>
                            <td
                                style="padding-top: 5px; padding-bottom: 5px; padding-left: 5px; padding-right: 5px;text-align: right;">
                                RP.{{ number_format((int) $grand_total->total, 0, ',', '.') }}</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </div>

    <div
        style="height: 64px; background-color: black; width: 100%; position: fixed; bottom: 0; text-align: center; line-height: 64px; color: white;">
        <div style="display: inline-block; vertical-align: middle; padding-top: 12px;">
            <img src="{{ public_path('assets/envelope-regular.svg') }}" alt=""
                style="width: 14px; vertical-align: middle; padding-top: 3px; margin-bottom: -3px">
            <a href=""
                style="vertical-align: middle; font-size: 14px; padding-left: 5px; color: white"><EMAIL></a>
            <span style="vertical-align: middle; font-size: 20px; padding-left: 5px; color: white">|</span>
        </div>
        <div style="display: inline-block; vertical-align: middle; padding-top: 12px; margin-left: 10px;">
            <img src="{{ public_path('assets/whatsapp-brands.svg') }}" alt=""
                style="width: 14px; vertical-align: middle; padding-top: 3px;">
            <a href="https://api.whatsapp.com/send/?phone=6281120002588&text&type=phone_number&app_absent=0"
                style="vertical-align: middle; font-size: 14px; padding-left: 5px; color: white">0811200002588</a>
            <span style="vertical-align: middle; font-size: 20px; padding-left: 5px; color: white">|</span>
        </div>
        <div style="display: inline-block; vertical-align: middle; padding-top: 12px; margin-left: 10px;">
            <img src="{{ public_path('assets/globe-solid.svg') }}" alt=""
                style="width: 14px; vertical-align: middle; padding-top: 3px; margin-bottom: -2px">
            <a href="eigerindo.co.id/"
                style="vertical-align: middle; font-size: 14px; padding-left: 5px; color: white">eigerindo.co.id/</a>
        </div>
    </div>
</body>

</html>
