<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\{
    Bulk<PERSON>art<PERSON><PERSON>roller,
    <PERSON>t<PERSON><PERSON>roller,
    <PERSON>u<PERSON><PERSON>roller,
    <PERSON>r<PERSON><PERSON>roller,
    <PERSON><PERSON><PERSON>roller,
    <PERSON>er<PERSON><PERSON>roller,
    Invoice<PERSON><PERSON>roller,
    Product<PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON>roller,
    <PERSON>rud<PERSON>ser<PERSON><PERSON>roller,
    Register<PERSON><PERSON>roller,
    Customer<PERSON><PERSON>roller,
    ParameterController,
    InvoiceB2BController,
    HomepageB2BController,
    TransactionController,
    WabDashboardController,
    TransactionB2BController,
    TransactionWABController,
    InvoiceInternalController,
    HomepageWholesalesController,
    NotificationController,
    InvoiceB2BInternalController,
    TransactionInternalController,
    WholesalesDashboardController,
    TransactionB2BInternalController,
    DashboardController,
    FileController,
    TransportationController,
    RegionController

};

use App\Http\Controllers\Auth\{
    NewPasswordController,
    PasswordResetLinkController
};
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/


Route::get('/storage/stock/{param?}', [CustomerController::class, 'downloadStock']);

Route::post('/checkCustomer', [RegisterController::class, 'checkCustomer']);
Route::post('/register', [RegisterController::class, 'register']);
Route::post('/validateNPWPKTP', [RegisterController::class, 'validateNPWPKTP']);
Route::post('/updatePassword', [RegisterController::class, 'updatePassword']);
// Route::post('/activate/{email}', [RegisterController::class, 'activateUser']);
Route::get('/terms-and-conditions', [RegisterController::class, 'termsAndConditions']);

Route::get('/activate/{activation_token}', [RegisterController::class, 'activateUserByToken']);
Route::post('/resend-activate', [RegisterController::class, 'resendActivateLink']);

Route::get('/invoice/file/{file_name}', [InvoiceController::class, 'url_download_invoice']);
Route::get('/proforma/file/{file_name}', [InvoiceController::class, 'url_download_proforma']);
//--DEPRECATED--
// Route::post('/approveCustomer/{id}', [CustomerController::class, 'approveCustomer']);
// Route::post('/rejectCustomer/{id}', [CustomerController::class, 'rejectCustomer']);
// Route::get('/getlist_Customer', [CustomerController::class, 'getlist_Customer']);
// Route::get('/getdetail_Customer/{id}', [CustomerController::class, 'getdetail_Customer']);
//--DEPRECATED--

Route::prefix('homepageb2b')->group(function () {
    Route::get('/productb2b', [HomepageB2BController::class, 'productb2b']);
    Route::get('/faq', [HomepageB2BController::class, 'faq']);
});

Route::get('/tnc', [HomepageB2BController::class, 'tnc']);

Route::get('/products-b2b', [ProductController::class, 'indexB2B']);
Route::get('products-b2b/recommended', [ProductController::class, 'getB2BProductsRecommended']);
Route::get('/products/detail-b2b/{sku}', [ProductController::class, 'getDetail']);
Route::get('/products/detail-b2b/{sku}/{color}', [ProductController::class, 'getDetailVariant']);

Route::post('/login', [UserController::class, 'authenticate']);
Route::post('/forgot-password', [PasswordResetLinkController::class, 'store']);
Route::post('/reset-password/verify', [PasswordResetLinkController::class, 'verify']);
Route::get('/reset-password/{token}', [PasswordResetLinkController::class, 'showResetPasswordForm'])->name('reset.password.get');
Route::post('/change-forgot-password', [NewPasswordController::class, 'store']);

// Route Get List All Transportation & List All Region (on progress)
Route::get('/region', [RegionController::class, 'getRegions']);
Route::get('/region-city', [RegionController::class, 'getRegionCity']);
Route::get('/region-district', [RegionController::class, 'getRegionDistrict']);
Route::get('/region-subdistrict', [RegionController::class, 'getRegionSubdistrict']);
Route::get('/region-postal-code', [RegionController::class, 'getRegionPostalCode']);
Route::get('/transportation', [TransportationController::class, 'getTransportations']);

Route::middleware(
    [
        'preCache',
        'postCache'
    ]
)->group(function () {
    Route::get('/footer-menu', [ParameterController::class, 'getFooter']);
    Route::get('/header-menu-wholesales', [HeaderController::class, 'getMenus']);
    Route::get('/header-menu', [HeaderController::class, 'getMenus']);

});
// Route::middleware(['auth-s3'])->group(function () {
//     Route::post('/file', [FileController::class, 'presignedUpload'])->withoutMiddleware("throttle:api");
// });

Route::post('/file', [FileController::class, 'presignedUpload'])->withoutMiddleware("throttle:api");

Route::middleware(['auth:sanctum'])->group(function () {
    Route::post('/read/{notification_id}', [NotificationController::class, 'readNotification']);
    Route::post('/read-all', [NotificationController::class, 'readAll']);
    Route::get('bank', [CustomerController::class, 'getBank']);
    Route::get('va', [CustomerController::class, 'getVa']);
    Route::post('/validateNPWPKTPUpdate', [RegisterController::class, 'validateNPWPKTPUpdate']);

    Route::prefix('homepage')->group(function () {
        Route::get('/jumlah_product/{customer_id?}', [HomepageWholesalesController::class, 'jumlah_product']);
        Route::get('/jumlah_transaksi/{customer_id?}', [HomepageWholesalesController::class, 'jumlah_transaksi']);
        Route::get('/total_transaksi/{customer_id?}', [HomepageWholesalesController::class, 'total_transaksi']);
        Route::get('/pesanan_berjalan/{customer_id?}', [HomepageWholesalesController::class, 'pesanan_berjalan']);
        Route::get('/get_banner', [HomepageWholesalesController::class, 'get_banner']);
    });

    Route::get('/notification', [NotificationController::class, 'getNotification']);
    Route::get('/notification/count', [NotificationController::class, 'getNotificationCount']);
    Route::get('/notification/{notification_id}', [NotificationController::class, 'getDetailNotification']);
    Route::delete('/notification/{notification_id}', [NotificationController::class, 'deleteNotification']);

    Route::middleware(
        [
            'preCache',
            'postCache'
        ]
    )->group(function () {
        Route::get('/detail-profile/{custId}', [ProfileController::class, 'detail']);
        Route::get('/daftar-customer', [ProfileController::class, 'listdaftarcustomer']);
        Route::get('/detail-pesanan/{id}', [ProfileController::class, 'getdetailcustPesanan']);

    });
    Route::get('user', function (Request $request) {
        return $request->user();
    });
    Route::get('logout', [UserController::class, 'logout']);
    Route::post('init-change-password', [UserController::class, 'initChangePassword']);
    Route::get('user-activation', [UserController::class, 'userActivation']);
    Route::get('order/list-status', [TransactionController::class, 'getOrderStatusList']);
    Route::get('get-store-list', [UserController::class, 'getStoreList']);

    Route::get('/get-customer-b2b', [CustomerController::class, 'getCustomerB2b']);

    Route::post('/checkStock', [ProductController::class, 'checkStock']);
    Route::get('/customer/stock', [CustomerController::class, 'getStock']);
    Route::get('/customer/store', [CustomerController::class, 'getCustomerStore']);
    Route::get('/customer/stock/history-upload', [CustomerController::class, 'getHistoryUpdateStock']);

    Route::post('b2b/simulate-discount', [CartController::class, 'simulateDiscountB2B']);
    Route::get('b2b/invoice/{invoice_no}/download/dp', [InvoiceB2BController::class, 'downloadInvoiceDetailDP']);
    Route::get('b2b/invoice/{invoice_no}/download/lunas', [InvoiceB2BController::class, 'downloadInvoiceDetailLunas']);

    Route::get('customer-shipment', [CartController::class, 'getCustomerShipmentList']);
    Route::post('add-address', [UserController::class, 'addShipmentAddress']);
    Route::put('edit-address/{id}', [UserController::class, 'editShipmentAddress']);
    Route::patch('change-address/{id}', [UserController::class, 'changeShipmentAddress']);
    Route::delete('delete-address/{id}', [UserController::class, 'deleteShipmentAddress']);

    Route::get('profile', [UserController::class, 'profile']);
    Route::post('edit-profile', [UserController::class, 'editProfile']);
    Route::post('change-password', [UserController::class, 'changePassword']);
    Route::post('check-password', [UserController::class, 'checkPassword']);

    Route::middleware(['verified-customer'])->group(function () {
        Route::get('credit-limit', [UserController::class, 'creditLimit']);
        Route::post('update-user', [RegisterController::class, 'updateUserData']);
        Route::get('update-user', [RegisterController::class, 'getUpdateUserData']);

        Route::post('cart/add/product-custom', [CartController::class, 'addProductCustom']);
        Route::get('cart/detail-custom/{order_no}', [TransactionB2BInternalController::class, 'getDataCustom']);
        Route::post('cart/add', [CartController::class, 'storeToCartDetail']);
        Route::post('cart/custom', [CartController::class, 'customAddToCart']);
        Route::post('cart/add-bulk', [CartController::class, 'bulkAddToCart']);
        Route::post('cart/add/{order_group_id}', [CartController::class, 'addToCartFromHistory']);
        Route::post('cart/check', [CartController::class, 'checkCart'])->withoutMiddleware("throttle:api");
        Route::post('cart/uncheck', [CartController::class, 'uncheck'])->withoutMiddleware("throttle:api");

        Route::post('cart/check-bulk', [CartController::class, 'checkCartBulk'])->withoutMiddleware("throttle:api");

        Route::get('cart/list', [CartController::class, 'index']);
        Route::delete('cart/delete/{id}', [CartController::class, 'destroy']);
        Route::post('cart/delete-bulk', [CartController::class, 'destroyBulk']);
        Route::post('/cart-delete-all', [CartController::class, 'deleteAllCart']);

        Route::post('product/custom/upload', [CartController::class, 'customUpload']);
        Route::post('cart/attachments', [CartController::class, 'cartAttachment']);

        Route::prefix('cart/custom')->group(function () {
            Route::post('add-bulk', [CartController::class, 'customAddToCart']);
            Route::post('check-bulk', [CartController::class, 'checkBulkCustom'])->withoutMiddleware("throttle:api");
            Route::post('delete-bulk', [CartController::class, 'destroyBulkCustom']);
            Route::post('remark', [CartController::class, 'addRemark']);
            Route::post('update-qty', [CartController::class, 'updateQty']);
        });

        Route::prefix('stock')->group(function () {
            Route::post('upload', [CustomerController::class, 'uploadStockToko']);
            Route::post('download', [CustomerController::class, 'unduhStockToko']);
            Route::post('create', [CustomerController::class, 'insertStockToko']);
            Route::post('stock', [CustomerController::class, 'checkStockToko']);
        });
        Route::post('checkout', [TransactionController::class, 'store']);
        Route::post('checkout/custom/remark', [
            CartController::class,
            'addRemark'
        ]);
        Route::post('checkout-wholesales', [TransactionController::class, 'storeWholesales']);
        Route::post('checkout-b2b', [TransactionController::class, 'storeB2B']);
        Route::post('transaction/confirm-wholesales', [TransactionController::class, 'confirmWolesales']);
        Route::middleware(['transaction-timeout'])->group(function () {
        });
        Route::get('transaction/checkout/{order_group_id}', [TransactionController::class, 'detailCheckout']);
        Route::get('transaction/checkout-b2b/{order_group_id}', [TransactionController::class, 'detailCheckoutB2B']);
        Route::get('transaction/{order_no}', [TransactionController::class, 'getTransactionDetail']);
        Route::middleware(['order-detail'])->group(function () {
            Route::post('transaction/partial', [TransactionController::class, 'removePartial']);
            Route::post('transaction/receive', [TransactionController::class, 'receiveOrder']);
            Route::post('transaction/reorder', [TransactionController::class, 'reOrder']);
            Route::post('transaction/cancel', [TransactionController::class, 'cancelOrder']);
            Route::middleware(['transaction-timeout'])->group(function () {
                Route::post('transaction/confirm', [TransactionController::class, 'confirm']);
            });
        });

        Route::prefix('bulk-cart')->group(function () {
            Route::post('upload', [BulkCartController::class, 'bulkUpload']);
            Route::post('process-order', [BulkCartController::class, 'processOrder']);
            Route::get('list', [BulkCartController::class, 'getBulkList']);

            Route::get('unduh', [BulkCartController::class, 'bulkUnduh']);
            Route::post('delete', [BulkCartController::class, 'bulkDelete']);
            Route::post('update-sku', [BulkCartController::class, 'bulkStore']);
            Route::post('lanjut-pesanan', [BulkCartController::class, 'nextOrder']);
        });

        Route::get('transaction', [TransactionController::class, 'getTransactions']);
        Route::post('rating', [TransactionController::class, 'rating']);

        Route::get('/proforma/{invoice_no}/download', [InvoiceController::class, 'downloadProforma']);
        Route::post('transaction/cancel/{order_no}', [TransactionController::class, 'cancelb2b']);

        // product detail
        Route::get('products', [ProductController::class, 'index']);

        Route::get('products/recommended', [ProductController::class, 'getProductsRecommended']);
        Route::get('products/recommended/{sku_code_c}', [ProductController::class, 'getProductsRecommendedBySku']);

        Route::get('products/{sku}', [ProductController::class, 'getProductDetail']);
        Route::get('/products/variant/{id}', [ProductController::class, 'getProductVariant']);

        // improve
        Route::get('products/detail/{sku}', [ProductController::class, 'getDetail']);
        Route::get('products/detail/{sku}/{color}', [ProductController::class, 'getDetailVariant']);

        /**
         * Not used
         */
        Route::get('products/detailcustomer/{sku}/{color}', [ProductController::class, 'getDetailVariantCustomer']);
        /** */

        Route::get('products/custom/{sku}', [ProductController::class, 'getCustomTags']);

        // tagihan
        Route::get('/invoice', [InvoiceController::class, 'index']);
        Route::get('/invoice/{invoice_no}/items', [InvoiceController::class, 'getDetailItems']);
        Route::get('/invoice/{invoice_no}/header', [InvoiceController::class, 'getDetailHeader']);
        Route::get('/invoice/{invoice_no}/download', [InvoiceController::class, 'downloadInvoice']);
        Route::middleware(['invoice-detail'])->group(function () {
            Route::get('/invoice/{invoice_no}/list-payment', [InvoiceController::class, 'listPayment']);
            Route::get('/invoice/{invoice_no}/download/dp', [InvoiceController::class, 'downloadDPInvoice']);
            Route::get('/invoice/{invoice_no}/download-faktur', [InvoiceInternalController::class, 'downloadFaktur']);
        });
        Route::get('invoice/list-status', [InvoiceController::class, 'getStatuses']);

        // stock
        Route::post('/customer/stock/upload', [CustomerController::class, 'uploadStock']);
        Route::post('/customer/stock/update', [CustomerController::class, 'updateStock']);

        Route::prefix('b2b')->group(function () {
            // transaksi b2b/b2g
            Route::get('/transaction', [TransactionB2BController::class, 'getTransactions']);
            Route::get('/transaction/{order_no}', [TransactionB2BController::class, 'getTransactionDetails']);
            Route::post('transaction/{order_no}/upload-payment', [TransactionB2BController::class, 'uploadPayment']);
            Route::delete('transaction/{order_no}/delete-payment', [TransactionB2BController::class, 'deletePayment']);
            Route::get('order/list-status', [TransactionController::class, 'getOrderStatusList']);
            // tagihan b2b/b2g
            Route::get('/invoice', [InvoiceB2BController::class, 'index']);
            Route::get('/invoice/{invoice_no}/items', [InvoiceB2BController::class, 'getDetailItems']);
            Route::get('/invoice/{invoice_no}/header', [InvoiceB2BController::class, 'getDetailHeader']);
            Route::middleware(['invoice-detail'])->group(function () {
                Route::get('/invoice/{invoice_no}', [InvoiceB2BController::class, 'getInvoiceDetail']);
                Route::get('/invoice/{invoice_no}/payment', [InvoiceB2BController::class, 'paymentInvoiceDetail']);
                Route::get('/invoice/{invoice_no}/download/faktur-pajak', [InvoiceB2BController::class, 'downloadFakturPajak']);
            });
        });
    });

    Route::middleware('sales')->group(function () {
        Route::prefix('internal')->group(function () {
            Route::get('profile', [UserController::class, 'profile']);
            Route::post('change-password', [UserController::class, 'changePassword']);
            //  transaction interanl
            Route::get('/transaction', [TransactionInternalController::class, 'getTransactions']);
            // Route::get('/transaction/download', [TransactionInternalController::class, 'downloadTransactionsNew']);
            Route::get('/transaction/download', [TransactionInternalController::class, 'downloadTransactionCSV']);
            Route::get('/transaction/summary', [TransactionInternalController::class, 'getTransactionsForInternalSummary']);
            Route::get('/transaction/{order_no}', [TransactionInternalController::class, 'getTransactionDetail']);
            Route::post('/transaction/{order_no}/upload-pks', [TransactionInternalController::class, 'uploadPks']);
            Route::get('/products/detail/{sku}', [ProductController::class, 'getDetailInternal']);

            // invoice internal
            Route::get('/invoice', [InvoiceInternalController::class, 'index']);
            Route::get('/invoice/download', [InvoiceInternalController::class, 'downloadInvoicesNew']);
            Route::get('/invoice/{invoice_no}/items', [InvoiceController::class, 'getDetailItems']);
            Route::get('/invoice/{invoice_no}/header', [InvoiceController::class, 'getDetailHeader']);
            Route::get('/invoice/{invoice_no}/list-payment', [InvoiceController::class, 'listPayment']);
            Route::get('/invoice/{invoice_no}/download', [InvoiceController::class, 'downloadInvoice']);
            Route::get('/invoice/{invoice_no}/download/dp', [InvoiceController::class, 'downloadDPInvoice']);
            Route::get('/proforma/{invoice_no}/download', [InvoiceController::class, 'downloadProforma']);
            Route::get('/transaction/internal', [TransactionInternalController::class, 'getTransactionsForInternal']);
            Route::get('/transaction/internal-summary', [TransactionInternalController::class, 'getTransactionsForInternalSummary']);

            Route::post('/invoice/{invoice_no}/upload-faktur', [InvoiceInternalController::class, 'uploadFaktur']);
            Route::post('/invoice/{invoice_no}/delete-faktur', [InvoiceInternalController::class, 'deleteFaktur']);
            Route::get('/invoice/{invoice_no}/download-faktur', [InvoiceInternalController::class, 'downloadFaktur']);

            // productdownload
            Route::get('products', [ProductController::class, 'internalIndex']);
            Route::get('products/{sku}', [ProductController::class, 'internalDetail']);

            //stok
            Route::get('/customer/stock', [CustomerController::class, 'getStock']);
            Route::get('/customer/store', [CustomerController::class, 'getCustomerStore']);
            Route::get('/customer/stock/history-upload', [CustomerController::class, 'getHistoryUpdateStock']);

            // customer
            Route::get('customers', [CustomerController::class, 'internalIndex']);
            Route::get('customers/export', [CustomerController::class, 'exportCustomers']);
            Route::get('customers/download', [CustomerController::class, 'downloadTransactionsCustomer']);
            Route::get('customers/{id}', [CustomerController::class, 'internalDetail']);
            Route::get('customers/{id}/orders', [CustomerController::class, 'internalOrder']);
            Route::get('customers/{id}/orders/export', [CustomerController::class, 'exportCustomerOrders']);

            // banner CMS
            Route::get('/banner', [BannerController::class, 'getBanner']);
            Route::post('/banner', [BannerController::class, 'postBanner']);
            Route::post('/banner/deactivate/{id}', [BannerController::class, 'deactivateBanner']);
            Route::post('/banner/{id}', [BannerController::class, 'updateBanner']);
            //Route::delete('/banner', [BannerController::class, 'deleteBannerById']);

            Route::prefix('wab')->group(function () {
                Route::get('sales-summary', [WabDashboardController::class, 'summary']);
                Route::get('order-dashboard', [WabDashboardController::class, 'orders']);
                Route::get('customer-dashboard', [WabDashboardController::class, 'customers']);
                Route::get('/transaction', [TransactionWABController::class, 'getTransactions']);
                Route::get('/transaction/download', [TransactionWABController::class, 'downloadTransactionsNew']);
                Route::get('/transaction/{order_no}', [TransactionWABController::class, 'getTransactionDetails']);
            });

            // Route::prefix('wholesales')->group(function (){
            //     Route::get('sales-summary', [WholesalesDashboardController::class, 'summary']);
            //     Route::get('order-dashboard', [WholesalesDashboardController::class, 'orders']);
            //     Route::get('customer-dashboard', [WholesalesDashboardController::class, 'customers']);
            // });

            Route::prefix('b2b')->group(function () {
                Route::post('transaction', [TransactionB2BInternalController::class, 'createTransaction']);

                Route::prefix('transaction-custom')->group(function () {
                    Route::post('/create-url', [TransactionB2BInternalController::class, 'customUploadUrl']);
                    Route::post('/create', [TransactionB2BInternalController::class, 'createTransactionCustom']);
                    Route::post('/update/{order_no}', [TransactionB2BInternalController::class, 'updateTransactionCustom']);
                });

                Route::get('transaction', [TransactionB2BInternalController::class, 'getTransactions']);
                Route::get('transaction/download', [TransactionB2BInternalController::class, 'downloadTransactions']);
                Route::post('transaction/approval', [TransactionB2BInternalController::class, 'transactionApproval']);
                Route::post('transaction/cancel', [TransactionController::class, 'cancelOrder']);
                Route::post('transaction/{order_no}', [TransactionB2BInternalController::class, 'updateTransaction']);
                // Route::post('transaction/approval-b2b', [TransactionB2BInternalController::class, 'transactionApproval']);
                Route::get('transaction/{order_no}', [TransactionB2BInternalController::class, 'getTransactionDetails']);
                Route::post('transaction/{order_no}/upload-pks', [TransactionInternalController::class, 'uploadPks']);
                Route::post('transaction/{order_no}/upload-payment', [TransactionB2BController::class, 'uploadPayment']);
                Route::delete('transaction/{order_no}/delete-payment', [TransactionB2BController::class, 'deletePayment']);
                Route::get('detail-custom/{order_no}', [TransactionB2BInternalController::class, 'getDataCustom']);
                Route::get('invoice', [InvoiceB2BInternalController::class, 'getInvoices']);
                Route::get('invoice/download', [InvoiceB2BInternalController::class, 'downloadInvoicesNew']);
                Route::get('invoice/{invoice_no}', [InvoiceB2BInternalController::class, 'getDetailItems']);
                Route::post('invoice/{invoice_no}/upload-faktur', [InvoiceB2BInternalController::class, 'uploadFaktur']);

                Route::post('customers', [CustomerController::class, 'createCustomer']);
                Route::get('customers/draft', [CustomerController::class, 'getDraftCustomer']);
                Route::post('customers/{id}', [CustomerController::class, 'updateCustomer']);
                Route::patch('customers/{id}/approve', [CustomerController::class, 'approveCustomer']);
                Route::patch('customers/{id}/revision', [CustomerController::class, 'revisionCustomer']);
                Route::patch('customers/{id}/reject', [CustomerController::class, 'rejectCustomer']);

                //edit data profil
                Route::post('edit-ktp/{customerId}', [CustomerController::class, 'editKTP']);
                Route::post('edit-npwp/{customerId}', [CustomerController::class, 'editNPWP']);

                //daftar customer baru                
                Route::post('/approveCustomer/{id}', [CustomerController::class, 'approveCustomer']);
                Route::post('/rejectCustomer/{id}', [CustomerController::class, 'rejectCustomer']);
                Route::get('/getlist_Customer', [CustomerController::class, 'getlist_Customer']);
                Route::get('/getdetail_Customer/{id}', [CustomerController::class, 'getdetail_Customer']);

                //edit pesanan internal B2B
                Route::put('/edit-orders/{order_id}', [TransactionB2BInternalController::class, 'editOrders']);
                Route::post('/edit-customs/{order_id}', [TransactionB2BInternalController::class, 'editCustoms']);
                Route::put('/edit-custom-price', [TransactionB2BInternalController::class, 'editCustomPrice']);
            });

            Route::resource('users', CrudUserController::class, ['names' => uniqid()]);

            Route::get('sales-summary', [DashboardController::class, 'summary']);
            Route::get('order-dashboard', [DashboardController::class, 'orders']);
            Route::get('customer-dashboard', [DashboardController::class, 'customers']);
        });
        Route::get('get-sidebar', [MenuController::class, 'getMenu']);
    });
});

Route::prefix('product-filter-list')->group(function () {
    Route::get('sub-category', [ProductController::class, 'getSubCategories']);
    Route::get('activity', [ProductController::class, 'getActivities']);
    Route::get('color', [ProductController::class, 'getColors']);
    Route::get('size', [ProductController::class, 'getSizes']);
});
//Route::post('/checkout', [CartController::class, 'store']);
Route::get('/get-business-units', [ParameterController::class, 'getBusinessUnits']);
Route::get('/get-roles', [ParameterController::class, 'getRoles']);
Route::get('/get-positions', [ParameterController::class, 'getPositions']);
Route::get('/get-tiers', [ParameterController::class, 'getTiers']);
Route::get('/get-sales', [ParameterController::class, 'getSales']);
Route::get('/get-direct', [ParameterController::class, 'getDirectTos']);
Route::get('/get-auth', [ParameterController::class, 'getAuths']);
Route::get('/get-taxtypes', [ParameterController::class, 'getTaxTypes']);
Route::get('/get-transportation-zones', [ParameterController::class, 'getTransportZones']);
Route::get('/get-custom-prices', [ParameterController::class, 'getCustomPrices']);
Route::get('/get-product-b2b', [ParameterController::class, 'getProductsB2b']);
Route::get('/get-configuration-b2b', [ParameterController::class, 'getConfigurationB2B']);

Route::prefix('/rsl')->group(base_path('routes/reseller.php'));
Route::prefix('/cr')->group(base_path('routes/others.php'));