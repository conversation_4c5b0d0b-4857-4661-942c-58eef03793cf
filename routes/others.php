<?php

use Illuminate\Support\Facades\Route;
use App\Helpers\RouteResource;


// Route::middleware(['auth:sanctum'])->group(function () {



$CRUDresources = RouteResource::$resources;

    foreach ($CRUDresources as $module) {

        $routeGroup = [];
        if(array_key_exists('prefix', $module)){
            $routeGroup['prefix'] = $module['prefix'];
        }
        if(array_key_exists('namespace', $module)){
            $routeGroup['namespace'] = $module['namespace'];
        }

        $resources = $module['resources'];

        // module route group
        Route::group($routeGroup, function () use ($resources) {
            foreach ($resources as $resource) {
                if(array_key_exists('controller', $resource)){
                    Route::post('/' . $resource['path'] . '/search', $resource['controller'] . '@search');
                    // resource rest api routes
                    Route::apiResource('/' . $resource['path'], $resource['controller']);
                    // resource multiple update
                    Route::post('/' . $resource['path'] . '/multiple-update', $resource['controller'] . '@multipleUpdate');
                    // resource multiple delete
                    $delete = array_key_exists('delete', $resource) ? $resource['delete'] : false;
                    if ($delete) {
                        Route::post('/' . $resource['path'] . '/multiple-delete', $resource['controller'] . '@multipleDelete');
                    }
                    // resource multiple add
                    $multipleAdd = array_key_exists('multipleAdd', $resource) ? $resource['multipleAdd'] : false;
                    if ($multipleAdd) {
                        Route::post('/' . $resource['path'] . '/multiple-add', $resource['controller'] . '@multipleAdd');
                    }
                    // resource search, sort and paginate
                    $search = array_key_exists('search', $resource) ? $resource['search'] : false;
                    if ($search) {
                        Route::post('/' . $resource['path'] . '/search', $resource['controller'] . '@search');
                    }
                    // resource custom routes
                    if (array_key_exists('custom', $resource)) {
                        $custom = $resource['custom'];
                        foreach ($custom as $customResource) {
                            $path = '/' . $resource['path'] . $customResource['path'];
                            $function = $resource['controller'] . '@' . $customResource['function'];
                            switch ($customResource['method']) {
                                case 'get':
                                    Route::get($path, $function);
                                    break;
                                case 'post':
                                    Route::post($path, $function);
                                    break;
                                case 'put':
                                    Route::put($path, $function);
                                    break;
                                case 'patch':
                                    Route::patch($path, $function);
                                    break;
                                case 'delete':
                                    Route::delete($path, $function);
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }
            }
        });
    }

  // });