<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\Reseller\{
  Link<PERSON>ontroller,
  OTPController,
  RegisterController,
  CartController,
  DanamonController,
  ResellerController,
  ResellerCommissionController,
  ResellerNotificationController,
  TransactionController,
  JNEController,
    MasterSiteController,
    UserController,
  TaxController,
  ProductController as RslProductController
};

use App\Http\Controllers\{
  ProductController
};

Route::middleware(['reseller-register'])->group(function () {
  Route::post('/reseller-registration', [RegisterController::class, 'registerAsReseller']);
});

Route::post('/check-email', [RegisterController::class, 'validateExistingEmail']);
Route::post('/check-phonenumber', [RegisterController::class, 'validateExistingPhoneNumber']);
Route::get('/faq', [RegisterController::class, 'faq']);
Route::get('/terms-and-conditions', [RegisterController::class, 'termsAndConditions']);

Route::post('/request-otp', [OTPController::class, 'requestOTP']);
Route::post('/verify-otp', [OTPController::class, 'verifyOTP']);

// guest user customer reseller
Route::post('/guest/token', [UserController::class, 'generateGuestToken']);

Route::get('/master-site',[MasterSiteController::class,'index']);

Route::middleware('reseller-link')->group(function(){
  Route::prefix('public')->group(function () {
    Route::prefix('product')->group(function () {
      // Route::middleware(
      //   ['preCache',
      //    'postCache'])->group(function () {
      //     Route::get('/catalog', [ProductController::class, 'indexPublic']);
      //    });
    // Route::get('/catalog', [ProductController::class, 'indexPublic']);
    Route::middleware(
        ['preCache',
         'postCache:120'])->group(function () {
          Route::get('/catalog', [RslProductController::class, 'indexCustomer']);
          Route::get('/detail/{sku}', [RslProductController::class, 'getDetailPublic']); 
         });

    });
  });
});

Route::middleware(['auth:sanctum'])->group(function () {
  // Route Internal
  Route::middleware('salesreseller')->group(function () {
    Route::prefix('internal')->group(function () {

      //pph21
      Route::resource('pph', TaxController::class, [
        'only' => ['show']
    ]);

      //product
      Route::get('/products', [ProductController::class, 'internalResellerIndex']);
      Route::get('/products/{sku}', [ProductController::class, 'getDetailPublicInternalResellerNew']);
      Route::get('/products/{sku}/detail', [ProductController::class, 'getDetailPublicInternal']);
      Route::get('/products/variant/{sku}', [ProductController::class, 'getProductVariant']);

      // applicants
      Route::get('/list-applicants', [RegisterController::class, 'listResellerApplicants']);
      Route::get('/list-applicants-history', [RegisterController::class, 'listResellerApplicantsHistory']);
      Route::post('/detail-applicant', [RegisterController::class, 'detailResellerApplicants']);
      Route::post('/verify-reseller', [RegisterController::class, 'verifyReseller']);

      // resellers
      Route::get('/list-resellers', [ResellerController::class, 'listResellers']);
      Route::get('/top-performance', [ResellerController::class, 'topResellers']);
      Route::prefix('reseller')->group(function () {
        Route::get('/download', [ResellerController::class, 'downloadResellerData']);
        Route::get('/{id}', [ResellerController::class, 'resellerProfile']);
        Route::post('/{id}', [ResellerController::class, 'editReseller']);
        Route::post('/{id}/status', [ResellerController::class, 'editResellerStatus']);
        Route::get('/{id}/transactions', [ResellerController::class, 'listTransactions']);
        Route::get('/{id}/chart', [ResellerController::class, 'resellerChart']);
        Route::get('/{id}/top-selling-item', [ResellerController::class, 'topSellingItems']);
        Route::post('/{id}/deactivate', [RegisterController::class, 'deactivateCustomerReseller']);
      });
      Route::get('/ongoing-orders', [ResellerController::class, 'ongoingOrdersDashboardInternal']);
      Route::get('/orders', [ResellerController::class, 'ordersDashboardInternal']);
      Route::get('/sales', [ResellerController::class, 'salesDashboardInternal']);
      Route::get('/orders/{id}', [ResellerController::class, 'resellerOrderListInternal']);
      Route::get('/orders/detail/{id}', [TransactionController::class, 'orderDetailInternal']);
      Route::get('/top-selling-item', [ResellerController::class, 'topSellingItemsAll']);
      Route::get('/chart-all', [ResellerController::class, 'resellerChartAll']);
      //todo
      Route::get('/chart-all/download', [ResellerController::class, 'resellerChartAllDownload']);

      // dash
      Route::get('/dashboard', [RegisterController::class, 'ResellerApplicantsDashboard']);
      Route::get('/dashboard/members', [ResellerController::class, 'ResellerInternalDashboard']);
      Route::get('/dashboard/summary', [ResellerController::class, 'ResellerInternalSummaryDashboard']);
      Route::get('/dashboard/commissions', [ResellerController::class, 'ResellerInternalCommissionsDashboard']);

      //commissions
      Route::prefix('commissions')->group(function () {
        Route::get('/rank', [ResellerController::class, 'topCommissions']);
        Route::get('/withdrawals', [ResellerController::class, 'listCommissionsWithdrawalsInternal']);
        Route::get('/withdrawals/{id}', [ResellerController::class, 'withdrawalDetailInternal']);
        Route::group([ 'middleware' => 'transaction'], function () {
          Route::post('/adjustment/{id}', [ResellerController::class, 'internalAdjustment']);
          Route::post('/withdrawals/verify', [ResellerController::class, 'verifyWithdrawalInternal']);
        });
      });
    });
  });

  // user reseller
  Route::middleware(['reseller'])->group(function () {
    Route::get('/count-history-link', [LinkController::class, 'countHistoryLink']);
    Route::post('/check-fee', [JNEController::class, 'checkFee']);
    Route::post('/generate-link', [LinkController::class, 'generateLink']);
    Route::get('/store-link', [LinkController::class, 'getStoreLink']);
    Route::get('/links', [LinkController::class, 'getAllLinks']);

    Route::get('/dashboard', [ResellerController::class, 'getPerformaDashboard']);
    Route::get('/latest-transaction', [ResellerController::class, 'getLatestTransactions']);
    Route::get('/monthly-performance', [ResellerController::class, 'getMonthlyPerformance']);
    Route::get('/popular-product', [ResellerController::class, 'getPopularProducts']);
    Route::get('/index-transaction', [ResellerController::class, 'getIndexTransaction']);
    Route::post('/transaction-list', [ResellerController::class, 'getListTransaction']);

    Route::middleware(['reseller-order'])->group(function () {
      Route::post('/transaction-detail', [ResellerController::class, 'getDetailTransaction']);
    });
   
    Route::get('/sales-report', [ResellerController::class, 'getSalesReport']);
     
    Route::get('/bank', [ResellerController::class, 'getRegisteredBank']);
    Route::get('/bank-list', [ResellerController::class, 'getListBank']);

    Route::get('/transfer-method', [ResellerCommissionController::class, 'getTransferMethod']);
    Route::get('/index-commission', [ResellerCommissionController::class, 'getIndexCommission']);
    Route::post('/commission-withdrawals', [ResellerCommissionController::class, 'getListCommissionWithdrawal']);
    Route::post('/commission-detail', [ResellerCommissionController::class, 'commissionWithdrawalDetail']);
    Route::post('/calculate-withdrawal', [ResellerCommissionController::class, 'calculateWithdrawalTax']);
    
    Route::middleware('throttle:custom_rc')->group(function () {
      Route::post('/request-withdrawal', [ResellerCommissionController::class, 'createWithdrawalNew']);
    });
    
    
    Route::get('/notifications', [ResellerNotificationController::class, 'getNotification']);
    Route::get('/read-notifications', [ResellerNotificationController::class, 'readAll']);
    Route::post('/read-notification', [ResellerNotificationController::class, 'readOne']);
  });

  Route::prefix('product')->group(function () {
    Route::get('/catalog', [RslProductController::class, 'index']);
    Route::get('/detail/{sku}', [ProductController::class, 'getDetail']);
  });

  Route::prefix('profile')->group(function () {
    Route::get('/', [ResellerController::class, 'getProfile']);
    Route::post('/update', [ResellerController::class, 'updateProfile']);
  });

  Route::middleware('customer-reseller')->group(function () {
    Route::prefix('customer')->group(function () {
      Route::get('/transactions',[TransactionController::class,'getTransactions']);

      Route::get('/profile',[UserController::class,'getProfileCustomer']);
      Route::post('/change-password',[UserController::class,'changePasswordCustomer']);
    });
  });
});


Route::get('/link/{identifier}', [LinkController::class, 'getIdentifier']);
Route::post('/check-stock',[TransactionController::class,'checkStock']);
Route::post('/resend-activation', [UserController::class, 'resendActivateLink']);

// user customer reseller
Route::post('/login', [UserController::class, 'login']);
Route::post('/forgot-password', [UserController::class, 'forgotPassword']);
Route::post('/change-forgot-password', [UserController::class, 'changeForgotPassword']);

Route::middleware('auth-device')->group(function () {

  Route::get('/transaction/{order_no}',[TransactionController::class,'getTransactionDetail']);

  Route::post('/register-customer', [UserController::class, 'registerCustomerReseller']);
  Route::post('/validate-1/register-customer', [UserController::class, 'validate1RegCustomer']);
  Route::post('/validate-2/register-customer', [UserController::class, 'validate2RegCustomer']);
  Route::post('/activation', [UserController::class, 'activateUserReseller']);

  Route::get('/profile/reseller/{reseller_id}', [UserController::class, 'getProfileResellerForCustomer']);
    
  Route::get('/transport',[TransactionController::class,'getTransport']);
  Route::post('/order-transporter',[TransactionController::class,'storeOrderTransporter']);
  Route::get('/customer-shipment',[UserController::class,'getShipment']);
  Route::post('/customer-shipment',[UserController::class, 'upsertShipment']);
  Route::delete('/customer-shipment',[UserController::class,'deleteShipment']);
  Route::middleware(['reseller-link'])->group(function () {
  
    Route::get('/cart', [CartController::class, 'getCart']);
    Route::post('/cart', [CartController::class, 'addCart']);
    Route::post('/cart-delete', [CartController::class, 'deleteCart']);
    Route::post('/cart-delete-all', [CartController::class, 'deleteAllCart']);
  
    Route::post('/checkout', [TransactionController::class, 'checkout']);
    Route::post('/proceed-to-payment',[TransactionController::class,'proceedToPayment']);
    Route::post('/transaction/cancel', [TransactionController::class, 'cancel']);
    Route::post('/apply-bundling',[TransactionController::class,'applyBundlingOrder']);
    
    Route::get('/order-promotions',[TransactionController::class,'getDiscounts']);
    Route::post('/order-promotions',[TransactionController::class,'storeOrderPromotions']);
    Route::delete('/order-promotions/{order_no}',[TransactionController::class,'deleteOrderPromotions']);
  
  });
});
// Route::get('/test',[TransactionController::class,'testAsync']);
// Route::group([ 'middleware' => 'transaction'], function () {
  Route::get('/test', [TransactionController::class, 'testAsync']);

// });
Route::prefix('danamon')->group(function () {
  // Route::post('/uat-debug', [DanamonController::class, 'aioFunction']);
  Route::apiResource('/va', DanamonController::class);
  // Route::post('/va/inquiry', [DanamonController::class, 'inquiryVA']);
  // Route::post('/va/inquiry-mutation', [DanamonController::class, 'mutationVA']);
  Route::post('/va/bank-inquiry', [DanamonController::class, 'inquiryBank']);
  // Route::post('/va/bank-danamon-inquiry', [DanamonController::class, 'inquiryBankDanamon']);
  // Route::post('/va/bank-danamon-inquiry/balance', [DanamonController::class, 'inquiryBalanceDanamon']);
  // Route::post('/va/transfer', [DanamonController::class, 'topupTransfer']);
  // Route::post('/va/transfer-inquiry', [DanamonController::class, 'topupInquiry']);

});


