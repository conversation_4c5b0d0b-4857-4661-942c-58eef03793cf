<?php

use App\Models\Invoice;
use App\Models\InvoiceDetail;
use App\Models\ProformaDetail;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return response()->json(
        [
            'error' => false,
            'status' => 'success',
            'message' => 'hi.',
            'data' => []
        ],
        200,
        [],
        JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT
    );
});

Route::get('/storage/stock/{param?}', function ($param) {
    $get = Storage::disk('s3')->get('public/stock/' . $param);
    $mime_type = Storage::disk('s3')->mimeType('public/stock/' . $param);
    return response()->make($get, 200, ['Content-Type' => (string) $mime_type]);
});

Route::get('/forgot-password', function () {
    return view('email_verification', [
        'reference_object' => "customer",
        "token" => 'eyjvndjvbbfb',
        "email" => "<EMAIL>",
        "distribution_channel" => "B2B",
        "name" => "Aghits",
        "youtube" => "youtube.com",
        "facebook" => "facebook.com",
        "twitter" => "twitter",
        "tiktok" => "tiktok.com",
        "instagram" => "instagram.com"
    ]);
});




// Route::get('/invoice/pdf/{invoice_no}', function ($invoice) {
//     $invoice_no = $invoice;
//     $data['invoice'] = DB::table('invoice')
//         ->leftjoin('customers as cst', 'cst.customer_id', '=', 'invoice.customer_id')
//         ->leftjoin('customer_shipment as cs', 'cs.customer_id', '=', 'cst.customer_id')
//         ->leftjoin('order_header as oh', 'invoice.order_no', '=', 'oh.order_no')
//         ->leftjoin('delivery_number as dn', 'invoice.invoice_no', '=', 'dn.invoice_no')
//         ->leftjoin('delivery_order as do', 'invoice.delivery_order_no', '=', 'do.delivery_order_no')
//         ->where('invoice.invoice_no', $invoice_no)
//         ->select('invoice.*', 'cst.owner_name', 'cst.npwp', 'cs.phone_number', 'cs.address as shipment_address', 'cs.city', 'cs.province', 'cs.zip_code', 'oh.distribution_channel', 'oh.shipping_charges', 'oh.order_status', 'oh.created_date as order_date', 'dn.delivery_no', 'do.good_issue_date')
//         ->first();

//     $data['invoice_detail'] = DB::table('invoice_detail as invd')
//         ->leftjoin('invoice as inv', 'inv.invoice_no', '=', 'invd.invoice_no')
//         ->leftjoin('article as art', 'art.article', '=', 'invd.article')
//         ->leftJoin('master_color as mc', 'art.product_variant_c', '=', 'mc.key')
//         ->where('inv.invoice_no', $invoice_no)
//         ->select('invd.*', 'art.article_description', 'mc.value')
//         ->get();

//     $data['grand_total'] = InvoiceDetail::query()
//         ->where('invoice_no', '=', $invoice_no)
//         ->select(
//             DB::raw('sum(gross_price) as subtotal'),
//             DB::raw('sum(discount_percent/100*gross_price) as discount'),
//             DB::raw('sum(nett_price) as total'),
//             DB::raw('sum(qty) as qty')
//         )
//         ->first();

//     if (empty($data['invoice'])) {
//         $data['invoice'] = DB::table('proforma_invoice as pinv')
//             ->join('customers as cst', 'cst.customer_id', '=', 'pinv.customer_external_id')
//             ->join('customer_shipment as cs', 'cs.customer_id', '=', 'pinv.customer_external_id')
//             ->where('pinv.sales_order_no', $invoice_no)
//             ->select('pinv.*', 'pinv.created_date as billing_date', 'cst.owner_name', 'cst.phone_number', 'cst.npwp', 'cs.address', 'cs.city', 'cs.province', 'cs.zip_code')
//             ->first();


//         $data['invoice_detail'] = ProformaDetail::query()
//             ->join('proforma_invoice as pinv', 'pinv.id', '=', 'proforma_invoice_detail.proforma_invoice_id')
//             ->where('proforma_invoice_id', $data['invoice']->id)
//             ->select('proforma_invoice_detail.*')
//             ->get();

//         $data['grand_total'] = ProformaDetail::query()
//             ->join('proforma_invoice as pinv', 'pinv.id', '=', 'proforma_invoice_id')
//             ->where('proforma_invoice_id', $data['invoice']->id)
//             ->select(
//                 DB::raw('sum(proforma_invoice_detail.gross_price) as subtotal'),
//                 DB::raw('sum(proforma_invoice_detail.nett_price) as total'),
//                 DB::raw('sum(proforma_invoice_detail.discount/100*proforma_invoice_detail.gross_price) as discount'),
//                 DB::raw('sum(qty) as qty'),
//                 'pinv.down_payment',
//                 'pinv.down_payment_percentage'
//             )
//             ->first();


//     }

//     if ($data['invoice'] && $data['invoice']->npwp) {
//         try {
//             $data['invoice']->npwp = Crypt::decrypt($data['invoice']->npwp);
//         } catch (\Exception $e) {
//             // Handle invalid decryption gracefully
//             // $data['invoice']->npwp = $e->getMessage();
//         }
//     }


//     // return $data;
//     $pdf = Pdf::loadView('pdf_invoice_new', $data)->setPaper('a4', 'potrait');
//     return $pdf->stream('invoice.pdf');
// });