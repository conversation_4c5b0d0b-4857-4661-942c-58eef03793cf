<?php

namespace Tests\Unit\Repositories;

use Tests\TestCase;
use App\Models\User;
use App\Models\Customer;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use Mockery;

class UserRepositoryTest extends TestCase
{
    protected $userRepo;

    protected function setUp(): void
    {
        parent::setUp();
        $this->userRepo = new UserRepository();
    }

    public function test_login_fails_with_invalid_credentials()
    {
        Auth::shouldReceive('attempt')
            ->once()
            ->andReturn(false);

        $req = new Request([
            'username' => 'wronguser',
            'password' => 'wrongpass',
            'type' => 'internal'
        ]);

        $response = $this->userRepo->login($req);
        $this->assertEquals(400, $response->getStatusCode());
    }

    // public function test_login_fails_with_unauthorized_access_type()
    // {
    //     $user = User::factory()->make([
    //         'reference_object' => 'unauthorized',
    //         'is_active' => 1
    //     ]);

    //     Auth::shouldReceive('attempt')->once()->andReturn(true);

    //     $req = Mockery::mock(Request::class);
    //     $req->shouldReceive('has')->with('email')->andReturn(false);
    //     $req->username = 'testuser';
    //     $req->password = 'password';
    //     $req->type = 'internal';
    //     $req->shouldReceive('user')->andReturn($user);

    //     $response = $this->userRepo->login($req);
    //     $this->assertEquals(401, $response->getStatusCode());
    // }

    // public function test_login_success_for_sales_user()
    // {
    //     $user = User::factory()->make([
    //         'reference_object' => 'sales',
    //         'is_active' => 1,
    //     ]);
    //     $user->setRelation('business_units', collect([1, 2]));

    //     Auth::shouldReceive('attempt')->once()->andReturn(true);

    //     $req = Mockery::mock(Request::class);
    //     $req->shouldReceive('has')->with('email')->andReturn(false);
    //     $req->username = 'salesuser';
    //     $req->password = 'password';
    //     $req->type = 'internal';
    //     $req->shouldReceive('user')->andReturn($user);

    //     $this->userRepo = Mockery::mock(UserRepository::class)->makePartial();
    //     $this->userRepo->shouldReceive('authResponse')->with($user)->andReturn([
    //         'auth' => [
    //             'access_token' => 'token123',
    //         ]
    //     ]);
    //     $this->userRepo->shouldReceive('sendSuccessWithCookies')->andReturnUsing(function ($msg, $data) {
    //         return response()->json(['status' => 200, 'message' => $msg]);
    //     });

    //     $response = $this->userRepo->login($req);
    //     $this->assertEquals(200, $response->getData()->status);
    // }

    // public function test_login_fails_when_customer_is_blocked()
    // {
    //     $customer = new Customer(['is_blocked' => 1]);

    //     $user = User::factory()->make([
    //         'reference_object' => 'customer',
    //         'is_active' => 1,
    //     ]);
    //     $user->setRelation('customer', $customer);

    //     Auth::shouldReceive('attempt')->once()->andReturn(true);

    //     $req = Mockery::mock(Request::class);
    //     $req->shouldReceive('has')->with('email')->andReturn(false);
    //     $req->username = 'blockeduser';
    //     $req->password = 'password';
    //     $req->type = 'external';
    //     $req->shouldReceive('user')->andReturn($user);

    //     $response = $this->userRepo->login($req);
    //     $this->assertEquals(401, $response->getStatusCode());
    // }

    // protected function tearDown(): void
    // {
    //     Mockery::close();
    //     parent::tearDown();
    // }
}