<?php
namespace Tests\Unit\Wholesales;

use Mockery;
use Tests\TestCase;
use App\Models\User;
use App\Models\Customer;
use Illuminate\Http\Request;
use App\Http\Controllers\CartController;

class CartControllerTest extends TestCase
{
    protected $cartController;

    protected function setUp(): void
    {
        parent::setUp();
        $this->cartController = new CartController();
    }

    public function test_add_bulk_success()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new \Illuminate\Http\Request([
            'sku_code_c' => '910001677',
            'items' => [
                [
                    'article' => '910000058002',
                    'qty' => 1
                ]
            ]
        ]);

        $response = $this->cartController->bulkAddToCart($request);

        $this->assertEquals(201, $response->status());

    }

    public function test_error_add_bulk_qty_invalid()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new \Illuminate\Http\Request([
            'sku_code_c' => '910001677',
            'items' => [
                [
                    'article' => '910000058002',
                    'qty' => 'test'
                ]
            ]
        ]);

        $response = $this->cartController->bulkAddToCart($request);

        $this->assertEquals(422, $response->status());

    }

    public function test_error_add_bulk_customer_not_verify()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100026',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new \Illuminate\Http\Request([
            'sku_code_c' => '910001677',
            'items' => [
                [
                    'article' => '910000058002',
                    'qty' => 1
                ]
            ]
        ]);

        $response = $this->cartController->bulkAddToCart($request);

        $this->assertEquals(403, $response->status());

    }

    public function test_check_cart_success()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new \Illuminate\Http\Request([
            'sku_code_c' => '910004046',
            'items' => [
                [
                    'article' => '910004046002',
                    'selected' => true
                ]
            ]
        ]);

        $response = $this->cartController->checkCartBulk($request);
        $this->assertEquals(201, $response->status());

    }


    public function test_check_cart_error_validation()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new \Illuminate\Http\Request([
            'sku_code_c' => '910004046',
            'items' => [
                [
                    'article' => '910004046002'
                ]
            ]
        ]);

        $response = $this->cartController->checkCartBulk($request);
        $this->assertEquals(422, $response->status());

    }

    public function test_destroy_cart_item_success()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new \Illuminate\Http\Request([
            'sku_code_c' => '910004046',
            'items' => [
                [
                    'article' => '910004046002',
                ]
            ]
        ]);

        $response = $this->cartController->destroyBulk($request);
        $this->assertEquals(201, $response->status());

    }

    public function test_destroy_cart_item_error_validation()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new \Illuminate\Http\Request([
            'sku_code_c' => '910004046',
            'items' => []
        ]);

        $response = $this->cartController->destroyBulk($request);
        $this->assertEquals(422, $response->status());

    }
    public function test_add_remark_success()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new \Illuminate\Http\Request([
            'attachment_group_id' => 'ec687768-6bed-4a07-8140-352f429904e9',
            'remark' => 'test remark update'
        ]);

        $response = $this->cartController->addRemark($request);
        $this->assertEquals(201, $response->status());

    }

    public function test_add_remark_error_validation()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new \Illuminate\Http\Request([
            'remark' => 'test remark update'
        ]);

        $response = $this->cartController->addRemark($request);
        $this->assertEquals(422, $response->status());

    }
}