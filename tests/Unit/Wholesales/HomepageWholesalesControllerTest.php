<?php

namespace Tests\Unit\Wholesales;

use PHPUnit\Framework\TestCase;
use App\Http\Controllers\HomepageWholesalesController;

class HomepageWholesalesControllerTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */
    protected $homepageController;

    protected function setUp(): void
    {
        parent::setUp();
        $this->homepageController = new HomepageWholesalesController();
    }

    public function test_pesanan_berjalan_success()
    {
        $this->assertTrue(true);
    }
}