<?php

namespace Tests\Unit\Wholesales;

use Tests\TestCase;
use App\Models\User;
use App\Models\Customer;
use Illuminate\Http\Request;
use App\Interfaces\InvoiceInterface;
use App\Repositories\InvoiceRepository;
use App\Http\Controllers\InvoiceController;

class InvoiceControllerTest extends TestCase
{
    /**
     * A basic unit test example.
     *
     * @return void
     */

    protected $invoiceController;

    protected function setUp(): void
    {
        parent::setUp();
        $this->invoiceController = new InvoiceController(new InvoiceRepository());
    }

    public function test_list_invoice_success()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new Request();

        $request->setUserResolver(fn () => $user);
    
        $response = $this->invoiceController->index($request);

    
        $this->assertEquals(200, $response->status());
    }

    public function test_get_invoice_header_success()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new Request();

        $request->setUserResolver(fn () => $user);
    
        $response = $this->invoiceController->getDetailHeader('20000105',$request);

    
        $this->assertEquals(200, $response->status());
    }
    public function test_get_detail_items_invoice_success()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new Request();

        $request->setUserResolver(fn () => $user);
    
        $response = $this->invoiceController->getDetailItems('20000105',$request);

    
        $this->assertEquals(200, $response->status());
    }
    public function test_get_list_payment_invoice_not_found()
    {
        $customer = Customer::factory()->make([
            'customer_id' => '100027',
        ]);

        $user = User::factory()->make([
            'reference_object' => 'customer',
            'reference_id' => $customer->customer_id, // ensures match
        ]);

        $user->setRelation('customer', $customer);
        $this->actingAs($user);

        $request = new Request();

        $request->setUserResolver(fn () => $user);
    
        $response = $this->invoiceController->listPayment('300001990',$request);

    
        $this->assertEquals(404, $response->status());
    }


}